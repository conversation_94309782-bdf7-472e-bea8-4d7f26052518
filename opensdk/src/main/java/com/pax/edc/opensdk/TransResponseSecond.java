/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210913 	         jiaguang                create
 *
 * ============================================================================
 */

package com.pax.edc.opensdk;

import android.os.Bundle;
import androidx.annotation.NonNull;

/**
 * trans response for some common response params
 */
public class TransResponseSecond extends TransResponse {
    //统一修改所有字段的名字，将开头的'm'去掉
    //necessary params
    private String approveCode;

    //RRN
    private String rRN;

    /**
     * Instantiates a new Trans response second.
     */
    public TransResponseSecond() {
    }

    /**
     * Instantiates a new Trans response second.
     *
     * @param bundle the bundle
     */
    public TransResponseSecond(Bundle bundle) {
        fromBundle(bundle);
    }

    @Override
    void fromBundle(Bundle bundle) {
        super.fromBundle(bundle);
        approveCode = IntentUtil.getStringExtra(bundle, Constants.Response.APPROVE_CODE);
        rRN = IntentUtil.getStringExtra(bundle, Constants.Response.RETRIEVAL_RESP_NO);
    }

    @NonNull
    Bundle toBundle(@NonNull Bundle bundle) {
        super.toBundle(bundle);
        bundle.putString(Constants.Response.APPROVE_CODE, approveCode);
        bundle.putString(Constants.Response.RETRIEVAL_RESP_NO, rRN);
        return bundle;
    }

    @Override
    boolean checkArgs() {
        return true;
    }

    /**
     * Gets approve code.
     *
     * @return the approve code
     */
    public String getApproveCode() {
        return approveCode;
    }

    /**
     * Sets approve code.
     *
     * @param approveCode the m approve code
     */
    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }

    /**
     * Gets retrieval resp no.
     *
     * @return the retrieval resp no
     */
    public String getRetrievalRespNo() {
        return rRN;
    }

    /**
     * Sets retrieval resp no.
     *
     * @param rRN the m rrn
     */
    public void setRetrievalRespNo(String rRN) {
        this.rRN = rRN;
    }

    @Override
    public String toString() {
        return "TransResponseSecond{\n" +
                "approveCode=" + approveCode + "\n" +
                "rRN=" + rRN + "\n"+super.toString()+
                "\n}";
    }
}
