/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20211231  	         jiaguang                create
 *
 * ============================================================================
 */

package com.pax.edc.opensdk;

/**
 * Some constants for 3rd api parameters checking
 */
public class RequestArgsConstants {

    public static final String HASE_PACKAGE_NAME = "com.pax.edc.hase";
    public static final String INSTALL_HASE_FIRST = "Please install HASE EDC first";


    public static final int TRANS_REF_NO_LENGTH = 16;
    public static final String TRANS_REF_NO_INVALID = "transReferenceNo is invalid";

    public static final int TRACE_NO_MAX_LENGTH = 6;
    public static final String TRACE_NO_INVALID = "traceNo is invalid";

    public static final int ACQUIRER_LOWER = 0x01;
    public static final int ACQUIRER_UPPER = 0x13;
    public static final String ACQUIRER_INDEX_INVALID = "acquirerIndex is invalid";


    public static final String SUCCESS = "";
    public static final String APP_ID_INVALID = "appId is invalid";
    public static final String PACKAGE_NAME_INVALID = "packageName is invalid";


    public static final String TRANS_TYPE_INVALID = "transType is invalid";
    public static final String TRANS_TYPE_DETAIL_INVALID = "transTypeDetail invalid";
    public static final String TOTAL_AMOUNT_INVALID = "totalAmount is invalid";
    public static final String TIP_AMOUNT_INVALID = "tipAmount is invalid";


    public static final int CIAM_LENGTH = 20;
    public static final String CIAM_INVALID = "ciam Invalid";

}
