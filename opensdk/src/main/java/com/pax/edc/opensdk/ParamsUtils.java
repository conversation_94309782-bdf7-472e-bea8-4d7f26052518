/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210810 	         jiaguang                create
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

/**
 * 对于三方应用api的请求和响应中的某个参数，大部分交易包含该参数，少部分不包含该参数，
 * 包含该参数的交易又没有可以抽离出一个共有父类的共性
 * 若要满足“最小知识原则”，那么不包含该参数的类，其自身、父类都不能包含该参数，
 * 这导致很多子类中包含同名的参数（因为不能抽离到父类中）
 * 为解决该问题，将所有交易的类都包含该参数，但是在设置/获取该参数时，判断是否包含该参数，
 * 若包含，则执行正常
 * 若不包含，则抛出运行时异常
 * 而判断某个交易是否包含该参数的逻辑，就放到这个类中
 * As some params  request and response for 3rd party,
 */
public class ParamsUtils {

    /**
     * The constant TRANS_TYPE_DETAIL_UNSUPPORTED.
     */
    public static final String TRANS_TYPE_DETAIL_UNSUPPORTED =
            "Transaction type detail unsupported!";
    /**
     * The constant TRANS_REF_NO_UNSUPPORTED.
     */
    public static final String TRANS_REF_NO_UNSUPPORTED =
            "Transaction reference number unsupported!";

    /**
     * Ensure to has trans type detail.
     *
     * @param transType the trans type
     */
    public static void ensureToHasTransTypeDetail(int transType) {
        if (!hasTransTypeDetail(transType)) {
            throw new RuntimeException(TRANS_TYPE_DETAIL_UNSUPPORTED);
        }
    }

    /**
     * Ensure to has trans ref no.
     *
     * @param transType the trans type
     */
    public static void ensureToHasTransRefNo(int transType) {
        if (!hasTransRefNo(transType)) {
            throw new RuntimeException(TRANS_REF_NO_UNSUPPORTED);
        }
    }

    /**
     * Has trans type detail boolean.
     *
     * @param transType the trans type
     * @return the boolean
     */
    public static boolean hasTransTypeDetail(int transType) {
        switch (transType) {
            case Constants.SALE:
            case Constants.OFFLINE:
            case Constants.REFUND:
            case Constants.VOID:
            case Constants.ADJUST:
            case Constants.INSTALLMENT:
            case Constants.TRANS_RETRIEVAL:
            case Constants.SETTLE:
            case Constants.DCC_OPT_OUT:
            case Constants.AUTH:
            case Constants.PRE_AUTH:
            case Constants.PRE_AUTH_CM:
            case Constants.PRE_AUTH_VOID:
            case Constants.PRE_AUTH_CM_VOID:
            case Constants.REPRINT_TRANS:
            case Constants.PRINT_SUMMARY:
            case Constants.PRINT_ALL_TRANSACTION_DETAIL:
            case Constants.PRINT_TRANSACTION_DETAIL:
            case Constants.PRINT_LAST_SETTLE:{
                return true;
            }
            case Constants.YUU_CREATION:
            case Constants.YUU_CREATION_RETRIEVAL:
            case Constants.YUU_ENQUIRY: {
                return false;
            }

            default:
                return false;
        }
    }

    /**
     * Has trans ref no boolean.
     *
     * @param transType the trans type
     * @return the boolean
     */
    public static boolean hasTransRefNo(int transType) {
        switch (transType) {
            case Constants.SALE:
            case Constants.OFFLINE:
            case Constants.REFUND:
            case Constants.VOID:
            case Constants.ADJUST:
            case Constants.INSTALLMENT:
            case Constants.YUU_CREATION:
            case Constants.YUU_ENQUIRY:
            case Constants.DCC_OPT_OUT:
            case Constants.AUTH:
            case Constants.PRE_AUTH:
            case Constants.PRE_AUTH_CM:
            case Constants.PRE_AUTH_VOID:
            case Constants.PRE_AUTH_CM_VOID: {
                return true;
            }
            case Constants.TRANS_RETRIEVAL:
            case Constants.REPRINT_TRANS:
            case Constants.PRINT_SUMMARY:
            case Constants.YUU_CREATION_RETRIEVAL:
            case Constants.PRINT_ALL_TRANSACTION_DETAIL:
            case Constants.PRINT_TRANSACTION_DETAIL:
            case Constants.SETTLE:
            case Constants.PRINT_LAST_SETTLE: {
                return false;
            }
            default:
                return false;
        }
    }

    /**
     * Has response code boolean.
     *
     * @param transType the trans type
     * @return the boolean
     */
    public static boolean hasResponseCode(int transType) {
        switch (transType) {
            case Constants.SALE:
            case Constants.REFUND:
            case Constants.VOID:
            case Constants.INSTALLMENT:
            case Constants.YUU_CREATION:
            case Constants.YUU_CREATION_RETRIEVAL:
            case Constants.YUU_ENQUIRY:
            case Constants.TRANS_RETRIEVAL:
            case Constants.SETTLE:
            case Constants.AUTH:
            case Constants.PRE_AUTH:
            case Constants.PRE_AUTH_CM:
            case Constants.PRE_AUTH_VOID:
            case Constants.PRE_AUTH_CM_VOID: {
                return true;
            }
            case Constants.OFFLINE:
            case Constants.ADJUST:
            case Constants.REPRINT_TRANS:
            case Constants.PRINT_SUMMARY:
            case Constants.PRINT_ALL_TRANSACTION_DETAIL:
            case Constants.PRINT_TRANSACTION_DETAIL:
            case Constants.PRINT_LAST_SETTLE:
            case Constants.DCC_OPT_OUT: {
                return false;
            }
        }
        return false;
    }

    /**
     * Has ori trans type boolean.
     *
     * @param transType the trans type
     * @return the boolean
     */
    public static boolean hasOriTransType(int transType) {
        switch (transType) {
            case Constants.VOID:
            case Constants.ADJUST:
            case Constants.TRANS_RETRIEVAL:
            case Constants.DCC_OPT_OUT:
            case Constants.PRE_AUTH_VOID:
            case Constants.PRE_AUTH_CM_VOID: {
                return true;
            }
        }
        return false;
    }


    /**
     * @param decimalNumber
     * 将Int数字转换成0x开头的十六进制
     * @return String
     */
    public static String decimalToHexString(int decimalNumber) {
        //bug-10871 by fanjiaming 2023/9/14 start
        //只有1以上的数字才是有效数字
        if(decimalNumber <= 0 ) return null;
        //bug-10871 by fanjiaming 2023/9/14 end
        String hexString = Integer.toHexString(decimalNumber).toUpperCase();
        //长度不够则左补零
        if(hexString.length()<2)
            hexString = "0"+hexString;
        return "0x" + hexString;
    }

    /**
     * @param hexString
     * 将0x开头的十六进制转成数字
     * @return int
     */
    public static int hexStringToDecimal(String hexString) {
        if(hexString==null || hexString.length()<=2)
            return 0;
        else
            return Integer.parseInt(hexString.substring(2), 16);
    }
}
