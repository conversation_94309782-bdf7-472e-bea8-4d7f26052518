/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20220107  	         jiaguang                modify
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.app.Activity;
import android.content.Intent;

public class HaseTransUtil {

    private final ITransAPI mTransAPI
            = TransAPIFactory.createTransAPI();
    private Activity mActivity;

    public HaseTransUtil(Activity activity){
        mActivity = activity;
    }

    //统一将所有的transTypeDetail修改为整数类型
    public String doSale(String appId, String packageName,
                       int transTypeDetail, String transReferenceNo,
                       long totalAmount, long tipAmount){
        SaleRequest saleRequest = new SaleRequest();
        saleRequest.setAppId(appId);
        saleRequest.setPackageName(packageName);
        saleRequest.setTransTypeDetail(transTypeDetail);
        saleRequest.setTransRefNo(transReferenceNo);
        saleRequest.setAmount(totalAmount);
        saleRequest.setTipAmount(tipAmount);
        return mTransAPI.doTrans(mActivity, saleRequest);
    }

    public String doOffline(String appId, String packageName,
                       int transTypeDetail, String transReferenceNo, long totalAmount){
        RequestTotalAmount offlineRequest = new RequestTotalAmount(Constants.OFFLINE);
        offlineRequest.setAppId(appId);
        offlineRequest.setPackageName(packageName);
        offlineRequest.setTransTypeDetail(transTypeDetail);
        offlineRequest.setTransRefNo(transReferenceNo);
        offlineRequest.setAmount(totalAmount);
        return mTransAPI.doTrans(mActivity, offlineRequest);
    }

    public String doRefund(String appId, String packageName,
                          int transTypeDetail, String transReferenceNo, long totalAmount){
        RequestTotalAmount refundRequest = new RequestTotalAmount(Constants.REFUND);
        refundRequest.setAppId(appId);
        refundRequest.setPackageName(packageName);
        refundRequest.setTransTypeDetail(transTypeDetail);
        refundRequest.setTransRefNo(transReferenceNo);
        refundRequest.setAmount(totalAmount);
        return mTransAPI.doTrans(mActivity, refundRequest);
    }

    public String doVoid(String appId, String packageName,
                         int transTypeDetail, String transReferenceNo, String traceNo){
        RequestTraceNo voidRequest = new RequestTraceNo(Constants.VOID);
        voidRequest.setAppId(appId);
        voidRequest.setPackageName(packageName);
        voidRequest.setTransTypeDetail(transTypeDetail);
        voidRequest.setTransRefNo(transReferenceNo);
        voidRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, voidRequest);
    }

    // 三方api 的 adjust交易实际并没有使用amount和tip这两个参数
    // ecr已支持adjust，但是只有totalAmount 一个参数
    public String doAdjust(String appId, String packageName,
                         int transTypeDetail, String transReferenceNo,
                         long totalAmount, long tipAmount, String traceNo){
        AdjustRequest adjustRequest = new AdjustRequest();
        adjustRequest.setAppId(appId);
        adjustRequest.setPackageName(packageName);
        adjustRequest.setTransTypeDetail(transTypeDetail);
        adjustRequest.setTransRefNo(transReferenceNo);
        adjustRequest.setAmount(totalAmount);
        adjustRequest.setTipAmount(tipAmount);
        adjustRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, adjustRequest);
    }

    public String doInstallment(String appId, String packageName,
                              int transTypeDetail, int acquireIndex,
                              String transReferenceNo, long totalAmount){
        InstallmentMsg.Request installmentRequest = new InstallmentMsg.Request();
        installmentRequest.setAppId(appId);
        installmentRequest.setPackageName(packageName);
        installmentRequest.setTransTypeDetail(transTypeDetail);
        installmentRequest.setAcquirerIndex(acquireIndex);
        installmentRequest.setTransRefNo(transReferenceNo);
        installmentRequest.setAmount(totalAmount);
        return mTransAPI.doTrans(mActivity, installmentRequest);
    }

    public String doTransRetrieval(String appId, String packageName,
                       int transTypeDetail, String traceNo){
        RequestTraceNo transRetrievalRequest = new RequestTraceNo(Constants.TRANS_RETRIEVAL);
        transRetrievalRequest.setAppId(appId);
        transRetrievalRequest.setPackageName(packageName);
        transRetrievalRequest.setTransTypeDetail(transTypeDetail);
        transRetrievalRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, transRetrievalRequest);
    }

    public String doYuuCreation(String appId, String packageName,
                         String transReferenceNo, String ciam){
        YuuCreationMsg.Request yuuCreationRequest = new YuuCreationMsg.Request();
        yuuCreationRequest.setAppId(appId);
        yuuCreationRequest.setPackageName(packageName);
        yuuCreationRequest.setTransRefNo(transReferenceNo);
        yuuCreationRequest.setCiam(ciam);
        return mTransAPI.doTrans(mActivity, yuuCreationRequest);
    }

    public String doYuuEnquiry(String appId, String packageName,
                              String transReferenceNo){
        YuuEnquiryMsg.Request yuuEnquiryRequest = new YuuEnquiryMsg.Request();
        yuuEnquiryRequest.setAppId(appId);
        yuuEnquiryRequest.setPackageName(packageName);
        yuuEnquiryRequest.setTransRefNo(transReferenceNo);
        return mTransAPI.doTrans(mActivity, yuuEnquiryRequest);
    }

    public String doYuuCreationRetrieval(String appId, String packageName){
        YuuCreationRetrievalMsg.Request yuuCreationRetrievalRequest = new YuuCreationRetrievalMsg.Request();
        yuuCreationRetrievalRequest.setAppId(appId);
        yuuCreationRetrievalRequest.setPackageName(packageName);
        return mTransAPI.doTrans(mActivity, yuuCreationRetrievalRequest);
    }


    public String doDccOptOut(String appId, String packageName,
                       int transTypeDetail, String transReferenceNo, String traceNo){
        RequestTraceNo dccOptOutRequest = new RequestTraceNo(Constants.DCC_OPT_OUT);
        dccOptOutRequest.setAppId(appId);
        dccOptOutRequest.setPackageName(packageName);
        dccOptOutRequest.setTransTypeDetail(transTypeDetail);
        dccOptOutRequest.setTransRefNo(transReferenceNo);
        dccOptOutRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, dccOptOutRequest);
    }

    public String doAuth(String appId, String packageName,
                         int transTypeDetail, String transReferenceNo, long totalAmount){
        RequestTotalAmount authRequest = new RequestTotalAmount(Constants.AUTH);
        authRequest.setAppId(appId);
        authRequest.setPackageName(packageName);
        authRequest.setTransTypeDetail(transTypeDetail);
        authRequest.setTransRefNo(transReferenceNo);
        authRequest.setAmount(totalAmount);
        return mTransAPI.doTrans(mActivity, authRequest);
    }

    public String doPreAuth(String appId, String packageName,
                          int transTypeDetail, String transReferenceNo, long totalAmount){
        RequestTotalAmount preAuthRequest = new RequestTotalAmount(Constants.PRE_AUTH);
        preAuthRequest.setAppId(appId);
        preAuthRequest.setPackageName(packageName);
        preAuthRequest.setTransTypeDetail(transTypeDetail);
        preAuthRequest.setTransRefNo(transReferenceNo);
        preAuthRequest.setAmount(totalAmount);
        return mTransAPI.doTrans(mActivity, preAuthRequest);
    }

    public String doPreAuthVoid(String appId, String packageName,
                              int transTypeDetail, String transReferenceNo,long totalAmount,String traceNo){
        PreAuthVoidRequest preAuthVoidRequest = new PreAuthVoidRequest();
        preAuthVoidRequest.setAppId(appId);
        preAuthVoidRequest.setPackageName(packageName);
        preAuthVoidRequest.setTransTypeDetail(transTypeDetail);
        preAuthVoidRequest.setTransRefNo(transReferenceNo);
        preAuthVoidRequest.setAmount(totalAmount);
        preAuthVoidRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, preAuthVoidRequest);
    }

    public String doPreAuthComplete(String appId, String packageName,
                          int transTypeDetail, String transReferenceNo, long totalAmount){
        RequestTotalAmount preAuthCompleteRequest = new RequestTotalAmount(Constants.PRE_AUTH_CM);
        preAuthCompleteRequest.setAppId(appId);
        preAuthCompleteRequest.setPackageName(packageName);
        preAuthCompleteRequest.setTransTypeDetail(transTypeDetail);
        preAuthCompleteRequest.setTransRefNo(transReferenceNo);
        preAuthCompleteRequest.setAmount(totalAmount);
        return mTransAPI.doTrans(mActivity, preAuthCompleteRequest);
    }

    public String doPreAuthCompleteVoid(String appId, String packageName,
                       int transTypeDetail, String transReferenceNo, String traceNo){
        RequestTraceNo preAuthCompleteVoidRequest = new RequestTraceNo(Constants.PRE_AUTH_CM_VOID);
        preAuthCompleteVoidRequest.setAppId(appId);
        preAuthCompleteVoidRequest.setPackageName(packageName);
        preAuthCompleteVoidRequest.setTransTypeDetail(transTypeDetail);
        preAuthCompleteVoidRequest.setTransRefNo(transReferenceNo);
        preAuthCompleteVoidRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, preAuthCompleteVoidRequest);
    }

    public String doReprintTrans(String appId, String packageName,
                                      int transTypeDetail, String traceNo){
        RequestTraceNo reprintTransRequest = new RequestTraceNo(Constants.REPRINT_TRANS);
        reprintTransRequest.setAppId(appId);
        reprintTransRequest.setPackageName(packageName);
        reprintTransRequest.setTransTypeDetail(transTypeDetail);
        reprintTransRequest.setTraceNo(traceNo);
        return mTransAPI.doTrans(mActivity, reprintTransRequest);
    }

    public String doPrintTransDetail(String appId, String packageName,
                                    int transTypeDetail, int acquireIndex){
        PrintTransDetailMsg.Request printTransDetailRequest = new  PrintTransDetailMsg.Request();
        printTransDetailRequest.setAppId(appId);
        printTransDetailRequest.setPackageName(packageName);
        printTransDetailRequest.setTransTypeDetail(transTypeDetail);
        printTransDetailRequest.setAcquirerIndex(acquireIndex);
        return mTransAPI.doTrans(mActivity, printTransDetailRequest);
    }

    public String doPrintAllTrans(String appId, String packageName, int transTypeDetail){
        PrintAllTransMsg.Request printAllTransRequest = new PrintAllTransMsg.Request();
        printAllTransRequest.setAppId(appId);
        printAllTransRequest.setPackageName(packageName);
        printAllTransRequest.setTransTypeDetail(transTypeDetail);
        return mTransAPI.doTrans(mActivity, printAllTransRequest);
    }

    public String doPrintSummary(String appId, String packageName,
                                int transTypeDetail, int acquireIndex) {
        PrintSummaryMsg.Request printSummaryRequest = new PrintSummaryMsg.Request();
        printSummaryRequest.setAppId(appId);
        printSummaryRequest.setPackageName(packageName);
        printSummaryRequest.setTransTypeDetail(transTypeDetail);
        printSummaryRequest.setAcquirerIndex(acquireIndex);
        return mTransAPI.doTrans(mActivity, printSummaryRequest);
    }

    public String doPrintLastSettle(String appId, String packageName, int transTypeDetail){
        PrintLastSettleMsg.Request printLastSettleRequest = new PrintLastSettleMsg.Request();
        printLastSettleRequest.setAppId(appId);
        printLastSettleRequest.setPackageName(packageName);
        printLastSettleRequest.setTransTypeDetail(transTypeDetail);
        return mTransAPI.doTrans(mActivity, printLastSettleRequest);
    }

    public String doSettle(String appId, String packageName,
                         int transTypeDetail, int acquirerIndex){
        SettleMsg.Request reprintRequest = new SettleMsg.Request();
        reprintRequest.setAppId(appId);
        reprintRequest.setPackageName(packageName);
        reprintRequest.setTransTypeDetail(transTypeDetail);
        reprintRequest.setAcquirerIndex(acquirerIndex);
        return mTransAPI.doTrans(mActivity, reprintRequest);
    }

    public BaseResponse getResult(int requestCode, int resultCode, Intent data){
        return mTransAPI.onResult(requestCode, resultCode, data);
    }
}
