/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210811  	         jiaguang                create
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.os.Bundle;

/**
 * Offline message struct
 */
public class OfflineMsg {
    /**
     * Offline Request see RequestTotalAmount.java
     * /


    /**
     * Offline Response
     */
    public static class Response extends TransResponse {

        public Response() {}

        Response(Bundle bundle) {
            fromBundle(bundle);
        }

        @Override
        boolean checkArgs() {
            return true;
        }

        @Override
        public String toString() {
            return "OfflineMsgResponse{\n" +super.toString()+
                    "\n}";
        }
    }
}
