/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210830  	         jiaguang                create
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.os.Bundle;
import androidx.annotation.NonNull;

/**
 * DccOptOut message struct
 */
public class DccOptOutMsg {
    /**
     * DccOptOut Request see RequestTraceNo.java
     */

    /**
     * DccOptOutResponse
     */
    public static class Response extends TransResponse {
        //统一修改所有字段的名字，将开头的'm'去掉
        private String tipAmount;

        /**
         * Instantiates a new Response.
         */
        public Response() {
        }

        /**
         * Instantiates a new Response.
         *
         * @param bundle the bundle
         */
        Response(Bundle bundle) {
            fromBundle(bundle);
        }

        @Override
        void fromBundle(Bundle bundle) {
            super.fromBundle(bundle);

            //optional params
            tipAmount = IntentUtil.getStringExtra(bundle, Constants.Response.TIP_AMOUNT);

        }

        @NonNull
        Bundle toBundle(@NonNull Bundle bundle) {
            super.toBundle(bundle);
            bundle.putString(Constants.Response.TIP_AMOUNT, tipAmount);
            return bundle;
        }

        @Override
        boolean checkArgs() {
            return true;
        }

        /**
         * Gets tip amount.
         *
         * @return the tip amount
         */
        public String getTipAmount() {
            return tipAmount;
        }

        /**
         * Sets tip amount.
         *
         * @param tipAmount the m tip amount
         */
        public void setTipAmount(String tipAmount) {
            this.tipAmount = tipAmount;
        }

        @Override
        public String toString() {
            return "DccOutMsgResponse{\n" +
                    "tipAmount=" + tipAmount + "\n" +super.toString()+
                    "\n}";
        }
    }
}
