/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20211222 	         jiaguang                create
 *
 * ============================================================================
 */

package com.pax.edc.opensdk;

import android.os.Bundle;
import androidx.annotation.NonNull;

/**
 * trans response for preAuth, preAuthVoid, preAuthComplete and preAuthCompleteVoid transactions
 */
public class PreAuthTransResponse extends TransResponse {

    //统一修改所有字段的名字，将开头的'm'去掉
    //necessary params
    private String approveCode;
    //RRN
    private String rRN;
    //CupRRN
    private String cupRRN;


    /**
     * Instantiates a new Trans response third.
     */
    public PreAuthTransResponse() {
    }

    /**
     * Instantiates a new Trans response third.
     *
     * @param bundle the bundle
     */
    PreAuthTransResponse(Bundle bundle) {
        fromBundle(bundle);
    }

    @Override
    void fromBundle(Bundle bundle) {
        super.fromBundle(bundle);

        approveCode = IntentUtil.getStringExtra(bundle, Constants.Response.APPROVE_CODE);
        rRN = IntentUtil.getStringExtra(bundle, Constants.Response.RETRIEVAL_RESP_NO);
        cupRRN = IntentUtil.getStringExtra(bundle, Constants.Response.CUP_RRN);
    }

    @NonNull
    Bundle toBundle(@NonNull Bundle bundle) {
        super.toBundle(bundle);

        bundle.putString(Constants.Response.APPROVE_CODE, approveCode);
        bundle.putString(Constants.Response.RETRIEVAL_RESP_NO, rRN);
        bundle.putString(Constants.Response.CUP_RRN, cupRRN);


        return bundle;
    }

    @Override
    boolean checkArgs() {
        return true;
    }

    /**
     * Gets approve code.
     *
     * @return the approve code
     */
    public String getApproveCode() {
        return approveCode;
    }

    /**
     * Sets approve code.
     *
     * @param approveCode the m approve code
     */
    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }

    /**
     * Gets retrieval resp no.
     *
     * @return the retrieval resp no
     */
    public String getRetrievalRespNo() {
        return rRN;
    }

    /**
     * Sets retrieval resp no.
     *
     * @param rRN the m rrn
     */
    public void setRetrievalRespNo(String rRN) {
        this.rRN = rRN;
    }

    /**
     * Gets cup RRN
     *
     * @return the cup RRN
     */
    public String getCupRRN() {
        return cupRRN;
    }

    /**
     * Sets CUP RRN.
     *
     * @param cupRRN the cup rrn
     */
    public void setCupRRN(String cupRRN) {
        this.cupRRN = cupRRN;
    }

    @Override
    public String toString() {
        return "PreAuthTransResponse{\n" +
                "approveCode=" + approveCode + "\n" +
                "rRN=" + rRN + "\n" +
                "cupRRN=" + cupRRN + "\n"+super.toString()+
                "\n}";
    }
}
