/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210812  	         jiaguang                create
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.os.Bundle;

/**
 * print all transaction message struct
 */
public class PrintAllTransMsg {
    /**
     * print all transaction Request
     */
    public static class Request extends BaseRequest {


        public Request(){}

        Request(Bundle bundle) {
            fromBundle(bundle);
        }

        @Override
        public int getTransType() {
            return Constants.PRINT_ALL_TRANSACTION_DETAIL;
        }

        @Override
        protected String checkTransTypeDetail() {
            if (getTransTypeDetail() == BaseRequest.TRANS_DETAIL_CREDIT_CARD) {
                return RequestArgsConstants.SUCCESS;
            }
            return RequestArgsConstants.TRANS_TYPE_DETAIL_INVALID;
        }

    }

    /**
     * print all transaction has no response
     */
}
