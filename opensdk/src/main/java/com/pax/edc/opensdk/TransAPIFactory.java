/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20170906               Kim.L                  create
 * 20210802  	         jiaguang                modify
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

/**
 * Factory for create a Transaction instance
 */
public class TransAPIFactory {

    /**
     * TransAPIFactory
     */
    private TransAPIFactory() {
    }

    /**
     * createTransAPI
     *
     * @return ITransAPI
     */
    public static ITransAPI createTransAPI() {
        return new TransAPIImpl();
    }
}
