/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20170918               Kim.L                  create
 * 20210802  	         jiaguang                modify
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.os.Bundle;

/**
 * YuuEnquiry message struct
 */
public class YuuEnquiryMsg {
    /**
     * YuuEnquiry Request
     */
    public static class Request extends BaseRequest {

        public Request(){}

        Request(Bundle bundle) {
            fromBundle(bundle);
        }

        @Override
        public int getTransType() {
            return Constants.YUU_ENQUIRY;
        }

        @Override
        protected String checkTransTypeDetail() {
            return RequestArgsConstants.TRANS_TYPE_DETAIL_INVALID;
        }

    }

    /**
     * YuuEnquiry response see TransResponseSecondBuilder.java
     */
}
