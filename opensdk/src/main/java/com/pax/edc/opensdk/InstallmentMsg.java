/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20170918               Kim.L                  create
 * 20210802  	         jiaguang                modify
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.os.Bundle;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;

/**
 * Installment message struct
 */
public class InstallmentMsg {
    /**
     * Installment Request
     */
    public static class Request extends BaseRequest {

        //necessary params
        @IntRange(from = 0L, to = 9999999999L)
        private long mTotalAmount;//Transaction amount include tips (Base Amount+ Tips Amount)

        @IntRange(from = 0x01, to = 0x0E)
        private int mAcquirerIndex;

        /**
         * Instantiates a new Request.
         */
        public Request() {
        }

        /**
         * Instantiates a new Request.
         *
         * @param bundle the bundle
         */
        Request(Bundle bundle) {
            fromBundle(bundle);
        }

        @Override
        public int getTransType() {
            return Constants.INSTALLMENT;
        }

        /**
         * get params from bundle
         * @param bundle the bundle contains params
         */
        @Override
        void fromBundle(Bundle bundle) {
            super.fromBundle(bundle);
            mTotalAmount = IntentUtil.getLongExtra(bundle, Constants.Request.REQ_AMOUNT);
            mAcquirerIndex = IntentUtil.getIntExtra(bundle, Constants.ACQUIRE_INDEX);
        }

        /**
         * add params to bundle
         * @param bundle the bundle to carry the params
         * @return the bundle with params
         */
        @Override
        @NonNull
        Bundle toBundle(@NonNull Bundle bundle) {
            super.toBundle(bundle);
            bundle.putLong(Constants.Request.REQ_AMOUNT, mTotalAmount);
            bundle.putInt(Constants.ACQUIRE_INDEX, mAcquirerIndex);
            return bundle;
        }

        @Override
        protected String checkArgs() {
            String checkResult = super.checkArgs();
            if (!RequestArgsConstants.SUCCESS.equals(checkResult)){
                return checkResult;
            }
            if (getAmount() < 0){
                return RequestArgsConstants.TOTAL_AMOUNT_INVALID;
            }
            if (getAcquirerIndex() < RequestArgsConstants.ACQUIRER_LOWER
                    || getAcquirerIndex() > RequestArgsConstants.ACQUIRER_UPPER){
                return RequestArgsConstants.ACQUIRER_INDEX_INVALID;
            }
            return RequestArgsConstants.SUCCESS;
        }

        /**
         * Gets amount.
         *
         * @return the amount
         */
        public long getAmount() {
            return mTotalAmount;
        }

        /**
         * Sets amount.
         *
         * @param amount the amount
         */
        public void setAmount(@IntRange(from = 0L, to = 9999999999L) long amount) {
            mTotalAmount = amount;
        }

        /**
         * Gets acquirer index.
         *
         * @return the acquirer index
         */
        public int getAcquirerIndex() {
            return mAcquirerIndex;
        }

        /**
         * Set acquirer index.
         *
         * @param mAcquirerIndex the m acquirer index
         */
        public void setAcquirerIndex(int mAcquirerIndex) {
            this.mAcquirerIndex = mAcquirerIndex;
        }

        @Override
        protected String checkTransTypeDetail() {
            if (getTransTypeDetail() == TRANS_DETAIL_CREDIT_CARD){
                return RequestArgsConstants.SUCCESS;
            }
            return RequestArgsConstants.TRANS_TYPE_DETAIL_INVALID;
        }
    }


    /**
     * Installment Response see TransResponseSecond.java
     */
}
