/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20170918               Kim.L                  create
 * 20210802  	         jiaguang                modify
 *
 * ============================================================================
 */
package com.pax.edc.opensdk;

import android.os.Bundle;

import androidx.annotation.NonNull;

/**
 * Adjust message struct
 */
public class AdjustMsg {
    /**
     * Adjust Request see AdjustRequest.java
     */

    /**
     * AdjustResponse
     */
    public static class Response extends TransResponse {

        //统一修改所有字段的名字，将开头的'm'去掉
        private String tipAmount;
        //Currency code of the DCC payment
        private String currencyCode;

        private String exchangeRate;

        //DCC Transaction amount include tips (DCC Base + DCC Tips)
        private String totalAmountForeign;

        //DCC Tips
        private String tipAmountForeign;

        //Merchant Dollar Redeemed, Right justified with leading zero
        private String merchantDolRedeemed;

        //HangSeng Cash Dollar Redeemed. Right justified with leading zero
        private String haseDolRedeemed;

        //Total Amount = Base + Tips, Base = Net + HASE dollar Redeemed + Merchant dollar Redeemed
        private String netAmount;

        //Merchant Dollar Balance, Right justified with leading zero
        private String merchantDolBalance;

        //HangSeng Cash Dollar Balance, Right justified with leading zero
        private String haseDolBalance;

        private String cupRrn;

        /**
         * Instantiates a new Response.
         */
        public Response() {
        }

        /**
         * Instantiates a new Response.
         *
         * @param bundle the bundle
         */
        Response(Bundle bundle) {
            fromBundle(bundle);
        }

        @Override
        void fromBundle(Bundle bundle) {
            super.fromBundle(bundle);

            tipAmount = IntentUtil.getStringExtra(bundle, Constants.Response.TIP_AMOUNT);
            currencyCode = IntentUtil.getStringExtra(bundle, Constants.Response.CURRENCY_CODE);
            exchangeRate = IntentUtil.getStringExtra(bundle, Constants.Response.EXCHANGE_RATE);
            totalAmountForeign = IntentUtil.getStringExtra(bundle, Constants.Response.TOTAL_AMOUNT_FOREIGN);
            tipAmountForeign = IntentUtil.getStringExtra(bundle, Constants.Response.TIP_AMOUNT_FOREIGN);
            merchantDolRedeemed = IntentUtil.getStringExtra(bundle, Constants.Response.MERCHANT_DOL_REDEEMED);
            haseDolRedeemed = IntentUtil.getStringExtra(bundle, Constants.Response.HASE_DOL_REDEEMED);
            netAmount = IntentUtil.getStringExtra(bundle, Constants.Response.NET_AMOUNT);
            merchantDolBalance = IntentUtil.getStringExtra(bundle, Constants.Response.MERCHANT_DOL_BALANCE);
            haseDolBalance = IntentUtil.getStringExtra(bundle, Constants.Response.HASE_DOL_BALANCE);

            cupRrn = IntentUtil.getStringExtra(bundle, Constants.Response.CUP_RRN);
        }

        @NonNull
        Bundle toBundle(@NonNull Bundle bundle) {
            super.toBundle(bundle);

            //optional params
            bundle.putString(Constants.Response.TIP_AMOUNT, tipAmount);
            bundle.putString(Constants.Response.CURRENCY_CODE, currencyCode);
            bundle.putString(Constants.Response.EXCHANGE_RATE, exchangeRate);
            bundle.putString(Constants.Response.TOTAL_AMOUNT_FOREIGN, totalAmountForeign);
            bundle.putString(Constants.Response.TIP_AMOUNT_FOREIGN, tipAmountForeign);
            bundle.putString(Constants.Response.MERCHANT_DOL_REDEEMED, merchantDolRedeemed);
            bundle.putString(Constants.Response.HASE_DOL_REDEEMED, haseDolRedeemed);
            bundle.putString(Constants.Response.NET_AMOUNT, netAmount);
            bundle.putString(Constants.Response.MERCHANT_DOL_BALANCE, merchantDolBalance);
            bundle.putString(Constants.Response.HASE_DOL_BALANCE, haseDolBalance);

            bundle.putString(Constants.Response.CUP_RRN, cupRrn);

            return bundle;
        }

        @Override
        boolean checkArgs() {
            return true;
        }

        /**
         * Gets tip amount.
         *
         * @return the tip amount
         */
        public String getTipAmount() {
            return tipAmount;
        }

        /**
         * Sets tip amount.
         *
         * @param tipAmount the m tip amount
         */
        public void setTipAmount(String tipAmount) {
            this.tipAmount = tipAmount;
        }

        /**
         * Gets currency code.
         *
         * @return the currency code
         */
        public String getCurrencyCode() {
            return currencyCode;
        }

        /**
         * Sets currency code.
         *
         * @param currencyCode the m currency code
         */
        public void setCurrencyCode(String currencyCode) {
            this.currencyCode = currencyCode;
        }

        /**
         * Gets exchange rate.
         *
         * @return the exchange rate
         */
        public String getExchangeRate() {
            return exchangeRate;
        }

        /**
         * Sets exchange rate.
         *
         * @param exchangeRate the m exchange rate
         */
        public void setExchangeRate(String exchangeRate) {
            this.exchangeRate = exchangeRate;
        }

        /**
         * Gets total amount foreign.
         *
         * @return the total amount foreign
         */
        public String getTotalAmountForeign() {
            return totalAmountForeign;
        }

        /**
         * Sets total amount foreign.
         *
         * @param totalAmountForeign the m total amount foreign
         */
        public void setTotalAmountForeign(String totalAmountForeign) {
            this.totalAmountForeign = totalAmountForeign;
        }

        /**
         * Gets tip amount foreign.
         *
         * @return the tip amount foreign
         */
        public String getTipAmountForeign() {
            return tipAmountForeign;
        }

        /**
         * Sets tip amount foreign.
         *
         * @param tipAmountForeign the m tip amount foreign
         */
        public void setTipAmountForeign(String tipAmountForeign) {
            this.tipAmountForeign = tipAmountForeign;
        }

        /**
         * Gets merchant dol redeemed.
         *
         * @return the merchant dol redeemed
         */
        public String getMerchantDolRedeemed() {
            return merchantDolRedeemed;
        }

        /**
         * Sets merchant dol redeemed.
         *
         * @param merchantDolRedeemed the m merchant dol redeemed
         */
        public void setMerchantDolRedeemed(String merchantDolRedeemed) {
            this.merchantDolRedeemed = merchantDolRedeemed;
        }

        /**
         * Gets hase dol redeemed.
         *
         * @return the hase dol redeemed
         */
        public String getHaseDolRedeemed() {
            return haseDolRedeemed;
        }

        /**
         * Sets hase dol redeemed.
         *
         * @param haseDolRedeemed the m hase dol redeemed
         */
        public void setHaseDolRedeemed(String haseDolRedeemed) {
            this.haseDolRedeemed = haseDolRedeemed;
        }

        /**
         * Gets net amount.
         *
         * @return the net amount
         */
        public String getNetAmount() {
            return netAmount;
        }

        /**
         * Sets net amount.
         *
         * @param netAmount the m net amount
         */
        public void setNetAmount(String netAmount) {
            this.netAmount = netAmount;
        }

        /**
         * Gets merchant dol balance.
         *
         * @return the merchant dol balance
         */
        public String getMerchantDolBalance() {
            return merchantDolBalance;
        }

        /**
         * Sets merchant dol balance.
         *
         * @param merchantDolBalance the m merchant dol balance
         */
        public void setMerchantDolBalance(String merchantDolBalance) {
            this.merchantDolBalance = merchantDolBalance;
        }

        /**
         * Gets hase dol balance.
         *
         * @return the hase dol balance
         */
        public String getHaseDolBalance() {
            return haseDolBalance;
        }

        /**
         * Sets hase dol balance.
         *
         * @param haseDolBalance the m hase dol balance
         */
        public void setHaseDolBalance(String haseDolBalance) {
            this.haseDolBalance = haseDolBalance;
        }

        /**
         * Gets cup rrn.
         *
         * @return the cup rrn
         */
        public String getCupRrn() {
            return cupRrn;
        }

        /**
         * Sets cup rrn.
         *
         * @param cupRrn the m cup rrn
         */
        public void setCupRrn(String cupRrn) {
            this.cupRrn = cupRrn;
        }

        @Override
        public String toString() {
            return "Response{\n" +
                    "tipAmount=" + tipAmount + "\n" +
                    "currencyCode=" + currencyCode + "\n" +
                    "exchangeRate=" + exchangeRate + "\n" +
                    "totalAmountForeign=" + totalAmountForeign + "\n" +
                    "tipAmountForeign=" + tipAmountForeign + "\n" +
                    "merchantDolRedeemed=" + merchantDolRedeemed + "\n" +
                    "haseDolRedeemed=" + haseDolRedeemed + "\n" +
                    "netAmount=" + netAmount + "\n" +
                    "merchantDolBalance=" + merchantDolBalance + "\n" +
                    "haseDolBalance=" + haseDolBalance + "\n" +
                    "cupRrn=" + cupRrn + "\n" +super.toString()+
                    "\n}";
        }
    }
}
