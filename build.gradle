// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven {
            url "https://maven.aliyun.com/repository/public"
        }
        maven {
            url "https://maven.aliyun.com/repository/google"
        }
        google()
        jcenter()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
        classpath "com.github.nic-bell:license-gradle-plugin:cac7e1c79a"
    }
    ext {
        compileSdkVersion = 28
        buildToolsVersion = '29.0.2'
        minSdkVersion = 26
        targetSdkVersion = 28
        applicationId = "com.pax.sbipay"
        eventbus = '3.0.0'
        glide = '3.7.0'
        rxpermissions = '0.9.4@aar'
        rxjava = '2.2.2'
        rxandroid = '2.1.0'
        guideHelper = '1.0.5'
        constraintlayout = '1.1.3'
        greendao = '3.3.0'
        sqlcipher = '3.5.6'
        material = '1.0.0'
        androidxSupportV4 = '1.0.0'
        androidxExtJunit = '1.1.1'
        androidAppcompat = '1.1.0'
        slf4jAndroid = '1.6.1-RC1'
        multidex = '1.0.3'
        paxstore = '7.1.1'
        fastjson = '1.1.70.android'
        //for test
        junit = '4.12'
        mockito = '1.10.19'
        hamcrest = '1.3'
        coreTesting = '2.1.0'
        runner = '1.2.0'
        espresso = '3.2.0'
        //for debug
        leakcanary = '2.8.1'
        pandora = 'v2.0.3'
    }
}

allprojects {
    apply plugin: "com.github.hierynomus.license-report"
    repositories {
        maven {
            url "https://maven.aliyun.com/repository/public"
        }
        maven {
            url "https://maven.aliyun.com/repository/google"
        }
        google()
        jcenter()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }

    downloadLicenses {
        includeProjectDependencies = true
        dependencyConfiguration = 'releaseRuntimeClasspath'
    }
}
