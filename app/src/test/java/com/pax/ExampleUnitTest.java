/*
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                 Author	                Action
 * 20190318  	         xieYb                  Create
 */

package com.pax;


import org.junit.Test;

import static org.junit.Assert.assertArrayEquals;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;

public class ExampleUnitTest {
    @Test
    public void testByte() {
        byte[] receiptData = new byte[3];
        receiptData[0] = 0;
        receiptData[1] = 1;
        receiptData[2] = 2;
        byte[] field63 = new byte[2 + receiptData.length];
        byte[] subField = "29".getBytes();
        System.arraycopy(subField, 0, field63, 0, subField.length);
        System.arraycopy(receiptData, 0, field63, 2, receiptData.length);
        byte[] result = new byte[]{(byte) 50, (byte) 57, (byte) 0, (byte) 1, (byte) 2};
        assertArrayEquals(field63, result);
    }


    @Test
    public void testKey() throws NoSuchAlgorithmException, InvalidKeySpecException, IOException, SignatureException, InvalidKeyException {

    }
}
