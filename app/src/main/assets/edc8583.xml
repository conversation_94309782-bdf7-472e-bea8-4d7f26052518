<?xml version="1.0" encoding="utf-8"?><!--
History:
	Date			Revision			
	20161118		initial
=================================================================================================================
  This is the template for EDC ISO8583, it defines the necessary attributes for packing/unpacking.
With this, you can define the attributes with a friendly interface, instead of setting them one by one
in the code. Of course, you can also set all these attributes in the code when necessary.
  The attributes are listed as follows:
  1. global settings
    1) secondary_bitmap(opt.)
      indicating if secondary bitmap(i.e. filed 1) exists or not, "YES" for true, "NO" otherwise, default to "NO".
    2) var_len_format(opt.)
      the format of the variable length(i.e. L/LL/LLL), can be "BCD"/"ASC"/"BIN", default to "BCD"
      i)   BCD: BCD
      * for LVAR, 1 byte, range 0~9;
      * for LLVAR, 1 byte, range 0~99;
      * for LLLVAR, 2 bytes, range 0~999;
      ii)  ASC: ASCII
      * for LVAR, 1 byte, range 0~9;
      * for LLVAR, 2 byte, range 0~99;
      * for LLLVAR, 3 bytes, range 0~999;
      iii) BIN: BINARY
      * for LVAR, 1 byte, range 0~0xF;
      * for LLVAR, 1 byte, range 0~0xFF;
      * for LLLVAR, 2 bytes, range 0~0xFFF;
      
  2. field settings
    1) id (mandatory)
      h: header, including TPDU and some other proprietary fields
      m: msg id
      2~...: field x
    2) format (mandatory)
      	format for both packing and unpacking. For unpacking, can be overrided by 'format_unpack'.
      	currently supports "A"/"N"/"S"/"AN"/"AS"/"ANS"/"B"/"Z"
        for variable length format, use ".x/..xx/...xxx"
        e.g. Alpha 10 bytes: "A10";  Binary 64 bits: "B64";  Alphanumeric LLVAR with max length 80: "AN..80"
    3) format_unpack (opt.)
    	see introduction of 'format'
        if exists, used for unpacking(i.e. override 'format' for unpacking)
    4) paddingpos (opt.)
        padding position for both packing and unpacking, if not exists, use default  
        paddingpos="L"(i.e. right-aligned),  paddingpos="R"(i.e. left-aligned)
      	rules are as follows:
        i) format "N"/"Z" with odd length.  By default, it's paddingpos="L"(i.e. right-aligned);
        ii) format "B"/"S", this is ignored (i.e. the length provided value MUST equals to the fields' length);
        ii) format "A"/"AN"/"ANS"/"AS" with FIXED length, but length of value provided is less than the fields' length. By default, paddingpos="L"(i.e. right-aligned)
    5) paddingpos_unpack (opt.)
    	see introduction of 'paddingpos'
        if exists, used for unpacking(i.e. override 'paddingpos' for unpacking)
    6) paddingchar (opt.)
        padding character, for both packing and unpacking, if not exists, use default.
        i) format "N"/"Z" with odd length, this is ignored(i.e. it's always padded with '0');
        ii) format "B"/"S", this is ignored (i.e. the length provided value MUST equals to the field's length);
        ii) format "A"/"AN"/"ANS"/"AS" with FIXED length, but length of value provided is less than the field's length. By default, padding char is blank space ' ';
    7) paddingchar_unpack (opt.)
    	see introduction of 'paddingchar'
        if exists, used for padding character for unpacking 
    8) desc (opt.)
        description, it's mainly for debug purpose.

NOTE:
  If you need to define fields above 64, 'secondary_bitmap' MUST be set to "YES" first;
  Field 65 is specially for tertiary bitmap, setting format or value to this field is ignored internally;
  For Android, typically you can place this file into assets.  
-->
<iso8583 secondary_bitmap="NO" var_len_format="BCD">
    <field desc="header" format="N10" id="h" />    <!-- example: ********** -->
    <field desc="msg_id" format="N4" id="m" />
    <field desc="主账号(Primary Account Number)" format="N..48" id="2" />
    <field desc="交易处理码(Transaction Processing Code)" format="N6" id="3" />
    <field desc="交易金额(Amount Of Transactions)" format="N12" id="4" />
    <field desc="受卡方系统跟踪号(System Trace Audit Number)" format="N6" id="11" />
    <field desc="受卡方所在地时间(Local Time Of Transaction)" format="N6" id="12" />
    <field desc="受卡方所在地日期(Local Date Of Transaction)" format="N4" id="13" />
    <field desc="卡有效期(Date Of Expired)" format="N4" id="14" />
    <field desc="服务点输入方式码(Point Of Service Entry Mode)" format="N4" id="22" paddingpos="R" />    <!-- NOTE: by default, N is left-padding -->
    <field desc="卡序列号(Card Sequence Number)" format="N3" id="23" />
    <field desc="NII" format="N3" id="24" />
    <field desc="服务点条件码(Point Of Service Condition Mode)" format="N2" id="25" />
    <field desc="2磁道数据(Track 2 Data)" format="Z..80" id="35" />
    <field desc="3磁道数据(Track 3 Data)" format="Z...104" id="36" />
    <field desc="检索参考号(Retrieval Reference Number)" format="AN12" id="37" />
    <field desc="授权标识应答码(Authorization Identification Response Code)" format="AN6" id="38" />
    <field desc="应答码(Response Code)" format="AN2" id="39" />
    <field desc="受卡机终端标识码(Card Acceptor Terminal Identification)" format="ANS8" id="41" />
    <field desc="受卡方标识码(Card Acceptor Identification Code)" format="ANS15" id="42" />
    <field desc="附加响应数据(Additional Response Data)" format="AN..25" id="44" />
    <field desc="1磁道数据(Track 1 Data)" format="AN..76" id="45" />
    <field desc="附加响应数据(Additional Response Data)" format="ANS...200" id="48" />
    <field desc="DCC Data" format="ANS3" id="49" />
    <field desc="个人标识码数据(PIN Data)" format="B64" id="52" />
    <field desc="安全控制信息(Security Related Control Information)" format="B172" id="53" />
    <field desc="余额(Balance Amount)" format="AN...020" id="54" />
    <!-- NOTE: treat it as ANS, just copy bytes-->
    <field desc="IC卡数据域(Integrated Circuit Card System Related Data)" format="ANS...255" id="55" />
    <field desc="Batch No" format="ANS6" id="56" />
    <field desc="自定义域(Reserved Private)" format="ANS...22" id="60" />
    <field desc="原始信息域(Original Information)" format="ANS...29" id="61" />
    <field desc="自定义域(Reserved Private)" format="ANS...100" id="62" />
    <field desc="自定义域(Reserved Private)" format="ANS...800" id="63" />
    <field desc="报文鉴别码(Message Authentication Code)" format="B64" id="64" />
</iso8583>
