[{"appName": "EMV", "aid": "A0000000999090", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "D84004F800", "tacDefault": "D84000A800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "008C", "riskManData": null}, {"appName": "JCB TEST", "aid": "F1234567890123", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "D84004F800", "tacDefault": "D84000A800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "008C", "riskManData": null}, {"appName": "JCB TSET 1", "aid": "A0000000651010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "FC60ACF800", "tacDefault": "FC6024A800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0200", "riskManData": null}, {"appName": "VISA CREDIT", "aid": "A0000000031010", "selFlag": 0, "priority": 0, "onlinePin": false, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "D84004F800", "tacDefault": "D84000A800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "008C", "riskManData": null}, {"appName": "VISA ELECTRON", "aid": "A0000000032010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "DC4004F800", "tacDefault": "DC4000A800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "008C", "riskManData": null}, {"appName": "MCHIP", "aid": "A0000000041010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MCHIP 1", "aid": "A0000000041010D05601", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MCHIP 2", "aid": "A0000000041010C123456789", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MAESTRO", "aid": "A000000004306", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "F850ACF800", "tacDefault": "F850ACA000", "acquirerId": "000000123456", "dDOL": null, "tDOL": null, "version": "0002", "riskManData": null}, {"appName": "MAESTRO 1", "aid": "A0000000043060D05602", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MAESTRO 2", "aid": "A0000000042203C123456789", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MAESTRO 3", "aid": "A0000000043060C123456789", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MAESTRO US", "aid": "A0000000042203", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MAESTRO US 1", "aid": "A0000000042203D84002", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MCC 4", "aid": "A0000000046010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "MCC 5", "aid": "A0000000101030", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 999999999, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "FE50B8F800", "tacDefault": "FE50B8F800", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "0F9F02065F2A029A039C0195059F3704", "version": "0002", "riskManData": null}, {"appName": "AMEX", "aid": "A00000002501", "selFlag": 0, "priority": 0, "onlinePin": false, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 15000, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 1500, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 1200, "rdClssFLmtFlg": 1, "rdCVMLmt": 1000, "rdCVMLmtFlg": 1, "rdClssODCVFLmt": 99999999, "rdClssODCVFLmtFlag": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "DE00FC9800", "tacDefault": "DC50FC9800", "acquirerId": "000000123456", "dDOL": "9F3704", "tDOL": "9F3704", "version": "0001", "riskManData": null}, {"appName": "DPAS", "aid": "A0000001523010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": false, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "0000000000", "tacDefault": "0000000000", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "9F3704", "version": "008C", "riskManData": null}, {"appName": "DPAS 1", "aid": "A0000003241010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": false, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "0000000000", "tacDefault": "0000000000", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "9F3704", "version": "008C", "riskManData": null}, {"appName": "DPAS 2", "aid": "A0000001524010", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": false, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "0000000000", "tacDefault": "0000000000", "acquirerId": "000000123456", "dDOL": "039F3704", "tDOL": "9F3704", "version": "008C", "riskManData": null}, {"appName": "PBOC 1", "aid": "A000000333010101", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 99, "maxTargetPer": 99, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "DC4004F800", "tacDefault": "D84000A800", "acquirerId": "000000000000", "dDOL": "9F3704", "tDOL": "9F3704", "version": "0030", "riskManData": null}, {"appName": "PBOC 2", "aid": "A000000333010102", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 99, "maxTargetPer": 99, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "DC4004F800", "tacDefault": "D84000A800", "acquirerId": "000000000000", "dDOL": "9F3704", "tDOL": "9F3704", "version": "0030", "riskManData": null}, {"appName": "PBOC 3", "aid": "A000000333010103", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 99, "maxTargetPer": 99, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "DC4004F800", "tacDefault": "D84000A800", "acquirerId": "000000000000", "dDOL": "9F3704", "tDOL": "9F3704", "version": "0030", "riskManData": null}, {"appName": "PBOC 6", "aid": "A000000333010106", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 0, "maxTargetPer": 0, "floorLimit": 10000, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0010000000", "tacOnline": "D84004F800", "tacDefault": "D84000A800", "acquirerId": "000000000000", "dDOL": "9F3704", "tDOL": "9F3704", "version": "0020", "riskManData": null}, {"appName": "PBOC 8", "aid": "A000000333010108", "selFlag": 0, "priority": 0, "onlinePin": true, "targetPer": 99, "maxTargetPer": 99, "floorLimit": 0, "floorLimitCheckFlg": 1, "rdClssTxnLmt": 100000000, "rdClssTxnLmtFlg": 1, "rdClssFLmt": 0, "rdClssFLmtFlg": 1, "rdCVMLmt": 100000, "rdCVMLmtFlg": 1, "randTransSel": true, "velocityCheck": true, "threshold": 0, "tacDenial": "0000000000", "tacOnline": "DC4004F800", "tacDefault": "D84000A800", "acquirerId": "000000000000", "dDOL": "9F3704", "tDOL": "9F3704", "version": "0030", "riskManData": null}]