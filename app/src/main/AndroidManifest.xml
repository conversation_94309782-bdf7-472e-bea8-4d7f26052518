<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="com.pax.permission.PED" />
    <uses-permission android:name="com.pax.permission.PRINTER" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:extractNativeLibs="true"
        android:icon="@drawable/key_check_icon"
        android:label="@string/app_name"
        android:roundIcon="@drawable/key_check_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.IN_SBI_KeyCheck"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.IN_SBI_KeyCheck">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>