<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.pax.sbipay"
    android:installLocation="internalOnly">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission
        android:name="android.permission.CHANGE_CONFIGURATION"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.pax.permission.MAGCARD" />
    <uses-permission android:name="com.pax.permission.PED" />
    <uses-permission android:name="com.pax.permission.ICC" />
    <uses-permission android:name="com.pax.permission.PICC" />
    <uses-permission android:name="com.pax.permission.PRINTER" />
    <uses-permission android:name="com.pax.pay.service.PaymentService" />
    <uses-permission android:name="com.pax.appstore.PaxAppStoreService" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.pax.permission.RECV_BOOT_COMPLETED" />

    <permission
        android:name="android.permission.START_ACTIVITIES_FROM_BACKGROUND"
        tools:ignore="ReservedSystemPermission" />

    <permission
        android:name="com.pax.permission.RESTART_MQTT"
        android:protectionLevel="normal" />

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <application
        android:name="com.pax.pay.app.FinancialApplication"
        android:allowBackup="false"
        android:extractNativeLibs="true"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:allowBackup">
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.EMISelectActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.GenerationTokenActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BankEMIOfferActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BankEMIBinActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BankEMIConfirmActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BankEMICheckCardActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BrandEMIConfirmActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BankEMINotifyActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BankEMINotifyReverseActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.INSTAEMIActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.BrandEMIActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.EMIVoidActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.EMIStatusCheckActivity"
            android:exported="false" />
        <activity
            android:name="com.pax.settings.MobileConfigActivity"
            android:exported="false"
            android:theme="@style/AnimFade.NoActionBar" />
        <activity
            android:name="com.pax.pay.SplashActivity"
            android:theme="@style/TransBgTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.pax.pay.MainActivity"
            android:configChanges="orientation|mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|screenLayout|fontScale|uiMode|screenSize|smallestScreenSize|layoutDirection"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:label="@string/app_name"
            android:screenOrientation="nosensor"
            android:theme="@style/TransBgThemeMain"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.InitTidActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" />
        <activity
            android:name="com.pax.pay.trans.action.activity.SelectAccountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" />
        <activity
            android:name="com.pax.pay.trans.action.activity.SelectChargeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" />
        <activity
            android:name="com.pax.pay.trans.action.activity.ShowQrCodeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="nosensor"
            android:theme="@style/QRBgTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" />
        <activity
            android:name="com.pax.pay.SelfTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.PaymentActivity"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/TransBgTheme"
            android:windowSoftInputMode="adjustPan|stateHidden">
            <intent-filter>
                <action android:name="android.pax.payment.entry" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="com.pax.edc" />
                <category android:name="android.pax.payment.sale" />
                <category android:name="android.pax.payment.void" />
                <category android:name="android.pax.payment.refund" />
                <category android:name="android.pax.payment.preAuth" />
                <category android:name="android.pax.payment.printTrans" />
                <category android:name="android.pax.payment.printTransTotal" />
                <category android:name="android.pax.payment.settle" />
                <category android:name="android.pax.payment.printBitmap" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.pax.payment.entry" />

                <category android:name="android.pax.payment.sale" />
                <category android:name="android.pax.payment.void" />
                <category android:name="android.pax.payment.refund" />
                <category android:name="android.pax.payment.preAuth" />
                <category android:name="android.pax.payment.printTrans" />
                <category android:name="android.pax.payment.printTransTotal" />
                <category android:name="android.pax.payment.settle" />
                <category android:name="android.pax.payment.printBitmap" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.pax.pay.trans.action.activity.SearchCardActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|navigation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.AdjustTipActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.DispTransDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.record.DispSQRDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.record.DispSQRCurrentBatchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.SignatureActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.UserAgreementActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade" />
        <activity
            android:name="com.pax.pay.trans.action.activity.PrintPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.TransPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.AuthMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.TmsMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.ManageMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.QsparcMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.MoneyAddMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.PasswordMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.DispSingleLineMsgActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.password.ChangeMerchantPwdActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.password.ChangeTerminalPwdActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.password.ChangeVoidPwdActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.password.ChangeSettlePwdActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.password.ChangeRefundPwdActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.InputTransDataTipActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.InputTransData1Activity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.PaperlessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.record.TransQueryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.record.SQRQueryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.SettleActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.EnterAmountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.EnterAuthCodeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.EnterPinActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.WebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden|adjustResize" />
        <activity
            android:name="com.pax.pay.trans.action.activity.PrinterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden|adjustResize" />
        <activity
            android:name="com.pax.pay.utils.lightscanner.LightScannerActivity"
            android:exported="false"
            android:theme="@style/NoActionBar" />
        <activity
            android:name="com.pax.pay.trans.action.activity.DispVersionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.DccMassageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.SettlePreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.menu.HelpNeededActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.SelectCurrencyActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.YonoTransActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.pax.pay.trans.action.activity.emi.EmiTransActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="nosensor"
            android:theme="@style/AnimFade"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <receiver
            android:name="com.pax.appstore.DownloadParamReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.paxmarket.ACTION_TO_DOWNLOAD_PARAMS" />

                <category android:name="com.pax.sbipay" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.pax.receiver.AutoStartReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="paydroid.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.pax.receiver.AutoSettlementReceiver"
            android:exported="false" />
        <receiver
            android:name="com.pax.receiver.AutoStartMQTTReceiver"
            android:exported="false"
            android:permission="com.pax.permission.RESTART_MQTT">
            <intent-filter>
                <action android:name="RE_START_MQTT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.pax.receiver.AlarmReceiver"
            android:exported="false" />

        <activity
            android:name="com.pax.pay.ConfigFirstActivity"
            android:exported="false"
            android:label="@string/settings_title"
            android:theme="@style/AnimFade.NoActionBar" />

        <service android:name="com.pax.pay.service.OtherDetectCard" />
        <service
            android:name="com.pax.pay.mqtt.MQTTForegroundService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.pax.pay.service.TTSService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.pax.settings.ConfigSecondActivity"
            android:exported="false"
            android:theme="@style/AnimFade.NoActionBar" />
        <activity
            android:name="com.pax.settings.ConfigThirdActivity"
            android:exported="false"
            android:theme="@style/AnimFade.NoActionBar"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- PAXSTORE APP_KEY -->
        <activity
            android:name="com.pax.settings.ProviderSelectActivity"
            android:exported="false"
            android:label="@string/settings_title"
            android:theme="@style/AnimFade.NoActionBar" />


        <meta-data
            android:name="PAXSTORE_APP_KEY"
            android:value="${PAXSTORE_APP_KEY}" /> <!-- PAXSTORE APP_SECRET -->
        <meta-data
            android:name="PAXSTORE_APP_SECRET"
            android:value="${PAXSTORE_APP_SECRET}" />
    </application>

</manifest>