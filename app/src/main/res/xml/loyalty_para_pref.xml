<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <ListPreference
        android:defaultValue="@string/wifi"
        android:dialogTitle="@string/lytParam_menu_mds_prompt"
        android:entries="@array/lytParam_menu_cds_list_entries"
        android:entryValues="@array/commParam_menu_cds_values_list_entries"
        android:key="@string/CDS_TYPE"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        android:title="@string/lytParam_menu_mds_type" />

    <com.pax.view.EditTextPreferenceFix
        android:capitalize="words"
        android:ems="50"
        android:key="@string/MERCHANT1_DOL_NAME"
        android:maxLength="50"
        android:maxLines="1"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:title="@string/merchant1_dol_name" />

    <com.pax.view.EditTextPreferenceFix
        android:capitalize="words"
        android:ems="50"
        android:key="@string/MERCHANT2_DOL_NAME"
        android:maxLength="50"
        android:maxLines="1"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:title="@string/merchant2_dol_name" />

    <com.pax.view.EditTextPreferenceFix
        android:capitalize="words"
        android:ems="50"
        android:key="@string/BONUS_DOL_NAME"
        android:maxLength="50"
        android:maxLines="1"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:title="@string/bonus_dol_name" />

    <com.pax.view.EditTextPreferenceFix
        android:capitalize="words"
        android:ems="50"
        android:key="@string/HASE_CASH_DOL_NAME"
        android:maxLength="50"
        android:maxLines="1"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:title="@string/hase_cash_dol_name" />

    <PreferenceCategory
        android:persistent="false"
        android:title="@string/lytParam_menu_pure_redemption">

        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/REDM_CASH"
            android:summaryOff="@string/no"
            android:summaryOn="@string/yes"
            android:title="@string/redeem_cash_dol" />

        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/REDM_MERCHANT1_DOL"
            android:summaryOff="@string/no"
            android:summaryOn="@string/yes"
            android:title="@string/redeem_merchant1_dol" />

        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/REDM_MERCHANT2_DOL"
            android:summaryOff="@string/no"
            android:summaryOn="@string/yes"
            android:title="@string/redeem_merchant2_dol" />

        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/DEFAULT_TRAN"
            android:summaryOff="@string/no"
            android:summaryOn="@string/yes"
            android:title="@string/defult_tran" />

        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/CASH_CHOICE"
            android:summaryOff="@string/no"
            android:summaryOn="@string/yes"
            android:title="@string/cash_dol_choice" />
    </PreferenceCategory>

</PreferenceScreen>
