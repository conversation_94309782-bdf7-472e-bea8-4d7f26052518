<?xml version="1.0" encoding="utf-8"?><!-- 可以直接输入的字符(如0-9,.)，他们在键盘映射xml中的android:codes值必须配置为该字符的ASCII码 -->
<Keyboard xmlns:android="http://schemas.android.com/apk/res/android"

    android:keyWidth="34%p"
    android:horizontalGap="0px"
    android:verticalGap="0px"
    android:keyHeight="13%p">
    <Row>
        <Key
            android:codes="49"
            android:keyLabel="@string/num_1" />
        <Key
            android:codes="50"
            android:keyLabel="@string/num_2" />
        <Key
            android:codes="51"
            android:keyLabel="@string/num_3" />
    </Row>
    <Row>
        <Key
            android:codes="52"
            android:keyLabel="@string/num_4" />
        <Key
            android:codes="53"
            android:keyLabel="@string/num_5" />
        <Key
            android:codes="54"
            android:keyLabel="@string/num_6" />
    </Row>
    <Row>
        <Key
            android:codes="55"
            android:keyLabel="@string/num_7" />
        <Key
            android:codes="56"
            android:keyLabel="@string/num_8" />
        <Key
            android:codes="57"
            android:keyLabel="@string/num_9" />

    </Row>
    <Row>
        <Key
            android:codes="-5"
            android:keyIcon="@drawable/selection_btn_delete_large"
            android:keyEdgeFlags="left"
            android:isRepeatable="true" />
        <Key
            android:codes="48"
            android:keyLabel="@string/num_0" />
        <Key
            android:codes="-4"
            android:keyLabel="OK"
            android:keyIcon="@drawable/selection_btn_confirm"
            android:keyEdgeFlags="right" />
    </Row>
</Keyboard>
