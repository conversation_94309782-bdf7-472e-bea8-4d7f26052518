<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <Preference
        android:key="@string/BASE_CONNECT_WIFI"
        android:title="@string/base_connect_wifi" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="@string/BASE_ENABLE"
        android:summaryOff="@string/no"
        android:summaryOn="@string/yes"
        android:title="@string/base_enable"
        android:persistent="false"/>


    <PreferenceCategory android:title="@string/wifi">
        <com.pax.view.EditTextPreferenceFix
            android:capitalize="words"
            android:defaultValue="L920-BN-1E0"
            android:dependency="@string/BASE_ENABLE"
            android:ems="50"
            android:key="@string/WIFI_NAME"
            android:maxLength="20"
            android:maxLines="1"
            android:selectAllOnFocus="true"
            android:singleLine="true"
            android:title="@string/wifi_name" />

        <com.pax.view.EditTextPreferenceFix
            android:capitalize="words"
            android:defaultValue="12345678"
            android:dependency="@string/BASE_ENABLE"
            android:ems="50"
            android:key="@string/WIFI_PASSWORD"
            android:maxLength="20"
            android:maxLines="1"
            android:selectAllOnFocus="true"
            android:singleLine="true"
            android:title="@string/wifi_password" />

    </PreferenceCategory>

</PreferenceScreen>