<?xml version="1.0" encoding="utf-8"?>
    <selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@color/colorPrimary" android:state_pressed="true"/>
    <item android:drawable="@color/colorPrimary" android:state_focused="true"/>
    <item android:drawable="@color/primary_light"/>
    </selector>

    <!--<?xml version="1.0" encoding="utf-8"?>-->
    <!--<ripple xmlns:android="http://schemas.android.com/apk/res/android"-->
    <!--android:color="@color/colorPrimary">-->
    <!--<item android:drawable="@color/primary_light"/>-->
    <!--</ripple>-->