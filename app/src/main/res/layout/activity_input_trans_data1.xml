<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="@dimen/space_horizontal"
    android:layout_marginEnd="@dimen/space_horizontal"
    android:orientation="vertical">

    <TextView
        android:id="@+id/prompt_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="30dp"
        android:text="@string/prompt_input_transno"
        android:textColor="@color/primary_text_light"
        android:textSize="@dimen/font_size_prompt" />

    <com.pax.view.keyboard.CustomKeyboardEditText
        android:id="@+id/input_data_1"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/edit_frame"
        android:gravity="end|center_vertical"
        android:inputType="text"
        android:textSize="@dimen/font_edit_text"
        app:keepKeyboardOn="true"
        app:xml="@xml/numeric_keyboard_confirm_large" />

</LinearLayout>
