<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fab_container"
    android:layout_width="64dp"
    android:layout_height="64dp"
    android:layout_gravity="right|center"
    tools:ignore="RtlHardcoded">

    <View
        android:id="@+id/arch_shape"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:alpha="0.6"
        android:background="@drawable/bg_arch_shape" />

    <ImageView
        android:id="@+id/fab_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:layout_marginRight="8dp"
        android:scaleType="fitCenter"
        android:background="@drawable/fab_background"
        android:contentDescription="@null"
        android:src="@drawable/mqtt_disconnected" />
</FrameLayout>
