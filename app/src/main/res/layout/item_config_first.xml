<?xml version="1.0" encoding="utf-8"?><!--
  ~ /*
  ~  * ============================================================================
  ~  * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~  * This software is supplied under the terms of a license agreement or nondisclosure
  ~  * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~  * disclosed except in accordance with the terms in that agreement.
  ~  *     Copyright (C) $YEAR-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  * Description:
  ~  * Revision History:
  ~  * Date	             Author	                Action
  ~  * 20181203   	     ligq           	Create/Add/Modify/Delete
  ~  * ============================================================================
  ~  */
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_configs_item"
        android:layout_width="0dp"
        android:layout_height="?actionBarSize"
        android:background="@drawable/item_configs_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >

        <ImageView
            android:id="@+id/iv_configs_left_icon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="12dp"
            android:contentDescription="@string/configs_left_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_configs_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_configs_left_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="WLAN" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:contentDescription="@string/the_right_icon_of_configs_item"
            android:src="@drawable/iv_configs_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_config_bottom_line1"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="57dp"
            android:background="@color/color_configs_line"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_configs_bottom_line2"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/color_configs_line_seg"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/cl_configs_item" />

</androidx.constraintlayout.widget.ConstraintLayout>