<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical"
              android:layout_marginStart="@dimen/space_horizontal"
              android:layout_marginEnd="@dimen/space_horizontal">

    <LinearLayout
        android:id="@+id/trans_amount_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical"
        android:layout_marginBottom="@dimen/space_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:text="@string/history_detail_amount"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt"/>

        <TextView
            android:id="@+id/amount_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="bottom|end"
            android:text="@string/amount_default"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_value"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/prompt_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/prompt_auth_code"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt"/>

        <EditText
            android:id="@+id/info_input_text"
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_height"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:focusable="true"
            android:inputType="number"
            android:gravity="center"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_edit_text"
            android:hint=""/>
    </LinearLayout>

</LinearLayout>