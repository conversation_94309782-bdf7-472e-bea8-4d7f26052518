<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/qr_background"
    android:orientation="vertical"
    android:padding="16dp">

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center_horizontal"
        android:layout_weight="1"
        android:src="@drawable/qr_banner" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_weight="10"
        android:background="@drawable/bhim_qr_bg"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvScanQRCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="@string/bhim_qr_scan_pay"
            android:textColor="@color/banner"
            android:textSize="30sp" />

        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="₹ 1.00"
            android:textColor="@color/black"
            android:textSize="25sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivQRCode"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:layout_weight="1"
            android:contentDescription="@string/qr_code"
            android:scaleType="center" />

        <!-- 修改为水平布局，包含商户名称标签和带下划线的文本框 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/merchant_name_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/merchant_name"
                android:textColor="@color/banner"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/merchant_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/underline"
                android:ellipsize="none"
                android:gravity="start"
                android:paddingBottom="2dp"
                android:textColor="@color/banner"
                android:textSize="12sp"
                app:autoSizeMaxTextSize="12sp"
                app:autoSizeMinTextSize="6sp"
                app:autoSizeStepGranularity="0.5sp"
                app:autoSizeTextType="uniform" />
        </LinearLayout>

        <!-- 添加BHIM/UPI标志 -->
        <ImageView
            android:id="@+id/ivBhimUpiLogo"
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:layout_marginTop="5dp"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@drawable/bhim_qr_brand" />

        <TextView
            android:id="@+id/tvPaymentStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:text="@string/payment_pending"
            android:textColor="@color/black"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tvCountdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_weight="1"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/yono_sbi" />

            <ImageView
                android:layout_width="80dp"
                android:layout_height="25dp"
                android:layout_weight="1"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/bhime_sbi" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_weight="1"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/yono_lite" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
