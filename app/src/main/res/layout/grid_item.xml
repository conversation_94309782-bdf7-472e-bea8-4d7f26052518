<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    android:layout_margin="0dp"
    android:background="@drawable/grid_view_bg"
    android:paddingTop="18dp"
    android:paddingBottom="18dp">

    <ImageView
        android:id="@+id/iv_item"
        android:layout_width="@dimen/grid_item"
        android:layout_height="@dimen/grid_item"
        android:layout_centerHorizontal="true"
        android:contentDescription="@string/image_desc"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/tv_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/iv_item"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/space_vertical_small"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/primary_text_light"
        android:textSize="@dimen/font_size_prompt" />
</RelativeLayout>