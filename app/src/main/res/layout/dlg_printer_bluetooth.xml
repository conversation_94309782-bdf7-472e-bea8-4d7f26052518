<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/printer_bluetooth"
        android:textColor="#ff000000"
        android:layout_marginBottom="10dp"
        android:textSize="18sp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/printer_rv_bonded"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:clickable="true"/>

    <TextView
        android:id="@+id/printer_tv_state"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:lines="2"
        android:text="printer status" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:visibility="gone"
            android:text="Connect" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/printer_btn_test_print"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Print" />
    </LinearLayout>

</LinearLayout>