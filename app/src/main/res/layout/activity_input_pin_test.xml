<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_marginStart="@dimen/space_horizontal"
    android:layout_marginEnd="@dimen/space_horizontal"
    android:keepScreenOn="true">

    <LinearLayout
        android:id="@+id/trans_total_amount_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/total_amount"
            android:gravity="bottom"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt" />

        <TextView
            android:id="@+id/total_amount_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/amount_default"
            android:gravity="bottom|end"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_value" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/trans_tip_amount_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical_small">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tip_amount"
            android:gravity="bottom"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt" />

        <TextView
            android:id="@+id/tip_amount_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/amount_default"
            android:gravity="bottom|end"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_value" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical_large"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/prompt_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/prompt_pin"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt" />

        <EditText
            android:id="@+id/pin_input_text"
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_height"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_edit_text"
            android:maxLines="1"
            android:maxLength="12"
            android:inputType="numberPassword"/>

        <TextView
            android:id="@+id/prompt_no_pin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:gravity="center"
            android:text="@string/prompt_no_pin"
            android:textColor="@color/secondary_text_light"
            android:textSize="@dimen/font_size_hint" />
    </LinearLayout>

</LinearLayout>