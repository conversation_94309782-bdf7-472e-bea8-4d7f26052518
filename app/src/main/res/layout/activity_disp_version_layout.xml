<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_marginStart="@dimen/space_horizontal"
    android:layout_marginEnd="@dimen/space_horizontal"
    android:scrollbars="none">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/app_version"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/app_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/aeVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/ae_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/dpasVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/dpas_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/emvVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/emv_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/entryVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/entry_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/jcbVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/jcb_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/mcVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/mc_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/qpbocVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/qpboc_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/rupayVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/rupay_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:background="@drawable/edit_frame"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:textColor="@color/secondary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:text="@string/waveVersion"/>

            <View style="@style/LongLine"/>

            <TextView
                android:id="@+id/wave_version_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:gravity="center"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_value" />
        </LinearLayout>
        <Button
            android:id="@+id/confirm_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_marginTop="@dimen/space_vertical"
            android:layout_marginBottom="@dimen/space_vertical"
            android:background="@drawable/btn_bg_light"
            android:text="@string/dialog_ok"
            android:textColor="@color/primary_text_dark"
            android:textSize="@dimen/font_button" />
    </LinearLayout>

</ScrollView>
