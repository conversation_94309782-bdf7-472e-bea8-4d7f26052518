<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_login"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:paddingStart="@dimen/space_horizontal"
    android:paddingEnd="@dimen/space_horizontal">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="4dp">

        <ImageView
            android:layout_width="84dp"
            android:layout_height="84dp"
            android:layout_centerInParent="true"
            android:contentDescription="@string/image_desc"
            android:src="@drawable/logo_shape" />

        <ImageView
            android:layout_width="56dp"
            android:layout_height="46dp"
            android:layout_centerInParent="true"
            android:contentDescription="@string/image_desc"
            android:src="@drawable/logo_sbi" />
    </RelativeLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="5dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/tms_connection"
            android:textColor="@color/secondary_text_dark"
            android:textSize="12sp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="14dp"
            android:src="@drawable/icon_split" />

        <ImageView
            android:id="@+id/tmsStatusIcon"
            android:layout_width="24dp"
            android:layout_height="14dp"
            android:src="@drawable/icon_tick" />
    </LinearLayout>

    <!-- Acq Host Status Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="4dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/acq_host_status"
            android:textColor="@color/secondary_text_dark"
            android:textSize="12sp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="14dp"
            android:src="@drawable/icon_split" />

        <ImageView
            android:id="@+id/acqStatusIcon"
            android:layout_width="24dp"
            android:layout_height="14dp"
            android:src="@drawable/icon_tick" />
    </LinearLayout>

    <!-- RKI Server Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="4dp">

        <TextView
            android:id="@+id/rki_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/rki_key"
            android:textColor="@color/secondary_text_dark"
            android:textSize="12sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/rki_split"
            android:layout_width="24dp"
            android:layout_height="14dp"
            android:src="@drawable/icon_split" />

        <ImageView
            android:id="@+id/rkiStatusIcon"
            android:layout_width="24dp"
            android:layout_height="14dp"
            android:src="@drawable/icon_cross" />
    </LinearLayout>

    <!-- Enter TID Section -->
    <EditText
        android:id="@+id/enterTID"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/edittext_background"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:hint="@string/enter_tid"
        android:inputType="textPassword"
        android:maxLines="1"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:textColor="@color/black"
        android:textColorHint="@color/secondary_text_dark"
        android:textSize="16sp" />

    <!-- Reenter TID Section -->
    <EditText
        android:id="@+id/reenterTID"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/edittext_background"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:hint="@string/reenter_tid"
        android:imeOptions="actionDone"
        android:inputType="text"
        android:maxLines="1"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:textColor="@color/black"
        android:textColorHint="@color/secondary_text_dark"
        android:textSize="16sp" />

    <!-- Error Message -->
    <TextView
        android:id="@+id/errorTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:textColor="@color/white"
        android:visibility="gone" />
</LinearLayout>