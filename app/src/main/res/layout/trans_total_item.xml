<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/grid_item"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/trans_type"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/primary_text_light"
        android:text="@string/trans_sale"
        android:textSize="@dimen/font_size_hint" />

    <TextView
        android:id="@+id/total_sum"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:textColor="@color/primary_text_light"
        android:gravity="center"
        android:textSize="@dimen/font_size_hint" />

    <TextView
        android:id="@+id/total_amount"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1.3"
        android:textColor="@color/primary_text_light"
        android:gravity="center"
        android:textSize="@dimen/font_size_hint" />
</LinearLayout>