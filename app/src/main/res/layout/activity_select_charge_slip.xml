<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="5dp"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/e_charge_slip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:orientation="vertical">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_vertical_large"
        android:layout_marginTop="@dimen/space_vertical_small"
        android:layout_marginBottom="@dimen/space_vertical_small"
        android:text="@string/e_charge_slip"
        android:textColor="@color/special_header_layout_color"
        android:textSize="@dimen/font_size_value" />
    <View
        android:layout_width="match_parent"
        android:background="@color/color_configs_line"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:layout_height="3dp" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/physical_charge_slip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_vertical_large"
        android:layout_marginTop="@dimen/space_vertical_small"
        android:layout_marginBottom="@dimen/space_vertical_small"
        android:textColor="@color/special_header_layout_color"
        android:text="@string/physical_charge_slip"
        android:textSize="@dimen/font_size_value" />
    <View
        android:layout_width="match_parent"
        android:background="@color/color_configs_line"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:layout_height="3dp" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/no_charge_slip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_vertical_large"
        android:layout_marginTop="@dimen/space_vertical_small"
        android:layout_marginBottom="@dimen/space_vertical_small"
        android:textColor="@color/special_header_layout_color"
        android:text="@string/no_charge_slip"
        android:textSize="@dimen/font_size_value" />
    <View
        android:layout_width="match_parent"
        android:background="@color/color_configs_line"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:layout_height="3dp" />
    </LinearLayout>
</LinearLayout>