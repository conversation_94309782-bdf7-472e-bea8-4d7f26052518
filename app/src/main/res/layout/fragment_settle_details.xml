<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/space_horizontal"
        android:layout_marginEnd="@dimen/space_horizontal"
        android:paddingBottom="@dimen/settlement_space_vertical_small">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_text_light"
                android:gravity="bottom"
                android:textSize="@dimen/font_size_hint"
                android:text="@string/settle_acquirer_name" />

            <TextView
                android:id="@+id/settle_acquirer_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom|end"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_hint" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/settlement_title_space_vertical_small">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_text_light"
                android:gravity="bottom"
                android:textSize="@dimen/font_size_hint"
                android:text="@string/settle_merchant_name" />

            <TextView
                android:id="@+id/settle_merchant_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom|end"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_hint" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/settlement_title_space_vertical_small">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_text_light"
                android:gravity="bottom"
                android:textSize="@dimen/font_size_hint"
                android:text="@string/settle_merchant_id" />

            <TextView
                android:id="@+id/settle_merchant_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom|end"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_hint" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/settlement_title_space_vertical_small">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_text_light"
                android:gravity="bottom"
                android:textSize="@dimen/font_size_hint"
                android:text="@string/settle_terminal_id" />

            <TextView
                android:id="@+id/settle_terminal_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom|end"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_hint" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/settlement_title_space_vertical_small">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="bottom"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_hint"
                android:text="@string/settle_batch_id" />

            <TextView
                android:id="@+id/settle_batch_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom|end"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_hint" />
        </LinearLayout>
    </LinearLayout>

    <include
        layout="@layout/fragment_trans_total"/>

</LinearLayout>