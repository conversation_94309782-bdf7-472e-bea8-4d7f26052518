<?xml version="1.0" encoding="utf-8"?><!--
  ~ /*
  ~  * ============================================================================
  ~  * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~  * This software is supplied under the terms of a license agreement or nondisclosure
  ~  * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~  * disclosed except in accordance with the terms in that agreement.
  ~  *     Copyright (C) $YEAR-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  * Description:
  ~  * Revision History:
  ~  * Date	             Author	                Action
  ~  * 20181207   	     ligq           	Create/Add/Modify/Delete
  ~  * ============================================================================
  ~  */
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mqtt_item"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/item_configs_selector"
    android:orientation="vertical">

    <include layout="@layout/layout_toolbar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mqtt_host_item"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:orientation="vertical">

        <TextView
            android:id="@+id/mqtt_config_host_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:gravity="center_vertical"
            android:textColor="@color/color_configs_title"
            android:textSize="16sp"
            android:text="@string/mqtt_ip"
            app:layout_constraintBottom_toTopOf="@id/mqtt_config_host_value"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/mqtt_config_host_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:textColor="@color/color_config_item_value"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/mqtt_config_host_title"
            tools:text="ip" />

        <ImageView
            android:id="@+id/iv_config_item_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:contentDescription="@string/the_right_icon_of_configs_item"
            android:src="@drawable/iv_configs_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_config_bottom_line1"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_configs_line"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mqtt_port_item"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:orientation="vertical">

        <TextView
            android:id="@+id/mqtt_config_port_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:gravity="center_vertical"
            android:textColor="@color/color_configs_title"
            android:textSize="16sp"
            android:text="@string/mqtt_port"
            app:layout_constraintBottom_toTopOf="@id/mqtt_config_port_value"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/mqtt_config_port_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:textColor="@color/color_config_item_value"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/mqtt_config_port_title"
            tools:text="port" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:contentDescription="@string/the_right_icon_of_configs_item"
            android:src="@drawable/iv_configs_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_configs_line"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>