<?xml version="1.0" encoding="utf-8"?><!--
  ~ /*
  ~  * ============================================================================
  ~  * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~  * This software is supplied under the terms of a license agreement or nondisclosure
  ~  * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~  * disclosed except in accordance with the terms in that agreement.
  ~  *     Copyright (C) $YEAR-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  * Description:
  ~  * Revision History:
  ~  * Date	             Author	                Action
  ~  * 20181203   	     ligq           	Create/Add/Modify/Delete
  ~  * ============================================================================
  ~  */
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_horizontal"
        android:layout_marginEnd="@dimen/space_horizontal"
        android:layout_marginTop="@dimen/space_vertical_small">

        <TextView
            android:id="@+id/tv_currency_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="start"
            android:layout_marginStart="@dimen/dcc_message_margin_space_horizontal"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt" />

        <TextView
            android:id="@+id/tv_currency_rate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:layout_marginEnd="@dimen/dcc_message_margin_space_horizontal"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt" />
    </LinearLayout>

    <View
        android:id="@+id/view_configs_bottom_line2"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/color_configs_line_seg"
        app:layout_constraintTop_toBottomOf="@id/cl_configs_item" />

</LinearLayout>