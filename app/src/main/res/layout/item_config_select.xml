<?xml version="1.0" encoding="utf-8"?><!--
  ~ /*
  ~  * ============================================================================
  ~  * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~  * This software is supplied under the terms of a license agreement or nondisclosure
  ~  * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~  * disclosed except in accordance with the terms in that agreement.
  ~  *     Copyright (C) $YEAR-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  * Description:
  ~  * Revision History:
  ~  * Date	             Author	                Action
  ~  * 20181206   	     ligq           	Create/Add/Modify/Delete
  ~  * ============================================================================
  ~  */
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_config_select"
    android:layout_width="match_parent"
    android:layout_height="?actionBarSize"
    android:background="@drawable/item_configs_selector">

    <TextView
        android:id="@+id/tv_config_select_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:textColor="@color/color_configs_title"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/app_name" />

    <ImageView
        android:id="@+id/iv_config_selected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="15dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_check_theme_24dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_config_bottom_line1"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_configs_line"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>