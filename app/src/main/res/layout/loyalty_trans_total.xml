<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="@string/lys_summary"
            android:textSize="@dimen/font_size_prompt"/>
        <include layout="@layout/trans_total_head"/>

        <LinearLayout
            android:id="@+id/lys_summary_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <!--<include layout="@layout/trans_total_item"/>-->
            <!--<include layout="@layout/trans_total_item"/>-->
            <!--<include layout="@layout/trans_total_item"/>-->
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="@string/voided_lys_summary"
            android:textSize="@dimen/font_size_prompt"/>
        <include layout="@layout/trans_total_head"/>
        <LinearLayout
            android:id="@+id/voided_lys_summary_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <!--<include layout="@layout/trans_total_item"/>-->
            <!--<include layout="@layout/trans_total_item"/>-->
            <!--<include layout="@layout/trans_total_item"/>-->
        </LinearLayout>

    </LinearLayout>
</ScrollView>
