<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:id="@+id/trans_item_header">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_horizontal"
        android:layout_marginEnd="@dimen/space_horizontal"
        android:paddingTop="@dimen/space_vertical_small"
        android:paddingBottom="@dimen/space_vertical_small"
        android:orientation="horizontal">

        <!--trans type-->
        <TextView
            android:id="@+id/trans_type_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="start"
            android:textSize="@dimen/font_size_hint"
            android:text="@string/history_detail_type" />
        <!-- Issuer -->
        <TextView
            android:id="@+id/issuer_type_tv"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/font_size_hint"
            android:text="@string/history_detail_issuer" />
        <!-- Amount -->
        <TextView
            android:id="@+id/trans_amount_tv"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textSize="@dimen/font_size_hint"
            android:text="@string/history_detail_amount" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_horizontal"
        android:layout_marginEnd="@dimen/space_horizontal"
        android:paddingTop="0dp"
        android:paddingBottom="@dimen/space_vertical_small"
        android:orientation="horizontal">

        <!-- trans no -->
        <TextView
            android:id="@+id/trans_no_tv"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textSize="@dimen/font_size_hint"
            android:text="@string/history_detail_invoice_no" />
        <!-- trans state -->
        <TextView
            android:id="@+id/trans_state_tv"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/font_size_hint"
            android:text="@string/history_detail_state" />
        <!-- trans date-->
        <TextView
            android:id="@+id/trans_date_tv"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textSize="@dimen/font_size_hint"
            android:text="@string/dateTime" />
    </LinearLayout>
</LinearLayout>