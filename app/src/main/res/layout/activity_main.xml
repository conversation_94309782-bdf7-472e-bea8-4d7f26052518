<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/password_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <EditText
            android:id="@+id/pwd_edit"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:hint="@string/enter_password_prompt"
            android:inputType="numberPassword" />

        <Button
            android:id="@+id/pwd_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/password_enter_button" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/key_check_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="15dp"
            android:layout_weight="7"
            android:background="@drawable/border">

            <TextView
                android:id="@+id/key_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:gravity="center" />
        </ScrollView>

        <Button
            android:id="@+id/print_button"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:layout_weight="1"
            android:text="@string/print_button" />
    </LinearLayout>
</FrameLayout>