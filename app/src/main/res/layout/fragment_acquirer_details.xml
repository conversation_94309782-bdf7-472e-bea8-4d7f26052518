<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="@dimen/space_horizontal"
    android:layout_marginEnd="@dimen/space_horizontal">

    <Spinner
        android:id="@+id/acquirer_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:prompt="@string/acq_select_hint" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/acquirer_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_is_default"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical|start"
                    android:layout_toStartOf="@+id/acquirer_is_default"/>

                <CheckBox
                    android:id="@+id/acquirer_is_default"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_terminal_id"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical"
                    android:layout_toStartOf="@+id/terminal_id" />

                <EditText
                    android:id="@+id/terminal_id"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:gravity="end|center"
                    android:maxLines="1"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_edit_text"
                    android:maxLength="8"
                    android:layout_gravity="center_vertical"
                    android:inputType="text"
                    android:hint="@string/acq_terminal_id_hint" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_merchant_id"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical"/>

                <EditText
                    android:id="@+id/merchant_id"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:maxLines="1"
                    android:layout_gravity="end"
                    android:textColor="@color/primary_text_light"
                    android:maxLength="15"
                    android:textSize="@dimen/font_edit_text"
                    android:inputType="text"
                    android:hint="@string/acq_merchant_id_hint" />

            </LinearLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small">

                <EditText
                    android:id="@+id/nii_acq"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical|end"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_edit_text"
                    android:hint="@string/acq_nii_hint"
                    android:maxLength="3"
                    android:inputType="number" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_batch_no"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical"
                    android:layout_toStartOf="@+id/batch_num" />

                <EditText
                    android:id="@+id/batch_num"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:maxLength="6"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_edit_text"
                    android:layout_gravity="center_vertical|end"
                    android:inputType="number"
                    android:hint="@string/acq_batch_no_hint" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="-2dp"
                    android:layout_marginEnd="2dp"
                    android:text="@string/acq_nii"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt" />
            </RelativeLayout>

            <View style="@style/LongLine"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/acq_address"
                android:textColor="@color/primary_text_light"
                android:textSize="@dimen/font_size_prompt"
                android:layout_gravity="center_vertical"
                android:paddingTop="@dimen/space_vertical_small" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/url_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/acq_url"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"/>

                <EditText
                    android:id="@+id/acq_url"
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:maxLines="1"
                    android:layout_gravity="end"
                    android:textColor="@color/primary_text_light"
                    android:maxLength="100"
                    android:textSize="@dimen/font_size_value"
                    android:inputType="text"
                    android:gravity="right"/>
            </LinearLayout>


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/ip_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/acq_ip"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_toStartOf="@+id/acq_ip"
                    android:visibility="gone"/>

                <EditText
                    android:id="@+id/acq_ip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:hint="@string/acq_ip_hint"
                    android:inputType="text"
                    android:maxLength="15"
                    android:maxLines="1"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_value"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:enabled="false"
                    android:visibility="gone"/>

            </RelativeLayout>


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/space_vertical_small">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_port"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical"
                    android:layout_toStartOf="@+id/acq_ip_port" />

                <EditText
                    android:id="@+id/acq_ip_port"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:maxLength="6"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_value"
                    android:layout_gravity="center_vertical|end"
                    android:inputType="number"
                    android:hint="@string/acq_port_hint" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="@dimen/space_vertical_small">

                <TextView
                    android:id="@+id/url_label2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/acq_url_sec"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"/>

                <EditText
                    android:id="@+id/acq_url2"
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:maxLines="1"
                    android:layout_gravity="end"
                    android:textColor="@color/primary_text_light"
                    android:maxLength="100"
                    android:textSize="@dimen/font_size_value"
                    android:inputType="text"
                    android:gravity="right"/>
            </LinearLayout>



            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/ip_label2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/acq_ip_sec"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_toStartOf="@+id/acq_ip2"
                    android:visibility="gone"/>

                <EditText
                    android:id="@+id/acq_ip2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:hint="@string/acq_ip_hint"
                    android:inputType="text"
                    android:maxLength="15"
                    android:maxLines="1"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_value"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:enabled="false"
                    android:visibility="gone"/>

            </RelativeLayout>


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/space_vertical_small">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_port_sec"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical"
                    android:layout_toStartOf="@+id/acq_ip_port2" />

                <EditText
                    android:id="@+id/acq_ip_port2"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:maxLength="6"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_value"
                    android:layout_gravity="center_vertical|end"
                    android:inputType="number"
                    android:hint="@string/acq_port_hint" />
            </RelativeLayout>
            <Spinner
                android:id="@+id/acquirer_ssl_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_vertical_small"
                android:layout_marginBottom="@dimen/space_vertical_small"
                android:entries="@array/acq_ssl_type_list_entries"
                android:entryValues="@array/acq_ssl_type_list_values" />

            <View style="@style/LongLine"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_disable_trick_feed"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical|start"
                    android:layout_toStartOf="@+id/acquirer_disable_trick_feed" />

                <CheckBox
                    android:id="@+id/acquirer_disable_trick_feed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_support_qr"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical|start"
                    android:layout_toStartOf="@+id/acq_support_qr"
                    android:visibility="gone"/>

                <CheckBox
                    android:id="@+id/acq_support_qr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:visibility="gone"/>
            </RelativeLayout>
            <RelativeLayout
                android:id="@+id/rl_acq_support_refno"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/space_vertical_small"
                android:paddingBottom="@dimen/space_vertical_small"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_support_refno"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical|start"
                    android:layout_toStartOf="@+id/acq_support_refno" />

                <CheckBox
                    android:id="@+id/acq_support_refno"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_acq_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/space_vertical_small"
                android:visibility="gone">

                <TextView
                    android:id="@+id/acq_description_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_description"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/acq_description"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:maxLength="12"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_value"
                    android:layout_gravity="center_vertical|end"
                    android:inputType="text"
                    android:hint="@string/acq_description_hint" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_acq_instalment_plan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/space_vertical_small"
                android:visibility="gone">

                <TextView
                    android:id="@+id/acq_instalment_plan_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acq_instalment_plan"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:textColor="@color/primary_text_light"
                    android:textSize="@dimen/font_size_prompt"
                    android:layout_gravity="center_vertical"
                    android:layout_toStartOf="@+id/instalment_plan_list" />

                <Spinner
                    android:id="@+id/instalment_plan_list"
                    android:layout_height="wrap_content"
                    android:layout_width="100dp"
                    android:dropDownWidth="100dp"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical|end"
                    android:prompt="@string/acq_select_instalment_plan" />
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>