<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_select_account_ll"
    android:layout_width="match_parent"
    android:layout_height="@dimen/edit_height"
    android:orientation="horizontal">

    <TextView
        app:layout_constraintStart_toStartOf="parent"
        android:id="@+id/item_select_account_type"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/space_horizontal"
        android:gravity="center"
        android:textColor="@color/special_header_layout_color"
        android:textSize="@dimen/font_size_value" />

    <ImageView
        android:layout_width="@dimen/space_horizontal"
        android:layout_height="@dimen/space_horizontal"
        android:layout_marginEnd="@dimen/space_vertical_large"
        android:src="@drawable/set_more"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>