<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.pax.pay.trans.action.activity.SettlePreviewActivity">

    <include
        layout="@layout/fragment_settle_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/settlement_space_vertical"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@id/settle_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="invisible" />

        <Button
            android:id="@+id/btn_confirm"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/btn_bg_light"
            android:focusable="true"
            android:text="@string/settle_all_selected"
            android:textColor="@color/primary_text_dark"
            android:textSize="@dimen/font_button" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
