<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:layout_marginStart="@dimen/space_horizontal"
              android:layout_marginEnd="@dimen/space_horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/space_vertical_small"
        android:paddingBottom="@dimen/space_vertical_small"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/acq_url"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt"
            android:layout_gravity="center_vertical"/>

        <EditText
            android:id="@+id/acq_url"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:maxLines="1"
            android:layout_gravity="end"
            android:textColor="@color/primary_text_light"
            android:maxLength="50"
            android:textSize="@dimen/font_size_value"
            android:inputType="text"
            android:text="@string/acq_url_hint"/>



        <!--<com.pax.view.keyboard.CustomKeyboardEditText-->
            <!--android:id="@+id/acq_url"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="@dimen/space_vertical_small"-->
            <!--android:layout_marginBottom="@dimen/space_vertical"-->
            <!--android:maxLines="1"-->
            <!--android:layout_gravity="end"-->
            <!--android:textColor="@color/primary_text_light"-->
            <!--android:maxLength="50"-->
            <!--android:textSize="@dimen/font_size_value"-->
            <!--android:inputType="text"-->
            <!--android:text="@string/acq_url_hint" />-->




    </LinearLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/space_vertical_small"
        android:paddingTop="@dimen/space_vertical_small">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="@string/acq_ip"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt"
            android:layout_toStartOf="@+id/acq_url_ip"/>

        <EditText
            android:id="@+id/acq_url_ip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:hint="@string/acq_ip_hint"
            android:enabled="false"
            android:inputType="text"
            android:maxLength="15"
            android:maxLines="1"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_value"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"/>
    </RelativeLayout>

</LinearLayout>