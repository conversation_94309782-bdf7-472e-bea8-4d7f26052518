<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="@dimen/space_vertical_small"
    android:layout_marginStart="@dimen/space_horizontal"
    android:layout_marginEnd="@dimen/space_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/setting_old_pwd_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical"
        android:layout_marginBottom="@dimen/space_vertical_small"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/pwd_old_pwd"
            android:textSize="@dimen/font_size_prompt" />

        <EditText
            style="@style/UnSelectableEditView"
            android:id="@+id/setting_old_pwd"
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_height"
            android:background="@drawable/edit_frame"
            android:inputType="numberPassword"
            android:lines="1"
            android:maxLength="6"
            android:paddingStart="@dimen/space_horizontal"
            android:paddingEnd="@dimen/space_horizontal"
            android:textSize="@dimen/font_edit_text" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical_small"
        android:layout_marginBottom="@dimen/space_vertical_small"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/pwd_new_pwd"
            android:textSize="@dimen/font_size_prompt" />

        <EditText
            style="@style/UnSelectableEditView"
            android:id="@+id/setting_new_pwd"
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_height"
            android:background="@drawable/edit_frame"
            android:inputType="numberPassword"
            android:lines="1"
            android:maxLength="6"
            android:paddingStart="@dimen/space_horizontal"
            android:paddingEnd="@dimen/space_horizontal"
            android:textSize="@dimen/font_edit_text" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical_small"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/pwd_verify_new_pwd"
            android:textSize="@dimen/font_size_prompt" />

        <EditText
            style="@style/UnSelectableEditView"
            android:id="@+id/setting_confirm_new_pwd"
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_height"
            android:background="@drawable/edit_frame"
            android:inputType="numberPassword"
            android:lines="1"
            android:maxLength="6"
            android:paddingStart="@dimen/space_horizontal"
            android:paddingEnd="@dimen/space_horizontal"
            android:textSize="@dimen/font_edit_text" />
    </LinearLayout>

    <Button
        android:id="@+id/setting_confirm_pwd"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_marginTop="@dimen/space_vertical"
        android:layout_marginBottom="@dimen/space_vertical"
        android:background="@drawable/btn_bg_light"
        android:text="@string/dialog_ok"
        android:textColor="@color/primary_text_dark"
        android:textSize="@dimen/font_button" />
</LinearLayout>

 