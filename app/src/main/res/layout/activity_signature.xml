<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 显示金额 -->

    <LinearLayout
        android:id="@+id/trans_amount_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space_horizontal"
        android:layout_marginTop="@dimen/space_vertical"
        android:layout_marginEnd="@dimen/space_horizontal"
        android:layout_marginBottom="@dimen/space_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:text="@string/history_detail_amount"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_prompt" />

        <TextView
            android:id="@+id/trans_amount_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="bottom|end"
            android:text="@string/amount_default"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_value" />
    </LinearLayout>

    <View style="@style/LongLine" />

    <RelativeLayout
        android:id="@+id/writeUserNameSpace"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="4" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/clear_btn"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_weight="1"
            android:background="@drawable/btn_bg_dark"
            android:text="@string/signature_clear"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_button" />

        <Button
            android:id="@+id/confirm_btn"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_weight="1"
            android:background="@drawable/btn_bg_light"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/signature_done"
            android:textColor="@color/primary_text_dark"
            android:textSize="@dimen/font_button" />
    </LinearLayout>

</LinearLayout>