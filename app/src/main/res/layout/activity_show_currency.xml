<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_horizontal"
            android:layout_marginBottom="@dimen/space_horizontal"
            android:layout_marginStart="@dimen/space_horizontal"
            android:layout_marginEnd="@dimen/space_horizontal"
            android:gravity="end">

            <TextView
                android:id="@+id/local_currency_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_horizontal"
                android:drawablePadding="@dimen/space_horizontal"
                android:textSize="@dimen/font_size_value"/>

        </LinearLayout>

        <View style="@style/LongLine"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/space_horizontal"
            android:layout_marginBottom="@dimen/space_horizontal"
            android:layout_marginStart="@dimen/space_horizontal"
            android:layout_marginEnd="@dimen/space_horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/space_horizontal"
                android:text="@string/rate"
                android:textSize="@dimen/font_size_value"/>

            <TextView
                android:id="@+id/rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginStart="@dimen/space_horizontal"
                android:textSize="@dimen/font_size_value"/>

        </LinearLayout>

        <TextView
            android:id="@+id/fee_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/space_horizontal"
            android:layout_marginStart="@dimen/space_horizontal"
            android:textSize="@dimen/font_size_value"
            android:visibility="gone"/>

        <View style="@style/LongLine"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_horizontal"
            android:layout_marginBottom="@dimen/space_horizontal"
            android:layout_marginStart="@dimen/space_horizontal"
            android:layout_marginEnd="@dimen/space_horizontal"
            android:gravity="end">

            <TextView
                android:id="@+id/home_currency_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_horizontal"
                android:drawablePadding="@dimen/space_horizontal"
                android:textSize="@dimen/font_size_value"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_horizontal"
            android:layout_marginBottom="@dimen/space_horizontal"
            android:layout_marginStart="@dimen/space_horizontal"
            android:layout_marginEnd="@dimen/space_horizontal"
            android:gravity="left">

            <TextView
                android:id="@+id/auto_opt_in"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_horizontal"
                android:drawablePadding="@dimen/space_horizontal"
                android:text="AUTO OPT - IN ..."
                android:textSize="@dimen/font_size_value"/>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>