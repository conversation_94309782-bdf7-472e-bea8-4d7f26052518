<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RadioGroup
        android:id="@+id/rg_switch_language"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="MissingConstraints">
        <RadioButton
            android:id="@+id/rb_language_english"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingLeft="20dp"
            android:text="@string/LANGUAGE_ENGLISH"
            android:textSize="16sp"
            android:buttonTint="@color/primary"/>
        <RadioButton
            android:id="@+id/rb_language_chinese"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingLeft="20dp"
            android:text="@string/LANGUAGE_CHINESE"
            android:buttonTint="@color/primary"
            android:textSize="16sp"/>
    </RadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>