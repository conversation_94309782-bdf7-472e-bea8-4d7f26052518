<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_marginStart="@dimen/space_horizontal"
    android:layout_marginEnd="@dimen/space_horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical"
        android:background="@drawable/edit_frame"
        android:orientation="vertical">

        <TextView
            android:id="@+id/version_prompt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:layout_marginBottom="@dimen/space_vertical_small"
            android:textColor="@color/secondary_text_light"
            android:textSize="@dimen/font_size_prompt"
            android:text="@string/placeholder"/>

        <View style="@style/LongLine"/>

        <TextView
            android:id="@+id/version_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:layout_marginBottom="@dimen/space_vertical_small"
            android:gravity="center"
            android:textColor="@color/primary_text_light"
            android:textSize="@dimen/font_size_value" />
    </LinearLayout>

    <Button
        android:id="@+id/confirm_btn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_marginTop="@dimen/space_vertical"
        android:background="@drawable/btn_bg_light"
        android:text="@string/dialog_ok"
        android:textColor="@color/primary_text_dark"
        android:textSize="@dimen/font_button" />

</LinearLayout>