<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2.5"
        android:background="@color/primary"
        android:paddingStart="@dimen/space_horizontal"
        android:paddingEnd="@dimen/space_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical"
            android:layout_marginBottom="@dimen/space_vertical_small"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/note_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/prompt_base_amount"
                android:textColor="@color/primary_text_dark"
                android:textSize="@dimen/font_size_prompt" />

            <TextView
                android:id="@+id/base_amount_input_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:textColor="@color/primary_text_dark"
                android:textSize="@dimen/font_size_value"
                android:visibility="invisible"
                android:hint="@string/amount_default"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/tip_amount_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:layout_marginBottom="@dimen/space_vertical"
            android:orientation="horizontal"
            android:visibility="invisible">

            <TextView
                android:id="@+id/prompt_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:text="@string/prompt_tip"
                android:textColor="@color/primary_text_dark"
                android:textSize="@dimen/font_size_prompt" />

            <TextView
                android:id="@+id/tip_amount_input_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:textColor="@color/primary_text_dark"
                android:textSize="@dimen/font_size_value"
                android:hint="@string/amount_default" />
        </LinearLayout>

        <com.pax.view.keyboard.CustomKeyboardEditText
            android:id="@+id/amount_edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_vertical_small"
            android:layout_marginBottom="@dimen/space_vertical"
            android:focusable="true"
            android:gravity="end|center_vertical"
            android:inputType="text"
            android:textColor="@color/primary_text_dark"
            android:textSize="@dimen/font_size_headline"
            android:text="@string/placeholder"
            app:xml="@xml/amount_keyboard_large"
            app:autoSize="true"
            app:keepKeyboardOn="true" />
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="3.2"
        android:background="@color/background"
        android:visibility="visible" />

</LinearLayout>
