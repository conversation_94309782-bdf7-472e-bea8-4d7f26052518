<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/space_horizontal"
    android:paddingEnd="@dimen/space_horizontal">

    <!-- 固定的标题行 -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/space_horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/settle_trans_type"
            android:textSize="@dimen/font_size_prompt" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/settle_trans_times"
            android:textSize="@dimen/font_size_prompt" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/settle_amount"
            android:textSize="@dimen/font_size_prompt" />
    </LinearLayout>

    <!-- 占屏幕 2/3 高度的可滚动区域 -->
    <ScrollView
        android:id="@+id/scrollview_detail"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/edit_frame"
        android:padding="@dimen/space_horizontal"
        app:layout_constraintTop_toBottomOf="@id/header_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.66">

        <LinearLayout
            android:id="@+id/detail_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />
    </ScrollView>

    <!-- 底部按钮 -->
    <Button
        android:id="@+id/print_sqr_batch"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_vertical"
        android:background="@drawable/btn_bg_light"
        android:text="@string/dialog_print"
        android:textAllCaps="false"
        android:textColor="@color/primary_text_dark"
        android:textSize="@dimen/font_button"
        app:layout_constraintTop_toBottomOf="@id/scrollview_detail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>