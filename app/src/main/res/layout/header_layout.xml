<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:layout_alignParentTop="true"
    android:elevation="4dp"
    android:background="@color/primary_dark">

    <ImageView
        android:id="@+id/header_back"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="15dp"
        android:layout_centerVertical="true"
        android:layout_alignParentStart="true"
        android:paddingTop="10dp"
        android:paddingRight="10dp"
        android:paddingBottom="10dp"
        android:src="@drawable/back_icon"
        android:contentDescription="@string/image_desc" />

    <TextView
        android:id="@+id/header_title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:layout_marginLeft="72dp"
        android:textColor="@android:color/white"
        android:textSize="20sp"/>


</RelativeLayout>