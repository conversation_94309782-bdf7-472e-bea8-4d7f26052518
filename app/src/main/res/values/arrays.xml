<?xml version="1.0" encoding="utf-8"?>

<!-- DO NOT TRANSLATE THIS FILE -->

<resources>

    <!-- 交易管理 ,交易开关控制,传统类交易 -->
    <string name="mobile">MOBILE</string>
    <string name="wifi">Wi-Fi</string>

    <string-array name="commParam_menu_comm_mode_values_list_entries">
        <item>@string/mobile</item>
        <item>@string/wifi</item>
    </string-array>

    <string name="NO_SSL">NO SSL</string>
    <string name="SSL">SSL</string>
    <string-array name="acq_ssl_type_list_entries">
        <item>@string/NO_SSL</item>
        <item>@string/SSL</item>
    </string-array>

    <string name="mqtt_qos">QOS</string>
    <string name="mqtt_qos_0">QOS 0</string>
    <string name="mqtt_qos_1">QOS 1</string>
    <string name="mqtt_qos_2">QOS 2</string>
    <string-array name="mqtt_qos_list_entries">
        <item>@string/mqtt_qos_0</item>
        <item>@string/mqtt_qos_1</item>
        <item>@string/mqtt_qos_2</item>
    </string-array>


    <string-array name="edc_ped_mode_entries">
        <item>Internal</item>
        <item>ExternalTypeA</item>
        <item>ExternalTypeB</item>
        <item>ExternalTypeC</item>
    </string-array>
    <string-array name="edc_clss_mode_entries">
        <item>Internal</item>
        <item>External</item>
        <item>Both Internal And External</item>
    </string-array>
    <string-array name="edc_connect_time_entries">
        <item>30</item>
        <item>60</item>
        <item>90</item>
        <item>120</item>
    </string-array>

    <string-array name="edc_preauth_completion">
        <item>Invoice Based</item>
        <item>RRN Based</item>
    </string-array>

    <!-- 通讯参数的wifi -->

    <string name="keyManage_menu_3des">3des</string>

    <!--parameters of Other ↓-->
    <string-array name="config_menu_other_title">
        <item>@string/om_clearTrade_menu_reversal</item>
        <item>@string/om_clearTrade_menu_trade_voucher</item>
        <item>@string/om_clearTrade_menu_key</item>
        <item>@string/om_clearTrade_menu_storage</item>
        <item>@string/om_paramPrint_menu_print_aid_para</item>
        <item>@string/om_paramPrint_menu_print_capk_para</item>
        <item>@string/om_paramPrint_menu_print_sys_para</item>
        <item>@string/go_system_setting_date</item>
    </string-array>

    <integer-array name="config_menu_other_icon">
        <item>@drawable/other</item>
        <item>@drawable/other</item>
        <item>@drawable/other</item>
        <item>@drawable/other</item>
        <item>@drawable/other</item>
        <item>@drawable/other</item>
        <item>@drawable/other</item>
        <item>@drawable/other</item>
    </integer-array>

    <integer-array name="config_menu_other_showline">
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
    </integer-array>
    <!--parameters of Other ↑-->
</resources>