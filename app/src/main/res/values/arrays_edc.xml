<?xml version="1.0" encoding="utf-8"?>

<!--
  ~
  ~  ============================================================================
  ~  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~  This software is supplied under the terms of a license agreement or nondisclosure
  ~  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~  disclosed except in accordance with the terms in that agreement.
  ~      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  Description:
  ~  Revision History:
  ~  Date	             Author	                Action
  ~  20190220   	     ligq           	Create/Add/Modify/Delete
  ~  ============================================================================
  ~
  -->

<resources>
    <!--parameters of EDC ↓-->
    <string-array name="config_menu_edc_title">
        <item>@string/edc_merchant_name</item>
        <item>@string/edc_merchant_address</item>
        <item>@string/currency_list</item>
        <item>@string/edc_receipt_no</item>
        <item>@string/edc_trace_no</item>
        <item>@string/edc_batch_no</item>-->
        <item>@string/edc_enable_tip</item>
        <item>@string/edc_tip_percentage</item>
        <item>@string/enable_void</item>
        <item>@string/edc_enable_refund</item>
        <item>@string/edc_enable_emi</item>
        <item>@string/edc_enable_cash_only</item>
        <item>@string/edc_enable_sale_cash</item>
        <item>@string/edc_enable_money_add_account</item>
        <item>@string/edc_enable_money_add_cash</item>
        <item>@string/edc_enable_balance_enquiry</item>
        <item>@string/edc_enable_balance_update</item>
        <item>@string/edc_enable_mqtt</item>
        <item>@string/issuer_enable_manualPan</item>
        <item>@string/edc_cvv_required</item>
        <item>@string/issuer_check_adjust</item>
        <item>@string/issuer_check_pan</item>
        <item>@string/issuer_enable_print</item>
        <item>@string/edc_invoice_no</item>
        <item>@string/edc_pre_auth_completion_percentage</item>
        <item>@string/edc_enable_dcc_support_chip_card</item>
        <item>@string/edc_enable_dcc_support_tap_card</item>
        <item>@string/edc_enable_dcc_support_swipe_card</item>
        <item>@string/edc_enable_dcc_support_manual_card</item>
        <item>@string/edc_enable_echargeSlip</item>
        <item>@string/edc_preauth_record_saving_day</item>
        <item>@string/edc_reversal_retry_time</item>
        <item>@string/edc_enable_save_log</item>
        <item>@string/edc_enable_print_log</item>
        <item>@string/edc_emv_debug_report</item>
        <item>@string/edc_enable_settlement_password</item>
        <item>@string/edc_enable_refund_password</item>
        <item>@string/edc_enable_void_password</item>
        <item>@string/edc_enable_manual_password</item>
        <item>@string/edc_qr_trans_query_interval</item>
        <item>@string/edc_qr_trans_query_repeat_interval</item>
        <item>@string/edc_enable_transaction_success_audio</item>
        <item>@string/edc_max_amount_digit</item>
        <item>@string/edc_transaction_number_limit</item>
        <item>@string/edc_max_transaction_number</item>
        <item>@string/edc_auto_settlement_time</item>
        <item>@string/edc_preauth_completion</item>
    </string-array>
    <!--config the key for save in sp:N means has no key,can be used as button-->
    <string-array name="config_menu_edc_key">
        <item>@string/EDC_MERCHANT_NAME</item>
        <item>@string/EDC_MERCHANT_ADDRESS</item>
        <item>@string/EDC_CURRENCY_LIST</item>
        <item>@string/EDC_RECEIPT_NUM</item>
        <item>@string/EDC_TRACE_NO</item>
        <item>@string/EDC_BATCH_NO</item>
        <item>@string/EDC_SUPPORT_TIP</item>
        <item>@string/EDC_TIP_PERCENTAGE</item>
        <item>@string/EDC_ENABLE_VOID</item>
        <item>@string/EDC_ENABLE_REFUND</item>
        <item>@string/EDC_ENABLE_EMI</item>
        <item>@string/EDC_ENABLE_CASH_ONLY</item>
        <item>@string/EDC_ENABLE_SALE_CASH</item>
        <item>@string/EDC_ENABLE_MONEY_ADD_ACCOUNT</item>
        <item>@string/EDC_ENABLE_MONEY_ADD_CASH</item>
        <item>@string/EDC_ENABLE_BALANCE_ENQUIRY</item>
        <item>@string/EDC_ENABLE_BALANCE_UPDATE</item>
        <item>@string/EDC_ENABLE_MQTT</item>
        <item>@string/EDC_ENABLE_MANUAL_PAN</item>
        <item>@string/EDC_CVV_REQUIRED</item>
        <item>@string/EDC_CHECK_EXPIRY</item>
        <item>@string/EDC_CHECK_PAN</item>
        <item>@string/EDC_ENABLE_PRINT</item>
        <item>@string/EDC_INVOICE_NO</item>
        <item>@string/EDC_PRE_AUTH_COMPLETION_PERCENTAGE</item>
        <item>@string/EDC_ENABLE_DCC_SUPPORT_CHIP_CARD</item>
        <item>@string/EDC_ENABLE_DCC_SUPPORT_TAP_CARD</item>
        <item>@string/EDC_ENABLE_DCC_SUPPORT_SWIPE_CARD</item>
        <item>@string/EDC_ENABLE_DCC_SUPPORT_MANUAL_CARD</item>
        <item>@string/EDC_ENABLE_ECHARGESLIP</item>
        <item>@string/EDC_PREAUTH_RECORD_SAVING_DAY</item>
        <item>@string/EDC_REVERSAL_RETRY_TIME</item>
        <item>@string/EDC_ENABLE_SAVE_LOG</item>
        <item>@string/EDC_ENABLE_PRINT_LOG</item>
        <item>@string/EDC_EMV_DEBUG_REPORT</item>
        <item>@string/EDC_ENABLE_SETTLEMENT_PASSWORD</item>
        <item>@string/EDC_ENABLE_REFUND_PASSWORD</item>
        <item>@string/EDC_ENABLE_VOID_PASSWORD</item>
        <item>@string/EDC_ENABLE_MANUAL_PASSWORD</item>
        <item>@string/EDC_QR_TRANS_QUERY_INTERVAL</item>
        <item>@string/EDC_QR_TRANS_QUERY_REPEAT_INTERVAL</item>
        <item>@string/EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO</item>
        <item>@string/EDC_MAX_AMOUNT_DIGIT</item>
        <item>@string/EDC_TRANSACTION_NUMBER_LIMIT</item>
        <item>@string/EDC_MAX_TRANSACTION_NUMBER</item>
        <item>@string/EDC_AUTO_SETTLEMENT_TIME</item>
        <item>@string/EDC_PREAUTH_COMPLETION</item>
    </string-array>
    <!--config the item type:0 is normal,1 is switch-->
    <integer-array name="config_menu_edc_type_switch">
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>0</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
    </integer-array>
    <!--config the item value:1 has value;0 has no value-->
    <integer-array name="config_menu_edc_has_value">
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>0</item>
        <item>1</item>
        <item>0</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
    </integer-array>
    <!--parameters of EDC ↑-->
</resources>