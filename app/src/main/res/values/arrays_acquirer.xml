<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~  ============================================================================
  ~  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~  This software is supplied under the terms of a license agreement or nondisclosure
  ~  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~  disclosed except in accordance with the terms in that agreement.
  ~      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  Description:
  ~  Revision History:
  ~  Date	             Author	                Action
  ~  20190220   	     ligq           	Create/Add/Modify/Delete
  ~  ============================================================================
  ~
  -->

<resources>
    <!--parameters of Acquirer ↓-->
    <string-array name="config_menu_acquirer_title">
        <item>@string/settings_menu_acquirer_parameter</item>
        <item>@string/acq_terminal_id</item>
        <item>@string/acq_merchant_id</item>
    </string-array>
    <!--config the item type:0 is normal,1 is switch-->
    <integer-array name="config_menu_acquirer_type_switch">
        <item>0</item>
        <item>0</item>
        <item>0</item>
    </integer-array>
    <!--config the item value:1 has value;0 has no value-->
    <integer-array name="config_menu_acquirer_has_value">
        <item>1</item>
        <item>1</item>
        <item>1</item>
    </integer-array>
    <!--parameters of Acquirer ↑-->
</resources>