<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <string name="app_name">HASE</string>
    <string name="app_name_test">HASE Test</string>

    <!-- Base -->
    <string name="num_0">0</string>
    <string name="num_1">1</string>
    <string name="num_2">2</string>
    <string name="num_3">3</string>
    <string name="num_4">4</string>
    <string name="num_5">5</string>
    <string name="num_6">6</string>
    <string name="num_7">7</string>
    <string name="num_8">8</string>
    <string name="num_9">9</string>
    <string name="num_00">00</string>
    <string name="num_000">000</string>

    <string name="dateTime">Date/Time</string>
    <string name="prompt_enter_amount">Enter Amount</string>
    <string name="prompt_enter_amount_demo">Enter Amount (Training Mode)</string>
    <string name="placeholder">:)</string>
    <string name="sec">s</string>
    <string name="ad">Ad</string>
    <string name="ad_widget_label">Ad</string>
    <string name="amount_widget_label">Amount</string>
    <string name="go_system_setting_date">Go System Time Setting</string>

    <!--issuer/card name -->
    <string name="issuer_visa">VISA</string>
    <string name="issuer_master">MASTER</string>
    <string name="issuer_jcb">JCB</string>
    <string name="issuer_amex">AMEX</string>
    <string name="issuer_cup">UnionPay</string>
    <string name="issuer_diners">DinersClub</string>
    <string name="issuer_hase_visa">HASE_VISA</string>
    <string name="issuer_hase_master">HASE_MC</string>
    <string name="issuer_hase_cup">HASE_CUP</string>
    <string name="issuer_hase_jcb">HASE_JCB</string>
    <string name="issuer_hase_enj">HASE_ENJ</string>
    <string name="issuer_hase_enj_e">HASE_ENJ_E</string>

    <string name="issuer_hase_jcb_06">HASE_JCB_06</string>
    <string name="issuer_hase_cup_06">HASE_CUP_06</string>
    <string name="issuer_hase_master_06">HASE_MC_06</string>
    <string name="issuer_hase_visa_06">HASE_VISA_06</string>
    <string name="issuer_hase_enj_06">HASE_ENJ_06</string>
    <string name="issuer_hase_enj_e_06">HASE_ENJ_E_06</string>
    <string name="issuer_hase_jcb_12">HASE_JCB_12</string>
    <string name="issuer_hase_cup_12">HASE_CUP_12</string>
    <string name="issuer_hase_master_12">HASE_MC_12</string>
    <string name="issuer_hase_visa_12">HASE_VISA_12</string>
    <string name="issuer_hase_enj_12">HASE_ENJ_12</string>
    <string name="issuer_hase_enj_e_12">HASE_ENJ_E_12</string>

    <!--acquirer name -->
    <string name="hase_dcc">HASE_DCC</string>

    <!-- to be sorted -->
    <string name="optional_parameters">Optional Parameters</string>
    <string name="base_amount">Base Amount</string>
    <string name="ecr_ref">ECR REF#</string>
    <string name="ecr_instalment_host">HOST</string>
    <string name="total_amount">Total Amount</string>
    <string name="tip_amount">Tip Amount</string>
    <string name="amount">Amount</string>
    <string name="trans_oriDate">Original Transaction Date</string>
    <string name="has_trans_for_settle">Please Settle Firstly</string>
    <string name="amount_default">0</string>
    <string name="demo_mode">****Demo Mode****</string>
    <string name="paperless">Paperless</string>
    <string name="transaction_limit">Transaction Support</string>
    <string name="transaction_password">Transaction Password</string>
    <string name="contactless_parameter">Contactless Parameter</string>
    <string name="no_sign_amt">NO SIGN AMT</string>
    <string name="no_sign_amt_visa">NO SIGN AMT(VISA)</string>
    <string name="no_sign_amt_mc">NO SIGN AMT(MC)</string>
    <string name="no_sign_amt_amex">NO SIGN AMT(AMEX)</string>
    <string name="no_sign_amt_cup">NO SIGN AMT(CUP)</string>
    <string name="no_sign_amt_jcb">NO SIGN AMT(JCB)</string>

    <!-- Transaction Type -->
    <string name="trans_menu">Menu</string>
    <string name="trans_sale">Sale</string>
    <string name="trans_sale_en">Sale</string>
    <string name="trans_void">Void</string>
    <string name="trans_void_en">Void</string>
    <string name="trans_adjust">Adjust</string>
    <string name="trans_adjust_en">Adjust</string>
    <string name="trans_offline">Offline</string>
    <string name="trans_authorization">Authorization</string>
    <string name="trans_authorization_en">Authorization</string>
    <string name="trans_inc_authorization">Inc Authorization</string>
    <string name="trans_inc_authorization_en">Inc Authorization</string>
    <string name="trans_auth_reversal">Auth Reversal</string>
    <string name="trans_auth_reversal_en">Auth Reversal</string>
    <string name="trans_pre_auth_cancelltion">PreAuth Void</string>
    <string name="trans_pre_auth_cancelltion_en">PreAuth Void</string>
    <string name="trans_auth_cancelltion">PreAuth Void</string>
    <string name="trans_dcc_opt_out">DCC-Opt-Out</string>
    <string name="trans_dcc_opt_out_en">DCC-Opt-Out</string>
    <string name="trans_pre_auth_completion">Completion</string>
    <string name="trans_pre_auth_completion_en">Completion</string>
    <string name="trans_pre_auth_comletion_cancelltion">Completion Void</string>
    <string name="trans_pre_auth_comletion_cancelltion_en">Completion Void</string>
    <string name="trans_cup_sale">Cup Sale</string>
    <string name="trans_cup_refund">Cup Refund</string>
    <string name="trans_preAuth">PreAuth</string>
    <string name="trans_preAuth_en">PreAuth</string>
    <string name="trans_refund">Refund</string>
    <string name="trans_refund_en">Refund</string>
    <string name="trans_qr_sale">QR Sale</string>
    <string name="trans_echo">Echo</string>
    <string name="trans_echo_en">Echo</string>
    <string name="trans_settle">Settlement</string>
    <string name="trans_settle_en">Settlement</string>
    <string name="trans_batch_up">Batch Upload</string>
    <string name="trans_batch_up_en">Batch Upload</string>
    <string name="trans_settle_end">Settlement End</string>
    <string name="trans_settle_end_en">Settlement End</string>
    <string name="trans_offline_send">Offline</string>
    <string name="trans_offline_send_en">Offline</string>
    <string name="trans_offline_send_bat">Offline Upload</string>
    <string name="trans_offline_send_bat_en">Offline Upload</string>
    <string name="trans_history">History</string>
    <string name="trans_password">Password</string>
    <string name="trans_signature">Signature</string>
    <string name="trans_user_agreement">User Agreement</string>
    <string name="trans_reprint">REPRINT</string>
    <string name="trans_manage">Management</string>
    <string name="trans_inquiry">Transaction Inquiry</string>
    <string name="trans_instalment">Instalment</string>
    <string name="trans_instalment_en">Instalment</string>
    <string name="trans_instal_void">Instal Void</string>
    <string name="trans_instal_refund">Instal Refund</string>
    <string name="trans_instal_offline">Instal Offline</string>
    <string name="trans_instal_auth">Instal Auth</string>
    <string name="trans_rate_report">Rate Report</string>
    <string name="trans_download_card_bin">Download Card Bin</string>
    <string name="trans_download_card_bin_en">Download Card Bin</string>
    <string name="trans_rate_report_download">Rate Report Download</string>
    <string name="trans_rate_report_download_en">Rate Report Download</string>
    <string name="trans_enquiry">Enquiry</string>
    <string name="trans_enquiry_en">Enquiry</string>
    <string name="trans_yuu_registration">YUU Registration</string>
    <string name="trans_yuu_registration_en">YUU Registration</string>
    <string name="trans_yuu_enquiry">YUU Enquiry</string>
    <string name="trans_yuu_enquiry_en">YUU Enquiry</string>
    <!--Transaction Type：loyalty-->
    <string name="trans_loyalty">Loyalty</string>
    <string name="trans_sales_with_cash_dollar">Sales with + FUN$</string>
    <string name="trans_void_with_cash_dollar">Void with + FUN$</string>
    <string name="trans_pure_redeem">Pure Redeem</string>
    <string name="trans_pure_redeem_en">Pure Redeem</string>
    <string name="trans_online_enquiry">Enquiry</string>
    <string name="trans_online_enquiry_en">Enquiry</string>
    <string name="trans_redeem_instalment">Redm Inst</string>
    <string name="trans_redeem_instalment_en">Redm Inst</string>
    <string name="trans_multiple_up">Multiple Up</string>
    <string name="trans_multiple_up_en">Multiple Up</string>
    <string name="trans_inst_multi_up">Inst Multi Up</string>
    <string name="trans_inst_multi_up_en">Inst Multi Up</string>
    <string name="trans_is_redeem">Redeem?</string>
    <string name="prompt_is_advance_redeem">Advance Redeem?</string>
    <string name="prompt_redeem_cash_dol_choose">Redeem + FUN$ Choose</string>
    <string name="lyt_query_print">Print</string>
    <string name="not_hase_card">NOT HASE CARD</string>
    <string name="card_read_error">CARD READ ERROR</string>

    <string name="instal_select_plan">Select Plan</string>

    <!-- Transaction History-->
    <string name="history_total">Total</string>
    <string name="history_detail">Detail</string>
    <string name="history_detail_type">Type</string>
    <string name="history_detail_issuer">Card Type</string>
    <string name="history_detail_card_no">Card NO.</string>
    <string name="history_detail_amount">Amount</string>
    <string name="history_detail_auth_code">Auth Code</string>
    <string name="history_detail_trace_no">Trace NO.</string>
    <string name="history_detail_invoice_no">Invoice NO.</string>
    <string name="history_detail_ref_no">Reference NO.</string>
    <string name="history_detail_state">State</string>
    <string name="history_summary_ref_no">Reference NO.:</string>
    <string name="history_menu_search">Search</string>
    <string name="history_menu_print_trans_last">Print Last</string>
    <string name="history_menu_print_trans_detail">Print Detail</string>
    <string name="history_menu_print_trans_total">Print Summary</string>
    <string name="history_menu_print_last_total">Print Last Settle</string>
    <string name="history_menu_print_trans_all">Print All Detail</string>

    <!-- Transaction State -->
    <string name="state_normal">Normal</string>
    <string name="state_voided">Voided</string>
    <string name="state_adjusted">Adjusted</string>
    <string name="state_uploaded">Uploaded</string>
    <string name="state_not_sent">Not Sent</string>
    <string name="state_adj_sent">Adj(Sent)</string>
    <string name="state_err_sent">Error Sent</string>

    <!-- Settlement -->
    <string name="settle_acquirer_name">Acquirer Name</string>
    <string name="settle_merchant_name">Merchant Name</string>
    <string name="settle_merchant_id">Merchant NO.</string>
    <string name="settle_terminal_id">Terminal NO.</string>
    <string name="settle_batch_id">Batch NO.</string>
    <string name="settle_trans_type">Trans Type</string>
    <string name="settle_trans_times">Trans Times</string>
    <string name="settle_amount">Amount</string>
    <string name="settle_print_detail_or_not">Print Settle Detail Or Not?</string>
    <string name="settle_print_fail_detail_or_not">Print Fail Detail Or Not?</string>
    <string name="settle_select_acquirer">Select Acquirer</string>
    <string name="settle_total_void_sale">Voided Sale</string>
    <string name="settle_total_void_refund">Voided Refund</string>
    <string name="settle_all_selected">Settle</string>
    <string name="settle_settled">Settled</string>
    <string name="settle_success">Settled Successful</string>
    <string name="settle_gross">GROSS</string>
    <string name="settle_redm">REDM</string>
    <string name="settle_hase">HASE</string>
    <string name="settle_mer1">MER1</string>
    <string name="settle_mer2">MER2</string>
    <string name="settle_bonus">BONUS</string>
    <string name="settle_net">NET</string>
    <string name="lys_summary">LYS SUMMARY</string>
    <string name="voided_lys_summary">VOIDED LYS SUMMARY</string>
    <string name="voided_trans">VOIDED TRANS</string>

    <!-- Password Management -->
    <string name="pwd_merchant">Merchant</string>
    <string name="pwd_manual">Manual</string>
    <string name="pwd_terminal">Terminal</string>
    <string name="pwd_void">Void</string>
    <string name="pwd_refund">Refund</string>
    <string name="pwd_adjust">Adjust</string>
    <string name="pwd_settle">Settlement</string>
    <string name="pwd_dcc_opt_out">DCC Opt Out</string>
    <string name="pwd_rate_report">Rate Report</string>
    <string name="pwd_old_pwd">Old Password</string>
    <string name="pwd_new_pwd">New Password</string>
    <string name="pwd_not_equal">Not Matched, Please Enter Again</string>
    <string name="pwd_verify_new_pwd">Verify New Password</string>
    <string name="pwd_succ">Success</string>
    <string name="pwd_incorrect_length">Request 4-6 Digits</string>

    <!-- signature -->
    <string name="signature_clear">Clear</string>
    <string name="signature_done">Enter</string>
    <string name="signature_redo">Signature Data Is Too Long,Please Re-sign</string>

    <!-- User Agreement -->
    <string name="user_agreement_agree">Agree</string>

    <!-- print -->
    <string name="print_history_detail">Transaction Detail List</string>
    <string name="print_settle_detail">Settle Detail List</string>
    <string name="print_history_total">Transaction Total List</string>
    <string name="print_last_total">Last Transaction Total List</string>
    <string name="print_offline_send_failed">Failed Offline Send Detail List</string>
    <string name="print_trans_all">All Transaction List</string>

    <!-- InputPwdDialog -->
    <string name="prompt_manual_pwd">Please Enter Manual Password</string>
    <string name="prompt_terminal_pwd">Please Enter Terminal Password</string>
    <string name="prompt_void_pwd">Please Enter Void Password</string>
    <string name="prompt_refund_pwd">Please Enter Refund Password</string>
    <string name="prompt_adjust_pwd">Please Enter Adjust Password</string>
    <string name="prompt_dccOptOut_pwd">Please Enter DCC-Opt-Out Password</string>
    <string name="prompt_settle_pwd">Please Enter Settle Password</string>
    <string name="prompt_sys_pwd">Please Enter System Administrator Password</string>
    <string name="prompt_pin">Please Enter PIN</string>
    <string name="prompt_no_pin">No PIN, Please Click OK</string>
    <string name="prompt_offline_pin">Please Enter PIN</string>
    <string name="prompt_auth_code">Please Enter Approval Code</string>
    <string name="prompt_dcc_auth_code">Please Enter Original Approval Code</string>
    <string name="prompt_rrn">Please Enter RRN</string>
    <string name="prompt_dcc_rrn">Please Enter Original RRN</string>
    <string name="prompt_lock_terminal">Lock Terminal?</string>
    <string name="prompt_rate_report_pwd">Please Enter Rate Report Password</string>
    <string name="prompt_management_pwd">Please Enter Management Password</string>

    <!-- SearchCardActivity -->
    <string name="prompt_insert_swipe_wave_card">Please Swipe Card/Insert Card/Wave Card</string>
    <string name="prompt_insert_card">Please Insert Card</string>
    <string name="prompt_swipe_card">Please Swipe Card</string>
    <string name="prompt_wave_card">Please Wave Card</string>
    <string name="prompt_please_retry">Read Card Failed, Please Retry</string>
    <string name="prompt_input_mode_params_error">Card Parameters Error</string>
    <string name="prompt_ic_card_input">The Card Is IC Card, Please Insert Card!</string>
    <string name="prompt_card_not_support_cup_aid">The Card Does Not Support UnionPay AID, Please Swipe The Card!</string>
    <string name="prompt_card_date">Card Date</string>
    <string name="prompt_card_num">Card No.</string>
    <string name="prompt_card_date_err">Date Format Error, Please Check!</string>
    <string name="prompt_card_num_err">Card Number Error, Please Check!</string>
    <string name="prompt_card_num_manual">Enter Card Number Manually</string>
    <string name="prompt_date_default">MM/YY</string>
    <string name="prompt_default_searchCard_prompt">Card Information</string>
    <string name="prompt_phone_number">Please Input Phone Number:</string>
    <string name="prompt_email_address">Please Input Email Account:</string>
    <string name="prompt_fall_back">Please Pull Card, then Swipe Card</string>
    <string name="prompt_scan_code">Scan Code</string>

    <!-- InfosInputActivity -->
    <string name="prompt_input_transno">Please Enter Original Trans NO.</string>
    <string name="prompt_input_invoice_no">Please Enter Original Invoice NO.</string>
    <string name="prompt_input_approval_no">Please Enter Approval NO.</string>
    <string name="prompt_input_ref_no">Please Enter Original RRN</string>
    <string name="prompt_input_hase_no">Please Enter Original HASE RRN</string>
    <string name="prompt_input_cup_no">Please Enter Original CUP RRN</string>
    <string name="prompt_input_card_bin">Please Enter Card Bin:</string>
    <string name="prompt_input_orig_refer">Original Transaction Reference NO.</string>
    <string name="prompt_input_amount">Amount:</string>
    <string name="prompt_input_refund_amount">Refund Amount:</string>
    <string name="prompt_input_auth_code">Authorization Code</string>
    <string name="prompt_input_date">Original Transaction Date</string>
    <string name="please_input_again">Please Enter Again</string>
    <string name="invalid_amount">Invalid Amount</string>
    <string name="prompt_date_default2">MM/DD</string>
    <string name="prompt_get_last">Click OK to Get Last Transaction</string>

    <!-- adjust -->
    <string name="prompt_base_amount">Base</string>
    <string name="prompt_tip">Tip</string>
    <string name="prompt_ori_tips">Original Tip</string>
    <string name="prompt_adjust_percent">Tip percent</string>
    <string name="prompt_new_tips">New Tip</string>
    <string name="prompt_total_amount">Total Amount</string>
    <string name="prompt_tip_exceed">tip amount over limit</string>
    <string name="prompt_new_total_amount">New Total Amount</string>
    <string name="prompt_currency_symbol">Currency Symbol</string>

    <string name="prompt_net_amount_label">Net Amount</string>
    <string name="prompt_net_total_label">Net Total With Tips</string>
    <string name="prompt_new_total_label">New Net Total With Tips</string>

    <!-- CustomAlertDialog -->
    <string name="dialog_delete">Delete</string>
    <string name="dialog_clear">Clear</string>
    <string name="dialog_ok">OK</string>
    <string name="dialog_cancel">Cancel</string>
    <string name="dialog_sms">SMS</string>
    <string name="dialog_email">Email</string>
    <string name="dialog_print">Print</string>


    <string name="dialog_trans_succ">Transaction Success</string>
    <string name="dialog_trans_succ_liff"><xliff:g id="SUCCESS">%1$s</xliff:g> Success</string>

    <string name="dialog_download_bin_err">Card Bin Not Downloaded Completely</string>
    <string name="dialog_download_rate_err">Rate Not Downloaded Completely</string>
    <string name="dialog_check_bin_err">Not Local Card Bin</string>
    <string name="dialog_clear_bin">Clear Card Bin</string>
    <string name="dialog_clear_bin_err">Clear Card Bin Failed</string>

    <!-- waiting -->
    <string name="wait_print">Printing, Please Wait&#8230;</string>
    <string name="wait_receipt_generate">Receipt generating, Please Wait&#8230;</string>
    <string name="wait_demo_mode">Demo Mode&#8230;</string>
    <string name="wait_initialize_net">Initialize Net&#8230;</string>
    <string name="wait_process">Processing, Please Wait&#8230;</string>
    <string name="wait_connect">Connecting, Please Wait&#8230;</string>
    <string name="wait_wifi2Cellular_data_connect">Wifi is weak, switching to cellular data&#8230;</string>
    <string name="wait_connect_other">Backup Host Connecting,Please Wait&#8230;</string>
    <string name="wait_send">Sending, Please Wait&#8230;</string>
    <string name="wait_recv">Receiving, Please Wait&#8230;</string>
    <string name="wait_2_init_device">Please Complete Initialization firstly!</string>
    <string name="wait_pull_card">Please Pull Card&#8230;</string>
    <string name="wait_remove_card">Please Remove Card&#8230;</string>
    <string name="wait_self_test">self-test&#8230;</string>

    <!-- error -->
    <string name="no_long_press">Please remove finger</string>
    <string name="err_timeout">Timeout</string>
    <string name="err_connect">Connect Error</string>
    <string name="err_send">Send Error</string>
    <string name="err_recv">Receive Error</string>
    <string name="err_pack">Pack Error</string>
    <string name="err_unpack">Unpack Error</string>
    <string name="err_packet">Illegal Packet</string>
    <string name="err_mac">Unpack MAC Error</string>
    <string name="err_proc_code">Unpack Precess Code Error</string>
    <string name="err_msg">Unpack Message Type Code Error</string>
    <string name="err_trans_amt">Unpack Amount Error</string>
    <string name="err_trace_no">Unpack Serial NO. Error</string>
    <string name="err_term_id">Unpack Terminal ID Error</string>
    <string name="err_merch_id">Unpack Merchant ID Error</string>
    <string name="err_no_trans">No Transaction</string>
    <string name="err_no_succ_trans">No Approved Transaction</string>
    <string name="err_no_failed_trans">No Failed Transaction</string>
    <string name="err_no_orig_trans">Original Transaction Not Existed</string>
    <string name="err_has_voided">Transaction Has Been Voided</string>
    <string name="err_un_voided">Transaction cannot be Voided</string>
    <string name="err_comm_channel">Open Communication Channel Error</string>
    <string name="err_host_reject">Host Reject</string>
    <string name="err_need_settle_now">Transaction Number Exceed,\nPlease Settle Now</string>
    <string name="err_need_settle_later">Transaction Number Exceed,\nPlease Settle Later</string>
    <string name="err_no_free_space">No Free Space,\nPlease Settle Now</string>
    <string name="err_unsupported_trans">Unsupported Transaction</string>
    <string name="err_batch_up_break_need_continue">Last Batch Up Break,\Please Click Settle Continue</string>
    <string name="err_undefine">Undefined Error</string>
    <string name="err_undefine_info">Undefined</string>
    <string name="err_original_cardno">Cannot Match the Original Card NO.</string>
    <string name="err_password">Wrong Password!</string>
    <string name="err_param">Parameter Error</string>
    <string name="err_amount">Amount Exceed</string>
    <string name="err_card_denied">Host Approval,Card Denied\nPlease Contact Issuer</string>
    <string name="err_card_unsupported_demotion">Demotion is Unsupported</string>
    <string name="err_card_bin">Card BIN format Error</string>
    <string name="err_settle_select_acq">Please Choose an Acquirer!</string>
    <string name="err_print_paper">Out of Paper&#8230;</string>
    <string name="err_print_hot">Printer Overheat, Please Wait&#8230;</string>
    <string name="err_print_voltage">Printer Voltage is Too Low</string>
    <string name="err_password_reenter">Password Error,Please Enter Again</string>
    <string name="err_reverse">Reverse Error, Please Contact Issuer</string>
    <string name="err_reverse_recv">Reverse Receive Error</string>
    <string name="err_unadjusted">Transaction cannot be Adjusted</string>
    <string name="err_card_unsupported">Unsupported Card</string>
    <string name="err_card_pan">Error Card PAN</string>
    <string name="err_expired_card">Expired Card</string>
    <string name="err_unsupported_func">Unsupported Function</string>
    <string name="err_not_allowed">Not Allowed</string>
    <string name="err_user_cancel">Transaction Cancelled</string>
    <string name="err_sms_sent_fail">Failed to send SMS</string>
    <string name="err_clss_preproc_fail">Clss PreProc Fail</string>
    <string name="err_invalid_emv_qr">Invalid QR Code</string>
    <string name="err_lys_unavailable">+ FUN$ Is Paused, Unavailable</string>
    <string name="err_tpk_length">TPK Length Error</string>
    <string name="err_write_tpk">Write TPK Error</string>
    <string name="err_verify_cert_fail">Verify Certification Fail</string>
    <string name="err_write_rsa_puk">Write RSA PUK to PED Error!</string>
    <string name="err_write_tmk_to_ped">Write TMK to PED Error!</string>
    <string name="err_tmk_kcv">TMK KCV Error!</string>
    <string name="err_param_data">Param Data Error</string>
    <string name="err_unsupported_adjust">Please Enable Tip</string>
    <string name="err_settlement_pending">Settlement pending, please settle first</string>
    <string name="err_settlement_pending_title">Settlement pending</string>
    <string name="err_settlement_pending_msg">Please settle first</string>
    <string name="err_abort">Error abort</string>
    <string name="err_please_use_contact">Please use contact</string>
    <string name="err_database_update_fail">Transaction fail\ndatabase update fail</string>
    <string name="err_application_rejection">Application rejection</string>
    <string name="err_reconcile">RECONCILE ERROR</string>
    <string name="err_unable_to_go_online">Unable to go online</string>
    <string name="err_ecr_mode">ECR Mode</string>
    <string name="err_no_card_bin">Card Bin Not Downloaded</string>
    <string name="err_ecr">ECR Declined</string>
    <string name="err_dcc_opt_out">Transaction cannot be Opted Out</string>
    <string name="err_no_match_acquirer">No Match Acquirer</string>
    <string name="err_database_operate">Database Operate Failed</string>
    <string name="err_ecr_line_encryption_merchant">Line Encryption is not supported by the
        current merchant</string>
    <string name="err_ecr_msg">ECR Message Error!</string>
    <string name="err_ecr_open">Ecr Open Error</string>
    <string name="err_cup_qr_falied">CupQR trans failed</string>
    <string name="err_eps_falied">EPS trans failed</string>

    <string name="prompt_reverse">Reverse&#160;</string>
    <string name="prompt_err_code">Error Code:</string>
    <string name="prompt_switch_domestic">Switch to domestic host</string>
    <string name="err_tips_exceed_limit">Tips Exceed Limit</string>
    <string name="err_trans_not_accept">Trans Not Accepted</string>
    <string name="err_invalid_amount">Invalid amount</string>
    <!-- Version -->
    <string name="version">Version</string>
    <string name="app_version">Software Version NO.</string>
    <string name="db_version">DataBase Version NO.</string>
    <string name="param_version">Parameter Template Version NO.</string>
    <string name="param_update_time">Parameter Template Update time</string>

    <!-- EMV -->
    <string name="emv_param_load">Loading Emv Param.</string>
    <string name="emv_init_succ">Emv Param Initiated Successfully.</string>
    <string name="emv_cardno_confirm">Card NO. Confirm</string>
    <string name="emv_application_choose">APP Choose</string>
    <string name="emv_application_choose_again">APP Choose[Choose Again]</string>
    <string name="emv_card_in_black_list">Card in Black list</string>

    <!-- Printer -->
    <string name="print_card_check">Check</string>
    <string name="print_card_check_uneven">Check Uneven</string>
    <string name="print_card_check_err">Check Error</string>

    <!-- Receipt -->
    <string name="receipt_preview">Print Preview</string>
    <string name="receipt_merchant_name">MERCHANT NAME</string>
    <string name="receipt_merchant_code">MID</string>
    <string name="receipt_long_merchant_id">MERCHANT ID.</string>
    <string name="receipt_long_terminal_id">TERMINAL ID.</string>
    <string name="receipt_terminal_code_space">TID</string>
    <string name="receipt_card_type">CARD TYPE</string>
    <string name="receipt_batch_num">BATCH NO.</string>
    <string name="receipt_batch_num_colon">BATCH NO.</string>
    <string name="receipt_batch_num_space">BATCH NO.</string>
    <string name="receipt_date">DATA/TIME</string>
    <string name="receipt_failed_trans_details">UPLOAD FAILED TRANSACTION DETAILS</string>
    <string name="receipt_reject_trans_details">REJECT TRANSACTION DETAILS</string>
    <string name="receipt_trans">TRANS</string>
    <string name="receipt_type">TYPE</string>
    <string name="receipt_count">COUNT</string>
    <string name="receipt_amount">AMOUNT</string>
    <string name="receipt_card_borrow">DEBIT</string>
    <string name="receipt_card_loan">LOAN</string>
    <string name="receipt_card_no">CARD NO. </string>
    <string name="receipt_card_holder">HOLDER</string>
    <string name="receipt_trans_type">TRANS. TYPE</string>
    <string name="receipt_trans_no">TRACE NO.</string>
    <string name="receipt_invoice_no">INVOICE NO.</string>
    <string name="receipt_card_date">EXP. DATE</string>
    <string name="receipt_card_issue">ISSUER</string>
    <string name="receipt_auth_code">AUTH CODE</string>
    <string name="receipt_card_acquirer">ACQUIRER</string>
    <string name="receipt_ref_no">REF. NO.</string>
    <string name="receipt_orig_trans_no">ORIGINAL TRANS NO.:</string>
    <string name="receipt_orig_date">ORIGINAL TRANS DATE:</string>
    <string name="receipt_orig_auth_code">ORIGINAL AUTH CODE:</string>
    <string name="receipt_orig_ref_no">ORIGINAL REFERENCE CODE:</string>
    <string name="receipt_print_again">重印 REPRINT</string>
    <string name="receipt_had_void">HAD VOID</string>
    <string name="receipt_amount_prompt_start">TRANSACTION AMOUNT NOT EXCEED</string>
    <string name="receipt_amount_prompt_start_Chinese">交易金額不超過</string>
    <string name="receipt_amount_prompt_end">NO ENCRYPTION OR SIGNATURE REQUIRED</string>
    <string name="receipt_amount_prompt_end_sign">NO SIGNATURE REQUIRED</string>
    <string name="receipt_amount_prompt_end_pin">NO NEED TO ENTER PIN</string>
    <string name="receipt_amount_prompt_end_pin_Chinese">不需要輸入密碼</string>
    <string name="receipt_qr_signature">NO NEED TO SIGN</string>
    <string name="receipt_sign">-------CARDHOLDER SIGNATURE-------</string>
    <string name="receipt_hase_sign">CARDHOLDER SIGNATURE</string>
    <string name="receipt_sign_line">----------------------------------</string>
    <string name="receipt_double_line">===========================</string>
    <string name="receipt_CUP_double_line">=================</string>
    <string name="receipt_verified_by_cdcvm">VERIFIED BY CDCVM</string>
    <string name="receipt_verify">I acknowledge satisfactory receipt of relative goods/services.</string>
    <string name="receipt_verify_Chinese">本人承認已妥為收到有關的產品/服務</string>
    <string name="receipt_sign_Chinese">持卡人簽名CARDHOLDER SIGNATURE</string>
    <string name="receipt_CUP_verify">I ACKNOWLEDGE SATISFACTORY RECEIPT OF RELATIVE GOODS/SERVICES.</string>
    <string name="receipt_stub_acquire">ACQUIRER COPY</string>
    <string name="receipt_stub_merchant">MERCHANT COPY</string>
    <string name="receipt_stub_user">CUSTOMER COPY</string>
    <string name="receipt_amount_sale">SALES</string>
    <string name="receipt_sale">SALE</string>
    <string name="receipt_amount_tip">TIPS</string>
    <string name="receipt_refund">REFUND</string>
    <string name="receipt_voided_sales">VOIDED SALES</string>
    <string name="receipt_voided_refund">VOIDED REFUND</string>
    <string name="receipt_amount_total">TOTAL</string>
    <string name="receipt_amount_base">BASE</string>
    <string name="receipt_app_code">APP.CODE</string>
    <string name="receipt_voucher">VOUCHER</string>
    <string name="receipt_rate">FX RATE:</string>
    <string name="receipt_used_amount">USED AMOUNT:</string>
    <string name="receipt_dcc_visa_claim_HKD">I have been offered the choice of payment currencies including HKD. This currency conversion service is provided by this merchant and I agree to pay above total amount according to card issuer agreement.</string>
    <string name="receipt_dcc_visa_claim_MOP">I have been offered the choice of payment currencies including MOP. This currency conversion service is provided by this merchant and I agree to pay above total amount according to card issuer agreement.</string>
    <string name="receipt_dcc_mc_claim">I acknowledge satisfactory receipt of relative goods/services and I agree to pay above total shown here in.</string>
    <string name="receipt_settle_uppercase">SETTLEMENT</string>
    <string name="receipt_host">HOST</string>
    <string name="receipt_acquirer">* ACQUIRER</string>
    <string name="receipt_issuer">* ISSUER</string>
    <string name="receipt_bonus">BONUS</string>
    <string name="receipt_net_amount">NET AMT</string>
    <string name="receipt_remaining">REMAINING</string>
    <string name="receipt_pure_redeem">PURE REDEEM</string>
    <string name="receipt_net_total">NET TOTAL</string>
    <string name="receipt_tips">TIPS</string>
    <string name="receipt_net">NET</string>
    <string name="receipt_bank_reference">For Bank Reference Only</string>
    <string name="receipt_lys_up">LYS UP</string>
    <string name="receipt_lys_down">LYS DOWN</string>
    <string name="receipt_instalment_month">No. OF INSTALMENT:</string>
    <string name="receipt_instalment_prompt">This is a loan application,\nnot a credit card transaction.</string>
    <string name="receipt_instalment_prompt_chinese_part1">此乃一項貸款申請</string>
    <string name="receipt_instalment_prompt_chinese_part2">非一般信用卡簽賬</string>
    <string name="receipt_signature_instalment_prompt">I ACKNOWLEDGE SATISFACTORY RECEIPT OF\nRELATIVE GOODS/SERVICES. I confirm that\nI have read and understood the Terms and\nConditions for interest-free Instalment\nPlan and agree to be bound by them</string>
    <string name="receipt_signature_prompt">I acknowledge satisfactory receipt of\nrelative goods/services.</string>
    <string name="receipt_trans_totals_by_card">TRANS TOTALS BY CARD</string>
    <string name="receipt_end_of_total">------ END OF TOTAL ------</string>
    <string name="receipt_trans_details">TRANSACTION LOG</string>
    <string name="saving_signature">Saving Signature…</string>
    <string name="receipt_lys_disclaimer">For a transaction with Cash Dollars redemption, Cash Dollars earned will be calculated based on the residual amount paid after Cash Dollars redemption. For a refund transaction, Cash Dollars deducted will be calculated based on the total refund amount. There may be a difference between the Cash Dollars earned and deducted in a refund due to the different amounts used in the calculation.</string>
    <!-- Settings -->
    <string name="set_cancel_save">Cancel Save</string>
    <string name="set_recv">Receiving&#8230;</string>
    <string name="set_key_success">Import Key Success</string>
    <string name="set_key_fail">Import Key Fail</string>
    <string name="demo_mode_prompting">Please input COM00</string>
    <string name="demo_mode_pwd">COM00</string>

    <!-- initialization -->
    <string name="init_pwd_hint">Initialization</string>

    <!-- select currency -->
    <string name="select_currency">Please Select Currency :</string>
    <string name="rate">Fx.Rate :</string>
    <string name="fee_rate">Markup Included</string>


    <!-- acquirer setting -->
    <string name="acquirer">Acquirer</string>
    <string name="acq_select_all">All</string>
    <string name="acq_select_hint">Please Choose an Acquirer</string>
    <string name="acq_select_instalment_plan">Please Choose Instalment Plan</string>
    <string name="acq_is_default">Default?</string>
    <string name="acq_terminal_id">Terminal ID</string>
    <string name="acq_terminal_id_hint">8 digits</string>
    <string name="acq_merchant_id">Merchant ID</string>
    <string name="acq_merchant_id_hint">15 digits</string>
    <string name="acq_nii">NII</string>
    <string name="acq_nii_hint">3 digits</string>
    <string name="acq_description_hint">12 characters</string>
    <string name="acq_batch_no">BATCH NO.</string>
    <string name="acq_batch_no_hint">6 digits</string>
    <string name="acq_commParam">Comm Param</string>
    <string name="acq_address">Host Address</string>
    <string name="acq_ip">IP</string>
    <string name="acq_ip_sec">IP(SEC)</string>
    <string name="acq_ip_hint">0.0.0.0</string>
    <string name="acq_url_hint">uat.haseafepos.hangseng.com</string>
    <string name="acq_url">URL</string>
    <string name="acq_port">PORT</string>
    <string name="acq_port_sec">PORT(SEC)</string>
    <string name="acq_url_sec">URL(SEC)</string>
    <string name="acq_port_hint">10000</string>
    <string name="acq_disable_trick_feed">Disable Trick Feed</string>
    <string name="acq_support_keyin">Allow Manual Input</string>
    <string name="acq_support_qr">Support QR</string>
    <string name="acq_support_refno">Support REF.NO</string>
    <string name="acq_description">Description</string>
    <string name="acq_instalment_plan">Instalment Plan</string>

    <!-- issuer -->
    <string name="issuer_select_hint">Please Choose an Issuer</string>
    <string name="issuer_floor_limit">Floor Limit</string>
    <string name="issuer_enable_adjust">Enable Adjust</string>
    <string name="issuer_adjust_percent">Adjust Percent</string>
    <string name="issuer_enable_offline">Enable Offline</string>
    <string name="issuer_enable_preauth">Enable PreAuth</string>
    <string name="issuer_enable_refund">Enable Refund</string>
    <string name="issuer_enable_void">Enable Void</string>
    <string name="issuer_enable_expiry">Enable Expiry</string>
    <string name="issuer_enable_manualPan">Enable Manual PAN</string>
    <string name="issuer_check_expire">Check Expiry</string>
    <string name="issuer_check_pan">Check Pan</string>
    <string name="issuer_enable_print">Enable Print</string>
    <string name="issuer_pin_required">Pin Required</string>
    <string name="issuer_security_code">Security Code?</string>
    <string name="issuer_security_code_manual">Secu. Code Manual?</string>
    <string name="issuer_mask_expiry">Mask Expiry Date</string>

    <!-- Exit -->
    <string name="please_install_neptune">Please Install Neptune</string>
    <string name="exit_app">Exit Current APP?</string>
    <string name="update_param_now_or_not">Update Parameter Now or Not?</string>
    <string name="param_need_update_please_settle">Parameter Need Update, Please Settle!</string>
    <string name="update_param">Update Param</string>
    <string name="no_update_param">No Update Param</string>
    <string name="wait_update">Updating, Please Wait&#8230;</string>

    <!-- image workaround -->
    <string name="image_desc">Code Scan</string>

    <string name="permission_rationale_storage">Please enable storage permission from System setting for saving crash log</string>
    <string name="permission_rationale_sms">Please enable sms permission from System setting if you need paperless function</string>
    <string name="permission_rationale">Please allow normal permissions</string>

    <!--Sign in Sign out-->
    <string name="pos_sign_on">Sign In</string>
    <string name="pos_master_sign_on">Mastercard  \nSign In</string>
    <string name="pos_sign_on_en">Sign In</string>
    <string name="pos_sign_in_wait">UPI Sign In…</string>
    <string name="mastercard_sign_in_wait">Mastercard Sign In…</string>

    <string name="redeem_cash_unavailable0">+ FUN$ is not available</string>
    <string name="redeem_cash_unavailable2">+ FUN$ is not available\nNOT HASE CARD</string>
    <string name="redeem_cash_unavailable3">+ FUN$ is not available\nREDEMPTION NOT ALLOWED</string>

    <string name="batch_upload">Batch upload [%1$d/%2$d]</string>
    <string name="settle_end">Settlement Ending</string>

    <!--Use for debug-->
    <string name="produce_reversal">Produce Reversal?</string>
    <string name="allow_produce_reversal">Allow Produce Reversal</string>
    <string name="debug_mode">Debug Mode</string>
    <string name="allow_debug_mode">Allow Debug Mode</string>

    <!--SPay App-->
    <string name="launch_Spay">WeChat Pay/Alipay/PayMe</string>
    <string name="launch_FPS">FPS+PTK</string>
    <string name="spay_package_name">cn.swiftpass.enterprise.overseas.pos.A920.hase_pos</string>
    <string name="menu_hidden_spay"> Force hidden SPay</string>

    <!-- Base -->
    <string name="base_connect">Connect Base</string>
    <string name="err_base_connect">Base Connect Error</string>
    <string name="err_base_not_connect">Base Has Not Connected</string>
    <string name="err_base_wifi_open">Wifi Open Error</string>
    <string name="err_base_wifi_connect">Wifi Connect Error</string>
    <string name="wait_base_connect">Base Connecting&#8230;</string>
    <string name="wait_base_wifi_open">Wifi Opening&#8230;</string>
    <string name="wait_base_wifi_connect">Wifi Connecting&#8230;</string>
    <string name="wait_base_wifi_set">Wifi Setting&#8230;</string>
    <string name="wait_base_scan">Scanning&#8230;</string>
    <string name="wait_open">Opening, Please Wait&#8230;</string>
    <string name="wait_close">Closing, Please Wait&#8230;</string>
    <string name="baselink_default_ip">***********</string>
    <string name="baselink_default_mask">*************</string>
    <string name="baselink_default_pool_end">*************</string>
    <string name="baselink_default_wifi_pwd">12345678</string>

    <string name="trans_upi">CUP QR CODE</string>
    <string name="trans_gp">GP PLC</string>
    <string name="trans_eps">EPS</string>
    <string name="err_no_match_app">There is no matching APP \nPlease download and install</string>
    <string name="alert_put_handset_back">Please put handset back</string>
    <string name="alert_terminal_busy">TERMINAL BUSY</string>
    <string name="ecr_transaction_retrieval">Ecr transaction retrieval</string>
    <string name="error_upi_enable">Please enable UPI</string>

    <!-- Ref NO-->
    <string name="prompt_ref_no">Please Enter REF NO</string>

    <string name="third_party">Third Party</string>
    <string name="send_print_info">send print info</string>
    <string name="send_print_info_key">send_print_info_key</string>
    <string name="no_acquirer">no acquire</string>
    <string name="trans_retrieval">Transaction retrieval</string>
    <string name="yuu_creation_retrieval">Loyalty membership creation retrieval</string>

    <string name="please_open_dcc_first">Please open dcc in setting page</string>
    <string name="error_micropay_enable">Please enable MicroPay</string>
    <string name="MICRO_PAY_IS_STAND_ALONE">MICRO_PAY_IS_STAND_ALONE</string>
    <string name="MICRO_PAY_IS_DO_ECR_TRANS">MICRO_PAY_IS_DO_ECR_TRANS</string>
    <string name="print_cardholder_name_key">print_cardholder_name_key</string>
    <string name="print_cardholder_name">print cardholder name</string>
    <string name="HASE_CASH_DOL_NAME">HASE_CASH_DOL_NAME</string>
    <string name="hase_cash_dol_name">Hase + FUN$ Name</string>
    <string name="Wifi">Wifi Setting</string>
    <string name="prompt_wifi_pwd">Please Enter Wifi Manage Password</string>
    <string name="wifi_manage">Wifi Manage</string>
    <string name="WIFI_MANAGE_PASSWORD">WIFI_MANAGE_PASSWORD</string>
    <string name="EDC_ENABLE_WIFI_MANAGE">EDC_ENABLE_WIFI_MANAGE</string>
    <string name="edc_enable_wifi_manage">Enable Wifi Manage</string>
    <string name="edc_enable_cup_ecr">Enable CUP ECR</string>
    <string name="edc_enable_ecr_mask_card_no">Mask ECR Auth Card No</string>
    <string name="edc_enable_ecr_mask_expire_date">Mask ECR Expire Date</string>
    <string name="receipt_total">TOTAL 總計</string>
    <string name="receipt_settlement">SETTLEMENT 結算</string>

    <!--Intended Offline -->
    <string name="prompt_intended_offline_pwd">Please Enter Intended Offline Password</string>
    <string name="prompt_approval_code_input">Please input Approval code</string>
    <string name="contingency_display">Contingency</string>
    <!--Party King -->
    <string name="partyking">PartyKing</string>
    <string name="error_partyking_enable">Please enable PartyKing</string>
    <string name="card_type">"CARD TYPE: "</string>
    <string name="mastercard_sign_in">Mastercard Sign in</string>
    <string name="wechat_pay_alipay_payme_octopus">WeChat Pay/Alipay/PayMe/Octopus</string>
    <!--Screen Saver -->
    <string name="screen_saver_video_no_exist">The screensaver video does not exist!</string>



</resources>