<?xml version="1.0" encoding="utf-8"?><!-- DO NOT TRANSLATE THIS FILE --><!-- DO NOT TRANSLATE THIS FILE --><!-- DO NOT TRANSLATE THIS FILE -->
<resources>

    <string name="COMM_TYPE">COMM_TYPE</string>
    <string name="COMM_TIMEOUT">COMM_TIMEOUT</string>

    <string name="MOBILE_KEEP_ALIVE">MOBILE_KEEP_ALIVE</string>
    <string name="MO<PERSON>LE_TEL_NO">MOBILE_TEL_NO</string>
    <string name="MO<PERSON><PERSON>_NEED_USER">MOBILE_NEED_USER</string>
    <string name="MO<PERSON>LE_USER">MOBILE_USER</string>
    <string name="MO<PERSON>LE_PWD">MOBILE_PWD</string>

    <string name="LAN_LOCAL_IP">LAN_LOCAL_IP</string>
    <string name="LAN_NETMASK">LAN_NETMASK</string>
    <string name="LAN_GATEWAY">LAN_GATEWAY</string>
    <string name="LAN_DNS1">LAN_DNS1</string>
    <string name="LAN_DNS2">LAN_DNS2</string>

    <string name="COMM_REDIAL_TIMES">COMM_REDIAL_TIMES</string>
    <string name="EDC_REVERSAL_RETRY_TIME">EDC_REVERSAL_RETRY_TIME</string>
    <string name="MAX_TRANS_COUNT">MAX_TRANS_COUNT</string>

    <string name="OFFLINE_TC_UPLOAD_TIMES">OFFLINE_TC_UPLOAD_TIMES</string>
    <string name="OFFLINE_TC_UPLOAD_NUM">OFFLINE_TC_UPLOAD_NUM</string>

    <string name="QUICK_PASS_TRANS_PIN_FREE_AMOUNT">QUICK_PASS_TRANS_PIN_FREE_AMOUNT</string>
    <string name="QUICK_PASS_TRANS_SIGN_FREE_AMOUNT">QUICK_PASS_TRANS_SIGN_FREE_AMOUNT</string>

    <string name="EDC_MERCHANT_NAME">EDC_MERCHANT_NAME</string>
    <string name="EDC_MERCHANT_ADDRESS">EDC_MERCHANT_ADDRESS</string>
    <string name="EDC_CURRENCY_LIST">EDC_CURRENCY_LIST</string>
    <string name="EDC_RECEIPT_NUM">EDC_RECEIPT_NUM</string>
    <string name="EDC_TRACE_NO">EDC_TRACE_NO</string>
    <string name="EDC_INVOICE_NO">EDC_INVOICE_NO</string>
    <string name="EDC_SUPPORT_TIP">EDC_SUPPORT_TIP</string>
    <string name="EDC_BATCH_NO">EDC_BATCH_NO</string>
    <string name="EDC_ENABLE_VOID">EDC_ENABLE_VOID</string>
    <string name="EDC_ENABLE_REFUND">EDC_ENABLE_REFUND</string>
    <string name="EDC_ENABLE_EMI">EDC_ENABLE_EMI</string>
    <string name="EDC_ENABLE_CASH_ONLY">EDC_ENABLE_CASH_ONLY</string>
    <string name="EDC_ENABLE_SALE_CASH">EDC_ENABLE_SALE_CASH</string>
    <string name="EDC_ENABLE_MONEY_ADD_ACCOUNT">EDC_ENABLE_MONEY_ADD_ACCOUNT</string>
    <string name="EDC_ENABLE_MONEY_ADD_CASH">EDC_ENABLE_MONEY_ADD_CASH</string>
    <string name="EDC_ENABLE_BALANCE_UPDATE">EDC_ENABLE_BALANCE_UPDATE</string>
    <string name="EDC_ENABLE_BALANCE_ENQUIRY">EDC_ENABLE_BALANCE_ENQUIRY</string>
    <string name="EDC_ENABLE_MQTT">EDC_ENABLE_MQTT</string>
    <string name="EDC_ENABLE_MANUAL_PAN">EDC_ENABLE_MANUAL_PAN</string>
    <string name="EDC_CHECK_EXPIRY">EDC_CHECK_EXPIRY</string>
    <string name="EDC_CHECK_PAN">EDC_CHECK_PAN</string>
    <string name="EDC_ENABLE_PRINT">EDC_ENABLE_PRINT</string>
    <string name="EDC_TIP_PERCENTAGE">EDC_TIP_PERCENTAGE</string>
    <string name="EDC_PRE_AUTH_COMPLETION_PERCENTAGE">EDC_PRE_AUTH_COMPLETION_PERCENTAGE</string>
    <string name="EDC_ENABLE_ECHARGESLIP">EDC_ENABLE_ECHARGESLIP</string>
    <string name="EDC_CVV_REQUIRED">EDC_CVV_REQUIRED</string>
    <string name="EDC_PREAUTH_RECORD_SAVING_DAY">EDC_PREAUTH_RECORD_SAVING_DAY</string>


    <string name="MK_INDEX">MK_INDEX</string>
    <string name="KEY_ALGORITHM">KEY_ALGORITHM</string>
    <string name="MK_INDEX_MANUAL">MK_INDEX_MANUAL</string>
    <string name="MK_VALUE">MK_VALUE</string>
    <string name="PK_VALUE">PK_VALUE</string>
    <string name="DK_VALUE">DK_VALUE</string>

    <string name="ACQ_NAME">ACQ_NAME</string>

    <string name="SEC_SYS_PWD">SEC_SYS_PWD</string>
    <string name="SEC_MERCHANT_PWD">SEC_MERCHANT_PWD</string>
    <string name="SEC_TMS_PWD">SEC_TMS_PWD</string>
    <string name="SEC_TERMINAL_PWD">SEC_TERMINAL_PWD</string>
    <string name="SEC_VOID_PWD">SEC_VOID_PWD</string>
    <string name="SEC_REFUND_PWD">SEC_REFUND_PWD</string>
    <string name="SEC_ADJUST_PWD">SEC_ADJUST_PWD</string>
    <string name="SEC_SETTLE_PWD">SEC_SETTLE_PWD</string>

    <string name="LAN_DHCP">LAN_DHCP</string>

    <string name="QUICK_PASS_TRANS_PIN_FREE_SWITCH">QUICK_PASS_TRANS_PIN_FREE_SWITCH</string>
    <string name="QUICK_PASS_TRANS_FLAG">QUICK_PASS_TRANS_FLAG</string>
    <string name="QUICK_PASS_TRANS_CDCVM_FLAG">QUICK_PASS_TRANS_CDCVM_FLAG</string>
    <string name="QUICK_PASS_TRANS_SIGN_FREE_FLAG">QUICK_PASS_TRANS_SIGN_FREE_FLAG</string>

    <string name="TTS_SALE">TTS_SALE</string>
    <string name="TTS_VOID">TTS_VOID</string>
    <string name="TTS_REFUND">TTS_REFUND</string>
    <string name="TTS_PREAUTH">TTS_PREAUTH</string>

    <string name="BT_MAC_ADDRESS">BT_MAC_ADDRESS</string>
    <string name="RESULT_READER_TYPE">result_reader_type</string>
    <string name="EDC_ENABLE_SAVE_LOG">EDC_ENABLE_SAVE_LOG</string>
    <string name="EDC_ENABLE_PRINT_LOG">EDC_ENABLE_PRINT_LOG</string>
    <string name="EDC_EMV_DEBUG_REPORT">EDC_EMV_DEBUG_REPORT</string>
    <string name="EDC_ENABLE_SETTLEMENT_PASSWORD">EDC_ENABLE_SETTLEMENT_PASSWORD</string>
    <string name="EDC_ENABLE_REFUND_PASSWORD">EDC_ENABLE_REFUND_PASSWORD</string>
    <string name="EDC_ENABLE_VOID_PASSWORD">EDC_ENABLE_VOID_PASSWORD</string>
    <string name="EDC_ENABLE_MANUAL_PASSWORD">EDC_ENABLE_MANUAL_PASSWORD</string>
    <string name="LOG_FILE_INDEX">LOG_FILE_INDEX</string>
    <string name="EDC_QR_TRANS_QUERY_INTERVAL">EDC_QR_TRANS_QUERY_INTERVAL</string>
    <string name="EDC_QR_TRANS_QUERY_REPEAT_INTERVAL">EDC_QR_TRANS_QUERY_REPEAT_INTERVAL</string>
    <string name="EDC_MAX_AMOUNT_DIGIT">EDC_MAX_AMOUNT_DIGIT</string>
    <string name="EDC_ENABLE_DCC_SUPPORT_CHIP_CARD">EDC_ENABLE_DCC_SUPPORT_CHIP_CARD</string>
    <string name="EDC_ENABLE_DCC_SUPPORT_TAP_CARD">EDC_ENABLE_DCC_SUPPORT_TAP_CARD</string>
    <string name="EDC_ENABLE_DCC_SUPPORT_SWIPE_CARD">EDC_ENABLE_DCC_SUPPORT_SWIPE_CARD</string>
    <string name="EDC_ENABLE_DCC_SUPPORT_MANUAL_CARD">EDC_ENABLE_DCC_SUPPORT_MANUAL_CARD</string>
    <string name="EDC_AUTO_SETTLEMENT_TIME">EDC_AUTO_SETTLEMENT_TIME</string>
    <string name="EDC_AUTO_SETTLEMENT_DATE">EDC_AUTO_SETTLEMENT_DATE</string>
    <string name="EDC_TRANSACTION_NUMBER_LIMIT">EDC_TRANSACTION_NUMBER_LIMIT</string>
    <string name="EDC_MAX_TRANSACTION_NUMBER">EDC_MAX_TRANSACTION_NUMBER</string>
    <string name="EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO">EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO</string>
    <string name="EDC_ENABLE_CONFIG">EDC_ENABLE_CONFIG</string>


    <string name="SSL_CER_FILE">SSL_CER_FILE</string>
    <string name="PROVIDER_LIST_FILE">PROVIDER_LIST_FILE</string>
    <string name="EDC_PREAUTH_COMPLETION">EDC_PREAUTH_COMPLETION</string>

    <string name="EDC_BANNER_IMAGE_FILE">EDC_BANNER_IMAGE_FILE</string>
</resources>