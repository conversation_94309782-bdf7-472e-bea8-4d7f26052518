<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="AutomaticPagerGridView">

        <!-- 行数 -->
        <attr name="auto_lines" format="integer" />
        <!-- 列数 -->
        <attr name="auto_column" format="integer" />
        <!-- button的样式 -->
        <attr name="auto_button" format="reference" />
        <!-- 是否显示导航点 -->
        <attr name="auto_button_visible" format="boolean" />
    </declare-styleable>
    <declare-styleable name="Rotate3dAnimation">
        <attr name="rollType" format="enum">
            <enum name="x" value="0" />
            <enum name="y" value="1" />
            <enum name="z" value="2" />
        </attr>
        <attr name="fromDeg" format="float" />
        <attr name="toDeg" format="float" />
        <attr name="pivotX" format="fraction" />
        <attr name="pivotY" format="fraction" />
    </declare-styleable>
    <declare-styleable name="MyEditText">
        <attr name="enableAlpha" format="boolean" />
    </declare-styleable>
    <declare-styleable name="Keyboard">
        <attr name="xml" format="reference" />
        <attr name="random_keys" format="reference|boolean" />
        <attr name="autoSize" format="boolean" />
        <attr name="keepKeyboardOn" format="boolean" />
        <attr name="timeout_sec" format="integer" />
    </declare-styleable>
    <declare-styleable name="MySoftKey">
        <attr name="textSize" format="dimension" />
    </declare-styleable>
    <declare-styleable name="ClssLight">
        <attr name="offSrc" format="reference" />
        <attr name="onSrc" format="reference" />
    </declare-styleable>
    <declare-styleable name="ProgressWheel">
        <attr name="matProg_progressIndeterminate" format="boolean" />
        <attr name="matProg_barColor" format="color" />
        <attr name="matProg_rimColor" format="color" />
        <attr name="matProg_rimWidth" format="dimension" />
        <attr name="matProg_spinSpeed" format="float" />
        <attr name="matProg_barSpinCycleTime" format="integer" />
        <attr name="matProg_circleRadius" format="dimension" />
        <attr name="matProg_fillRadius" format="boolean" />
        <attr name="matProg_barWidth" format="dimension" />
        <attr name="matProg_linearProgress" format="boolean" />
    </declare-styleable>
    <declare-styleable name="WifiEncryptionState">
        <attr name="state_encrypted" format="boolean" />
    </declare-styleable>
    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="pstsIndicatorColor" format="color" />
        <attr name="pstsUnderlineColor" format="color" />
        <attr name="pstsDividerColor" format="color" />
        <attr name="pstsIndicatorHeight" format="dimension" />
        <attr name="pstsUnderlineHeight" format="dimension" />
        <attr name="pstsDividerPadding" format="dimension" />
        <attr name="pstsTabPaddingLeftRight" format="dimension" />
        <attr name="pstsScrollOffset" format="dimension" />
        <attr name="pstsTabBackground" format="reference" />
        <attr name="pstsShouldExpand" format="boolean" />
        <attr name="pstsTextAllCaps" format="boolean" />
        <attr name="selectedTabTextColor" format="color" />
    </declare-styleable>

</resources>