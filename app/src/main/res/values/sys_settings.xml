<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- 系统设置 -->
    <string name="settings_title">Config</string>
    <string name="settings_menu_communication_type">Communication Type</string>
    <string name="settings_menu_communication_parameter">Comm</string>
    <string name="settings_menu_edc_parameter">EDC</string>
    <string name="settings_menu_issuer_parameter">Issuer</string>
    <string name="settings_menu_acquirer_parameter">Acquirer</string>
    <string name="settings_menu_clss_parameter">Clss</string>
    <string name="settings_menu_pwd_manage">Password</string>
    <string name="settings_menu_keyManage">Key</string>
    <string name="settings_menu_otherManage">Other</string>
    <string name="settings_menu_mqtt_parameter">MQTT</string>
    <string name="settings_menu_mqtt_host_config">MQTT Host</string>
    <string name="settings_menu_provider">Select Provider</string>

    <!-- 商户参数 -->

    <string name="terminal_date_time" tools:keep="@string/terminal_date_time">Terminal Date/Time:</string>
    <string name="currency_list">Currency:</string>

    <!-- 系统设置的通讯参数 -->
    <string name="commParam_menu_comm_mode">Communication Type</string>
    <string name="commParam_menu_comm_timeout">Comm Timeout</string>
    <string name="edc_merchant_name">Merchant Name</string>
    <string name="edc_merchant_address">Merchant Address</string>
    <string name="edc_receipt_no">Receipt #</string>
    <string name="edc_trace_no">Trace No.</string>
    <string name="edc_batch_no">BATCH NO.</string>
    <string name="edc_qr_trans_query_interval">QR Trans Query Interval</string>
    <string name="edc_qr_trans_query_repeat_interval">QR Trans Query Repeat Interval</string>
    <string name="edc_max_amount_digit">Max Amount Digit</string>
    <string name="edc_enable_dcc_support_chip_card">Enable DCC Support Chip Card</string>
    <string name="edc_enable_dcc_support_tap_card">Enable DCC Support Tap Card</string>
    <string name="edc_enable_dcc_support_swipe_card">Enable DCC Support Swipe Card</string>
    <string name="edc_enable_dcc_support_manual_card">Enable DCC Support Manual Card</string>
    <string name="edc_preauth_completion">PreAuth Completion/Cancel</string>
    <string name="edc_auto_settlement_time">Auto Settlement Time</string>
    <string name="edc_transaction_number_limit">Transaction Number Limit</string>
    <string name="edc_max_transaction_number">Max Transaction Number</string>
    <string name="edc_invoice_no">Invoice No.</string>
    <string name="edc_tip_percentage">Tip  Percentage</string>
    <string name="edc_pre_auth_completion_percentage">Pre auth Completion Percentage</string>
    <string name="edc_enable_dcc">Enable DCC</string>
    <string name="edc_enable_echargeSlip">Enable EchargeSlip</string>
    <string name="edc_cvv_required">CVV Required</string>
    <string name="edc_preauth_record_saving_day">Preauth Record Saving Day</string>
    <string name="edc_reversal_retry_time">Reversal Retry Time</string>
    <string name="edc_enable_print_log">Enable Print Log</string>
    <string name="edc_emv_debug_report">EMV Debug Report</string>
    <string name="edc_enable_settlement_password">Enable Settlement Password</string>
    <string name="edc_enable_refund_password">Enable Refund Password</string>
    <string name="edc_enable_void_password">Enable Void Password</string>
    <string name="edc_enable_manual_password">Enable Manual Password</string>
    <string name="edc_enable_refund">Enable Refund</string>
    <string name="edc_enable_emi">Enable EMI</string>
    <string name="edc_enable_cash_only">Enable Cash Only</string>
    <string name="edc_enable_sale_cash">Enable Sale Cash</string>
    <string name="edc_enable_money_add_account">Enable Money Add Account</string>
    <string name="edc_enable_money_add_cash">Enable Money Add Cash</string>
    <string name="edc_enable_balance_update">Enable Balance Update</string>
    <string name="edc_enable_balance_enquiry">Enable Balance enquiry</string>
    <string name="edc_enable_mqtt">Enable MQTT</string>
    <string name="edc_enable_void">Enable Void</string>
    <string name="edc_enable_save_log">Enable Save Log</string>
    <string name="edc_enable_tip">Support Tip</string>
    <string name="edc_enable_transaction_success_audio">Enable Transaction Success Audio</string>

    <!-- 通讯参数的移动网络 -->
    <string name="commParam_menu_mobile_wifi_keep_alive">Keep Alive</string>
    <string name="commParam_menu_mobile_dial_no">AP</string>
    <string name="commParam_menu_mobile_apn">APN</string>
    <string name="commParam_menu_mobile_need_user">Need User</string>
    <string name="commParam_menu_mobile_username">User Name</string>
    <string name="commParam_menu_mobile_user_password">User Password</string>

    <!-- wifi status -->

    <string name="open_wifi">Open Wi-Fi Setting</string>
    <string name="open_sim">Open SIM Setting</string>

    <!-- 系统设置的终端密钥管理 -->

    <string name="keyManage_menu_key">Key</string>
    <string name="keyManage_menu_tmk_index">Master Key Index(0&#8211;99)</string>
    <string name="keyManage_menu_tmk_index_no">Master Key(Import) Index(0&#8211;99)</string>
    <string name="keyManage_menu_tmk_value">Master Key</string>
    <string name="keyManage_menu_tpk_value">PIN Key</string>
    <string name="keyManage_menu_tdk_value">Session Key</string>

    <!-- 系统设置的密码管理 -->

    <string name="input_old_password">Please Input Old Password</string>
    <string name="input_new_password">Please Input New Password</string>
    <string name="input_again_new_password">Confirm New Password</string>
    <string name="error_input_length">Length Error</string>
    <string name="error_old_password">Old Password Error</string>
    <string name="error_password">Password Error</string>
    <string name="error_password_no_same">Password Not Match\nPlease Input Again</string>
    <string name="password_modify_success">Modify Password Success</string>

    <!-- 系统设置的其他管理 -->

    <string name="om_clearTrade_menu_reversal">Clear Reversal</string>
    <string name="om_clearTrade_menu_trade_voucher">Clear Batch</string>
    <string name="om_clearTrade_menu_key">Clear Key</string>
    <string name="om_clearTrade_menu_storage">Clear Storage</string>

    <string name="selfTest_succ">SelfTest Success</string>

    <string name="om_download_menu_echo_test">Echo Test</string>

    <string name="om_paramPrint_menu_print_aid_para">AID Parameter</string>
    <string name="om_paramPrint_menu_print_capk_para">CAPK Parameter</string>
    <string name="om_paramPrint_menu_print_sys_para">Sys Parameter</string>

    <!-- 响应码错误提示语 -->
    <string name="response_00">Success</string>
    <string name="response_01">DECLINE, CALL ISSUER</string>
    <string name="response_02">DECLINE, CALL ISSUER</string>
    <string name="response_03">INVALID MID/TID, CALL HELP</string>
    <string name="response_04">PICKUP CARD</string>
    <string name="response_05">DO NOT HONOR</string>
    <string name="response_06">CALL HELP, 06</string>

    <string name="response_12">INVALID TRANSACTION</string>
    <string name="response_13">INVALID AMOUNT</string>
    <string name="response_14">INVALID CARD</string>
    <string name="response_15">BANK NOT SUPPORTED</string>

    <string name="response_30">FORMAT ERROR</string>
    <string name="response_33">EXPIRED CARD, PICKUP</string>
    <string name="response_34">SUSPECTED FRAUD, PICKUP</string>
    <string name="response_36">RESTRICTED CARD, PICKUP</string>
    <string name="response_38">PIN TRIES EXCEEDED</string>
    <string name="response_39">NO CREDIT ACCOUNT</string>

    <string name="response_40">FUNCTION NOT SUPPORTED</string>
    <string name="response_41">LOST CARD, PICKUP</string>
    <string name="response_42">NO ACCOUNT</string>
    <string name="response_43">STOLEN CARD, PICKUP</string>

    <string name="response_51">INSUFFICIENT FUNDS</string>
    <string name="response_52">NO CHECKING ACCOUNT</string>
    <string name="response_53">NO SAVINGS ACCOUNT</string>
    <string name="response_54">EXPIRED CARD</string>
    <string name="response_55">INCORRECT PIN</string>
    <string name="response_57">TRANSACTION NOT PERMITTED TO CARDHOLDER</string>
    <string name="response_58">TRANSACTION NOT PERMITTED ON TERMINAL</string>
    <string name="response_59">SUSPECTED FRAUD</string>

    <string name="response_61">EXCEEDS LIMIT</string>
    <string name="response_62">RESTRICTED CARD</string>
    <string name="response_63">DEBIT KEY ERROR, CALL HELP</string>
    <string name="response_65">ACTIVITY COUNT LIMIT EXCEEDED</string>

    <string name="response_75">PIN TRIES EXCEEDED</string>

    <string name="response_81">PIN CRYPTOGRAPHIC ERROR</string>
    <string name="response_89">TID NOT PRESENT, CALL HELP</string>

    <string name="response_91">ISSUER / SWITCH INOPERATIVE</string>
    <string name="response_92">ROUTING ERROR</string>
    <string name="response_95">RECONCILE ERROR</string>
    <string name="response_96">SYSTEM MALFUNCTION</string>
    <string name="response_98">EXCEEDS CASH LIMIT</string>

    <string name="response_L1">MERCHANT LIMIT EXCEEDED</string>
    <string name="response_L2">CARD REUSE LIMIT EXCEEDED</string>
    <string name="response_L3">INTL CARD ACCEPT DISABLED</string>


    <string name="response_unknown">System Error</string>

    <!--  -->
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="input_len_err">Input Length Error</string>
    <string name="transType_print">Print</string>

    <!-- select provider -->
    <string name="mobile_config">Mobile Config</string>


    <string name="provider_name_airtel">AIRTEL</string>
    <string name="provider_name_vodafone">VODAFONE</string>
    <string name="provider_name_bsnl">BSNL</string>
    <string name="provider_name_public">PUBLIC</string>
    <string name="provider_name_airtel_iot">AIRTEL_IOT</string>
    <string name="provider_name_bsnl_new">BSNL_NEW</string>
    <string name="provider_name_airtel_sbi">AIRTEL_SBI</string>
    <string name="provider_name_vodafone_iot">VODAFONE_IOT</string>

    <!-- EMI VALUE -->
    <string name="emi_value_airtel">AIRTEL</string>
    <string name="emi_value_vodafone">VODAFONE</string>
    <string name="emi_value_bsnl">BSNL</string>
    <string name="emi_value_wifi">WIFI</string>

    <string name="settle_preauth_sale">Pending-Preauth Sale !!!!</string>
    <string name="tms_reset_tid">TID Reset</string>


</resources>