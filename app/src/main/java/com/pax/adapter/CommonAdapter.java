package com.pax.adapter;

import android.content.Context;
import android.view.LayoutInflater;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public abstract class CommonAdapter<T> extends MultiItemTypeAdapter<T> {
    @NotNull
    private LayoutInflater mInflater;

    public CommonAdapter(@NotNull Context context, final int layoutId, @NotNull List<T> dataList) {
        super(context, dataList);
        mInflater = LayoutInflater.from(context);
        ItemViewDelegate itemViewDelegate = new ItemViewDelegate<T>() {
            @Override
            public int getItemViewLayoutId() {
                return layoutId;
            }

            @Override
            public boolean isForViewType(T item, int position) {
                return true;
            }

            @Override
            public void convert(ViewHolder holder, T item, int position) {
                CommonAdapter.this.convert(holder, item, position);

            }
        };
        addItemViewDelegate(itemViewDelegate);
    }

    @NotNull
    protected final LayoutInflater getMInflater() {
        return this.mInflater;
    }

    protected final void setMInflater(@NotNull LayoutInflater var1) {
        this.mInflater = var1;
    }

    protected abstract void convert(ViewHolder holder, T t, int position);
}
