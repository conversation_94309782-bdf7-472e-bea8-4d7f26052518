package com.pax.pay.service;

import android.app.Service;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.Nullable;
import com.pax.commonlib.LogUtils;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.menu.TransMenuActivity;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;
import java.util.Timer;
import java.util.TimerTask;

/**
 * The type Service check pick up.
 */
public class ServiceCheckPickUp extends Service {

    private static final int MESSAGE_ON_BASE = 0;
    private static final int MESSAGE_PICK_UP = 1;
    private static boolean isOnBase = isOnBase();
    private Timer mTimer;
    private CustomAlertDialog dialog;

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == MESSAGE_PICK_UP) {
                //Show dialog and alert sound
                Device.beepErr();
                dialog.show();
            } else if (msg.what == MESSAGE_ON_BASE) {
                //Hide dialog
                dialog.hide();
            }
            super.handleMessage(msg);
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    private void pollingOnBase() {
        //Polling is terminal on base or not
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!FinancialApplication.getSysParam()
                        .get(SysParam.BooleanParam.EDC_HANDSET_ALERT)) {
                    return;
                }
                if (!(ActivityStack.getInstance().top() instanceof TransMenuActivity)) {
                    return;
                }
                isOnBase = isOnBase();
                if (isOnBase) {
                    mHandler.sendEmptyMessage(MESSAGE_ON_BASE);
                } else {
                    mHandler.sendEmptyMessage(MESSAGE_PICK_UP);
                }
            }
        }, 3000, 1000);
    }

    private void initDialog() {
        dialog = new CustomAlertDialog(ActivityStack.getInstance().top(), CustomAlertDialog.WARN_TYPE);
        dialog.setCancelable(false);
        dialog.setImage(BitmapFactory.decodeResource(FinancialApplication.getApp().getResources(), R.drawable.ic16));
        dialog.setTimeout(-1);
        dialog.setContentText(getString(R.string.alert_put_handset_back));
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        initDialog();
        pollingOnBase();
        return super.onStartCommand(intent, flags, startId);
    }

    private static boolean isOnBase() {
        try {
            return FinancialApplication.getDal().getSys().isOnBase();
        } catch (Exception e) {
            LogUtils.e("ServiceCheckPickUp", e.toString());
        }
        return false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (dialog != null) {
            dialog.dismiss();
        }
        mTimer.cancel();
    }
}
