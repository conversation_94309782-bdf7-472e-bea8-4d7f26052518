/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         XuShuang                Create
 * ===========================================================================================
 */
package com.pax.pay.service;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.IBinder;
import android.os.SystemClock;

import androidx.annotation.Nullable;

import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.IDAL;
import com.pax.dal.IIcc;
import com.pax.dal.IMag;
import com.pax.dal.entity.EReaderType;
import com.pax.dal.entity.TrackData;
import com.pax.dal.exceptions.IccDevException;
import com.pax.dal.exceptions.MagDevException;
import com.pax.device.Device;
import com.pax.eventbus.NoticeSwipe;
import com.pax.eventbus.OtherDetectEvent;
import com.pax.pay.app.FinancialApplication;
import com.pax.sbipay.R;

public class OtherDetectCard extends Service {
    private static final String TAG = "OtherDetectCard";
    private static final int ICC_REVERSE = 97;
    private static final int ICC_RESET = 51;
    private IDAL dal = FinancialApplication.getDal();
    private IIcc icc = dal.getIcc();
    private IMag mag = dal.getMag();
    private boolean running = false;
    private Byte readType = 0;
    private Byte iccSlot = 0;
    private boolean isDetcting = false;
    private boolean isSwiped = false;
    private boolean magDisabled = false;
    private boolean iccDisabled = false;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.d(TAG, "OtherDetectCard Service Created");

        // 注册广播接收器，监听 magDisabled 的变化
        IntentFilter filter = new IntentFilter("com.example.CHANGE_MAG_STATE");
        registerReceiver(magStateReceiver, filter);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        running = true;
        if (intent == null || !intent.hasExtra("readType") || !intent.hasExtra("iccSlot")) {
            return super.onStartCommand(intent, flags, startId);
        }
        readType = intent.getByteExtra("readType", (byte) 0);
        iccSlot = intent.getByteExtra("iccSlot", (byte) 0);
        startDetect();
        return super.onStartCommand(intent, flags, startId);
    }

    private void startDetect() {
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                while (running && !iccDisabled) {
                    detectIcCard(readType);
                }
            }
        });

        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                while (running && !magDisabled) {
                    detectSwipeCard(readType);
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterReceiver(magStateReceiver); // 取消广播监听
        running = false;
        LogUtils.i(TAG, "stop service");
    }

    private void detectSwipeCard(Byte readerType) {
        LogUtils.i(TAG, "detectOtherCard readerType = $readerType");
        if ((readType & EReaderType.MAG.getEReaderType()) == EReaderType.MAG.getEReaderType()) {//reset mag
            try {
                mag.close();
                mag.open();
                mag.reset();
            } catch (MagDevException e) {
                showDisableMsg(String.format("%s%s", e.getErrModule(), e.getErrMsg()));
                LogUtils.e(e);
            }
        }
        while (running && !magDisabled) {
            try {
                if ((readerType & EReaderType.MAG.getEReaderType()) == EReaderType.MAG.getEReaderType()) {
                    if (mag.isSwiped()) {
                        isSwiped = true;
                        TrackData info = mag.read();
                        if ((info.getTrack1() == null || info.getTrack1().isEmpty()) && (info.getTrack2() == null || info.getTrack2().isEmpty()) && (info.getTrack3() == null || info.getTrack3().isEmpty())) {
                            LogUtils.i(TAG, "track data is null");
                            continue;
                        }
                        FinancialApplication.getApp().doEvent(new OtherDetectEvent(EReaderType.MAG.getEReaderType(), info.getTrack1(), info.getTrack2(), info.getTrack3()));
                        LogUtils.i(TAG, "mag.isSwiped = ${info.track1}=${info.track2}");
                        break;
                    }
                    SystemClock.sleep(5);
                } else {
                    SystemClock.sleep(1);
                }
            } catch (MagDevException e) {
                LogUtils.e(e);
                //close the mag function from pax store
                if (e.getErrCode() == NoticeSwipe.FUNC_SEARCH_CLOSED) {
                    magDisabled = true;
                    FinancialApplication.getApp().doEvent(new NoticeSwipe("MAG"));
                    break;
                }
            }
        }
    }

    private void detectIcCard(Byte readerType) {
        if ((readType & EReaderType.ICC.getEReaderType()) == EReaderType.ICC.getEReaderType()) {
            try {
                icc.close((byte) 0);
            } catch (IccDevException e) {
                showDisableMsg(String.format("%s%s", e.getErrModule(), e.getErrMsg()));
                LogUtils.e(e);
            }
        }
        while (running && !iccDisabled) {
            try {
                if ((readerType & EReaderType.ICC.getEReaderType()) == EReaderType.ICC.getEReaderType()) {
                    if (icc.detect(iccSlot)) {
                        isDetcting = true;
                        byte[] res = icc.init(iccSlot);
                        if (res != null) {
                            FinancialApplication.getApp().doEvent(new OtherDetectEvent(EReaderType.ICC.getEReaderType()));
                            LogUtils.i(TAG, "icc.detect = $readerType");
                            running = false;
                            break;
                        } else {
                            showNotice(R.string.icc_error_swipe_card);
                        }
                    } else {
                        if (isDetcting) {
                            isDetcting = false;
                            SystemClock.sleep(1000);
                            if (!isSwiped) {
                                showNotice(R.string.remove_read_card_error);
                            } else {
                                isSwiped = false;
                            }
                        }
                    }
                    SystemClock.sleep(5);
                }
            } catch (IccDevException e) {
                showNotice(R.string.icc_error_swipe_card);
                LogUtils.e(e);
                if (e.getErrCode() == ICC_REVERSE) {
                    FinancialApplication.getApp().doEvent(new OtherDetectEvent(EReaderType.ICC.getEReaderType()));
                    running = false;
                    break;
                }
                if (e.getErrCode()==ICC_RESET){
                    FinancialApplication.getApp().doEvent(new OtherDetectEvent(EReaderType.ICC.getEReaderType()));
                    running = false;
                    break;
                }
                //close icc function from pax store
                if (e.getErrCode() == NoticeSwipe.FUNC_SEARCH_CLOSED) {
                    iccDisabled = true;
                    FinancialApplication.getApp().doEvent(new NoticeSwipe("ICC"));
                    break;
                }
            }

        }
    }

    private void showNotice(int resId) {
        if ("A60".equals(Device.getDeviceModel())) {
            FinancialApplication.getApp().doEvent(new NoticeSwipe(FinancialApplication.getApp().getResources().getString(resId)));
        }
    }

    private void showDisableMsg(String errMsg) {
        FinancialApplication.getApp().doEvent(new NoticeSwipe(errMsg));
    }

    private final BroadcastReceiver magStateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.example.CHANGE_MAG_STATE".equals(intent.getAction())) {
                magDisabled = intent.getBooleanExtra("magDisabled", false);
                LogUtils.d(TAG, "magDisabled changed to: " + magDisabled);
            }
        }
    };

}
