package com.pax.pay.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Intent;
import android.os.IBinder;
import android.speech.tts.TextToSpeech;

import java.lang.ref.WeakReference;
import java.util.Deque;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedDeque;

import android.speech.tts.UtteranceProgressListener;
import android.speech.tts.Voice;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.local.GreendaoHelper;
import com.pax.eventbus.MQTTMessageEvent;
import com.pax.eventbus.OnTransSuccessEvent;
import com.pax.eventbus.TerminalIdleTriggerEvent;
import com.pax.pay.MainActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.action.ActionPreview;
import com.pax.pay.trans.receipt.TTSPrintListenerImpl;
import com.pax.pay.trans.receipt.ReceiptPrintTrans;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class TTSService extends Service implements TextToSpeech.OnInitListener {

    private TextToSpeech textToSpeech;
    private boolean isInitialized = false;
    private static final String TAG = "TTSService";
    private static final String CHANNEL_ID = "TTSServiceChannel";
    private static final String TRANS_SUCCESS_PREFIX = "trans_success_";
    private final Deque<SpeechItem> transQueue = new ConcurrentLinkedDeque<>();
    private final Deque<SQRData> waitPrintgQueue = new ConcurrentLinkedDeque<>();
    private boolean isDoingTrans = false;
    private SQRData sqrData;
    private boolean isHindiInstallDialogShowing = false;

    private static class SpeechItem {
        String text;
        Locale locale;
        SQRData sqrData;

        SpeechItem(String text, Locale locale, SQRData sqrData) {
            this.text = text;
            this.locale = locale;
            this.sqrData = sqrData;
        }
    }


    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
        textToSpeech = new TextToSpeech(getApplicationContext(), this);
        // Use the static class with WeakReference to this service
        textToSpeech.setOnUtteranceProgressListener(new MyUtteranceProgressListener(this));
        createNotificationChannel();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTransSuccess(OnTransSuccessEvent event) {
        if (textToSpeech != null && Constants.TRANS_PREVIOUS_AUDIO.equals(event.message)) {
            // 添加一个特殊的标识符
            final String specialUtteranceId = TRANS_SUCCESS_PREFIX + UUID.randomUUID().toString();
            // 播报语音内容
            FinancialApplication.getApp().runInBackground(() ->
                    textToSpeech.speak(getString(R.string.transaction_success_voice_text), TextToSpeech.QUEUE_FLUSH, null, specialUtteranceId));
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onTerminalIdle(TerminalIdleTriggerEvent event) {
        if (event != null && event.isIdle) {
            synchronized (this) {
                if (!waitPrintgQueue.isEmpty()) {
                    doPrint(waitPrintgQueue.pollFirst());
                } else if (!isDoingTrans) {
                    // 只有当前没有正在进行的交易时，才处理下一个语音项
                    processNextSpeech();
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onMQTTSuccess(MQTTMessageEvent event) {
        if (event != null && event.sqrData != null) {
            boolean isHindi = event.sqrData.getLanguage().equalsIgnoreCase("hi");
            String amount = event.sqrData.getAmount();
            String text = MQTTUtils.ConvertToSpeak(amount, isHindi);
            Locale locale = isHindi ? new Locale("hi", "IN") : Locale.US;
            SpeechItem speechItem = new SpeechItem(text, locale, event.sqrData);
            //语音播报需要在主线程执行
            FinancialApplication.getApp().runOnUiThread(() -> {
                synchronized (TTSService.this) {
                    transQueue.offerLast(speechItem);
                    LogUtils.d(TAG, "SpeechItem: " + text + " add into queue");
                    processNextSpeech();
                }
            });
        }
    }

    private synchronized void processNextSpeech() {
        // 检查TTS是否初始化
        if (!isInitialized) {
            Log.e(TAG, "TextToSpeech is not initialized");
            return;
        }

        // 如果当前正在处理交易，不进行新的处理
        if (isDoingTrans) {
            Log.i(TAG, "Transaction is currently being processed, will process next item later");
            return;
        }

        // 检查设备是否空闲
        if (BaseTrans.isTransRunning() || FinancialApplication.isIsEnteringSaleAmount()
                || !(ActivityStack.getInstance().top() instanceof MainActivity)) {
            Log.i(TAG, "Normal Trans is running or not in idle, will try again later");
            return;
        }

        // 从队列中获取下一个语音项
        SpeechItem nextItem = null;
        if (!transQueue.isEmpty()) {
            nextItem = transQueue.pollFirst();
            LogUtils.d(TAG, "Processing next speech item from queue, remaining items: " + transQueue.size());
        }

        if (nextItem != null) {
            // 检查印地语语音包并处理安装需求
            if (checkAndHandleHindiVoicePackage(nextItem)) {
                return;
            }

            // 设置标志，表示正在处理交易
            isDoingTrans = true;
            speak(nextItem);
        }
    }

    /**
     * 检查是否是印地语且是否需要安装印地语语音包
     *
     * @param speechItem 语音项
     * @return 如果需要安装印地语语音包返回 true，否则返回 false
     */
    private boolean checkAndHandleHindiVoicePackage(SpeechItem speechItem) {
        // 检查是否是印地语
        boolean isHindi = speechItem.locale != null && "hi".equalsIgnoreCase(speechItem.locale.getLanguage());
        if (!isHindi) {
            return false; // 不是印地语，不需要处理
        }

        // 检查是否已安装印地语语音包
        Locale hindiLocale = new Locale("hi", "IN");
        if (isVoicePackageInstalled(hindiLocale)) {
            return false; // 已安装印地语语音包，不需要处理
        }

        synchronized (this) {
            // 如果已经显示了安装提示对话框，则不再显示新的对话框
            if (isHindiInstallDialogShowing) {
                LogUtils.d(TAG, "Hindi install dialog is already showing, queueing item for later");
                // 将项目放回队列前端，以便在安装语音包后重试
                transQueue.offerFirst(speechItem);
                return true;
            }

            isHindiInstallDialogShowing = true;
            LogUtils.d(TAG, "Showing Hindi voice data installation dialog");
        }

        FinancialApplication.getApp().runOnUiThread(() -> DialogUtils.showConfirmDialog(
                ActivityStack.getInstance().top(),
                Utils.getString(R.string.please_install_hindi_voice_data_first),
                alertDialog -> {
                    // 取消按钮点击时，重置标志
                    alertDialog.dismiss();
                    synchronized (TTSService.this) {
                        isHindiInstallDialogShowing = false;
                    }
                    LogUtils.d(TAG, "Hindi install dialog canceled, resetting flag");
                },
                alertDialog -> {
                    alertDialog.dismiss();
                    // 重置标志
                    synchronized (TTSService.this) {
                        isHindiInstallDialogShowing = false;
                    }
                    LogUtils.d(TAG, "Opening TTS settings to install Hindi voice package");
                    Intent intent = new Intent();
                    intent.setAction("com.android.settings.TTS_SETTINGS");
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    FinancialApplication.getApp().startActivity(intent);
                }
        ));

        // 将项目放回队列前端，以便在安装语音包后重试
        transQueue.offerFirst(speechItem);
        return true;
    }

    private boolean isVoicePackageInstalled(Locale locale) {
        if (textToSpeech == null) {
            LogUtils.d(TAG, "TextToSpeech实例为空，无法检查语音包");
            return false;
        }

        Set<Voice> voices = textToSpeech.getVoices();
        if (voices != null && !voices.isEmpty()) {
            for (Voice voice : voices) {
                Locale voiceLocale = voice.getLocale();
                // 检查语言代码是否匹配
                if (voiceLocale.getLanguage().equals(locale.getLanguage())) {
                    // 检查该语音包是否已安装（不包含"notInstalled"特性）
                    Set<String> features = voice.getFeatures();
                    if (features != null && !features.contains("notInstalled")) {
                        return true;
                    } else {
                        LogUtils.i(TAG, locale.getDisplayName() + " Voice Data Not Installed: " + voice.getName());
                    }
                }
            }
        }
        return false;
    }

    private void speak(SpeechItem speechItem) {
        try {
            int langResult = textToSpeech.setLanguage(speechItem.locale);
            if (langResult == TextToSpeech.LANG_MISSING_DATA ||
                    langResult == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.e(TAG, "Language not supported: " + speechItem.locale);
                ToastUtils.showMessage(R.string.selected_language_not_supported);
                // 重置标志并尝试处理下一个项目
                isDoingTrans = false;
                processNextSpeech();
                return;
            }

            if (!speechItem.text.isEmpty()) {
                String utteranceId = UUID.randomUUID().toString();
                Log.i(TAG, "Starting to speak transaction rrn: " + speechItem.sqrData.getRrn());
                textToSpeech.speak(speechItem.text, TextToSpeech.QUEUE_FLUSH, null, utteranceId);
                sqrData = speechItem.sqrData;
            } else {
                Log.i(TAG, "TextToSpeech text is empty");
                // 重置标志并尝试处理下一个项目
                isDoingTrans = false;
                processNextSpeech();
            }

        } catch (Exception e) {
            Log.e(TAG, "TTS speak error: ", e);
            isDoingTrans = false;
            sqrData = null;
            processNextSpeech();
        }
    }


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 启动前台服务
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID).
                setContentTitle("TTS Service").setContentText("Service is running in the background").
                setSmallIcon(R.drawable.logo)
                .build();
        startForeground(1, notification);
        if (intent == null) {
            // 如果 Intent 为空，意味着服务是被系统自动重启的
            Log.d(TAG, "Service restarted after being killed by the system");
        } else {
            // 正常启动服务时处理传递的 Intent
            Log.d(TAG, "Service started with Intent: " + intent);
        }
        return START_STICKY;
    }

    // 创建通知渠道（针对 Android 8.0 及以上）
    private void createNotificationChannel() {
        NotificationChannel serviceChannel = new NotificationChannel(CHANNEL_ID, "TTS Service Channel", NotificationManager.IMPORTANCE_HIGH);
        serviceChannel.setSound(null, null); // 设置声音为 null，关闭声音
        serviceChannel.enableVibration(false); //关闭振动
        NotificationManager manager = getSystemService(NotificationManager.class);
        if (manager != null) {
            manager.createNotificationChannel(serviceChannel);
        }
    }

    @Override
    public void onInit(int status) {
        FinancialApplication.getApp().runInBackground(() -> {
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech.setPitch(0.7f);
                textToSpeech.setSpeechRate(1.2f);
                int result = textToSpeech.setLanguage(Locale.US);
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    LogUtils.d(TAG, "US Locale Language not supported");
                } else {
                    LogUtils.d(TAG, "TTS initialized successfully with US locale");
                    isInitialized = true;
                }
            } else {
                LogUtils.d(TAG, "TextToSpeech initialization failed");
            }
        });
    }

    @Override
    public void onDestroy() {
        stopForeground(true);
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        // 清空队列
        synchronized (this) {
            transQueue.clear();
            waitPrintgQueue.clear();
            // 重置对话框显示标志
            isHindiInstallDialogShowing = false;
        }
        shutdown();
        Log.d(TAG, "Service destroyed");
        if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
            Intent broadcastIntent = new Intent("RE_START_MQTT");
            broadcastIntent.setComponent(new ComponentName("com.pax.receiver",
                    "com.pax.receiver.AutoStartMQTTReceiver"));
            sendBroadcast(broadcastIntent);
            Log.d(TAG, "Broadcast sent to restart TTSService");
        }
    }

    private void shutdown() {
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
            textToSpeech.setOnUtteranceProgressListener(null);
            textToSpeech = null;
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null; // No binding required
    }

    // Static class with WeakReference to avoid memory leaks
    private static class MyUtteranceProgressListener extends UtteranceProgressListener {
        private final WeakReference<TTSService> serviceRef;

        public MyUtteranceProgressListener(TTSService service) {
            this.serviceRef = new WeakReference<>(service);
        }

        @Override
        public void onStart(String utteranceId) {
            Log.d(TAG, "TTS started speaking: " + utteranceId);
            TTSService service = serviceRef.get();
            if (service != null && !utteranceId.startsWith(TRANS_SUCCESS_PREFIX)) {
                service.isDoingTrans = true;
            }
        }

        @Override
        public void onDone(String utteranceId) {
            Log.d(TAG, "TTS completed speaking");
            TTSService service = serviceRef.get();
            if (service != null && !utteranceId.startsWith(TRANS_SUCCESS_PREFIX) && service.sqrData != null) {
                service.sqrData.setVoiceStatus(1);
                GreendaoHelper.getSQRDataHelper().updateSQRData(service.sqrData);
                MQTTUtils.pushMQTTMessage("1", service.sqrData.getAmount(), service.sqrData.getLanguage());
                service.doPrint(service.sqrData);
            }
        }

        @Override
        public void onError(String utteranceId) {
            LogUtils.d(TAG, "Error occurred while speaking: " + utteranceId);
            TTSService service = serviceRef.get();
            if (service != null && !utteranceId.startsWith(TRANS_SUCCESS_PREFIX)) {
                if (service.sqrData != null) {
                    MQTTUtils.pushMQTTMessage("0", service.sqrData.getAmount(), service.sqrData.getLanguage());
                }
                // 重置标志并尝试处理下一个项目
                service.isDoingTrans = false;
                service.processNextSpeech();
            }
        }
    }

    private void doPrint(final SQRData transData) {
        if (BaseTrans.isTransRunning() || !(ActivityStack.getInstance().top() instanceof MainActivity)) {
            // 如果当前无法打印，将交易添加到打印队列
            waitPrintgQueue.offerLast(transData);
            Log.d(TAG, "doPrint: is TransRunning or not in idle, added to print queue, size: " + waitPrintgQueue.size());
            return;
        }
        if (Utils.isA50()) {
            processA50Preview(transData);
        } else {
            // 使用专用的TTSPrintListenerImpl替代通用的PrintListenerImpl
            TTSPrintListenerImpl printListener = new TTSPrintListenerImpl(ActivityStack.getInstance().top());
            int receiptNum = SysParam.getInstance().getInt(R.string.EDC_RECEIPT_NUM);
            if (receiptNum < 1 || receiptNum > 3) {
                receiptNum = 2; // 默认值
            }
            LogUtils.d(TAG, "获取小票数量: " + receiptNum);

            // 设置小票总数，确保只有在所有小票都打印完成后才触发回调
            printListener.setReceiptCount(receiptNum);

            // 设置打印完成回调
            printListener.setPrintCompleteListener(() -> {
                sqrData.setPrintStatus(1);
                GreendaoHelper.getSQRDataHelper().updateSQRData(sqrData);
                isDoingTrans = false;
                processNextPendingItem();
            });

            if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_PRINT)) {
                new ReceiptPrintTrans().print(transData, false, printListener, null);
            } else {
                isDoingTrans = false;
            }
        }
    }

    private void processA50Preview(final SQRData transData) {
        ActionPreview actionPreview = new ActionPreview(action -> ((ActionPreview) action).setParam(ActivityStack.getInstance().top(), transData));
        actionPreview.setEndListener((action, result) -> {
            sqrData.setPrintStatus(1);
            GreendaoHelper.getSQRDataHelper().updateSQRData(sqrData);
            // 重置标志，表示当前交易已完全处理完毕
            isDoingTrans = false;
            ActivityStack.getInstance().pop();
            processNextPendingItem();
        });
        actionPreview.execute();
    }

    private void processNextPendingItem() {
        if (!waitPrintgQueue.isEmpty()) {
            doPrint(waitPrintgQueue.pollFirst());
        } else {
            processNextSpeech();
        }
    }
}
