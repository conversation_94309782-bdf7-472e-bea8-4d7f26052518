/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date	                 Author	                Action
 * 2016-11-25 	         Steven.W           	Create
 * 2021-08-02            jiaguang              Modify
 * ============================================================================
 */
package com.pax.pay.service;

import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.BaseResponse;
import com.pax.pay.response.ResponseBuilder;
import com.pax.pay.response.ResponseBuilderFactory;
import com.pax.pay.utils.Utils;

/**
 * Util for getting response according to transaction type and actionResult
 */
public class ParseResp {

    private ParseResp() {
        //do nothing
    }

    /**
     * Gets response.
     *
     * @param transType the trans type
     * @param result the result
     * @return the response
     */
    public static BaseResponse getResponse(int transType, final ActionResult result) {
        ResponseBuilder responseBuilder = ResponseBuilderFactory.get(transType);
        if (null != responseBuilder) {
            //给response传入交易类型，以便在解析时根据此类型判断具体实例化对象，从而解析结果
            String transTypeString = Utils.decimalToHexString(transType);
            return responseBuilder.get(result,transTypeString);
        }
        return null;
    }
}
