package com.pax.pay.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;
import com.pax.commonlib.LogUtils;
import com.pax.device.Device;
import com.pax.edc.BuildConfig;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.menu.TransMenuActivity;
import com.pax.settings.SysParam;

public class CheckPickUpService extends Service {
    private static final String TAG = "CheckPickUpService";
    public static final String ACTION = BuildConfig.APPLICATION_ID + ".checkPickUp.action";

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        polling();
        return super.onStartCommand(intent, flags, startId);
    }

    private void polling() {
        LogUtils.e(TAG, "checking if terminal is on the base");
        if (!FinancialApplication.getSysParam()
                .get(SysParam.BooleanParam.EDC_HANDSET_ALERT)) {
            return;
        }

        if (!(ActivityStack.getInstance().top() instanceof TransMenuActivity)) {
            return;
        }

        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                boolean isOnBase = isOnBase();
                if (!isOnBase) {
                    com.pax.commonlib.LogUtils.e(TAG, "terminal isn't on the base");
                    Device.beepErr();
                }
            }
        });
    }

    private boolean isOnBase() {
        try {
            return FinancialApplication.getDal().getSys().isOnBase();
        } catch (Exception e) {
            com.pax.commonlib.LogUtils.e("ServiceCheckPickUp", e.toString());
        }
        return false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
