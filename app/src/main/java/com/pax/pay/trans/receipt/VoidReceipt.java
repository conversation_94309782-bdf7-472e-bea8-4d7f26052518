package com.pax.pay.trans.receipt;

import android.view.Gravity;
import com.pax.edc.R;
import com.pax.eemv.enums.ETransResult;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

public class VoidReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isReprint isReprint
     */
    public VoidReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isReprint isReprint
     * @param isPrintPreview isPrintPreview
     */
    public VoidReceipt(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint,
            boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addReferenceNO();
        addAppCode();
        addNewline(1);
        addEcrRef();
        addNewline(1);
        addEMVInfo();
        addSystemTrace();
        addNewline(1);
        addInstalmentNo();
        addAmount();
        addDoubleLine();
        addNewline(1);
        addEnd();
    }

    @Override
    public void addInvoiceNo() {
        temp = Component.getPaddedNumber(transData.getOriginvoiceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INVOICE NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END));
    }

    @Override
    public void addCUPBatchTransNo() {
        temp = Component.getPaddedNumber(transData.getTraceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_batch_num_colon))
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_trans_no))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6))
                        .setFontSize(FONT_BIG))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_BIG)
                        .setGravity(Gravity.END));

        temp = Component.getPaddedNumber(transData.getOriginvoiceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INVOICE NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_BIG)
                        .setGravity(Gravity.END));
    }

    @Override
    public void addTransType() {
        String transName;
        // check which trans type need to be voided
        // print out different void type on the receipt
        switch (transData.getOrigTransType()) {
            case SALE:
            case SALES_WITH_CASH_DOLLAR:
                transName = "(VOID SALE) (取消 銷售)";
                break;
            case REFUND:
                transName = "(VOID REFUND) (取消 退貨)";
                break;
            case INSTALMENT:
                transName = "(VOID INSTAL) (取消 分期)";
                break;
            case OFFLINE_TRANS_SEND:
                transName = "(VOID OFFLINE) (取消 離線)";
                break;
            case DCC_OPT_OUT:
                transName = "(VOID DCC-Opt-Out) (取消 外滙調整)";
                break;
            case PREAUTH:
                transName = "預授權(VOID)";
                break;
            case PREAUTHCM:
                transName = "預授權完成(VOID)";
                break;
            case AUTHORIZATION:
                transName = "(VOID AUTH) (取消 授權)";
                break;
            default:
                transName = "";
                break;
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(transName)
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

    @Override
    public void addEMVInfo() {
        if (!(transData.getEnterMode() == TransData.EnterMode.INSERT || transData.getEnterMode() == TransData.EnterMode.CLSS)) {
            return;
        }
        // for the other need addition data on the receipt
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("APP:")
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getEmvAppLabel())
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("AID:")
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getAid())
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        if (!transData.getAcquirer().getName().equals("HASE_CUP")) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TC CODE:")
                            .setWeight(2.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getTc())
                            .setGravity(Gravity.END)
                            .setWeight(5.0f));
        } else {
            if (transData.getEmvResult() == ETransResult.OFFLINE_APPROVED) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TC CODE:")
                                .setWeight(2.0f))
                        .addUnit(page.createUnit()
                                .setText(transData.getTc())
                                .setGravity(Gravity.END)
                                .setWeight(5.0f));
            }

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TVR:"))
                    .addUnit(page.createUnit()
                            .setText(transData.getTvr())
                            .setGravity(Gravity.END));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TSI:  " + transData.getTsi()))
                    .addUnit(page.createUnit()
                            .setText("ATC:  " + transData.getAtc())
                            .setGravity(Gravity.END));
        }
    }
}
