package com.pax.pay.trans.pack;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;

import androidx.annotation.NonNull;

/**
 * ===========================================================================================
 * = COPYRIGHT
 * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or nondisclosure
 * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 * disclosed except in accordance with the terms in that agreement.
 * Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 * // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                         Author	                Action
 * 2020/8/18 13:38  	         Kael           	Create/Add/Modify/Delete
 * ===========================================================================================
 */
public class PackCallHelp extends PackIso8583 {

    public PackCallHelp(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{3, 11, 41, 42, 48, 60, 63};
    }

    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("60", transData.getHelpCode());
    }
}
