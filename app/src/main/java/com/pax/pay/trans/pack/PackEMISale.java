/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;

public class PackEMISale extends PackIso8583 {

    public PackEMISale(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{2, 3, 4, 11, 12, 13, 14, 22, 24, 25, 35, 41, 42, 45, 49, 52, 53, 54, 55, 56, 61, 62, 63};
    }

    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        String accountType = transData.getAccountType();
        if (!TextUtils.isEmpty(accountType)) {
            StringBuilder str = new StringBuilder(transData.getTransType().getProcCode());
            setBitData("3", str.replace(2, 4, accountType).toString());
            return;
        }
        super.setBitData3(transData);
    }

    @Override
    protected void setBitData45(@NonNull TransData transData) throws Iso8583Exception {
        if (TextUtils.isEmpty(transData.getTrack2())) {
            setBitData("45", transData.getTrack1());
        }
    }

    @Override
    protected void setBitData49(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("49", Component.getPaddedNumber(Integer.parseInt(transData.getInternationalCurrency().getCode()), 3));
        }
    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        StringBuilder temp = new StringBuilder();
        if (!TextUtils.isEmpty(transData.getTenure())) {
            temp.append("EM").append(transData.getTenure());
        }

        if (!TextUtils.isEmpty(transData.getEmiRefNo())) {
            temp.append("P3020").append(transData.getEmiRefNo());
        }
        if (!temp.toString().isEmpty()) {
            setBitData("63", temp.toString());
        }
    }
}
