/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.transmit;

/**
 * The type Tcp response.
 */
public class TcpResponse {
    private int retCode;
    private byte[] data;

    /**
     * Instantiates a new Tcp response.
     *
     * @param retCode the ret code
     * @param data the data
     */
    public TcpResponse(int retCode, byte[] data) {
        this.retCode = retCode;
        this.data = data;
    }

    /**
     * Gets ret code.
     *
     * @return the ret code
     */
    public int getRetCode() {
        return retCode;
    }

    /**
     * Sets ret code.
     *
     * @param retCode the ret code
     */
    public void setRetCode(int retCode) {
        this.retCode = retCode;
    }

    /**
     * Get data byte [ ].
     *
     * @return the byte [ ]
     */
    public byte[] getData() {
        return data;
    }

    /**
     * Sets data.
     *
     * @param data the data
     */
    public void setData(byte[] data) {
        this.data = data;
    }
}
