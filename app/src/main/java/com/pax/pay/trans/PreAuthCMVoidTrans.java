package com.pax.pay.trans;

import android.content.Context;

import com.pax.edc.opensdk.BaseRequest;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;

/**
 * Created by xub on 2018/2/7.
 */

public class PreAuthCMVoidTrans extends SaleVoidTrans {

    /**
     * 构造器
     * @param context 上下文
     * @param transListener listener
     */
    public PreAuthCMVoidTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.PREAUTHCMVOID, transListener);
    }

    /**
     * 构造器
     * @param context 上下文
     * @param transListener listener
     */
    public PreAuthCMVoidTrans(Context context, TransEndListener transListener, BaseRequest baseRequest) {
        super(context, ETransType.PREAUTHCMVOID, transListener);
        setBaseRequest(baseRequest);
    }

    /**
     * 构造器
     * @param context 上下文
     * @param transType ETransType
     * @param origTransData TransData
     * @param transListener TransEndListener
     */
    public PreAuthCMVoidTrans(Context context,ETransType transType, TransData origTransData, TransEndListener transListener){
        super(context, ETransType.PREAUTHCMVOID, origTransData, transListener);
    }

}
