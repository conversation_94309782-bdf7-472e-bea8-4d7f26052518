/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;

import androidx.annotation.NonNull;

public class PackSettle extends PackIso8583 {

    public PackSettle(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{3, 11, 41, 42, 60, 63};
    }

    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        String f60 = Component.getPaddedNumber(transData.getBatchNo(), 6);
        setBitData("60", f60);
    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getField63() == null) {
            setBitData("63", "");
        } else {
            super.setBitData63(transData);
        }
    }
}
