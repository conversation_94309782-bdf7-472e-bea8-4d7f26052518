package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.style.BulletSpan;

import com.pax.abl.core.AAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.DispBalanceDetailActivity;
import com.pax.pay.trans.model.TransData;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/27.
 */

public class ActionDispBalanceDetail extends AAction {
    private Context context;
    private String title;
    private TransData transData;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionDispBalanceDetail(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String title, TransData transData) {
        this.context = context;
        this.title = title;
        this.transData = transData;
    }

    /**
     * process
     */
    @Override
    protected void process() {
        Intent intent = new Intent(context, DispBalanceDetailActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
        bundle.putByteArray(EUIParamKeys.CONTENT_BALANCEDATA.toString(), transData.getBalanceData());
        intent.putExtras(bundle);
        context.startActivity(intent);
    }
}
