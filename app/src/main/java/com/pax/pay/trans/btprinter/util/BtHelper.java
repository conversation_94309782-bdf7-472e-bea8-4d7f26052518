/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the publicction of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         ligq                    Create
 * ===========================================================================================
 */
package com.pax.pay.trans.btprinter.util;

import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;

import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.HashSet;
import java.util.Set;

public class BtHelper {
    public static final int REQUEST_ENABLE_BT = 1;
    private BluetoothAdapter mBlueToothAdapter = BluetoothAdapter.getDefaultAdapter();

    private BtHelper() {
    }

    public static BtHelper getInstance() {
        return LazyHolder.INSTANCE;
    }

    public boolean btEnable() {
        return mBlueToothAdapter != null;
    }

    public boolean btOpened() {
        return btEnable() && mBlueToothAdapter.isEnabled();
    }

    public void startBtSetting(Activity act) {
        Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
        act.startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
    }

    public Set<BluetoothDevice> getBondedDevices() {
        try {
            return mBlueToothAdapter.getBondedDevices();
        } catch (Exception e) {
            return new HashSet<>();
        }
    }

    public void saveMac(String mac) {
        SysParam.getInstance().set(Utils.getString(R.string.BT_MAC_ADDRESS), mac);
    }

    public String getMac() {
        return SysParam.getInstance().getString(Utils.getString(R.string.BT_MAC_ADDRESS));
    }

    private static class LazyHolder {
        public static final BtHelper INSTANCE = new BtHelper();
    }

}
