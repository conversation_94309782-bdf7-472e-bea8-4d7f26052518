/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.SignatureActivity;

public class ActionSignature extends AAction {
    private String amount;
    private Context context;
    private boolean isSymbolNegative = false;//only for refund
    private String currencyCode;

    public ActionSignature(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String amount) {
        this.context = context;
        this.amount = amount;
    }

    /**
     * Sets param.
     *
     * @param context      the context
     * @param amount       the amount
     * @param currencyCode the currency code
     */
    public void setParam(Context context, String amount, String currencyCode) {
        this.context = context;
        this.amount = amount;
        this.currencyCode = currencyCode;
    }

    /**
     * Sets param.
     *
     * @param context          the context
     * @param amount           the amount
     * @param currencyCode     the currency code
     * @param isSymbolNegative the is symbol negative
     */
    public void setParam(Context context, String amount, String currencyCode, boolean isSymbolNegative) {
        this.context = context;
        this.amount = amount;
        this.currencyCode = currencyCode;
        this.isSymbolNegative = isSymbolNegative;
    }

    @Override
    protected void process() {
        Intent intent = new Intent(context, SignatureActivity.class);
        intent.putExtra(EUIParamKeys.TRANS_AMOUNT.toString(), amount);
        intent.putExtra(EUIParamKeys.CURRENCY_CODE.toString(), currencyCode);
        intent.putExtra(EUIParamKeys.IS_SYMBOL_NEGATIVE.toString(), isSymbolNegative);
        context.startActivity(intent);
    }
}
