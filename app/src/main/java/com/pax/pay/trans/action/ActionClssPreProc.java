/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         laiyi                   Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.ClssParam;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.DeviceImplNeptune;
import com.pax.eemv.IClss;
import com.pax.eemv.entity.AidParam;
import com.pax.eemv.exception.EmvException;
import com.pax.eemv.utils.Tools;
import com.pax.jemv.device.DeviceManager;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.component.Component;
import com.pax.sdk.Sdk;

import java.util.List;

public class ActionClssPreProc extends AAction {

    private TransData transData;
    private IClss clss;

    public ActionClssPreProc(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(IClss clss, TransData transData) {
        this.clss = clss;
        this.transData = transData;
    }

    @Override
    protected void process() {
        if (Sdk.isPaxDevice())
            FinancialApplication.getApp().runInBackground(new ProcessRunnable());
        else
            setResult(new ActionResult(TransResult.SUCC, null));
    }

    private class ProcessRunnable implements Runnable {

        ProcessRunnable() {
            DeviceManager.getInstance().setIDevice(DeviceImplNeptune.getInstance());
        }

        @Override
        public void run() {
            try {
                clss.init();
                clss.setConfig(ClssTransProcess.genClssConfig());
                clss.setAidParamList(updateAidParamList());
                clss.setCapkList(FinancialApplication.getCapkList());
                clss.preTransaction(Component.toClssInputParam(transData));
            } catch (EmvException e) {
                LogUtils.e(TAG, "", e);
                setResult(new ActionResult(TransResult.ERR_CLSS_PRE_PROC, null));
            }
            setResult(new ActionResult(TransResult.SUCC, null));
        }


        private List<AidParam> updateAidParamList() {
            List<AidParam> aidParamList = FinancialApplication.getAidParamList();
            List<ClssParam> clssParamList = FinancialApplication.getClssParamDbHelper().loadAll();
            List<Issuer> issuerList = GreendaoHelper.getIssuerHelper().lookupIssuersForAcquirer(FinancialApplication.getAcqManager().getCurAcq());
            if (clssParamList.isEmpty()) {
                return aidParamList;
            }
            for (int i = 0; i < aidParamList.size(); i++) {
                ClssParam clssParam = null;
                Issuer issuer = null;

                String aid = Tools.bcd2Str(aidParamList.get(i).getAid());
                String aidName = "";
                if (aid.toUpperCase().contains("A000000003")) {
                    aidName = "VISA";
                } else if (aid.toUpperCase().contains("A0000000041010")) {
                    aidName = "MASTER";
                } else if (aid.toUpperCase().contains("A0000000043060")) {
                    aidName = "MAESTRO";
                } else if (aid.toUpperCase().contains("A000000025")) {
                    aidName = "AMEX";
                } else if (aid.toUpperCase().contains("A000000333")
                        || aid.toUpperCase().contains("A000000152")
                        || aid.toUpperCase().contains("A000000065")
                        || aid.toUpperCase().contains("A000000324")
                        || aid.toUpperCase().contains("A000000524")) {
                    aidName = "RUPAY";//当为JCB，DPAS,UPI,RUPAY卡时，非接floorlimit都按照RUPAY来设置
                }

                for (int j = 0; j < clssParamList.size(); j++) {
                    if (aidName.toUpperCase().contains(clssParamList.get(j).getName())) {
                        clssParam = clssParamList.get(j);
                        break;
                    }
                }
                if (clssParam == null) {
                    continue;
                } else {
                    //update limit
                    aidParamList.get(i).setRdClssTxnLmt(clssParam.getClssMaxLimit());
                    aidParamList.get(i).setRdClssFLmt(clssParam.getOfflineLimit());
                    aidParamList.get(i).setRdCVMLmt(clssParam.getCvmLimit());

                }


                for (int j = 0; j < issuerList.size(); j++) {
                    if (aidName.toUpperCase().contains(issuerList.get(j).getName())) {
                        issuer = issuerList.get(j);
                        break;
                    }
                }

                if (issuer == null) {
                    continue;
                } else {
                    //update contact floorlimit
                    aidParamList.get(i).setFloorLimit(issuer.getFloorLimit());
                }
            }
            return aidParamList;
        }
    }
}
