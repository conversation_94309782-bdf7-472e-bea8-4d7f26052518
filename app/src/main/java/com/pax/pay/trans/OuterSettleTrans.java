/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210826  	         jiaguang                create
 *
 * ============================================================================
 */
package com.pax.pay.trans;

import android.content.Context;
import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.edc.R;
import com.pax.edc.opensdk.SettleMsg;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.service.RequestUtil;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionSelectAcquirer;
import com.pax.pay.trans.action.ActionSettle;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransTotal;
import com.pax.settings.SysParam;
import java.util.ArrayList;
import org.javatuples.Pair;

/**
 * 外部调用（比如三方api）发起settle
 */
public class OuterSettleTrans extends SettleTrans {

    /**
     * Constructor
     * @param context Context
     * @param listener TransEndListener
     */
    public OuterSettleTrans(Context context, TransEndListener listener, SettleMsg.Request request) {
        super(context, listener);
        ArrayList<String> acquires = new ArrayList<>();
        acquires.add(RequestUtil.getAcquirerInfo(request.getAcquirerIndex()));
        selectAcqs = acquires;
        thirdPartyBaseRequest = request;
    }


    @Override
    protected void bindStateOnAction() {

        transData.setTransTypeDetail(thirdPartyBaseRequest.getTransTypeDetail());
        transData.setEcrMsgType((byte)thirdPartyBaseRequest.getTransType());
        transData.setEcrOrigTransType((byte)thirdPartyBaseRequest.getTransType());

        ActionInputPassword inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(getCurrentContext(), 6,
                        getString(R.string.prompt_settle_pwd), null);
            }
        });
        bind(State.INPUT_PWD.toString(), inputPasswordAction, true);

        ActionSelectAcquirer actionSelectAcquirer = new ActionSelectAcquirer(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSelectAcquirer) action).setParam(getCurrentContext(),
                        getString(R.string.settle_select_acquirer));
            }
        });
        bind(State.SELECT_ACQ.toString(), actionSelectAcquirer, true);

        ActionSettle settleAction = new ActionSettle(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSettle) action).setParam(getCurrentContext(),
                        getString(R.string.trans_settle), selectAcqs);
            }

        });

        bind(State.SETTLE.toString(), settleAction);

        //结算是否需要输入密码
        if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SETTLEMENT_PWD)) {
            gotoState(State.INPUT_PWD.toString());
        } else {
            afterPwd();
        }
    }

    private void afterPwd(){
        if (null == selectAcqs || selectAcqs.isEmpty()) {
            gotoState(State.SELECT_ACQ.toString());
        } else {
            gotoState(State.SETTLE.toString());
        }
    }

    /**
     * on action result
     * @param currentState ：current State
     * @param result       ：current action result
     */
    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);
        switch (state) {
            case INPUT_PWD:
                String data = EncUtils.pwdSha((String) result.getData());
                if (!data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_SETTLE_PWD)) && !data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_SYS_PWD))) {
                    if (selectAcqs != null)
                        selectAcqs.clear();
                    transEnd(new ActionResult(TransResult.ERR_PASSWORD, null));
                    return;
                }
                afterPwd();
                break;
            case SELECT_ACQ:
                //noinspection unchecked
                selectAcqs = (ArrayList<String>) result.getData();
                gotoState(State.SETTLE.toString());
                break;
            case SETTLE:
                if (result.getRet() == TransResult.ERR_USER_CANCEL) {
                    afterPwd();
                } else {
                    if (selectAcqs != null && selectAcqs.contains("HASE_CUP") && result.getRet() == TransResult.SUCC) {
                        isHaseCupSettleSuccess = true;
                        //这个字段控制HASE_CUP结算之后进行签到的逻辑
                        setTransRunning(false);
                    }
                    //拿到回传的TransData数据，取出RefNo和AuthCode
                    if(result.getData()!=null &&result.getData() instanceof TransData){
                        TransData transDataRec = (TransData) result.getData();
                        transData.setRefNo(transDataRec.getRefNo());
                        transData.setAuthCode(transDataRec.getAuthCode());
                    }
                    result.setData(isHaseCupSettleSuccess);
                    if (selectAcqs != null)
                        selectAcqs.clear();
                    addTransDataAndTotal(result);
                    transEnd(result);
                }
                break;
        }
    }

    // ActionResult 中增加 transData
    private void addTransDataAndTotal(ActionResult result){
        if (null != result.getData1() && result.getData1() instanceof ArrayList) {
            //根据结算结果设置返回码
            if(result.getRet() == 0) transData.setResponseCode("00");
            else transData.setResponseCode(String.valueOf(result.getRet()));
            Pair<TransData, ArrayList<TransTotal>> transDataTransTotalPair
                    = new Pair<>(transData, (ArrayList<TransTotal>) result.getData1());
            result.setData1(transDataTransTotalPair);
        }
    }

}
