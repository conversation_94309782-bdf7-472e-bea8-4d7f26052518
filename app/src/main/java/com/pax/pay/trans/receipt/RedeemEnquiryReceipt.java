package com.pax.pay.trans.receipt;

import com.pax.glwrapper.page.IPage;
import com.pax.pay.trans.model.TransData;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

public class RedeemEnquiryReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isReprint isReprint
     */
    public RedeemEnquiryReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isReprint isReprint
     * @param isPrintPreview isPrintPreview
     */
    public RedeemEnquiryReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addReferenceNO();
        addAppCode();
        addEMVInfo();
        addSystemTrace();
        addNewline(1);
        addAmount();
        addNewline(1);
        addEnd();
    }

    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("BALANCE 積分查詢")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

    @Override
    public void addAmount() {
        addDoubleLine();
        addAvailableRedeemBalance();
        addNewline(1);
        addExpiredRedeemBalance();
    }

}
