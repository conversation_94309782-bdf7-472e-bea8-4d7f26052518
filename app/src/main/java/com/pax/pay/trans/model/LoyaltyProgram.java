package com.pax.pay.trans.model;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/26.
 */
public class LoyaltyProgram {

    private String programId;
    private String cashBalance;
    private String cashSign;
    private String cashExpDate;
    private String expCashBalance;
    private String expCashExpiry;

    /**
     * Gets program id.
     *
     * @return the program id
     */
    public String getProgramId() {
        return programId;
    }

    /**
     * Sets program id.
     *
     * @param programId the program id
     */
    public void setProgramId(String programId) {
        this.programId = programId;
    }

    /**
     * Gets cash balance.
     *
     * @return the cash balance
     */
    public String getCashBalance() {
        return cashBalance;
    }

    /**
     * Sets cash balance.
     *
     * @param cashBalance the cash balance
     */
    public void setCashBalance(String cashBalance) {
        this.cashBalance = cashBalance;
    }

    /**
     * Gets cash sign.
     *
     * @return the cash sign
     */
    public String getCashSign() {
        return cashSign;
    }

    /**
     * Sets cash sign.
     *
     * @param cashSign the cash sign
     */
    public void setCashSign(String cashSign) {
        this.cashSign = cashSign;
    }

    /**
     * Gets cash exp date.
     *
     * @return the cash exp date
     */
    public String getCashExpDate() {
        return cashExpDate;
    }

    /**
     * Sets cash exp date.
     *
     * @param cashExpDate the cash exp date
     */
    public void setCashExpDate(String cashExpDate) {
        this.cashExpDate = cashExpDate;
    }

    /**
     * Gets exp cash balance.
     *
     * @return the exp cash balance
     */
    public String getExpCashBalance() {
        return expCashBalance;
    }

    /**
     * Sets exp cash balance.
     *
     * @param expCashBalance the exp cash balance
     */
    public void setExpCashBalance(String expCashBalance) {
        this.expCashBalance = expCashBalance;
    }

    /**
     * Gets exp cash expiry.
     *
     * @return the exp cash expiry
     */
    public String getExpCashExpiry() {
        return expCashExpiry;
    }

    /**
     * Sets exp cash expiry.
     *
     * @param expCashExpiry the exp cash expiry
     */
    public void setExpCashExpiry(String expCashExpiry) {
        this.expCashExpiry = expCashExpiry;
    }
}
