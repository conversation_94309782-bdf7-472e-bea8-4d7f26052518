/*
 *
 *  * ============================================================================
 *  * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  * This software is supplied under the terms of a license agreement or nondisclosure
 *  * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  * disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) $YEAR-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * Description:
 *  * Revision History:
 *  * Date	             Author	                Action
 *  * 20190422   	     ligq           	    Modify
 *  * ============================================================================
 *
 */
package com.pax.pay.trans.btprinter;

import com.pax.pay.trans.btprinter.adapters.BtDeviceAdapter;

import io.reactivex.Observable;

public interface IBtPrinter {
    String TAG = "BT";
    String BP60A = "BP60A";

    Observable<BtDeviceAdapter.ItemBtDevice> scan();

    Observable<Boolean> connect(String macAddress);

    <T> Observable<Integer> printBitmap(T t, String printType, String title, String resultMsg);
}
