/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.Context;
import android.content.DialogInterface;
import android.os.ConditionVariable;

import com.pax.pay.app.FinancialApplication;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.CustomAlertDialog.OnCustomClickListener;

public class PrintListenerImpl implements PrintListener {

    private Context context;
    private CustomAlertDialog showMsgDialog;
    private CustomAlertDialog confirmDialog;
    private ConditionVariable cv;
    private Status result = Status.OK;

    private PrintCompleteListener printCompleteListener;

    public PrintListenerImpl(Context context) {
        this.context = context;
    }

    public interface PrintCompleteListener {
        void onPrintComplete();
    }

    // 提供一个设置回调的方法
    public void setPrintCompleteListener(PrintCompleteListener listener) {
        this.printCompleteListener = listener;
    }

    @Override
    public void onShowMessage(final String title, final String message) {
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                if (showMsgDialog == null) {
                    showMsgDialog = new CustomAlertDialog(context, CustomAlertDialog.PROGRESS_TYPE);
                    showMsgDialog.show();
                    showMsgDialog.setCancelable(false);
                    showMsgDialog.setTitleText(title);
                    showMsgDialog.setContentText(message);

                } else {
                    if (!showMsgDialog.isShowing()) {
                        showMsgDialog.show();
                    }
                    showMsgDialog.setTitleText(title);
                    showMsgDialog.setContentText(message);
                }
            }
        });
    }

    @Override
    public Status onConfirm(final String title, final String message) {
        cv = new ConditionVariable();
        result = Status.OK;
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                if (confirmDialog != null) {
                    confirmDialog.dismiss();
                }
                confirmDialog = new CustomAlertDialog(context, CustomAlertDialog.PROGRESS_TYPE);
                confirmDialog.show();
                confirmDialog.setTimeout(30);
                confirmDialog.setTitleText(title);
                confirmDialog.setContentText(message);
                confirmDialog.setCancelable(false);
                confirmDialog.setCanceledOnTouchOutside(false);
                confirmDialog.showCancelButton(true);
                confirmDialog.setCancelClickListener(new OnCustomClickListener() {

                    @Override
                    public void onClick(CustomAlertDialog alertDialog) {
                        result = Status.CANCEL;
                        alertDialog.dismiss();
                    }
                });
                confirmDialog.showConfirmButton(true);
                confirmDialog.setConfirmClickListener(new OnCustomClickListener() {

                    @Override
                    public void onClick(CustomAlertDialog alertDialog) {
                        result = Status.CONTINUE;
                        alertDialog.dismiss();
                    }
                });
                confirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialogInterface) {
                        if (result == Status.OK) {
                            result = Status.CANCEL;
                        }
                        if (cv != null) {
                            cv.open();
                        }
                    }
                });
                confirmDialog.show();

            }
        });
        cv.block();
        return result;
    }

    /**
     * 只关闭对话框，不触发回调
     * 这个方法是为了允许子类在不触发回调的情况下关闭对话框
     */
    protected void dismissDialogsOnly() {
        if (showMsgDialog != null) {
            showMsgDialog.dismiss();
        }
        if (confirmDialog != null) {
            confirmDialog.dismiss();
        }
    }

    @Override
    public void onEnd() {
        // 先关闭对话框
        dismissDialogsOnly();

        // 打印流程结束时调用回调
        if (printCompleteListener != null) {
            printCompleteListener.onPrintComplete();
        }
    }
}
