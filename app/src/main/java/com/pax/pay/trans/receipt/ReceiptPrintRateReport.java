/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-4-23
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.receipt;

import com.pax.edc.R;
import com.pax.pay.utils.Utils;

public class ReceiptPrintRateReport extends AReceiptPrint {

    /**
     * print Rate Report
     *
     * @param content rate report
     * @param listener PrintListener
     */
    public int print(String content, PrintListener listener) {
        this.listener = listener;

        int ret;
        if (listener != null) {
            listener.onShowMessage(null, Utils.getString(R.string.wait_print));
        }

        ret = printStr(content);
        if (ret == 0) {
            printStr("\n\n\n\n");
        }

        if (listener != null) {
            listener.onEnd();
        }

        return ret;
    }
}
