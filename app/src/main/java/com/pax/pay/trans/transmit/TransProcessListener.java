/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.transmit;

import androidx.annotation.NonNull;
import com.pax.pay.trans.model.TransData;

public interface TransProcessListener {
    /**
     * show process message
     *
     * @param message message
     * @param timeout timeout
     */
    void onShowProgress(String message, int timeout);

    /**
     * show warning message
     *
     * @param message message
     * @param timeout timeout
     */
    void onShowWarning(String message, int timeout);

    void onUpdateProgressTitle(String title);

    void onHideProgress();

    void onShowNormalMessage(String message, int timeout, boolean confirmable);

    void onShowErrMessage(String message, int timeout, boolean confirmable);

    int onInputOnlinePin(TransData transData);

    /**
     * calc mac
     */
    @NonNull
    byte[] onCalcMac(byte[] data);

    /**
     * enc track data
     */
    @NonNull
    byte[] onEncTrack(byte[] track);

    /**
     * enc pii data
     */
    byte[] onEncPii(byte[] track);

    void updateParam(TransData transdata);
    TransData getProcessTransData();
}