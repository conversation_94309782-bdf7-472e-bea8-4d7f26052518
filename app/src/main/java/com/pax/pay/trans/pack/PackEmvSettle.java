package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.PPTransTotal;
import com.pax.pay.trans.model.TransData;
import java.util.List;
import java.util.Locale;

public class PackEmvSettle extends PackIso8583 {

    public PackEmvSettle(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{3, 11, 24, 41, 42, 56, 60, 62, 63};
    }

    /**
     * setBitData3
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        String procCode = transData.getTransType().getProcCode();
        setBitData("3", procCode);
    }

    /**
     * setBitData24
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData24(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("24", transData.getAcquirer().getNii());
    }

    /**
     * setBitData56
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData56(@NonNull TransData transData) throws Iso8583Exception {
        String acquirerName = transData.getAcquirer().getName();

        if (acquirerName != null && !(acquirerName.toUpperCase().contains("DCC") || acquirerName.toUpperCase().contains("MACAO"))) {
            List<TransData> allTrans = FinancialApplication.getTransDataDbHelper()
                    .findAllTransData(transData.getAcquirer());
            if (allTrans.isEmpty()) {
             byte[] data = new byte[] {
                     (byte) 0xDF, 0x5C, 0x07, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
             };
                setBitData("56", data);
                return;
            }
            String f56 = allTrans.get(allTrans.size() - 1)
                 .getSendField56(); //the previous transaction f56 data
            if (f56 != null && f56.length() > 0) {
             setBitData("56", FinancialApplication.getConvert()
                     .strToBcd(f56, IConvert.EPaddingPosition.PADDING_LEFT));
            } else {
             byte[] data = new byte[] {
                     (byte) 0xDF, 0x5C, 0x07, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
             };
                setBitData("56", data);
            }
        }
    }
    /**
     * setBitData60
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        String f60 =
                Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6); // f60.2
        setBitData("60", f60);
    }

    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        String acquirerName = transData.getAcquirer().getName();

        if (acquirerName != null && (acquirerName.toUpperCase().contains("DCC") || acquirerName.toUpperCase().contains("MACAO"))) {
            List<PPTransTotal> PPTransTotals = acquirerName.toUpperCase().contains("DCC") ? FinancialApplication.getTransTotalDbHelper().caclDccTotal() : FinancialApplication.getTransTotalDbHelper().caclMacaoTotal();

            // length = 13 + 18*n
            StringBuilder f62 = new StringBuilder();

            if (acquirerName.toUpperCase().contains("MACAO")) {
                f62.append(String.format(Locale.getDefault(), "%04d", Long.parseLong(PPTransTotals.get(0).getCurrencyCode())));
                //Number of Opt-Out  Sales/Auths
                f62.append(String.format(Locale.getDefault(), "%04d", PPTransTotals.get(0).getTransCount()));
                //Total of Opt-In Sales/Auths In Merchant Currency
                f62.append(String.format(Locale.getDefault(), "%012d", PPTransTotals.get(0).getAmount()));
            } else {
                //Currency Code for local currency
                f62.append(String.format(Locale.getDefault(), "%04d", Long.parseLong(PPTransTotals.get(0).getCurrencyCode())));
                //Number of Sales
                f62.append(String.format(Locale.getDefault(), "%04d", PPTransTotals.get(0).getOptOutCount()));
                //Total of Sales in Local Currency
                f62.append(String.format(Locale.getDefault(), "%012d", PPTransTotals.get(0).getOptOutAmount()));

                if (PPTransTotals.size() > 1) {
                    for (int i = 1; i < PPTransTotals.size(); i++) {
                        //Currency Code that these statistics are reporting on //Cardholder Currency Code
                        f62.append(String.format(Locale.getDefault(), "%04d", Long.parseLong(PPTransTotals.get(i).getCurrencyCode())));
                        //Number of Opt-Out  Sales/Auths
                        f62.append(String.format(Locale.getDefault(), "%04d", PPTransTotals.get(i).getOptOutCount()));
                        //Total of Opt-Out Sales/Auths in Merchant Currency
                        f62.append(String.format(Locale.getDefault(), "%012d", PPTransTotals.get(i).getOptOutAmount()));
                        //Number of Opt-In Sales/Auths
                        f62.append(String.format(Locale.getDefault(), "%04d", PPTransTotals.get(i).getOptInCount()));
                        //Total of Opt-In Sales/Auths In Merchant Currency
                        f62.append(String.format(Locale.getDefault(), "%012d", PPTransTotals.get(i).getOptInAmount()));
                    }
                }
            }
                //"V02" indicator that this is Version 2 of the statistics
                byte[] header = {0x56, 0x30, 0x32};
                byte[] data = FinancialApplication.getConvert().strToBcd(f62.toString(), IConvert.EPaddingPosition.PADDING_LEFT);
                byte[] combine = new byte[header.length + data.length];
                System.arraycopy(header, 0, combine, 0, header.length);
                System.arraycopy(data, 0, combine, header.length, data.length);
                setBitData("62", combine);
        }
        /*else {
            setBitData("62", Component.getPaddedNumber(transData.getInvoiceNo(), 6));
        }*/

    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        String acquirerName = transData.getAcquirer().getName();
        String temp = "";
        if (acquirerName != null && (acquirerName.toUpperCase().contains("DCC") || acquirerName.toUpperCase().contains("MACAO") )) {
            temp += transData.getField63().substring(0, 30);
            temp += "000";  //reversalCnt
            temp += "000"; //tipCnt
            temp += String.format("%1$0" + (90 - temp.length()) + "d", 0);
            setBitData("63", temp);
        } else {
            setBitData("63", transData.getField63());
        }
    }
}
