package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.data.entity.Acquirer;
import com.pax.data.emi.BankEMIBinRequestData;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.emi.BankEMIBinActivity;
import com.pax.pay.utils.PaymentEncryptionHandler;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

public class ActionBinValidation extends AAction {
    private Context context;
    private String pan;
    private String expDate;
    private String amount;
    private boolean isBrandEMI;
    private boolean isBankSwipeFlow;
    private boolean isFinishActivity;

    public ActionBinValidation(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String pan, String expDate,
                         String amount, boolean isBrandEMI, boolean isBankSwipeFlow,boolean isFinishActivity) {
        this.context = context;
        this.pan = pan;
        this.expDate = expDate;
        this.amount = amount;
        this.isBrandEMI = isBrandEMI;
        this.isBankSwipeFlow = isBankSwipeFlow;
        this.isFinishActivity = isFinishActivity;
    }


    @Override
    protected void process() {
        Intent intent = new Intent(context, BankEMIBinActivity.class);
        BankEMIBinRequestData bankEMIBinRequestData = new BankEMIBinRequestData();
        String name = SysParam.getInstance().getString(R.string.ACQ_NAME);
        Acquirer acquirer = GreendaoHelper.getAcquirerHelper().findAcquirer(name);
        bankEMIBinRequestData.setTid(acquirer.getTerminalId());
        bankEMIBinRequestData.setMid(acquirer.getMerchantId());
        bankEMIBinRequestData.setDeviceModel("PAX");
        bankEMIBinRequestData.setAmount(Utils.insertDecimalPoint(amount));
        bankEMIBinRequestData.setCardBin(pan.substring(0, 8));
        String integrationCode = SysParam.getInstance().getString(R.string.integration_code);
        String certificate = SysParam.getInstance().getString(R.string.certificate_base64);
        if ("IDFCDEBIT".equals(integrationCode) || "CC".equals(integrationCode)) {
            bankEMIBinRequestData.setCardHash(PaymentEncryptionHandler.handlePayment(integrationCode, certificate, pan));
        } else {
            bankEMIBinRequestData.setEncryptedPan(PaymentEncryptionHandler.handlePayment(integrationCode, certificate, pan));

        }

        if (isBrandEMI) {
            bankEMIBinRequestData.setMode("BRANDEMI");
        } else if (isBankSwipeFlow) {
            bankEMIBinRequestData.setMode("CARD");
        } else {
            bankEMIBinRequestData.setMode("OFFER");
        }

        bankEMIBinRequestData.setApiRefNumber(SysParam.getInstance().getString(Utils.getString(R.string.api_ref_number)));
        bankEMIBinRequestData.setToken(SysParam.getInstance().getString(Utils.getString(R.string.token)));
        bankEMIBinRequestData.setCardExpiryDate(expDate);
        intent.putExtra(EUIParamKeys.BANK_EMI_BIN_DATA.toString(), bankEMIBinRequestData);
        intent.putExtra("IS_FINISH_ACTIVITY", isFinishActivity);
        context.startActivity(intent);
    }
}
