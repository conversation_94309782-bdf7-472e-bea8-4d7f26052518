package com.pax.pay.trans.action;

import android.content.Context;
import android.os.AsyncTask;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.dal.IPed;
import com.pax.dal.entity.ECheckMode;
import com.pax.dal.entity.EPedKeyType;
import com.pax.dal.entity.EPedType;
import com.pax.dal.exceptions.PedDevException;
import com.pax.device.DeviceImplNeptune;
import com.pax.edc.opensdk.TransResult;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.TlvException;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.RSAKeyData;
import com.pax.pay.trans.model.TMKDownloadTag;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.transmit.Online;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.util.Arrays;

/**
 * Created by liliang on 4/4 0004.
 */

public class ActionDownloadTmk extends AAction {
    private static final String TAG_DL_TMK = "DOWN_TMK";
    private Context mContext;
    private BaseTrans mTrans;

    private String keyType;

    /**
     *
     */
    public ActionDownloadTmk(Context context, BaseTrans trans, String keyType) {
        super(null);
        mContext = context;
        mTrans = trans;
        this.keyType = keyType;
    }

    @Override
    protected void process() {
        new DownloadTmkTask().execute();
    }

    private class DownloadTmkTask extends AsyncTask<Void, Void, Integer> {
        private TransProcessListenerImpl mListener;

        @Override
        protected void onPreExecute() {
            if (mListener == null) {
                mListener = new TransProcessListenerImpl(mContext);
                mListener.onUpdateProgressTitle(ETransType.POS_RSA_KEY_DOWN.getTransName());
            }
        }

        @Override
        protected Integer doInBackground(Void... voids) {
            return downloadTMK(mListener);
        }

        @Override
        protected void onPostExecute(Integer result) {
            if (mListener != null) {
                mListener.onHideProgress();
            }

            setResult(new ActionResult(result, null));
            mListener = null;
        }
    }

    /**
     * download tmk
     * @param listener TransProcessListener
     * @return int
     */
    private int downloadTMK(TransProcessListener listener) {
        int ret;
        if(Component.isDemo()){
            byte[] pinKeyTpk = {
                    (byte) 0x00, (byte) 0x01, (byte) 0x02, (byte) 0x03,
                    (byte) 0x04, (byte) 0x05, (byte) 0x06, (byte) 0x07,
                    (byte) 0x08, (byte) 0x09, (byte) 0x0A, (byte) 0x0B,
                    (byte) 0x0C, (byte) 0x0D, (byte) 0x0E, (byte) 0x0F};
            int tmkIndex = Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam
                    .NumberParam.MK_INDEX, -1));

            if (keyType.equals(Constants.MASTERCARD_TYPE)) {
                //master card TMK index
                tmkIndex = Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam
                        .NumberParam.MASTERCARD_MK_INDEX, -1));
            }
            try {
                FinancialApplication.getDal().getPed(EPedType.INTERNAL).writeKey(EPedKeyType.TMK, (byte) 0, EPedKeyType.TMK, (byte) tmkIndex, pinKeyTpk,
                        ECheckMode.KCV_NONE, null);
            } catch (Exception e) {
                Log.e(TAG_DL_TMK, "", e);
                return TransResult.ERR_TMK_TO_PED;
            }
            return TransResult.SUCC;
        }
        Online online = new Online();

        //TMK下载阶段一
        TransData transData = mTrans.getTransData();
        transData.setTransType(ETransType.POS_RSA_KEY_DOWN);
        listener.onUpdateProgressTitle(ETransType.POS_RSA_KEY_DOWN.getTransName());
        ret = online.online(transData, listener);
        if (ret != TransResult.SUCC) {
            return ret;
        }

        // 平台拒绝   (此处需要分情况重新处理)
        if (!"00".equals(transData.getResponseCode())) {
            return TransResult.ERR_HOST_REJECT;
        }

        // 解析返回数据
        ret = parseRsaKey(transData);
        if (ret != TransResult.SUCC) {
            return ret;
        }

        //TMK下载阶段二
        transData.setTransType(ETransType.POS_TMK_DOWN);
        listener.onUpdateProgressTitle(ETransType.POS_TMK_DOWN.getTransName());
        ret = online.online(transData, listener);
        if (ret != TransResult.SUCC) {
            return ret;
        }

        // 平台拒绝
        if (!"00".equals(transData.getResponseCode())) {
            return TransResult.ERR_HOST_REJECT;
        }

        //解析返回数据
        byte[] bF62 = FinancialApplication.getConvert().strToBcd(transData.getField62(), IConvert
                .EPaddingPosition
                .PADDING_LEFT);
        try {
            ret = writeTMK(transData, bF62);
            if (ret != TransResult.SUCC) {
                return ret;
            }
        } catch (TlvException e) {
            Log.e(TAG_DL_TMK, "", e);
            return TransResult.ERR_TMK_TO_PED;
        } catch (Exception e) {
            Log.e(TAG_DL_TMK, "", e);
            return TransResult.ERR_TMK_TO_PED;
        }

        //TMK下载阶段三 激活TMK
        transData.setTransType(ETransType.POS_ACTIVATE_TMK);
        listener.onUpdateProgressTitle(ETransType.POS_ACTIVATE_TMK.getTransName());
        ret = online.online(transData, listener);
        if (ret != TransResult.SUCC) {
            return ret;
        }

        // 平台拒绝
        if (!"00".equals(transData.getResponseCode())) {
            return TransResult.ERR_HOST_REJECT;
        }

        return ret;
    }

    /**
     * 解析RSA公钥
     *
     * @param transData TransData
     * @return int
     */
    private int parseRsaKey(@NonNull TransData transData) {
        String f62 = transData.getField62();
        RSAKeyData rsaKeyData = new RSAKeyData();
        int index;
        int len;

        if (TextUtils.isEmpty(f62)) {
            return TransResult.ERR_PARAM;
        }

        index = f62.indexOf("9F06");
        rsaKeyData.setRsaPukRID(f62.substring(index + 6, index + 16));
        Log.d(TAG_DL_TMK, "PukRid:" + f62.substring(index + 6, index + 16));

        index = f62.indexOf("9F22");
        int pubKid = Integer.parseInt(f62.substring(index + 6, index + 8), 16);
        rsaKeyData.setRsaPukID(pubKid);
        Log.d(TAG_DL_TMK, "PukID:" + f62.substring(index + 6, index + 8));

        index = f62.indexOf("DF02");
        int tmkLength = 128;
        int header = 58;
        int headerLength = 6;
        if(FinancialApplication.getSysParam().get(SysParam.BooleanParam.TMK_TYPE)){
            tmkLength = 256;
            header = 66;
            headerLength = 8;
        }
        rsaKeyData.setRsaPukModulLen(tmkLength);
        rsaKeyData.setRsaPukModul(f62.substring(index + headerLength + header, index + headerLength + header + tmkLength*2));
        Log.d(TAG_DL_TMK, "PukModul:" + f62.substring(index + headerLength + header, index + headerLength + header + tmkLength*2));

        index = f62.indexOf("DF04");
        len = Integer.parseInt(f62.substring(index + 4, index + 6), 16);
        rsaKeyData.setRsaPukExponent(f62.substring(index + 6, index + 6 + 2 * len));
        Log.d(TAG_DL_TMK, "PukExponent:" + f62.substring(index + 6, index + 6 + 2 * len));

        transData.setRsaKeyData(rsaKeyData);

        return TransResult.SUCC;
    }

    /**
     * 解析tmk，并存到PED中
     *
     * @param tmk byte[]
     * @throws TlvException Exception
     * @throws PedDevException Exception
     */
    private int writeTMK(@NonNull TransData transData, byte[] tmk) throws TlvException,
            PedDevException {

        if (tmk == null || tmk.length == 0) {
            return TransResult.ERR_PARAM;
        }

        IPed ped = FinancialApplication.getDal().getPed(EPedType.INTERNAL);
        ITlv iTlv = FinancialApplication.getPacker().getTlv();

        byte[] value;
        byte[] tmkKcv = null;
        byte[] plainTmk = new byte[16];
        byte[] tmkCheckValue;
        byte[] randomKey;
        byte[] checkData = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

        ITlv.ITlvDataObjList tmkTlvList = iTlv.unpack(tmk);
        RSAKeyData tmkData = transData.getRsaKeyData();
        if (tmkData == null) {
            return TransResult.ERR_PARAM;
        }

        // DF21 Encrypted TMK
        ITlv.ITlvDataObj tlvDataObj = tmkTlvList.getByTag(TMKDownloadTag.TAG_ENCRYPTED_TMK);
        if (tlvDataObj != null) {
            value = tlvDataObj.getValue();
            if (value != null && value.length > 0) {
                randomKey = FinancialApplication.getConvert().strToBcd(tmkData.getRandomKey(),
                        IConvert.EPaddingPosition.PADDING_LEFT);
                plainTmk = DeviceImplNeptune.getInstance().tdes(value, randomKey,
                        DeviceImplNeptune.TDES_DNCRYPT_MODE);
            }
        }

        // DF22 TMK KCV
        tlvDataObj = tmkTlvList.getByTag(TMKDownloadTag.TAG_TMK_CHECK_VALUE);
        if (tlvDataObj != null) {
            value = tlvDataObj.getValue();
            if (value != null && value.length > 0) {
                tmkKcv = value;
            }
        }

        tmkCheckValue = DeviceImplNeptune.getInstance().tdes(checkData, plainTmk,
                DeviceImplNeptune.TDES_ENCRYPT_MODE);

        byte[] caclKcv = new byte[4];
        System.arraycopy(tmkCheckValue, 0, caclKcv, 0, 4);

        if (Arrays.equals(caclKcv, tmkKcv)) {
            int tmkIndex = Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam
                    .NumberParam.MK_INDEX, -1));

            if (keyType.equals(Constants.MASTERCARD_TYPE)) {
                //master card TMK index
                tmkIndex = Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam
                        .NumberParam.MASTERCARD_MK_INDEX, -1));
            }

            ped.writeKey(EPedKeyType.TMK, (byte) 0, EPedKeyType.TMK, (byte) tmkIndex, plainTmk,
                    ECheckMode.KCV_NONE, null);
        } else {
            return TransResult.ERR_TMK_TO_PED;
        }

        return TransResult.SUCC;
    }
}
