package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.BankEMIOfferRequestData;
import com.pax.data.emi.BankEMIOfferResponseData;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;

public class BankEMIOfferActivity extends BaseEMIActivity<BankEMIOfferRequestData, BankEMIOfferResponseData> {
    private BankEMIOfferRequestData bankEMIOfferRequestData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestData = bankEMIOfferRequestData;
        try {
            handleRequest();
        } catch (JsonProcessingException e) {
            handleError(getGeneralErrorCode(), null);
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected void loadParam() {
        bankEMIOfferRequestData = (BankEMIOfferRequestData) getIntent().getSerializableExtra(EUIParamKeys.BANK_EMI_OFFER_DATA.toString());
    }

    @Override
    protected String getTitleString() {
        return "Bank EMI Offer";
    }

    @Override
    protected String getRequestType() {
        return "EMIOFFER";
    }

    @Override
    protected int getRequestCode() {
        return 111;
    }

    @Override
    protected int getGeneralErrorCode() {
        return TransResult.ERR_CHECK_EMI_OFFER;
    }

    @Override
    protected Class<BankEMIOfferResponseData> getResponseClass() {
        return BankEMIOfferResponseData.class;
    }

}