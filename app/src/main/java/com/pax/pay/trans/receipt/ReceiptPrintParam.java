/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import com.pax.dal.exceptions.PrinterDevException;
import com.pax.pay.trans.action.PrintType;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

import androidx.annotation.IntDef;


/**
 * print receipt
 *
 * <AUTHOR>
 */
public class ReceiptPrintParam extends AReceiptPrint {

    public static final int AID = 1;
    public static final int CAPK = 2;
    public static final int SYS = 3;
    public static final int DEVICE = 4;


    public int print(@Type int type, PrintListener listener, Context context) {
        this.listener = listener;
        if (Utils.showA50NoPrint(false, context, dialog -> {
            if (listener != null) {
                listener.onEnd();
            }
        }, false)) {
            return -1;
        }
        if (listener != null)
            listener.onShowMessage(null, Utils.getString(R.string.wait_print));

        ReceiptGeneratorParam receiptGeneratorParam;
        switch (type) {
            case AID:
                receiptGeneratorParam = new ReceiptGeneratorAidParam();
                break;
            case CAPK:
                receiptGeneratorParam = new ReceiptGeneratorCapkParam();
                break;
            case SYS:
                receiptGeneratorParam = new ReceiptGeneratorSysParam();
                break;
            case DEVICE:
                receiptGeneratorParam = new ReceiptGeneratorDeviceDetailParam();
                break;
            default:
                return -1;
        }

        int ret = 0;
        try {
            ret = printBitmap(receiptGeneratorParam.generateBitmaps());
        } catch (PrinterDevException e) {
            if (listener != null) {
                listener.onConfirm(null, String.format("%s %s", e.getErrModule(), e.getErrMsg()));
            }
        }
        if (listener != null) {
            listener.onEnd();
        }
        return ret;
    }

    public View getOtherParamView(String type, Context context) {
        ReceiptGeneratorParam receiptGeneratorParam;
        if (PrintType.TYPE_AID.equals(type)) {
            receiptGeneratorParam = new ReceiptGeneratorAidParam();
        } else if (PrintType.TYPE_SYS.equals(type)) {
            receiptGeneratorParam = new ReceiptGeneratorSysParam();
        } else {
            return null;
        }

        // 创建一个 ScrollView 作为容器，使得内容可以滚动
        ScrollView scrollView = new ScrollView(context);
        scrollView.setScrollbarFadingEnabled(false); // 禁用滚动条消失的效果
        scrollView.setVerticalScrollBarEnabled(false); // 隐藏垂直滚动条

        // 创建一个 LinearLayout 作为子容器
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL); // 设置垂直排列

        List<Bitmap> bitmaps = receiptGeneratorParam.generateBitmaps();
        for (Bitmap bitmap : bitmaps) {
            container.addView(Utils.createImageView(context, bitmap));
        }
        // 将 LinearLayout 添加到 ScrollView 中
        scrollView.addView(container);
        // 返回包含所有 Bitmap 的可滚动视图
        return scrollView;
    }

    @IntDef({AID, CAPK, SYS, DEVICE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Type {
    }

}
