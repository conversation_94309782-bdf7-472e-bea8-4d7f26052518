package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.model.TransData;
/**
 * Created by terry on 2019/01/09.
 */

public class PackIncAuthorization extends PackIsoBase {
    public PackIncAuthorization(PackListener listener) {
        super(listener);
    }

    /**
     * get IncAuthorization RequiredFields
     *
     * @return int[]
     */
    protected int[] getRequiredFields() {
        return new int[] { 1, 2, 3, 4, 6, 11, 14, 22, 24, 25, 35, 37, 41, 42, 49, 51, 55, 62, 63 };
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == TransData.EnterMode.MANUAL) {
            setBitData("2", transData.getPan());
        }
    }

    /**
     * setBitData14
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == TransData.EnterMode.MANUAL) {
            setBitData("14", transData.getExpDate());
        }
    }

    /**
     * setBitData35
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("35", transData.getTrack2());
    }

    /**
     * setBitData37
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getAcquirer().getName().equals("HASE_EMV") || transData.getAcquirer()
                .getName()
                .equals("HASE_OLS"))) {
            super.setBitData37(transData);
        }
    }
}
