package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.data.emi.BankEMICheckCardRequestData;
import com.pax.data.entity.Acquirer;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.emi.BankEMICheckCardActivity;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

public class ActionCheckCardBin extends AAction {
    private Context context;
    private String pan;
    private String expDate;

    public ActionCheckCardBin(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String pan, String expDate) {
        this.context = context;
        this.pan = pan;
        this.expDate = expDate;
    }

    @Override
    protected void process() {
        Intent intent = new Intent(context, BankEMICheckCardActivity.class);
        BankEMICheckCardRequestData bankEMICheckCardRequestData = new BankEMICheckCardRequestData();
        String name = SysParam.getInstance().getString(R.string.ACQ_NAME);
        Acquirer acquirer = GreendaoHelper.getAcquirerHelper().findAcquirer(name);
        bankEMICheckCardRequestData.setTid(acquirer.getTerminalId());
        bankEMICheckCardRequestData.setMid(acquirer.getMerchantId());
        bankEMICheckCardRequestData.setDeviceModel("PAX");
        bankEMICheckCardRequestData.setToken(SysParam.getInstance().getString(Utils.getString(R.string.token)));
        bankEMICheckCardRequestData.setApiRefNumber(SysParam.getInstance().getString(Utils.getString(R.string.api_ref_number)));
        bankEMICheckCardRequestData.setCardBin(pan.substring(0, 8));
        bankEMICheckCardRequestData.setCardExpiryDate(expDate);
        bankEMICheckCardRequestData.setType("BANKEMI");
        intent.putExtra(EUIParamKeys.BANK_EMI_CHECK_CARD.toString(), bankEMICheckCardRequestData);
        context.startActivity(intent);
    }
}
