/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.transmit;

import android.annotation.SuppressLint;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransData.OfflineStatus;
import com.pax.pay.trans.model.TransTotal;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ResponseCode;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * 单独联机处理， 例如签到
 *
 * <AUTHOR>
 */
public class TransOnline {
    private Online online = new Online();

    /**
     * 结算
     *
     * @return {@link TransResult}
     */
    @SuppressLint("DefaultLocale")
    public int settle(TransTotal total, TransProcessListener listener) {
        String settleF62 = "";
        Acquirer acquirer = total.getAcquirer();

        TransData transData = new TransData();
        transData.setAcquirer(acquirer);
        transData.setBatchNo(acquirer.getCurrBatchNo());
        transData.setTraceNo(Component.getTransNo());
        transData.setInvoiceNo(Component.getInvoiceNo());
        transData.setDateTime(Device.getTime(Constants.TIME_PATTERN_TRANS));
        transData.setTransState(TransData.ETransStatus.NORMAL);
        transData.setCurrency(CurrencyConverter.getDefCurrency());
        ETransType transType;
        if ("HASE_OLS".equalsIgnoreCase(acquirer.getName())) {
            transType = ETransType.LYS_SETTLE;
        } else if ("HASE_CUP".equalsIgnoreCase(acquirer.getName())) {
            transType = ETransType.CUP_SETTLE;
        } else {
            transType = ETransType.EMV_SETTLE;
        }
        transData.setTransType(transType);

        String saleNum;
        String saleAmt;
        if (transType == ETransType.LYS_SETTLE) {
            saleNum = Component.getPaddedNumber(total.getNonFullRedeemNum(), 3);
            saleAmt = Component.getPaddedNumber(total.getNonFullRedeemAmt(), 12);
        } else {
            saleNum = Component.getPaddedNumber(total.getSaleNum(), 3);
            saleAmt = Component.getPaddedNumber(total.getSaleAmt(), 12);
        }
        String refundNum = Component.getPaddedNumber(total.getRefundTotalNum(), 3);
        String refundAmt = Component.getPaddedNumber(total.getRefundTotalAmt(), 12);
        String buf = saleNum + saleAmt + refundNum + refundAmt;
        buf += String.format("%060d", 0);
        transData.setField63(buf);

        if (transType == ETransType.LYS_SETTLE) {
            //pack field 62
            settleF62 = getLoyaltyF62(total);
            transData.setField62(settleF62);
        }

        if (Component.isAmex(transData.getAcquirer().getName())) {
            //Amex Settlement
            String amexSaleNum;
            String amexSaleAmt;
            String amexRefundNum;
            String amexRefundAmt;

            amexSaleNum = Component.getPaddedNumber(total.getSaleNum(), 3);
            amexSaleAmt = Component.getPaddedNumber(total.getSaleAmt(), 12);
            amexRefundNum = Component.getPaddedNumber(total.getRefundTotalNum(), 3);
            amexRefundAmt = Component.getPaddedNumber(total.getRefundTotalAmt(), 12);

            transData.setDebitCount(amexSaleNum);
            transData.setDebitAmount(amexSaleAmt);
            transData.setCreditCount(amexRefundNum);
            transData.setCreditAmount(amexRefundAmt);
        }

        if (listener != null) {
            listener.onUpdateProgressTitle(transType.getTransName());
        }
        //发送settle
        int ret = online.online(transData, listener);
        //将临时transData回传
        if (listener != null) {
            listener.updateParam(transData);
        }
        if (ret != TransResult.SUCC) {
            return ret;
        }
        String rspCode = transData.getResponseCode();
        if ("00".equals(rspCode)) {
            return TransResult.SUCC;
        }

        if (transType == ETransType.LYS_SETTLE) {
            if (!"95".equals(rspCode) && !"94".equals(rspCode) && !"93".equals(rspCode)) {
                if (listener != null) {
                    listener.onHideProgress();
                    listener.onShowErrMessage(Utils.getString(R.string.prompt_err_code) + rspCode,
                            Constants.FAILED_DIALOG_SHOW_TIME, true);
                }
                return TransResult.ERR_HOST_REJECT;
            }
        } else if (!"95".equals(rspCode)) {
            if (listener != null) {
                listener.onHideProgress();
                listener.onShowErrMessage(Utils.getString(R.string.prompt_err_code) + rspCode,
                        Constants.FAILED_DIALOG_SHOW_TIME, true);
            }
            return TransResult.ERR_HOST_REJECT;
        }

        //Settle unbalanced需要batch upload
        ret = batchUpload(acquirer, rspCode, listener);
        if (ret != TransResult.SUCC) {
            return ret;
        }

        //Batch upload成功再结算
        if (transType == ETransType.LYS_SETTLE) {
            switch (rspCode) {
                case "95":
                    transData.setTransType(ETransType.LYS_SETTLE_END_AFE_REJECT);
                    break;
                case "94":
                    transData.setTransType(ETransType.LYS_SETTLE_END_LYS_REJECT);
                    break;
                case "93":
                    transData.setTransType(ETransType.LYS_SETTLE_END_AFE_LYS_REJECT);
                    break;
            }
            //pack field 62
            transData.setField62(settleF62);
        } else if (transType == ETransType.EMV_SETTLE) {
            transData.setTransType(ETransType.EMV_SETTLE_END);
        } else {
            transData.setTransType(ETransType.CUP_SETTLE_END);
        }
        transData.setTraceNo(Component.getTransNo());
        transData.setDateTime(Device.getTime(Constants.TIME_PATTERN_TRANS));
        if (listener != null) {
            listener.onUpdateProgressTitle(FinancialApplication.getApp().getString(R.string.settle_end));
        }
        ret = online.online(transData, listener);
        if (ret != TransResult.SUCC) {
            return ret;
        }
        rspCode = transData.getResponseCode();
        if ("00".equals(rspCode)) {
            return TransResult.SUCC;
        }

        String acqName = acquirer.getName();
        boolean displayReconcileMsg = (acqName.equals("HASE_OLS") || acqName.equals("HASE_EMV") || acqName.equals("HASE")) &&
                ("95".equals(rspCode) || "77".equals(rspCode));

        ResponseCode responseCode = ResponseCode.getInstance().parse(rspCode);
        String errMsg = responseCode.getMessage();
        if (displayReconcileMsg) {
            errMsg = TransResultUtils.getMessage(TransResult.ERR_RECONCILE);
        }

        if (listener != null) {
            listener.onHideProgress();
            listener.onShowErrMessage(errMsg, Constants.FAILED_DIALOG_SHOW_TIME, true);
        }

        if (displayReconcileMsg) {
            //settlement end, HASE_OLS/HASE_EMV/HASE, 若后台返95/77,应用不需再batch upload 或再做settlement,需要close batch並打印settlement小票
            return TransResult.SUCC;
        }

        return TransResult.ERR_HOST_REJECT;
    }

    /**
     * 批上送
     */
    private int batchUpload(Acquirer acquirer, String settleRspCode,
            TransProcessListener listener) {
        List<TransData> allTransWithoutVoid = null;
        if ("95".equals(settleRspCode) || "93".equals(settleRspCode)) {
            //If response code of 1st settlement is 93/95, all transactions are uploaded.
            String acquirerName = acquirer.getName();
            if (acquirerName.toUpperCase().contains("DCC") || acquirerName.toUpperCase().contains("MACAO")) {
                allTransWithoutVoid = FinancialApplication.getTransDataDbHelper().findAllDccTransData(acquirer, false, false, false);
            } else {
                allTransWithoutVoid = FinancialApplication.getTransDataDbHelper().findAllTransData(acquirer, false, false, false);
            }
        } else if ("94".equals(settleRspCode)) {
            //HASE_OLS acquirer
            //LYS rejects, Batch Upload of LYS transactions(for LYS status flag = '1' only)
            allTransWithoutVoid =
                    FinancialApplication.getTransDataDbHelper().findLoyaltyTransUpload(acquirer,
                            TransData.LYSStatus.LYS_OK, false);
        }

        if (Component.isAmex(acquirer.getName())) {
            List<TransData> allTrans = FinancialApplication.getTransDataDbHelper().findAllTransData(acquirer, true, false, false);
            if (allTrans == null || allTrans.isEmpty()) {
                return TransResult.ERR_NO_TRANS;
            }
            return upload(allTrans, settleRspCode, listener);
        }

        if (allTransWithoutVoid == null || allTransWithoutVoid.isEmpty()) {
            //当只有void交易时，不进行batch upload，直接进行2nd settlement
            List<TransData> voidTrans = FinancialApplication.getTransDataDbHelper().findAllTransData(
                    acquirer, true, false, false);
            if (voidTrans.size() > 0) {
                return TransResult.SUCC;
            }
            return TransResult.ERR_NO_TRANS;
        }

        return upload(allTransWithoutVoid, settleRspCode, listener);
    }

    /**
     * @param list upload transdatd list
     */
    private int upload(List<TransData> list, String settleRspCode, TransProcessListener listener) {
        try {
            for (int i = 0; i < list.size(); i++) {
                if (listener != null) {
                    listener.onUpdateProgressTitle(FinancialApplication.getApp()
                            .getString(R.string.batch_upload, i + 1, list.size()));
                }
                TransData copy = copyData(list.get(i), settleRspCode, i < list.size() - 1);
                //Offline send & Batch upload just connect one time
                int ret = online.online(copy, listener, false);
                if (ret != TransResult.SUCC) {
                    return ret;
                }
                String rspCode = copy.getResponseCode();
                if ("HAES_OLS".equals(copy.getAcquirer().getName()) && !"00".equals(rspCode)) {
                    return TransResult.ERR_BATCH_UP_NOT_COMPLETED;
                }
            }
        } finally {
            online.close();
        }
        return TransResult.SUCC;
    }

    /**
     *
     */
    private TransData copyData(TransData orgData, String settleResCode, boolean hasMore) {

        Acquirer acquirer = orgData.getAcquirer();
        TransData copyData = new TransData();
        copyData.setTransState(TransData.ETransStatus.NORMAL);
        copyData.setAcquirer(acquirer);
        copyData.setCurrency(CurrencyConverter.getDefCurrency());

        // field 2
        copyData.setPan(orgData.getPan());
        // field 4
        copyData.setAmount(orgData.getAmount());
        copyData.setTipAmount(orgData.getTipAmount());
        // field 6
        copyData.setHomeCurrencyAmount(orgData.getHomeCurrencyAmount());
        //field 11
        copyData.setTraceNo(orgData.getTraceNo());

        copyData.setInvoiceNo(orgData.getInvoiceNo());
        copyData.setOriginvoiceNo(orgData.getOriginvoiceNo());

        //field 12
        copyData.setDateTime(orgData.getDateTime());
        //field 13
        //field 14
        copyData.setExpDate(orgData.getExpDate());
        //field 22
        copyData.setEnterMode(orgData.getEnterMode());
        //field 24
        copyData.setNii(orgData.getNii());
        //field 37
        copyData.setRefNo(orgData.getRefNo());
        //field 38
        copyData.setOrigAuthCode(orgData.getAuthCode());
        copyData.setAuthCode(orgData.getAuthCode());
        //field 39
        copyData.setResponseCode(orgData.getResponseCode());

        copyData.setOrigTransType(orgData.getTransType());

        copyData.setIssuer(orgData.getIssuer());
        //Copy data of track2
        copyData.setTrack2(orgData.getTrack2());

        copyData.setOrigTransNo(orgData.getTraceNo());

        //field 49
        copyData.setLocalCurrency(orgData.getLocalCurrency());

        //field 51
        copyData.setHomeCurrency(orgData.getHomeCurrency());

        //field 55
        copyData.setSendIccDataForOffline(orgData.getSendIccDataForBatchUp());

        //Trans state
        copyData.setTransState(orgData.getTransState());
        copyData.setTxnID(orgData.getTxnID());
        copyData.setDcc(orgData.isDcc());
        copyData.setRate(orgData.getRate());
        copyData.setOrigTransType(orgData.getTransType());

        if ("HASE_OLS".equals(acquirer.getName())) {
            String procCode = orgData.getTransType().getProcCode();
            String more = hasMore ? "1" : "0";
            if ("95".equals(settleResCode)) {
                procCode = procCode.substring(0, 4) + "0" + more;
            } else if ("94".equals(settleResCode)) {
                procCode = procCode.substring(0, 4) + "1" + more;
            } else if ("93".equals(settleResCode)) {
                procCode = procCode.substring(0, 4) + "2" + more;
            }
            copyData.setProcessCode(procCode);
            copyData.setTransType(ETransType.LYS_BATCH_UP);
            copyData.setLysStatus(orgData.getLysStatus());
            copyData.setPromotionCode(orgData.getPromotionCode());
            copyData.setNetAmount(orgData.getNetAmount());
            copyData.setCashDolBonusRedeemed(orgData.getCashDolBonusRedeemed());
            copyData.setCashDolProgramId(orgData.getCashDolProgramId());
            copyData.setCashDolRedeemed(orgData.getCashDolRedeemed());
            copyData.setMer1ProgramId(orgData.getMer1ProgramId());
            copyData.setMer1CashDolRedeemed(orgData.getMer1CashDolRedeemed());
            copyData.setMer2ProgramId(orgData.getMer2ProgramId());
            copyData.setMer2CashDolRedeemed(orgData.getMer2CashDolRedeemed());
            copyData.setReversalStatus(orgData.getReversalStatus());
        } else if ("HASE_CUP".equals(acquirer.getName())) {
            copyData.setProcessCode(orgData.getTransType().getProcCode());
            copyData.setTransType(ETransType.CUP_BATCH_UP);
        } else {
            if ("HASE_MACAO".equals(acquirer.getName())){
                copyData.setIsFullOutSource(orgData.isFullOutSource());
            }
            copyData.setProcessCode(orgData.getTransType().getProcCode());
            copyData.setTransType(ETransType.EMV_BATCH_UP);
        }
        copyData.setBelowFloorLimitTerminalApproved(orgData.isBelowFloorLimitTerminalApproved());
        return copyData;
    }

    /**
     * get the field62 of loyalty acquirer
     *
     * @param total trans total
     * @return String
     */
    private String getLoyaltyF62(TransTotal total) {
        String totalSaleCnt = Component.getPaddedNumber(total.getLysGrossNum(), 3);
        String totalOrgSaleAmt;
        String totalNetSaleAmt;
        String totalBonusAmt;
        totalOrgSaleAmt = new String(Utils.str2Bcd(Component.getPaddedNumber(total
                .getLysGrossAmt(), 12)), StandardCharsets.ISO_8859_1);
        totalNetSaleAmt = new String(Utils.str2Bcd(Component.getPaddedNumber(total
                .getLysOkNetAmt(), 12)), StandardCharsets.ISO_8859_1);
        totalBonusAmt = new String(Utils.str2Bcd(Component.getPaddedNumber(total
                .getBounsAmt(), 12)), StandardCharsets.ISO_8859_1);

        StringBuilder f62 = new StringBuilder();
        f62.append(totalSaleCnt).append(totalOrgSaleAmt).append(totalNetSaleAmt).append
                (totalBonusAmt);

        List<String> list = FinancialApplication.getTransDataDbHelper().findLoyaltyProgID(total.getAcquirer());
        final String threeSpace = new String(new byte[]{0x20, 0x20, 0x20}, StandardCharsets.ISO_8859_1); // "   "
        String defProgAmt = new String(new byte[]{0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, StandardCharsets.ISO_8859_1); // 00 00 00
        // 00 00 00

        if (list.isEmpty()) {
            for (int i = 0; i < 3; i++) {
                f62.append(threeSpace).append(defProgAmt);
            }
            return f62.toString();
        }

        if (list.size() == 1) {
            f62.append(list.get(0)).append(new String(Utils.str2Bcd(Component.getPaddedNumber
                    (total.getHaseCashAmt(), 12)), StandardCharsets.ISO_8859_1));
            f62.append(threeSpace).append(defProgAmt);
            f62.append(threeSpace).append(defProgAmt);
        } else if (list.size() == 2) {
            f62.append(list.get(0)).append(new String(Utils.str2Bcd(Component.getPaddedNumber
                    (total.getHaseCashAmt(), 12)), StandardCharsets.ISO_8859_1));
            f62.append(list.get(1)).append(new String(Utils.str2Bcd(Component.getPaddedNumber
                    (total.getMer1Amt(), 12)), StandardCharsets.ISO_8859_1));
            f62.append(threeSpace).append(defProgAmt);
        } else { //list.size() == 3
            f62.append(list.get(0)).append(new String(Utils.str2Bcd(Component.getPaddedNumber
                    (total.getHaseCashAmt(), 12)), StandardCharsets.ISO_8859_1));
            f62.append(list.get(1)).append(new String(Utils.str2Bcd(Component.getPaddedNumber
                    (total.getMer1Amt(), 12)), StandardCharsets.ISO_8859_1));
            f62.append(list.get(2)).append(new String(Utils.str2Bcd(Component.getPaddedNumber
                    (total.getMer2Amt(), 12)), StandardCharsets.ISO_8859_1));
        }

        return f62.toString();
    }

    /**
     *
     */
    public int offlineUpload(Acquirer acquirer, boolean isSettlement,
            TransProcessListener listener) {
        List<TransData.OfflineStatus> defFilter =
                Collections.singletonList(OfflineStatus.OFFLINE_NOT_SENT);
        List<TransData> offlineList = FinancialApplication.getTransDataDbHelper()
                .findOfflineTransData(acquirer, defFilter);
        if (offlineList == null || offlineList.isEmpty()) {
            return TransResult.SUCC;
        }
        if (!isSettlement) {
            //如果是关闭Intended Offline之后的第一笔交易，则一次上传所有离线交易
            if((FinancialApplication.getSysParam().get(SysParam.NumberParam.INTENDED_OFFLINE_CLOSE_FIRST,-1) != 1)){
                offlineList = offlineList.subList(0, 1);
            }else{
                FinancialApplication.getSysParam().set(SysParam.NumberParam.INTENDED_OFFLINE_CLOSE_FIRST, -1);
            }
        }

        try {
            for (int i = 0; i < offlineList.size(); i++) {
                if (listener != null) {
                    listener.onUpdateProgressTitle(
                            ETransType.OFFLINE_TRANS_SEND.getTransName() + "["
                                    + (i + 1) + "]");
                }
                TransData orgData = offlineList.get(i);
                TransData copyData = (TransData) orgData.cloneTransData();
                copyData.setTransType(ETransType.OFFLINE_TRANS_SEND);
                Component.transInit(copyData);
                copyData.setAcquirer(acquirer);
                copyData.setTransState(orgData.getTransState()); //reserve transState of record
                //F11:交易当下traceNo
                copyData.setTraceNo(Component.getTransNo());

                copyData.setInvoiceNo(orgData.getInvoiceNo());
                //Offline send & Batch upload just connect one time
                int ret = online.online(copyData, listener, false);
                if (ret != TransResult.SUCC) {
                    return ret;
                }

                String code = copyData.getResponseCode();
                if (!"00".equals(code)) {
                    ResponseCode rspCode = ResponseCode.getInstance().parse(code);
                    String errMsg = rspCode.getMessage();
                    listener.onShowErrMessage(errMsg, Constants.FAILED_DIALOG_SHOW_TIME, true);
                    orgData.setOfflineSendState(OfflineStatus.OFFLINE_ERR_RESP);
                    FinancialApplication.getTransDataDbHelper().updateTransData(orgData);
                    continue;
                }

                orgData.setSettleDateTime(
                        copyData.getSettleDateTime() != null ? copyData.getSettleDateTime() : "");
                orgData.setAuthCode(copyData.getAuthCode() != null ? copyData.getAuthCode() : "");
                orgData.setRefNo(copyData.getRefNo());
                orgData.setAcqCode(copyData.getAcqCode() != null ? copyData.getAcqCode() : "");
                orgData.setIssuerCode(copyData.getIssuerCode() != null ? copyData.getIssuerCode() : "");
                orgData.setReserved(copyData.getReserved() != null ? copyData.getReserved() : "");
                orgData.setDateTime(copyData.getDateTime());
                orgData.setOfflineSendState(OfflineStatus.OFFLINE_SENT);
                copyData.setBelowFloorLimitTerminalApproved(orgData.isBelowFloorLimitTerminalApproved());
                FinancialApplication.getTransDataDbHelper().updateTransData(orgData);
            }
        } finally {
            online.close();
        }
        return TransResult.SUCC;
    }

    /**
     * 回响功能
     *
     * @return {@link TransResultUtils}
     */
    public int echo(TransProcessListener listener) {
        TransData transData = Component.transInit();
        int ret;
        transData.setTransType(ETransType.ECHO);
        Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
        transData.setAcquirer(acquirer);
        if (listener != null) {
            listener.onUpdateProgressTitle(ETransType.ECHO.getTransName());
        }
        ret = online.online(transData, listener);
        if (ret != TransResult.SUCC) {
            if (listener != null) {
                listener.onHideProgress();
            }
            return ret;
        }
        return ret;
    }

}
