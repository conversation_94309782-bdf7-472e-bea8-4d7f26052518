/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;

import androidx.annotation.NonNull;

public class PackYONO extends PackIso8583 {

    public PackYONO(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{2, 3, 4, 11, 22, 24, 25, 41, 42, 52, 53, 60, 62, 63};
    }

    @Override
    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("22", "0011");
    }

    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        String f60 = Component.getPaddedNumber(transData.getBatchNo(), 6);
        setBitData("60", f60);
    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        String field63 = "";
        if (ETransType.YONO_CASH == transData.getTransType()) {
            field63 = "41" + Component.getPaddedString(transData.getAmount(), 12, '0');
        }
        field63 += "YO000000" + transData.getYonoNum();
        setBitData("63", field63);
    }
}
