package com.pax.pay.trans.receipt;

import com.pax.glwrapper.page.IPage;
import com.pax.pay.trans.model.TransData;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

/**
 * Created by terry on 2019/01/09.
 */

public class IncAuthorizationReceipt extends AReceiptGenerator {

    public IncAuthorizationReceipt(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    public IncAuthorizationReceipt(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addReferenceNO();
        addAppCode();
        addNewline(1);
        addEMVInfo();
        addAmount();
        addDoubleLine();
        addNewline(1);
        addEnd();
    }

    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INC AUTH 追加授權")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

}
