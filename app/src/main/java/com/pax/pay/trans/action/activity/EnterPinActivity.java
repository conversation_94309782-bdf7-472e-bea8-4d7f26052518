/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action.activity;

import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.pax.abl.core.ActionResult;
import com.pax.dal.IPed;
import com.pax.dal.entity.EKeyCode;
import com.pax.dal.entity.EPedType;
import com.pax.dal.entity.RSAPinKey;
import com.pax.dal.exceptions.EPedDevException;
import com.pax.dal.exceptions.PedDevException;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eventbus.EmvCallbackEvent;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.ActionEnterPin.EEnterPinType;
import com.pax.pay.trans.action.ActionEnterPin.OfflinePinResult;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.pay.utils.ViewUtils;
import com.pax.sdk.Sdk;
import com.pax.view.dialog.CustomAlertDialog;

public class EnterPinActivity extends BaseActivityWithTickForAction {

    private TextView pwdTv;

    private String title;
    private String panBlock;
    private String prompt2;
    private String prompt1;
    private String totalAmount;
    private String tipAmount;
    private String currencyCode;

    private CustomAlertDialog promptDialog;

    private boolean supportBypass;
    private boolean isFirstStart = true;// 判断界面是否第一次加载
    private boolean isDCC;

    private EEnterPinType enterPinType;
    private RSAPinKey rsaPinKey;

    private boolean isMasterCard;

    private static final byte ICC_SLOT = 0x00;
    public static final String OFFLINE_EXP_PIN_LEN = "0,4,5,6,7,8,9,10,11,12";

    private boolean landscape = false;

    private IPed ped = FinancialApplication.getDal().getPed(EPedType.INTERNAL);

    private IPed.IPedInputPinListener pedInputPinListener = new IPed.IPedInputPinListener() {

        @Override
        public void onKeyEvent(final EKeyCode key) {
            String temp;
            Log.d(TAG, "onKeyEvent Pin: "+key.name());
            if (key == EKeyCode.KEY_CLEAR) {
                temp = "";
            } else if (key == EKeyCode.KEY_ENTER || key == EKeyCode.KEY_CANCEL) {
                // do nothing
                return;
            } else {
                temp = pwdTv.getText().toString();
                temp += "*";
            }
            setContentText(temp);
        }
    };

    private IPed.IPedInputPinListener inputPCIPinListener = new IPed.IPedInputPinListener() {

        @Override
        public void onKeyEvent(final EKeyCode key) {
            //AET-148
            String temp = pwdTv.getText().toString();
            Log.d(TAG, "inputPCIPinListener onKeyEvent Pin: "+key.name());
            if (key == EKeyCode.KEY_CLEAR) {
                temp = "";
            } else if (key == EKeyCode.KEY_CANCEL) {
                ped.setInputPinListener(null);
                finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
                return;
            } else if (key == EKeyCode.KEY_ENTER) {
                if (temp.length() > 3 || temp.length() == 0) {
                    ped.setInputPinListener(null);
                    finish(new ActionResult(TransResult.SUCC, null));
                    return;
                }
            } else {
                temp += "*";
            }

            setContentText(temp);
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        // 界面不需要超时， 超时有输密码接口控制
        tickTimer.stop();
    }

    // 当页面加载完成之后再执行弹出键盘的动作
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus && isFirstStart) {
            FinancialApplication.getApp().runInBackground(new DetectFingerRunnable());
            isFirstStart = false;
        }
    }

    private class DetectFingerRunnable implements Runnable {
        @Override
        public void run() {
            //AET-226
            //workaround:get the touch event from by native code which may
            com.pax.sdk.Sdk.TouchEvent nte = Sdk.getInstance().getTouchEvent();
            while (nte.detect(200)) {
                FinancialApplication.getApp().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtils.showMessage(R.string.no_long_press);
                    }
                });
                SystemClock.sleep(500);
            }
            if (enterPinType == EEnterPinType.ONLINE_PIN) {
                enterOnlinePin(panBlock, supportBypass, isDCC, isMasterCard);
            } else if (enterPinType == EEnterPinType.OFFLINE_CIPHER_PIN) {
                enterOfflineCipherPin();
            } else if (enterPinType == EEnterPinType.OFFLINE_PLAIN_PIN) {
                enterOfflinePlainPin();
            } else if (enterPinType == EEnterPinType.OFFLINE_PCI_MODE) {
                enterOfflinePCIMode();
            }
        }

        private void enterOnlinePin(final String panBlock, final boolean supportBypass, final boolean isDCC,
                final boolean isMasterCard) {
            FinancialApplication.getApp().runInBackground(new OnlinePinRunnable(panBlock, supportBypass, isDCC, isMasterCard));
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_input_pin;
    }

    @Override
    protected void loadParam() {
        title = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        prompt1 = getIntent().getStringExtra(EUIParamKeys.PROMPT_1.toString());
        prompt2 = getIntent().getStringExtra(EUIParamKeys.PROMPT_2.toString());
        totalAmount = getIntent().getStringExtra(EUIParamKeys.TRANS_AMOUNT.toString());
        tipAmount = getIntent().getStringExtra(EUIParamKeys.TIP_AMOUNT.toString());
        currencyCode = getIntent().getStringExtra(EUIParamKeys.CURRENCY_CODE.toString());
        isDCC = getIntent().getBooleanExtra(EUIParamKeys.IS_DCC.toString(), false);
        isMasterCard = getIntent().getBooleanExtra(EUIParamKeys.IS_MASTERCARD.toString(), false);
        enterPinType = (EEnterPinType) getIntent().getSerializableExtra(EUIParamKeys.ENTERPINTYPE.toString());
        if (enterPinType == EEnterPinType.ONLINE_PIN) {
            panBlock = getIntent().getStringExtra(EUIParamKeys.PANBLOCK.toString());
            supportBypass = getIntent().getBooleanExtra(EUIParamKeys.SUPPORTBYPASS.toString(), false);
        } else {
            rsaPinKey = getIntent().getParcelableExtra(EUIParamKeys.RSA_PIN_KEY.toString());
        }
    }

    @Override
    protected String getTitleString() {
        return title;
    }

    @Override
    protected void initViews() {
        landscape = !ViewUtils.isScreenOrientationPortrait(EnterPinActivity.this);

        enableActionBar(false);

        TextView totalAmountTv = (TextView) findViewById(R.id.total_amount_txt);
        LinearLayout totalAmountLayout = (LinearLayout) findViewById(R.id.trans_total_amount_layout);
        if (totalAmount != null && !totalAmount.isEmpty()) {
            totalAmount = CurrencyConverter.convert(Utils.parseLongSafe(totalAmount, 0), CurrencyCode.getByCode(currencyCode));
            totalAmountTv.setText(totalAmount);
        } else {
            totalAmountLayout.setVisibility(View.INVISIBLE);
        }

        TextView tipAmountTv = (TextView) findViewById(R.id.tip_amount_txt);
        LinearLayout tipAmountLayout = (LinearLayout) findViewById(R.id.trans_tip_amount_layout);
        if (tipAmount != null && !tipAmount.isEmpty()) {
            tipAmount = CurrencyConverter.convert(Utils.parseLongSafe(tipAmount, 0), CurrencyCode.getByCode(currencyCode));
            tipAmountTv.setText(tipAmount);
        } else {
            tipAmountLayout.setVisibility(View.INVISIBLE);
        }

        TextView promptTv1 = (TextView) findViewById(R.id.prompt_title);
        promptTv1.setText(prompt1);

        TextView promptTv2 = (TextView) findViewById(R.id.prompt_no_pin);
        if (prompt2 != null) {
            promptTv2.setText(prompt2);
        } else {
            promptTv2.setVisibility(View.INVISIBLE);
        }

        pwdTv = (TextView) findViewById(R.id.pin_input_text);
    }

    @Override
    protected void setListeners() {
        //do nothing
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (!isFirstStart) {
            FinancialApplication.getApp().runInBackground(new DetectFingerRunnable());
            pwdTv.setText("");
        }

    }

    public void setContentText(final String content) {
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                Log.d(TAG, "start set pin: "+pwdTv);
                if (pwdTv != null) {
                    pwdTv.setText(content);
                    pwdTv.setTextSize(FinancialApplication.getApp().getResources().getDimension(R.dimen.font_size_key));
                }
            }
        });
    }

    public void enterOfflineCipherPin() {
        FinancialApplication.getApp().runInBackground(new OfflineCipherPinRunnable());
    }

    public void enterOfflinePlainPin() {
        FinancialApplication.getApp().runInBackground(new OfflinePlainPinRunnable());
    }

    public void enterOfflinePCIMode() {
        try {
            ped.setIntervalTime(1, 1);
            ped.setInputPinListener(inputPCIPinListener);
            FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.OFFLINE_PIN_ENTER_READY));
        } catch (PedDevException e) {
            Log.e(TAG, "", e);
            OfflinePinResult offlinePinResult = new OfflinePinResult();
            offlinePinResult.setRet(e.getErrCode());
            finish(new ActionResult(TransResult.ERR_ABORTED, offlinePinResult));
        }
    }

    private class OnlinePinRunnable implements Runnable {
        private final String onlinePanBlock;
        private final boolean isSupportBypass;
        private final boolean isDCC;

        private final boolean isMasterCard;

        OnlinePinRunnable(final String panBlock, final boolean supportBypass, final boolean isDCC, final boolean isMasterCard) {
            this.onlinePanBlock = panBlock;
            this.isSupportBypass = supportBypass;
            this.isDCC = isDCC;
            this.isMasterCard = isMasterCard;
            ped.setInputPinListener(pedInputPinListener);
        }

        @Override
        public void run() {
            try {
                ped.setIntervalTime(1, 1);
                byte[] pinData = Device.getPinBlock(onlinePanBlock, isSupportBypass, landscape, isMasterCard);
                if (pinData == null || pinData.length == 0)
                    finish(new ActionResult(TransResult.SUCC, null));
                else {
                    finish(new ActionResult(TransResult.SUCC, Utils.bcd2Str(pinData)));
                }
            } catch (final PedDevException e) {
                Log.e(TAG, "", e);
                handleException(e);
            } finally {
                //no memory leak
                ped.setInputPinListener(null);
            }
        }

        private void handleException(final PedDevException e) {
            if (e.getErrCode() == EPedDevException.PED_ERR_INPUT_CANCEL.getErrCodeFromBasement()) {
                finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
            } else {

                FinancialApplication.getApp().runOnUiThread(new Runnable() {

                    @Override
                    public void run() {
                        Device.beepErr();
                        promptDialog = new CustomAlertDialog(EnterPinActivity.this, CustomAlertDialog.ERROR_TYPE);
                        promptDialog.setTimeout(3);
                        promptDialog.setContentText(e.getErrMsg());
                        promptDialog.show();
                        promptDialog.showConfirmButton(true);
                        promptDialog.setOnDismissListener(new OnDismissListener() {

                            @Override
                            public void onDismiss(DialogInterface arg0) {
                                finish(new ActionResult(TransResult.ERR_ABORTED, null));
                            }
                        });
                    }
                });
            }
        }
    }

    private abstract class OfflinePinRunnable implements Runnable {
        OfflinePinRunnable() {
            ped.setInputPinListener(pedInputPinListener);
        }

        @Override
        public void run() {
            try {
                ped.setIntervalTime(1, 1);
                ped.setKeyboardLayoutLandscape(landscape);
                byte[] resp = callPed();
                OfflinePinResult offlinePinResult = new OfflinePinResult();
                offlinePinResult.setRet(EEmvExceptions.EMV_OK.getErrCodeFromBasement());
                offlinePinResult.setRespOut(resp);
                finish(new ActionResult(TransResult.SUCC, offlinePinResult));
            } catch (PedDevException e) {
                Log.e(TAG, "", e);
                handleException(e);
            } finally {
                //no memory leak
                ped.setInputPinListener(null);
            }
        }

        private void handleException(PedDevException e) {
            if (e.getErrCode() == EPedDevException.PED_ERR_INPUT_CANCEL.getErrCodeFromBasement()) {
                finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
            } else {
                OfflinePinResult offlinePinResult = new OfflinePinResult();
                offlinePinResult.setRet(e.getErrCode());
                finish(new ActionResult(TransResult.ERR_ABORTED, offlinePinResult));
            }
        }

        abstract byte[] callPed() throws PedDevException;
    }

    private class OfflineCipherPinRunnable extends OfflinePinRunnable {
        @Override
        byte[] callPed() throws PedDevException {
            return ped.verifyCipherPin(ICC_SLOT, OFFLINE_EXP_PIN_LEN, rsaPinKey, (byte) 0x00, 60 * 1000);
        }
    }

    /**
     * Offline plain pin runnable
     */
    private class OfflinePlainPinRunnable extends OfflinePinRunnable {
        @Override
        byte[] callPed() throws PedDevException {
            return ped.verifyPlainPin(ICC_SLOT, OFFLINE_EXP_PIN_LEN, (byte) 0x00, 60 * 1000);
        }
    }
}