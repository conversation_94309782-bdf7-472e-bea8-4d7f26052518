/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans;

import android.content.Context;


import com.pax.abl.core.ActionResult;
import com.pax.commonbusiness.card.PanUtils;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.action.ActionDispTransDetail;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionInputTransData.EInputType;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.lang.ref.WeakReference;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;

public class PreAuthCancelTrans extends BaseTrans {

    private TransData origTransData;
    int inputMaxLength;
    int inputMinLength;
    String inputPrompt;

    private boolean isRRNInput = Utils.getString(R.string.edc_preauth_rrn_based).
            equals(SysParam.getInstance().getString(R.string.EDC_PREAUTH_COMPLETION));
    private PrintTask printTask;


    public PreAuthCancelTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.PREAUTHCANCEL, transListener);
    }

    @Override
    protected void bindStateOnAction() {

        ActionInputTransData enterTransNoAction = new ActionInputTransData(action -> ((ActionInputTransData) action).setParam(new WeakReference<>(getCurrentContext()), getString(R.string.trans_preAuth_cancel))
                .setInputLine(inputPrompt, EInputType.NUM, inputMaxLength, inputMinLength));
        bind(State.ENTER_ORIGINAL_NO.toString(), enterTransNoAction, true);

        // confirm information
        ActionDispTransDetail confirmInfoAction = new ActionDispTransDetail(action -> {

            String amount;
            String formattedDate = null;
            String authCode = null;
            String cardNo = null;
            if(origTransData != null){
                amount = CurrencyConverter.convert(Utils.parseLongSafe(origTransData.getAmount(), 0), origTransData.getCurrency());
                formattedDate = TimeConverter.convert(origTransData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                        Constants.TIME_PATTERN_CUSTOM);
                authCode = origTransData.getAuthCode();
                cardNo = PanUtils.maskCardNo(origTransData.getPan(), origTransData.getIssuer().getPanMaskPattern());
            }else{
                amount = CurrencyConverter.convert(Utils.parseLongSafe(transData.getAmount(), 0), transData.getCurrency());
            }

            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put(getString(R.string.history_detail_type), ETransType.PREAUTH.getTransName());
            map.put(getString(R.string.history_detail_amount), amount);
            map.put(getString(R.string.history_detail_card_no), cardNo);
            map.put(getString(R.string.history_detail_auth_code), authCode);
            map.put(getString(R.string.history_detail_ref_no), transData.getRefNo());
            map.put(getString(R.string.history_detail_invoice_no), Component.getPaddedNumber(transData.getOrigInvoiceNo(), 6));
            map.put(getString(R.string.dateTime), formattedDate);
            ((ActionDispTransDetail) action).setParam(getCurrentContext(),
                    getString(R.string.trans_preAuth_cancel), map);
        });
        bind(State.TRANS_DETAIL.toString(), confirmInfoAction, true);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(action -> ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0));

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);

        ActionTransOnline inquiryOnlineAction = new ActionTransOnline(action -> ((ActionTransOnline) action).setParam(getCurrentContext(), transData, ETransType.TRANS_INQUIRY,0));
        bind(State.INQUIRY_ONLINE.toString(), inquiryOnlineAction, false);

        // signature action
        ActionSignature signatureAction = new ActionSignature(action -> ((ActionSignature) action).setParam(getCurrentContext(), transData.isDcc() ? transData.getInternationalCurrencyAmount() : transData.getAmount(),
                transData.isDcc() ? transData.getInternationalCurrency().getCode() : transData.getLocalCurrency().getCode()));
        bind(State.SIGNATURE.toString(), signatureAction);
        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(action -> ((ActionOfflineSend) action).setParam(getCurrentContext()));
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);

        //e charge slip
        EChargeSlipTask chargeSlipTask = new EChargeSlipTask(getCurrentContext(), getString(R.string.trans_preAuth_cancel), transData, EChargeSlipTask.genTransEndListener(PreAuthCancelTrans.this, State.E_CHARGE_SLIP.toString()));
        bind(State.E_CHARGE_SLIP.toString(), chargeSlipTask);

        //print preview action
        printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(PreAuthCancelTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        if (isRRNInput) {
            inputPrompt = getString(R.string.prompt_input_rrn_no);
            inputMaxLength = 12;
            inputMinLength = 12;
        } else {
            inputPrompt = getString(R.string.prompt_input_invoice_no);
            inputMaxLength = 6;
            inputMinLength = 0;
        }
        gotoState(State.ENTER_ORIGINAL_NO.toString());
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);

        switch (state) {
            case ENTER_ORIGINAL_NO:
                onEnterOriginalNumber(result);
                break;
            case TRANS_DETAIL:
                gotoState(State.MAG_ONLINE.toString());
                break;
            case MAG_ONLINE: //  subsequent processing of online
                // update original trans data
                updateOriginalTransData();
                toSignOrPrint();
                break;
            case INQUIRY_ONLINE:
                if(result.getRet() != TransResult.SUCC){
                    transEnd(new ActionResult(TransResult.ERR_INQUIRY_TRANSACTION, null));
                } else {
                    gotoState(State.TRANS_DETAIL.toString());
                }
                break;
            case SIGNATURE:
                onSignature(result);
                break;
            case OFFLINE_SEND:
                toChargeSlipOrPrint();
                break;
            case E_CHARGE_SLIP:
                String chooseResult = (String) result.getData();
                if (Constants.E_CHARGE_SLIP.equals(chooseResult)) {
                    transEnd(new ActionResult(TransResult.SUCC, null));
                } else if (Constants.NO_CHARGE_SLIP.equals(chooseResult)) {
                    printTask.setParam(true);
                    gotoState(State.PRINT.toString());
                } else {
                    gotoState(State.PRINT.toString());
                }
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }

    }

    private void toChargeSlipOrPrint() {
        if (!Utils.isA50() || SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            gotoState(State.E_CHARGE_SLIP.toString());
        } else {
            gotoState(State.PRINT.toString());
        }
    }

    private void onEnterOriginalNumber(ActionResult result) {
        String originNumber = (String) result.getData();
        if (originNumber == null) {
            transEnd(new ActionResult(TransResult.ERR_NO_TRANS, null));
            return;
        }
        if (isRRNInput) {
            origTransData = GreendaoHelper.getTransDataHelper().findTransDataByRrn(originNumber);
            if (origTransData == null) {
                transData.setRefNo(originNumber);
                gotoState(State.INQUIRY_ONLINE.toString());
            } else {
                validateOrigTransData(origTransData);
            }
        } else {
            origTransData = GreendaoHelper.getTransDataHelper().
                    findTransDataByInvoiceNo(Utils.parseLongSafe(originNumber, -1));
            validateOrigTransData(origTransData);
        }
    }

    private void onSignature(ActionResult result) {
        // save signature data
        byte[] signData = (byte[]) result.getData();
        byte[] signPath = (byte[]) result.getData1();

        if (signData != null && signData.length > 0 && signPath != null) {
            transData.setSignData(signData);
            transData.setSignPath(signPath);
            // 同步签名标志一致
            if(origTransData != null) {
                origTransData.setSignFree(false);
                origTransData.setSignData(signData);
                origTransData.setSignPath(signPath);
                FinancialApplication.getApp().runInBackground(() -> GreendaoHelper.getTransDataHelper().update(origTransData));
            }
            // update trans data，save signature
            FinancialApplication.getApp().runInBackground(() -> GreendaoHelper.getTransDataHelper().update(transData));
        }

        //get offline trans data list
        List<TransData.OfflineStatus> filter = new ArrayList<>();
        filter.add(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        List<TransData> offlineTransList = GreendaoHelper.getTransDataHelper().findOfflineTransData(filter);
        if (!offlineTransList.isEmpty() &&
                !offlineTransList.get(0).getId().equals(transData.getId())) { //AET-92
            //offline send
            gotoState(State.OFFLINE_SEND.toString());
            return;
        }

        // if terminal does not support signature ,card holder does not sign or time out，print preview directly.
        toChargeSlipOrPrint();
    }

    private void validateOrigTransData(TransData origTransData) {
        if (origTransData == null || (!origTransData.getTransType().equals(ETransType.PREAUTH))) {
            // trans not exist
            transEnd(new ActionResult(TransResult.ERR_NO_ORIG_TRANS, null));
            return;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.TIME_PATTERN_TRANS, Locale.US);
        //预授权交易超过最大保存天数禁止进行预授权完成交易
        try {
            Date transDate = dateFormat.parse(origTransData.getDateTime());
            long nd = 1000L * 24 * 60 * 60;
            long day = new Date().getTime() - transDate.getTime();
            if (day > SysParam.getInstance().getInt(Utils.getString(R.string.EDC_PREAUTH_RECORD_SAVING_DAY), 15) * nd) {
                transEnd(new ActionResult(TransResult.ERR_TRANSACTION_EXPIRED, null));
                return;
            }
        } catch (ParseException e) {
            transEnd(new ActionResult(TransResult.ERR_TRANSACTION_EXPIRED, null));
            return;
        }

        copyOrigTransData();
        gotoState(State.TRANS_DETAIL.toString());
    }

    private void copyOrigTransData() {
        //由于原流程有寻卡流程，因此移除了很多拷贝原数据的动作，这里参考其他交易全部粘贴回来。
        transData.setOrigAmount(origTransData.getAmount());
        transData.setAmount(transData.getOrigAmount());
        transData.setOrigBatchNo(origTransData.getBatchNo());
        transData.setOrigAuthCode(origTransData.getAuthCode());
        transData.setOrigRefNo(origTransData.getRefNo());
        transData.setRefNo(origTransData.getRefNo());
        transData.setAuthCode(origTransData.getAuthCode());
        transData.setOrigTransNo(origTransData.getTraceNo());
        transData.setPan(origTransData.getPan());
        transData.setExpDate(origTransData.getExpDate());
        transData.setAcquirer(origTransData.getAcquirer());
        transData.setInvoiceNo(origTransData.getInvoiceNo());
        transData.setIssuer(origTransData.getIssuer());
        transData.setOrigTransType(origTransData.getTransType());
        transData.setOrigInvoiceNo(origTransData.getInvoiceNo());
        transData.setIssuerScriptResults(origTransData.getIssuerScriptResults());
        transData.setSendIccData(origTransData.getSendIccData());
        transData.setEmvAppName(origTransData.getEmvAppName());
        transData.setEmvAppLabel(origTransData.getEmvAppLabel());
        transData.setTc(origTransData.getTc());
        transData.setAid(origTransData.getAid());
        transData.setTvr(origTransData.getTvr());
        transData.setTsi(origTransData.getTsi());
        transData.setEmvResult(origTransData.getEmvResult());
        transData.setFullOnline(origTransData.isFullOnline());
        transData.setEnterMode(origTransData.getEnterMode());
        transData.setTrack2(origTransData.getTrack2());
        transData.setDcc(origTransData.isDcc());
        transData.setInternationalCurrency(origTransData.getInternationalCurrency());
        transData.setMakeUp(origTransData.getMakeUp());
        transData.setCommission(origTransData.getCommission());
        transData.setRate(origTransData.getRate());
        transData.setAccountType(origTransData.getAccountType());
        transData.setInternationalCurrencyAmount(origTransData.getInternationalCurrencyAmount());
        transData.setCardHolderName(origTransData.getCardHolderName());
    }

    private void toSignOrPrint() {
        if (Component.isSignatureFree(transData) || transData.isHasPin()) {// 免签
            // 打印
            transData.setSignFree(true);
            toChargeSlipOrPrint();
        } else {
            // 电子签名
            transData.setSignFree(false);
            gotoState(State.SIGNATURE.toString());
        }
        // origTransData为空证明是RRN远端查询，是新增交易记录，因此需要自增InvoiceNo
        if(origTransData == null) {
            Component.incInvoiceNo();
        }
        FinancialApplication.getApp().runInBackground(() -> GreendaoHelper.getTransDataHelper().update(transData));
    }

    private void updateOriginalTransData() {
        if(origTransData != null) {
            origTransData.setTransType(transType);
            origTransData.setTraceNo(transData.getTraceNo());
            origTransData.setAuthCode(transData.getAuthCode());
            origTransData.setAmount(transData.getAmount());
            origTransData.setDcc(transData.isDcc());
            origTransData.setCurrency(transData.getCurrency());
            origTransData.setInternationalCurrency(transData.getInternationalCurrency());
            origTransData.setInternationalCurrencyAmount(transData.getInternationalCurrencyAmount());
            origTransData.setDateTime(transData.getDateTime());
            FinancialApplication.getApp().runInBackground(() -> {
                GreendaoHelper.getTransDataHelper().delete(transData);
                GreendaoHelper.getTransDataHelper().update(origTransData);
            });
        }
    }

    enum State {
        ENTER_ORIGINAL_NO,
        TRANS_DETAIL,
        MAG_ONLINE,
        INQUIRY_ONLINE,
        SIGNATURE,
        OFFLINE_SEND,
        PRINT,
        E_CHARGE_SLIP,
    }
}
