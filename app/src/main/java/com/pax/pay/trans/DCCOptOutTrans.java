/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-8
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans;

import android.content.Context;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.edc.R;
import com.pax.edc.opensdk.RequestTraceNo;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.service.RequestUtil;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.math.BigDecimal;
import java.util.LinkedHashMap;

/**
 * The type Dcc opt out trans.
 * convert DCC trans to DCD trans
 */
public class DCCOptOutTrans extends BaseTrans {

    private String pwd;
    private String origTransNo;
    private String adjustAmount;

    private TransData origTransData;

    private boolean isNeedInputTransNo = true;
    private boolean isNeedEnterAdjustAmount = true;

    /**
     * Instantiates a new Dcc opt out trans.
     *
     * @param context       the context
     * @param transListener the trans listener
     */
    public DCCOptOutTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.DCC_OPT_OUT, transListener);
        isNeedInputTransNo = true;
        isNeedEnterAdjustAmount = true;
    }

    public DCCOptOutTrans(Context context, RequestTraceNo request, TransEndListener transListener) {
        super(context, ETransType.DCC_OPT_OUT, transListener);
        isNeedInputTransNo = false;
        isNeedEnterAdjustAmount = true;
        origTransNo = request.getTraceNo();
        isThirdMode = true;
        thirdPartyBaseRequest = request;
    }

    @Override
    protected void bindStateOnAction() {

        if (adjustAmount == null || adjustAmount.isEmpty()) {
            isNeedEnterAdjustAmount = true;
        }

        //input manager password
        ActionInputPassword inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(getCurrentContext(), 6,
                        getString(R.string.prompt_dccOptOut_pwd), null);
            }
        });
        bind(DCCOptOutTrans.State.INPUT_PWD.toString(), inputPasswordAction, true);

        //input original trance no
        ActionInputTransData enterTransNoAction = new ActionInputTransData(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                Context context = getCurrentContext();
                ((ActionInputTransData) action).setParam(context, getString(R.string.trans_dcc_opt_out))
                        .setInputLine(getString(R.string.prompt_input_transno), ActionInputTransData.EInputType.NUM, 6, true);
            }
        });
        bind(DCCOptOutTrans.State.ENTER_TRANSNO.toString(), enterTransNoAction, true);

        //enter adjust amount
        ActionInputTransData newTotalAmountAction = new ActionInputTransData(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                String title = getString(R.string.trans_dcc_opt_out);
                String transAmount = String.valueOf(origTransData.getAmount());
                String transTips = String.valueOf(origTransData.getTipAmount());
                float adjustPercent = origTransData.getIssuer().getAdjustPercent();
                boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP);

                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                map.put(getString(R.string.prompt_total_amount), transAmount);
                map.put(getString(R.string.prompt_ori_tips), transTips);
                map.put(getString(R.string.prompt_adjust_percent), Float.toString(adjustPercent));
                map.put(getString(R.string.prompt_currency_symbol), origTransData.getLocalCurrency().getSymbol());

                ((ActionInputTransData) action).setParam(getCurrentContext(), title, map, true, enableTip).setInputLine(
                        getString(R.string.prompt_new_total_amount), ActionInputTransData.EInputType.DCC_OPTOUT_AMOUNT, Constants.AMOUNT_DIGIT, false);
            }
        });
        bind(DCCOptOutTrans.State.ENTER_ADJUST_AMOUNT.toString(), newTotalAmountAction, true);

        //print preview action
        PrintTask printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(DCCOptOutTrans.this, State.PRINT.toString()));
        bind(DCCOptOutTrans.State.PRINT.toString(), printTask);

        // if need pwd for adjust
        if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_DCCOPTOUT_PWD)) {
            if (pwd != null && !pwd.isEmpty()) {
                onInputPwd(new ActionResult(TransResult.SUCC, pwd));
                return;
            }
            gotoState(DCCOptOutTrans.State.INPUT_PWD.toString());
        } else if (isNeedInputTransNo) {// need input trans NO
            gotoState(DCCOptOutTrans.State.ENTER_TRANSNO.toString());
        } else {
            validateOrigTransData(Utils.parseLongSafe(origTransNo, -1));
            toEnterAdjustAmount();
        }
    }

    private void setThirdParams(){
        if (!isThirdMode){
            return;
        }
        transData.setEcrRefferenceNo(thirdPartyBaseRequest.getTransRefNo());
        transData.setTransTypeDetail(thirdPartyBaseRequest.getTransTypeDetail());
        transData.setEcrMsgType((byte)thirdPartyBaseRequest.getTransType());
        transData.setEcrOrigTransType(get3rdOrigTransType());

    }
    private byte get3rdOrigTransType() {
        return (byte) RequestUtil.getType(origTransData.getTransType(), thirdPartyBaseRequest.getTransTypeDetail());
    }

    /**
     * The enum State.
     */
    enum State {
        /**
         * Input pwd state.
         */
        INPUT_PWD,
        /**
         * Enter transno state.
         */
        ENTER_TRANSNO,
        /**
         * Enter adjust amount state.
         */
        ENTER_ADJUST_AMOUNT,
        /**
         * Print state.
         */
        PRINT,
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);
        switch (state) {
            case INPUT_PWD:
                onInputPwd(result);
                break;
            case ENTER_TRANSNO:
                onEnterTransNo(result);
                break;
            case ENTER_ADJUST_AMOUNT:
                onEnterAdjustAmount(result);
                break;
            case PRINT:
                onPrint(result);
                break;
            default:
                transEnd(result);
                break;
        }
    }

    private void onInputPwd(ActionResult result) {
        String data = EncUtils.pwdSha((String) result.getData());
        if (!data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_DCC_OPT_OUT_PWD))) {
            transEnd(new ActionResult(TransResult.ERR_PASSWORD, null));
            return;
        }
        if (isNeedInputTransNo) {
            gotoState(DCCOptOutTrans.State.ENTER_TRANSNO.toString());
        } else {
            validateOrigTransData(Utils.parseLongSafe(origTransNo, -1));
            toEnterAdjustAmount();
        }
    }

    private void onEnterTransNo(ActionResult result) {
        String content = (String) result.getData();
        long InvoiceNo;
        if (content == null) {
            TransData lastTransData = FinancialApplication.getTransDataDbHelper().findLastTransData();
            if (lastTransData == null) {
                transEnd(new ActionResult(TransResult.ERR_NO_TRANS, null));
                return;
            }
            InvoiceNo = lastTransData.getInvoiceNo();
        } else {
            InvoiceNo = Utils.parseLongSafe(content, -1);
        }
        validateOrigTransData(InvoiceNo);
        toEnterAdjustAmount();
    }

    private void toEnterAdjustAmount() {
        if (isNeedEnterAdjustAmount) {
            gotoState(DCCOptOutTrans.State.ENTER_ADJUST_AMOUNT.toString());
        } else {
            long maxValue = 99999999999999L;
            long adjustTotalAmount = Utils.parseLongSafe(adjustAmount.replace(".", ""), 0);
            long origTotalAmount = origTransData.getAmount();
            long origTipAmount = origTransData.getTipAmount();
            long baseAmount = origTotalAmount - origTipAmount;
            long newTipAmount;
            if (adjustTotalAmount > maxValue || adjustTotalAmount < baseAmount) {
                transEnd(new ActionResult(TransResult.ERR_AMOUNT, null));
                return;
            }
            //get new tip amount
            newTipAmount = adjustTotalAmount - baseAmount;

            BigDecimal hundredBd = new BigDecimal(100);
            BigDecimal percentBd = BigDecimal.valueOf(origTransData.getIssuer().getAdjustPercent());
            BigDecimal baseAmountBd = new BigDecimal(baseAmount);
            BigDecimal maxTipsBd = baseAmountBd.multiply(percentBd).divide(hundredBd, 2);
            BigDecimal tipAmountBd = new BigDecimal(newTipAmount);

            //check tip amount
            if (maxTipsBd.doubleValue() < tipAmountBd.doubleValue()) {
                transEnd(new ActionResult(TransResult.ERR_AMOUNT, null));
                return;
            }
            transSuccess(adjustTotalAmount, newTipAmount);
        }
    }

    private void onEnterAdjustAmount(ActionResult result) {
        long newTotalAmount = (long) result.getData();
        long newTipAmount = (long) result.getData1();
        transSuccess(newTotalAmount, newTipAmount);
    }

    // check original transaction information
    private void validateOrigTransData(long InvoiceNo) {
        origTransData = FinancialApplication.getTransDataDbHelper().findTransDataByInvoiceNo(InvoiceNo);
        if (origTransData == null) {
            // no original transaction
            transEnd(new ActionResult(TransResult.ERR_NO_ORIG_TRANS, null));
            return;
        }

        ETransType trType = origTransData.getTransType();
        if (trType != ETransType.SALE && trType != ETransType.OFFLINE_TRANS_SEND) {
            transEnd(new ActionResult(TransResult.ERR_DCC_OPTOUT_UNSUPPORTED, null));
            return;
        }

        if (!origTransData.isDcc()) {
            transEnd(new ActionResult(TransResult.ERR_DCC_OPTOUT_UNSUPPORTED, null));
            return;
        }

        TransData.ETransStatus trStatus = origTransData.getTransState();
        if (trStatus.equals(TransData.ETransStatus.VOIDED)) {
            transEnd(new ActionResult(TransResult.ERR_DCC_OPTOUT_UNSUPPORTED, null));
        }
        setThirdParams();
    }

    private void transSuccess(long newTotalAmount, long newTipAmount) {
        Component.transSuccess(newTotalAmount, newTipAmount, origTransData, transData, true);

        //error checking
        if (transData.getAcquirer() == null) {
            transEnd(new ActionResult(TransResult.ERR_NO_MATCH_ACQUIRER, null));
            return;
        }

        if (Component.chkSaleRRNNoExist(transData)) {
            transEnd(new ActionResult(TransResult.ERR_UNPACK, null));
            return;
        }

        boolean isInsertSuccess = FinancialApplication.getTransDataDbHelper().insertTransData(transData);
        if (!isInsertSuccess) {
            transEnd(new ActionResult(TransResult.ERR_DATABASE_OPERATE, null));
            return;
        }

        Component.origTransDataInit(origTransData);

        Component.incInvoiceNo();
        goPrint();
    }

    private void onPrint(ActionResult result) {
        if (result.getRet() == TransResult.SUCC) {
            afterPrint();
        } else {
            dispResult(transType.getTransName(), result, null);
            gotoState(DCCOptOutTrans.State.PRINT.toString());
        }
    }

    private void goPrint() {
        gotoState(DCCOptOutTrans.State.PRINT.toString());
    }

    private void afterPrint() {
        // end trans
        if (!isThirdMode) {
            transEnd(new ActionResult(TransResult.SUCC, null));
        } else {
            transEnd(new ActionResult(TransResult.SUCC, transData, origTransData));
        }
    }
}
