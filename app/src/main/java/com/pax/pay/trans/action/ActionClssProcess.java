/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-2-28
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.util.Log;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.device.Device;
import com.pax.device.DeviceImplNeptune;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.IClss;
import com.pax.eemv.clss.ClssImpl;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.eventbus.ClssCallbackEvent;
import com.pax.eventbus.SearchCardEvent;
import com.pax.eventbus.SettlePendingEvent;
import com.pax.jemv.clcommon.RetCode;
import com.pax.jemv.device.DeviceManager;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.clss.ClssListenerImpl;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransData.EnterMode;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.ResponseCode;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.view.ClssLight;
import com.pax.view.dialog.DialogUtils;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * The type Action clss process.
 */
public class ActionClssProcess extends AAction {
    private Context context;
    private IClss clss;
    private TransData transData;
    private TransProcessListener transProcessListener;
    private ClssListenerImpl clssListener;

    /**
     * Instantiates a new Action clss process.
     *
     * @param listener the listener
     */
    public ActionClssProcess(ActionStartListener listener) {
        super(listener);
    }

    /**
     * Sets param.
     *
     * @param context the context
     * @param clss the clss
     * @param transData the trans data
     */
    public void setParam(Context context, IClss clss, TransData transData) {
        this.context = context;
        this.clss = clss;
        this.transData = transData;
        transProcessListener = new TransProcessListenerImpl(context);
        clssListener = new ClssListenerImpl(context, clss, transData, transProcessListener);
    }

    /**
     * On card num confirm event.
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCardNumConfirmEvent(ClssCallbackEvent event) {
        switch ((ClssCallbackEvent.Status) event.getStatus()) {
            case CARD_NUM_CONFIRM_SUCCESS:
                clssListener.readCardSuccess();
                break;
            case CARD_NUM_CONFIRM_ERROR:
                clssListener.readCardFail();
                break;
            default:
                break;
        }
    }

    /**
     * On settle pending.
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSettlePending(SettlePendingEvent event) {
        SettlePendingEvent.Status status = (SettlePendingEvent.Status) event.getStatus();
        if (status == SettlePendingEvent.Status.SETTLE_PENDING_FOR_CLSS) {
            transProcessListener.onHideProgress();
            DialogUtils.showErrMessage(context, context.getString(R.string
                            .err_settlement_pending_title),
                    context.getString(R.string.err_settlement_pending_msg),
                    new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            clssListener.readCardFail();
                        }
                    }, Constants.FAILED_DIALOG_SHOW_TIME);
        }
    }

    @Override
    protected void process() {
        DeviceManager.getInstance().setIDevice(DeviceImplNeptune.getInstance());
        FinancialApplication.getApp().runInBackground(new ProcessRunnable());
    }

    private class ProcessRunnable implements Runnable {
        private final ClssTransProcess clssTransProcess;

        /**
         * Instantiates a new Process runnable.
         */
        ProcessRunnable() {
            if (transData.getEnterMode() == EnterMode.CLSS) {
                transProcessListener.onShowProgress(context.getString(R.string.wait_process), 0);
            }
            clssTransProcess = new ClssTransProcess(clss);
        }

        @Override
        public void run() {
            try {
                FinancialApplication.getApp().register(ActionClssProcess.this);
                FinancialApplication.getApp().doEvent(new SearchCardEvent(SearchCardEvent.Status.CLSS_LIGHT_STATUS_PROCESSING));
                boolean isIntendedOfflineMode = FinancialApplication.getBaseManager().isIntendedOfflineMode();
                //设置transData的IntendedOffline标签，传入clssTransProcess
                if(isIntendedOfflineMode){
                    transData.setIntendedOffline(true);
                }else {
                    transData.setIntendedOffline(false);
                }
                CTransResult result = clssTransProcess.transProcess(transData, clssListener);
                //读取到卡号之后，如果是Intended Offline则进行中断，进行半EMV流程
                if(transData.getPan() != null && isIntendedOfflineMode && ClssImpl.isEMVAcquirer){
                    setResult(new ActionResult(TransResult.SUCC, new CTransResult(ETransResult.ONLINE_APPROVED)));
                    return;
                }
                transProcessListener.onHideProgress();

                ETransResult eTransResult = result.getTransResult();
                if (eTransResult == ETransResult.ONLINE_DENIED) {
                    ResponseCode rspCode = ResponseCode.getInstance().parse(transData.getResponseCode());
                    transProcessListener.onShowErrMessage(
                            Utils.getString(R.string.prompt_err_code) + rspCode.toString(),
                            Constants.FAILED_DIALOG_SHOW_TIME, true);
                } else if (eTransResult == ETransResult.ABORT_TERMINATED) {
                    setResult(new ActionResult(TransResult.ERR_ABORTED, result));
                    return;
                } else if (eTransResult == ETransResult.ONLINE_RECV_FAIL
                        || eTransResult == ETransResult.ERR_TIME_OUT
                        || eTransResult == ETransResult.ERR_UNPACK
                        || eTransResult == ETransResult.CLSS_OC_TRY_ANOTHER_INTERFACE) {
                    setResult(new ActionResult(TransResult.SUCC, result));
                    return;
                }

                updateReversalStatus();
                setResult(new ActionResult(TransResult.SUCC, result));
                FinancialApplication.getApp().doEvent(new SearchCardEvent(SearchCardEvent.Status.CLSS_LIGHT_STATUS_COMPLETE));
            } catch (EmvException e) {
                Log.e(TAG, "", e);
                transProcessListener.onHideProgress();
                ActivityStack.getInstance().popTo((Activity) context);//到search card Activity来弹框，而不是在EnterPinActivity弹框，弹到一般EnterPinActivity就销毁，导致窗体泄漏Crash
                if (transData.isOnlineTrans() && Component.isDemo()) {
                    updateReversalStatus();
                    setResult(new ActionResult(TransResult.SUCC, new CTransResult(ETransResult.ONLINE_APPROVED)));
                    return;
                }

                //非接拍卡失败时, 提示beepErr
                if (e.getErrCode() == EEmvExceptions.EMV_ERR_ICC_CMD.getErrCodeFromBasement()) {
                    Device.beepErr();
                }
                if (e.getErrCode() == EEmvExceptions.EMV_ERR_CLSS_CARD_EXPIRED.getErrCodeFromBasement()) {
                    setResult(new ActionResult(TransResult.ERR_CARD_EXPIRED, new CTransResult(ETransResult.CLSS_EXP_CARD)));
                    return;
                } else if (e.getErrCode() == EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement()) {
                    setResult(new ActionResult(TransResult.ERR_USER_CANCEL, new CTransResult(ETransResult.USER_CANCEL)));
                    return;
                } else if (e.getErrCode() == EEmvExceptions.EMV_ERR_CLSS_TRY_ANOTHER_CARD.getErrCodeFromBasement()) {
                    setResult(new ActionResult(TransResult.ERR_CARD_UNSUPPORTED,
                            new CTransResult(ETransResult.ERR_CARD_UNSUPPORTED)));
                    return;
                } else if (e.getErrCode()
                        == EEmvExceptions.EMV_ERR_NOT_HASE_CARD.getErrCodeFromBasement()) {
                    setResult(new ActionResult(TransResult.ECR_NOT_HASE_CARD,
                            new CTransResult(ETransResult.NOT_HASE_CARD)));
                    return;
                } else if (e.getErrCode()
                        == EEmvExceptions.EMV_ERR_TRANS_NOT_ACCEPTED.getErrCodeFromBasement()) {
                    setResult(new ActionResult(TransResult.TRANS_NOT_ACCEPT,
                            new CTransResult(ETransResult.TRANS_NOT_ACCEPT)));
                    return;
                } else if (e.getErrCode()
                        == EEmvExceptions.EMV_ERR_FALL_BACK.getErrCodeFromBasement()) {
                    if (transData.getAcquirer().getName() != null) {
                        if (transData.getAcquirer().getName().equals("HASE_CUP")) {
                            transProcessListener.onShowNormalMessage(
                                    context.getString(R.string.prompt_card_not_support_cup_aid),
                                    Constants.SUCCESS_DIALOG_SHOW_TIME, true);
                        }
                    }
                    transData.setEnterMode(EnterMode.FALLBACK);
                    setResult(new ActionResult(TransResult.NEED_FALL_BACK, null));
                    return;
                }else if (e.getErrCode() == EEmvExceptions.EMV_ERR_TIMEOUT.getErrCodeFromBasement()) {
                    //添加超时判断
                    setResult(new ActionResult(TransResult.ERR_TIMEOUT, new CTransResult(ETransResult.ERR_TIME_OUT)));
                    return;
                } else if (e.getErrCode() <= RetCode.ICC_RESET_ERR) {
                    transProcessListener.onShowNormalMessage(
                            EEmvExceptions.getErrMsgFromErrCode(e.getErrCode()),
                            Constants.FAILED_DIALOG_SHOW_TIME, true);
                    setResult(new ActionResult(TransResult.APPLICATION_REJECTION, new CTransResult(ETransResult.APPLICATION_REJECTION)));
                    return;
                }
                setResult(new ActionResult(TransResult.ERR_ABORTED, null));
            } catch (Exception e) {
                Log.e(TAG, "", e);
                transProcessListener.onHideProgress();
                if (transData.isOnlineTrans() && Component.isDemo()) {
                    updateReversalStatus();
                    setResult(new ActionResult(TransResult.SUCC, new CTransResult(ETransResult.ONLINE_APPROVED)));
                    return;
                }
                FinancialApplication.getApp().doEvent(new SearchCardEvent(SearchCardEvent.Status.CLSS_LIGHT_STATUS_ERROR));
                setResult(new ActionResult(TransResult.ERR_ABORTED, null));
            } finally {
                Device.setPiccLed(-1, ClssLight.OFF);
                byte[] value95 = clss.getTlv(0x95);
                byte[] value9B = clss.getTlv(0x9B);

                Log.e("TLV", "95:" + Utils.bcd2Str(value95));
                Log.e("TLV", "9b:" + Utils.bcd2Str(value9B));

                // no memory leak
                clss.setListener(null);
                FinancialApplication.getApp().unregister(ActionClssProcess.this);
            }
        }

        /**
         * update reversal status
         */
        private void updateReversalStatus() {
            transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            transData.setDupReason("");
            FinancialApplication.getTransDataDbHelper().updateTransData(transData);
        }
    }
}
