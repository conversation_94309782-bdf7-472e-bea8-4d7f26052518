/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.pax.abl.core.AAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.InputTransData1Activity;
import com.pax.pay.trans.action.activity.InputTransDataTipActivity;
import com.pax.pay.trans.action.activity.PaperlessActivity;
import com.pax.sbipay.R;

import java.lang.ref.WeakReference;
import java.util.Map;

public class ActionInputTransData extends AAction {
    private WeakReference<Context> weakReference;
    private String title;
    private String prompt;
    private EInputType inputType;
    private int maxLen;
    private int minLen;
    private boolean isAuthZero;
    private String followOrigData;
    private String errorPromMsg;
    private boolean needAudio;
    private Map<String, String> map;

    public ActionInputTransData(ActionStartListener listener) {
        super(listener);
    }

    public ActionInputTransData setParam(WeakReference<Context> weakReference, String title) {
        this.weakReference = weakReference;
        this.title = title;
        return this;
    }

    public ActionInputTransData setParam(WeakReference<Context> weakReference, String title,boolean needAudio) {
        this.weakReference = weakReference;
        this.title = title;
        this.needAudio = needAudio;
        return this;
    }


    public ActionInputTransData setParam(Context context, String title, Map<String, String> map) {
        this.weakReference = new WeakReference<>(context);
        this.title = title;
        this.map = map;
        return this;
    }

    public ActionInputTransData setInputLine(String prompt, EInputType inputType, int maxLen) {
        return setInputLine(prompt, inputType, maxLen, 0);
    }

    public ActionInputTransData setInputLine(String prompt, EInputType inputType, int maxLen, int minLen) {
        this.prompt = prompt;
        this.inputType = inputType;
        this.maxLen = maxLen;
        this.minLen = minLen;
        return this;
    }

    public ActionInputTransData setInputLine(String prompt, EInputType inputType, int maxLen, int minLen, boolean isAuthZero) {
        this.prompt = prompt;
        this.inputType = inputType;
        this.maxLen = maxLen;
        this.minLen = minLen;
        this.isAuthZero = isAuthZero;
        return this;
    }


    //传入对比数据，数据不相同则不返回result
    public ActionInputTransData setInputLine(String prompt, EInputType inputType, int maxLen, int minLen, String followData, String errorPromMsg) {
        this.prompt = prompt;
        this.inputType = inputType;
        this.maxLen = maxLen;
        this.minLen = minLen;
        this.followOrigData = followData;
        this.errorPromMsg = errorPromMsg;
        return this;
    }

    @Override
    protected void process() {

        FinancialApplication.getApp().runOnUiThread(new ProcessRunnable());
    }

    /**
     * 输入数据类型定义
     *
     * <AUTHOR>
     */
    public enum EInputType {
        AMOUNT,
        NUM, // 数字
        TEXT, // 所有类型
        PHONE,
        EMAIL,
    }

    private class ProcessRunnable implements Runnable {

        @Override
        public void run() {
            if (inputType == EInputType.PHONE || inputType == EInputType.EMAIL) {
                runPaperless();
            } else if (inputType == EInputType.AMOUNT) {
                runTipStyle();
            } else {
                runStyle1();
            }
        }

        private void runPaperless() {
            Intent intent = new Intent(weakReference.get(), PaperlessActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
            bundle.putString(EUIParamKeys.PROMPT_1.toString(), prompt);
            bundle.putSerializable(EUIParamKeys.INPUT_TYPE.toString(), inputType);
            intent.putExtras(bundle);
            if (weakReference.get() != null) {
                weakReference.get().startActivity(intent);
            }
        }

        private void runStyle1() {
            Intent intent = new Intent(weakReference.get(), InputTransData1Activity.class);
            Bundle bundle = new Bundle();
            bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
            bundle.putString(EUIParamKeys.PROMPT_1.toString(), prompt);
            bundle.putInt(EUIParamKeys.INPUT_MAX_LEN.toString(), maxLen);
            bundle.putInt(EUIParamKeys.INPUT_MIN_LEN.toString(), minLen);
            bundle.putSerializable(EUIParamKeys.INPUT_TYPE.toString(), inputType);
            bundle.putBoolean(EUIParamKeys.INPUT_PADDING_ZERO.toString(), isAuthZero);
            bundle.putBoolean(EUIParamKeys.NEED_AUDIO.toString(), needAudio);
            bundle.putString(EUIParamKeys.ORIG_FOLLOW_DATA.toString(), followOrigData);
            bundle.putString(EUIParamKeys.ERROR_MSG.toString(), errorPromMsg);
            intent.putExtras(bundle);
            if (weakReference.get() != null) {
                weakReference.get().startActivity(intent);
            }
        }

        private void runTipStyle() {
            Intent intent = new Intent(weakReference.get(), InputTransDataTipActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
            bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
            if (map != null && weakReference.get() != null) {
                String totalAmount = map.get(weakReference.get().getString(R.string.prompt_total_amount));
                String oriTips = map.get(weakReference.get().getString(R.string.prompt_ori_tips));
                String adjustPercent = map.get(weakReference.get().getString(R.string.prompt_adjust_percent));
                bundle.putString(EUIParamKeys.TRANS_AMOUNT.toString(), totalAmount);
                bundle.putString(EUIParamKeys.ORI_TIPS.toString(), oriTips);
                bundle.putFloat(EUIParamKeys.TIP_PERCENT.toString(), Float.valueOf(adjustPercent));
            }
            intent.putExtras(bundle);
            if (weakReference.get() != null) {
                weakReference.get().startActivity(intent);
            }
        }
    }
}
