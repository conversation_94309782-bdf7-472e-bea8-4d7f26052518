package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import android.os.ConditionVariable;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.pax.abl.core.ActionResult;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransTotal;
import com.pax.mvp.contract.SettlePreviewContract;
import com.pax.mvp.presenter.SettlePreviewPresenter;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.thread.PrintDetailRunnable;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

public class SettlePreviewActivity extends BaseActivityWithTickForAction implements SettlePreviewContract.View {
    private SettlePreviewPresenter presenter;
    private String navTitle;
    private Boolean navBack = false;
    private TextView acquirerName;
    private TextView merchantName;
    private TextView merchantId;
    private TextView terminalId;
    private TextView batchNo;
    private Button confirm;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        presenter = new SettlePreviewPresenter(this);
        presenter.attachView(this);
        super.onCreate(savedInstanceState);
        if (FinancialApplication.isAutoSettlement()) {
            disableSettle();
        }
        //一进入Settlement则开启子线程进行打印详情的任务
        FinancialApplication.getApp().runInBackground(this::printDetail);
        tickTimer.stop();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_settle_preview;
    }

    @Override
    protected void loadParam() {
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        navBack = getIntent().getBooleanExtra(EUIParamKeys.NAV_BACK.toString(), false);
        presenter.init();
    }

    @Override
    protected void initViews() {
        acquirerName = findViewById(R.id.settle_acquirer_name);
        merchantName = findViewById(R.id.settle_merchant_name);
        merchantId = findViewById(R.id.settle_merchant_id);
        terminalId = findViewById(R.id.settle_terminal_id);
        batchNo = findViewById(R.id.settle_batch_num);
        confirm = findViewById(R.id.btn_confirm);
    }

    @Override
    protected void setListeners() {
        enableBackAction(navBack);
        confirm.setOnClickListener(this);
    }

    @Override
    protected void onClickProtected(View v) {
        if (v.getId() == R.id.btn_confirm) {
            presenter.confirmSettle();
        }
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        presenter.detachView();
    }

    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

    @Override
    public void setCurrAcquirerContent(Acquirer acquirer, String saleAmt, String refundAmt, String voidSaleAmt,
                                       String voidRefundAmt, String yonoTotalAmt,
                                       String bqrAmt, String reversalAmt, TransTotal total,
                                       String emiSaleAmt, String emiVoidSaleAmt) {
        acquirerName.setText(acquirer.getName());
        merchantName.setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_NAME));
        merchantId.setText(acquirer.getMerchantId());
        terminalId.setText(acquirer.getTerminalId());
        batchNo.setText(String.valueOf(SysParam.getInstance().getInt(R.string.EDC_BATCH_NO)));

        ((TextView) findViewById(R.id.sale_total_sum)).setText(String.valueOf(total.getSaleTotalNum()));
        ((TextView) findViewById(R.id.sale_total_amount)).setText(saleAmt);
        ((TextView) findViewById(R.id.refund_total_sum)).setText(String.valueOf(total.getRefundTotalNum()));
        ((TextView) findViewById(R.id.refund_total_amount)).setText(refundAmt);

        ((TextView) findViewById(R.id.void_sale_total_sum)).setText(String.valueOf(total.getSaleVoidTotalNum()));
        ((TextView) findViewById(R.id.void_sale_total_amount)).setText(voidSaleAmt);

        ((TextView) findViewById(R.id.yono_total_sum)).setText(String.valueOf(total.getYonoSaleTotalNum() + total.getYonoCashTotalNum()));
        ((TextView) findViewById(R.id.yono_total_amount)).setText(yonoTotalAmt);

        ((TextView) findViewById(R.id.emi_sale_total_sum)).setText(String.valueOf(total.getEmiSaleTotalNum()));
        ((TextView) findViewById(R.id.emi_sale_total_amount)).setText(emiSaleAmt);
        ((TextView) findViewById(R.id.emi_void_sale_total_sum)).setText(String.valueOf(total.getEmiVoidTotalNum()));
        ((TextView) findViewById(R.id.emi_void_sale_total_amount)).setText(emiVoidSaleAmt);

        ((TextView) findViewById(R.id.bqr_total_sum)).setText(String.valueOf(total.getBqrTotalNum()));
        ((TextView) findViewById(R.id.bqr_total_amount)).setText(bqrAmt);

        ((TextView) findViewById(R.id.reversal_total_sum)).setText(String.valueOf(total.getReversalTotalNum()));
        ((TextView) findViewById(R.id.reversal_total_amount)).setText(reversalAmt);
    }

    //打印详情的动作
    @Override
    public void printDetail() {
        ConditionVariable cv2 = new ConditionVariable();
        //开启子线程进行具体的打印任务
        FinancialApplication.getApp().runOnUiThread(new PrintDetailRunnable(false, cv2, this, presenter.acquirer));
        //阻塞当前线程，等待打印完成之后解锁
        cv2.block();
    }

    @Override
    public void disableSettle() {
        // 禁止点击，自动结算，防止异常
        confirm.setClickable(false);
        enableBackAction(false);
    }
}
