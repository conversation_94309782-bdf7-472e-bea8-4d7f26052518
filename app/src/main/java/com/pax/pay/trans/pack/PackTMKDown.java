package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.util.Log;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.device.DeviceImplNeptune;
import com.pax.gl.pack.IIso8583;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.RSAKeyData;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.security.SecureRandom;
import java.util.Arrays;

/**
 * Created by zhouhong on 2018/1/9.
 */

public class PackTMKDown extends PackIso8583 {

    public PackTMKDown(PackListener listener) {
        super(listener);
    }

    /**
     * get TMKDown RequiredFields
     *
     * @return int[]
     */
    @Override
    protected int[] getRequiredFields() {
        return new int[] { 11, 24, 41, 42, 60, 62 };
    }

    /**
     * setBitData60
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        IIso8583.IIso8583Entity.IFieldAttrs attrs = entity.createFieldAttrs();
        attrs.setVarLenFormat(IIso8583.IIso8583Entity.EVarLenFormat.BCD);
        attrs.setFormat("N...23");
        entity.setFieldAttrs("60", attrs);
        String netInfoCode = "350";
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.TMK_TYPE)) {
            netInfoCode = "354";
        }
        setBitData("60", "00" + Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6) + netInfoCode);
    }

    /**
     * tmk 下载第二阶段，对62域进行TLV打包
     *
     * @param tag
     * @param value
     * @return
     */
    private static ITlv.ITlvDataObj structureTLV(byte[] tag, byte[] value) {
        ITlv tlv = FinancialApplication.getPacker().getTlv();
        ITlv.ITlvDataObj obj = tlv.createTlvDataObject();

        if (value != null && value.length > 0) {
            obj.setTag(tag);
            obj.setValue(value);
            return obj;
        }
        return null;
    }

    /**
     * 产生numSize位16进制的数
     */
    private String getRandomValue(int numSize) {
        StringBuilder str = new StringBuilder();
        SecureRandom random = new SecureRandom();

        //随机数长度为32位
        for (int i = 0; i < numSize; i++) {
            char temp;
            boolean key = random.nextBoolean();
            if (key) {
                temp = (char) (random.nextInt(10) + 48);//产生随机数字
            } else {
                temp = (char) (random.nextInt(6) + 'a');//产生a-f
            }
            str.append(temp);
        }

        return str.toString();
    }

    /**
     * TMK下载第二阶段使用,组装62域
     *
     * @throws Iso8583Exception
     */
    @Override
    protected void setBitData62(TransData transData) throws Iso8583Exception {
        StringBuilder f62 = new StringBuilder();
        RSAKeyData rsaKeyData = transData.getRsaKeyData();

        ITlv tlv = FinancialApplication.getPacker().getTlv();
        ITlv.ITlvDataObjList tlvList = tlv.createTlvDataObjectList();
        ITlv.ITlvDataObj tlvData;

        int rsaPukId;
        String rsaPukRid;
        String randomValue;
        byte[] checkValue;
        byte[] randomTmk;
        byte[] cipherRandomTmk;
        byte[] checkData = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
        byte[] kcv = new byte[4];

        rsaPukId = rsaKeyData.getRsaPukID();  //RSA_PUK 后台索引号

        rsaPukRid = rsaKeyData.getRsaPukRID();// RSA_PUK RID

        randomValue = getRandomValue(32);  //生成32位随机数
        Log.d("RSA", "randomValue:" + randomValue);

        rsaKeyData.setRandomKey(randomValue);

        randomTmk = FinancialApplication.getConvert().strToBcd(randomValue,
                IConvert.EPaddingPosition.PADDING_LEFT);//转换成16字节BCD码，随机密钥
        Log.d("RSA", "randTMK:" + Utils.bcd2Str(randomTmk));

        checkValue = DeviceImplNeptune.getInstance().tdes(checkData, randomTmk,
                DeviceImplNeptune.TDES_ENCRYPT_MODE);
        Log.d("RSA", "checkValue:" + Utils.bcd2Str(checkValue));

        System.arraycopy(checkValue, 0, kcv, 0, 4);
        Log.d("RSA", "kcv:" + Utils.bcd2Str(kcv));

        int moudlLen = rsaKeyData.getRsaPukModulLen();
        byte[] randomTmkPack = new byte[moudlLen];
        randomTmkPack[0] = 0x00;
        randomTmkPack[1] = 0x02;
        Arrays.fill(randomTmkPack, 2, moudlLen - 16 - 1, (byte) 0xFF);
        randomTmkPack[moudlLen - 1 - 16] = 0x00;
        System.arraycopy(randomTmk, 0, randomTmkPack, moudlLen - 16, 16);

        byte[] pukExp = FinancialApplication.getConvert().strToBcd(rsaKeyData.getRsaPukExponent(), IConvert.EPaddingPosition.PADDING_LEFT);
        Log.d("RSA", "pukExp:" + Utils.bcd2Str(pukExp));

        byte[] pukModul = FinancialApplication.getConvert().strToBcd(rsaKeyData.getRsaPukModul(),
                IConvert.EPaddingPosition.PADDING_LEFT);
        Log.d("RSA", "pukModule:" + Utils.bcd2Str(pukModul) + "\n" + Utils.bcd2Str(pukModul).length());
        Log.d("RSA", "randomTmk:" + Utils.bcd2Str(randomTmkPack) + "\n" + Utils.bcd2Str(randomTmkPack).length());
        cipherRandomTmk = DeviceImplNeptune.getInstance().encryptWithPublicKey(pukModul, moudlLen, pukExp, pukExp.length, randomTmkPack);
        Log.d("RSA", "RSAPUB[seedTMK]:" + Utils.bcd2Str(cipherRandomTmk));


        // 9F06 RID
        tlvData = structureTLV(new byte[]{(byte) 0x9F, (byte) 0x06},
                FinancialApplication.getConvert().strToBcd(rsaPukRid, IConvert.EPaddingPosition
                        .PADDING_LEFT));
        if (tlvData != null) {
            tlvList.addDataObj(tlvData);
        }
        f62.append("9F06").append("05").append(rsaPukRid);

        // 9F22  PUK ID
        tlvData = structureTLV(new byte[]{(byte) 0x9F, (byte) 0x22},
                FinancialApplication.getConvert().strToBcd(Integer.toString(rsaPukId), IConvert
                        .EPaddingPosition.PADDING_LEFT));
        if (tlvData != null) {
            tlvList.addDataObj(tlvData);
            f62.append("9F22").append("01").append(Utils.bcd2Str(tlvData.getValue()));
        }

        // DF23 encrypted random key
        tlvData = structureTLV(new byte[]{(byte) 0xDF, (byte) 0x23}, cipherRandomTmk);
        if (tlvData != null) {
            tlvList.addDataObj(tlvData);
        }
        String randomKeyPart = "80";
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.TMK_TYPE)) {
            randomKeyPart = "0100";
        }
        f62.append("DF23").append(randomKeyPart).append(Utils.bcd2Str(cipherRandomTmk));

        // DF24 KCV
        tlvData = structureTLV(new byte[]{(byte) 0xDF, (byte) 0x24}, kcv);
        if (tlvData != null) {
            tlvList.addDataObj(tlvData);
        }
        f62.append("DF24").append("04").append(Utils.bcd2Str(kcv));

        setBitData("62", Utils.str2Bcd(f62.toString()));
    }

}
