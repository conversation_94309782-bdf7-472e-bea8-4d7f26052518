/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/10/20                      Kimberley                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.view.KeyEvent;
import com.pax.abl.core.AAction;
import com.pax.edc.R;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.menu.ManageMenuActivity;
import com.pax.pay.trans.TransContext;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;

public class ActionSwitchLanguage extends AAction {
    private Context context;
    private Timer t;
    private String[] data;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionSwitchLanguage(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context) {
        this.context = context;
        data = new String[]{context.getString(R.string.LANGUAGE_ENGLISH), context.getString(R.string.LANGUAGE_CHINESE)};
    }

    @Override
    protected void process() {
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                showDialog();
            }
        });
    }

    /**
     * 修改语言dialog
     */
    private void showDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle(Utils.getString(R.string.SWITCH_LANGUAGE));
        builder.setCancelable(false);
        t = new Timer();
        int checkedItem = 0;
        if (FinancialApplication.getSysParam()
                .get(SysParam.StringParam.APP_LANGUAGE)
                .equals(Utils.getString(R.string.LANGUAGE_CHINESE))
                || "zh_".equals(Locale.getDefault().toString().substring(0, 3))) {
            checkedItem = 1;
        } else {
            checkedItem = 0;
        }
        builder.setSingleChoiceItems(data, checkedItem, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                if(data[which].equals(context.getString(R.string.LANGUAGE_ENGLISH))){
                    FinancialApplication.getSysParam().set(SysParam.StringParam.APP_LANGUAGE, Utils.getString(R.string.LANGUAGE_ENGLISH));
                }else if(data[which].equals(context.getString(R.string.LANGUAGE_CHINESE))){
                    FinancialApplication.getSysParam().set(SysParam.StringParam.APP_LANGUAGE, Utils.getString(R.string.LANGUAGE_CHINESE));
                }
                Utils.changeAppLanguageInApp(FinancialApplication.getApp());
                t.cancel();
                ActivityStack.getInstance().pop();
                Intent intent = new Intent(context, ManageMenuActivity.class);
                intent.putExtra(EUIParamKeys.NAV_TITLE.toString(), context.getString(R.string.trans_manage));
                intent.putExtra(EUIParamKeys.NAV_BACK.toString(), true);
                context.startActivity(intent);
                TransContext.getInstance().getCurrentAction().setFinished(false); //AET-229
                TransContext.getInstance().setCurrentAction(null); //fix leaks
            }
        });

        builder.setCancelable(false);
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    dialog.dismiss();
                    t.cancel();
                }
                return false;
            }
        });

        final AlertDialog alertDialog = builder.create();
        alertDialog.show();
        t.schedule(new TimerTask() {
            @Override
            public void run() {
                alertDialog.dismiss(); // when the task active then close the dialog
                t.cancel(); // also just top the timer thread, otherwise, you may receive a crash report
            }
        }, 60000);
    }
}
