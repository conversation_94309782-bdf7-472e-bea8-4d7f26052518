package com.pax.pay.trans.action;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.commonlib.application.BaseApplication;
import com.pax.data.entity.TransTotal;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.SettleTrans;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @Date 2024/7/10
 */
public class ActionAutoSettle extends AAction {
    Disposable disposable;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionAutoSettle(ActionStartListener listener) {
        super(listener);
        this.setEndListener((action, result) -> {
            if(disposable != null && !disposable.isDisposed()){
                disposable.dispose();
            }
        });
    }

    @Override
    protected void process() {
        disposable = Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
                    TransTotal transTotal = GreendaoHelper.getTransTotalHelper().calcTotal(FinancialApplication.getAcqManager().getCurAcq());
                    emitter.onNext(transTotal.isZero());
                    emitter.onComplete();
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(isZero -> {
                            // 有交易记录才进行结算
                            if (Boolean.FALSE.equals(isZero)) {
                                ToastUtils.showMessage(Utils.getString(R.string.auto_settlement_propmt));
                                FinancialApplication.setIsAutoSettlement(true);
                                new SettleTrans(BaseApplication.getAppContext(), true, result1 -> {
                                    // 重置自动结算的标志位
                                    FinancialApplication.setIsAutoSettlement(false);
                                    // 重置当天时间，防止失败重复结算
                                    SysParam.getInstance().set(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE), Utils.getString(R.string.disable));
                                    Utils.restart();
                                }).execute();
                            }
                        }
                );
    }
}