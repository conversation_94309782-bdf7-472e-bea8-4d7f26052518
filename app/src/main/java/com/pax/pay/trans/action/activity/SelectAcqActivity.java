/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-12-28
 * Module Author: caowb
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.expandablerecyclerview.BaseViewHolder;
import com.pax.edc.expandablerecyclerview.ExpandItemAnimator;
import com.pax.edc.expandablerecyclerview.ExpandableRecyclerAdapter;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransTotal;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ToastUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SelectAcqActivity extends BaseActivityWithTickForAction {

    private static final String ACQ_NAME = "acq_name";

    //ALL
    private CheckBox mAllCheck;
    private Button mSettle;
    private RecyclerView mRecyclerView;

    private ExpandableRecyclerAdapter<Map<String, String>> acquirerListAdapter;

    //已勾选的acquirer列表
    private List<String> checkedAcqs;

    private int acqCount;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        tickTimer.stop();
    }

    /**
     * get layout id
     * @return int
     */
    @Override
    protected int getLayoutId() {
        return R.layout.activity_selectacq_layout;
    }

    /**
     * load param
     */
    @Override
    protected void loadParam() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            checkedAcqs = bundle.getStringArrayList(Constants.ACQUIRER_NAME);
        }

        if (checkedAcqs == null)
            checkedAcqs = new ArrayList<>();

        List<Acquirer> acquirers = FinancialApplication.getAcqManager().findAllAcquirers();
        ArrayList<Map<String, String>> myListArray = new ArrayList<>();
        acqCount = acquirers.size();

        for (Acquirer i : acquirers) {
            Map<String, String> map = new HashMap<>();
            i.setName(Component.changeInstalmentAcqName(i));
            map.put(ACQ_NAME, i.getName());
            //YUU交易不参与结算
            if (!i.getName().equalsIgnoreCase("HASE_YUU")) {
                myListArray.add(map);
            } else {
                acqCount = acquirers.size() - 1;
            }
        }
        acquirerListAdapter = new ExpandableRecyclerAdapter<>(SelectAcqActivity.this, R.layout.selectacq_item,
                new ExpandableRecyclerAdapter.ItemViewListener<Map<String, String>>() {
                    @Override
                    public BaseViewHolder<Map<String, String>> generate(View view) {
                        return new AcqSettleViewHolder(view);
                    }
                })
                .setDataBeanList(myListArray);
    }

    /**
     * get title string
     * @return String
     */
    @Override
    protected String getTitleString() {
        return getString(R.string.settle_select_acquirer);
    }

    /**
     * init views
     */
    @Override
    protected void initViews() {
        mAllCheck = (CheckBox) findViewById(R.id.item_select_acq_check);
        mSettle = (Button) findViewById(R.id.select_acq_settle);
        mRecyclerView = (RecyclerView) findViewById(R.id.select_acq_list);

        mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        mRecyclerView.setAdapter(acquirerListAdapter);
        mRecyclerView.setItemAnimator(new ExpandItemAnimator());

        confirmBtnChange();
    }

    /**
     * set Listeners
     */
    @Override
    protected void setListeners() {
        mSettle.setOnClickListener(this);

        mRecyclerView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //                tickTimer.start();
                return false;
            }
        });

        mAllCheck.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                //AET-39
                if (isChecked) {
                    List<Acquirer> acqList = FinancialApplication.getAcqManager().findAllAcquirers();
                    for (Acquirer acquirer : acqList) {
                        if (!checkedAcqs.contains(acquirer.getName()) && !acquirer.getName()
                                .equalsIgnoreCase("HASE_YUU")) {
                            checkedAcqs.add(acquirer.getName());
                        }
                    }
                } else {
                    if (checkedAcqs.size() == acqCount) {
                        checkedAcqs.clear();
                    }
                }
                confirmBtnChange();

                //only notifyDataSetChanged when RecyclerView is not computing and in scroll_idle state
                //otherwise throw exception "Cannot call this method while RecyclerView is computing a layout or scrolling"
                if (!mRecyclerView.isComputingLayout() &&
                        mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                    acquirerListAdapter.notifyDataSetChanged();
                }
            }
        });

        //AET-39
        if (checkedAcqs != null && checkedAcqs.size() == acqCount) {
            mAllCheck.setChecked(true);
        }
    }

    /**
     * on key back down
     * @return boolean
     */
    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

    /**
     * 点击事件
     * @param v View
     */
    @Override
    public void onClickProtected(View v) {
        if (v.getId() == R.id.select_acq_settle) {
            finish2SettleAcq();
        }
    }

    /**
     * finish to settle acquirer
     */
    private void finish2SettleAcq() {
        if (checkedAcqs.isEmpty()) {
            ToastUtils.showMessage(R.string.err_settle_select_acq);
            return;
        }
        finish(new ActionResult(TransResult.SUCC, checkedAcqs));
    }

    private class AcqSettleViewHolder extends BaseViewHolder<Map<String, String>> {

        private TextView textView;
        private CheckBox checkBox;
        private TextView acqName;
        private TextView merchantName;
        private TextView merchantId;
        private TextView terminalId;
        private TextView batchNo;
        private TextView saleSum;
        private TextView saleAmt;
        private TextView refundSum;
        private TextView refundAmt;
        private TextView voidSaleSum;
        private TextView voidSaleAmt;
        private TextView voidRefundSum;
        private TextView voidRefundAmt;
        private TextView grossSum;
        private TextView grossAmt;
        private TextView redmSum;
        private TextView redmAmt;
        private TextView haseSum;
        private TextView haseAmt;
        private TextView mer1Sum;
        private TextView mer1Amt;
        private TextView mer2Sum;
        private TextView mer2Amt;
        private TextView bonusSum;
        private TextView bonusAmt;
        private TextView netSum;
        private TextView netAmt;

        private TextView grossVoidSum;
        private TextView grossVoidAmt;
        private TextView redmVoidSum;
        private TextView redmVoidAmt;
        private TextView haseVoidSum;
        private TextView haseVoidAmt;
        private TextView mer1VoidSum;
        private TextView mer1VoidAmt;
        private TextView mer2VoidSum;
        private TextView mer2VoidAmt;
        private TextView bonusVoidSum;
        private TextView bonusVoidAmt;
        private TextView netVoidSum;
        private TextView netVoidAmt;

        private Button settleConfirm;

        public AcqSettleViewHolder(View itemView) {
            super(itemView, R.id.select_acq_item_header, R.id.expandable);
        }

        @Override
        protected void initView() {
            textView = (TextView) itemView.findViewById(R.id.expandable_toggle_button);
            checkBox = (CheckBox) itemView.findViewById(R.id.item_select_acq_check);

            acqName = (TextView) itemView.findViewById(R.id.settle_acquirer_name);
            merchantName = (TextView) itemView.findViewById(R.id.settle_merchant_name);
            merchantId = (TextView) itemView.findViewById(R.id.settle_merchant_id);
            terminalId = (TextView) itemView.findViewById(R.id.settle_terminal_id);
            batchNo = (TextView) itemView.findViewById(R.id.settle_batch_num);

            saleSum = (TextView) itemView.findViewById(R.id.sale_total_sum);
            saleAmt = (TextView) itemView.findViewById(R.id.sale_total_amount);
            refundSum = (TextView) itemView.findViewById(R.id.refund_total_sum);
            refundAmt = (TextView) itemView.findViewById(R.id.refund_total_amount);

            voidSaleSum = (TextView) itemView.findViewById(R.id.void_sale_total_sum);
            voidSaleAmt = (TextView) itemView.findViewById(R.id.void_sale_total_amount);
            voidRefundSum = (TextView) itemView.findViewById(R.id.void_refund_total_sum);
            voidRefundAmt = (TextView) itemView.findViewById(R.id.void_refund_total_amount);

            grossSum = (TextView) itemView.findViewById(R.id.gross_total_sum);
            grossAmt = (TextView) itemView.findViewById(R.id.gross_total_amount);
            redmSum = (TextView) itemView.findViewById(R.id.redm_total_sum);
            redmAmt = (TextView) itemView.findViewById(R.id.redm_total_amount);
            haseSum = (TextView) itemView.findViewById(R.id.hase_total_sum);
            haseAmt = (TextView) itemView.findViewById(R.id.hase_total_amount);
            mer1Sum = (TextView) itemView.findViewById(R.id.mer1_total_sum);
            mer1Amt = (TextView) itemView.findViewById(R.id.mer1_total_amount);
            mer2Sum = (TextView) itemView.findViewById(R.id.mer2_total_sum);
            mer2Amt = (TextView) itemView.findViewById(R.id.mer2_total_amount);
            bonusSum = (TextView) itemView.findViewById(R.id.bonus_total_sum);
            bonusAmt = (TextView) itemView.findViewById(R.id.bonus_total_amount);
            netSum = (TextView) itemView.findViewById(R.id.net_total_sum);
            netAmt = (TextView) itemView.findViewById(R.id.net_total_amount);

            grossVoidSum = (TextView) itemView.findViewById(R.id.gross_void_total_sum);
            grossVoidAmt = (TextView) itemView.findViewById(R.id.gross_void_total_amount);
            redmVoidSum = (TextView) itemView.findViewById(R.id.redm_void_total_sum);
            redmVoidAmt = (TextView) itemView.findViewById(R.id.redm_void_total_amount);
            haseVoidSum = (TextView) itemView.findViewById(R.id.hase_void_total_sum);
            haseVoidAmt = (TextView) itemView.findViewById(R.id.hase_void_total_amount);
            mer1VoidSum = (TextView) itemView.findViewById(R.id.mer1_void_total_sum);
            mer1VoidAmt = (TextView) itemView.findViewById(R.id.mer1_void_total_amount);
            mer2VoidSum = (TextView) itemView.findViewById(R.id.mer2_void_total_sum);
            mer2VoidAmt = (TextView) itemView.findViewById(R.id.mer2_void_total_amount);
            bonusVoidSum = (TextView) itemView.findViewById(R.id.bonus_void_total_sum);
            bonusVoidAmt = (TextView) itemView.findViewById(R.id.bonus_void_total_amount);
            netVoidSum = (TextView) itemView.findViewById(R.id.net_void_total_sum);
            netVoidAmt = (TextView) itemView.findViewById(R.id.net_void_total_amount);

            settleConfirm = (Button) itemView.findViewById(R.id.settle_confirm);
        }

        /**
         * set listener
         */
        @Override
        protected void setListener() {
            settleConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    checkedAcqs.clear();
                    checkedAcqs.add(findAcquirer(getAdapterPosition()));
                    finish(new ActionResult(TransResult.SUCC, checkedAcqs));
                }
            });

            //对holder中checkbox设置点击响应
            checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    int pos = getAdapterPosition();
                    List<Map<String, String>> myListArray = acquirerListAdapter.getDataBeanList();
                    Map<String, String> item = myListArray.get(pos);

                    if (isChecked) {
                        if (!checkedAcqs.contains(item.get(ACQ_NAME))) {
                            //for instal acquirer, item.get(ACQ_NAME) is like "HASE_INS_P8-12m"
                            // while checkedAcqs just is HASE_INS_P8, so need to remove "-12m"
                            if (item.get(ACQ_NAME).contains("INS")) {
                                checkedAcqs.add(item.get(ACQ_NAME).split("-")[0]);
                            } else {
                                checkedAcqs.add(item.get(ACQ_NAME));
                            }
                        }
                    } else {
                        if (item.get(ACQ_NAME).contains("INS")) {
                            checkedAcqs.remove(item.get(ACQ_NAME).split("-")[0]);
                        } else {
                            checkedAcqs.remove(item.get(ACQ_NAME));
                        }
                    }
                    confirmBtnChange();
                    //AET-39
                    mAllCheck.setChecked(checkedAcqs.size() == acqCount);
                }
            });
        }

        /**
         * bind view
         *
         * @param dataBean Map
         * @param viewHolder BaseViewHolder
         * @param pos int
         */
        @Override
        public void bindView(final Map<String, String> dataBean, final BaseViewHolder viewHolder,
                final int pos) {
            textView.setText(dataBean.get(ACQ_NAME));

            if (dataBean.get(ACQ_NAME).contains("INS")) {
                dataBean.put(ACQ_NAME, dataBean.get(ACQ_NAME).split("-")[0]);
            }
            //AET-39
            checkBox.setChecked(checkedAcqs.contains(dataBean.get(ACQ_NAME)));

            if (viewHolder.getExpandView().getVisibility() == View.VISIBLE) {
                updateValueTable(viewHolder, pos);
            }
        }

        /**
         * update value table
         * @param viewHolder BaseViewHolder
         * @param position int
         */
        private void updateValueTable(final BaseViewHolder viewHolder, final int position) {
            String acquirerName = findAcquirer(position);
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(acquirerName);

            acqName.setText(acquirer.getName());
            merchantName.setText(getString(R.string.settle_merchant_name));
            merchantId.setText(acquirer.getMerchantId());
            terminalId.setText(acquirer.getTerminalId());
            batchNo.setText(String.valueOf(acquirer.getCurrBatchNo()));

            if (acquirerName.equalsIgnoreCase("HASE_OLS")) {
                viewHolder.getExpandView().findViewById(R.id.normal_trans_total_include).setVisibility(View.GONE);
                viewHolder.getExpandView().findViewById(R.id.loyalty_trans_total_include).setVisibility(View.VISIBLE);
                TransTotal loytalTotal = FinancialApplication.getTransTotalDbHelper().loyaltyTotal(acquirer);
                grossSum.setText(String.valueOf(loytalTotal.getLysGrossNum()));
                grossAmt.setText(CurrencyConverter.convert(loytalTotal.getLysGrossAmt()));
                redmSum.setText(String.valueOf(loytalTotal.getRedeemNum()));
                redmAmt.setText(CurrencyConverter.convert(loytalTotal.getRedeemAmt()));
                haseSum.setText(String.valueOf(loytalTotal.getHaseCashNum()));
                haseAmt.setText(CurrencyConverter.convert(loytalTotal.getHaseCashAmt()));
                mer1Sum.setText(String.valueOf(loytalTotal.getMer1Num()));
                mer1Amt.setText(CurrencyConverter.convert(loytalTotal.getMer1Amt()));
                mer2Sum.setText(String.valueOf(loytalTotal.getMer2Num()));
                mer2Amt.setText(CurrencyConverter.convert(loytalTotal.getMer2Amt()));
                bonusSum.setText(String.valueOf(loytalTotal.getBounsNum()));
                bonusAmt.setText(CurrencyConverter.convert(loytalTotal.getBounsAmt()));
                netSum.setText(String.valueOf(loytalTotal.getLysOkNetNum()));
                netAmt.setText(CurrencyConverter.convert(loytalTotal.getLysOkNetAmt()));

                grossVoidSum.setText(String.valueOf(loytalTotal.getLysGrossVoidNum()));
                grossVoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getLysGrossVoidAmt()));
                redmVoidSum.setText(String.valueOf(loytalTotal.getRedeemVoidNum()));
                redmVoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getRedeemVoidAmt()));
                haseVoidSum.setText(String.valueOf(loytalTotal.getHaseCashVoidNum()));
                haseVoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getHaseCashVoidAmt()));
                mer1VoidSum.setText(String.valueOf(loytalTotal.getMer1VoidNum()));
                mer1VoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getMer1VoidAmt()));
                mer2VoidSum.setText(String.valueOf(loytalTotal.getMer2VoidNum()));
                mer2VoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getMer2VoidAmt()));
                bonusVoidSum.setText(String.valueOf(loytalTotal.getBounsVoidNum()));
                bonusVoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getBounsVoidAmt()));
                netVoidSum.setText(String.valueOf(loytalTotal.getLysOkNetVoidNum()));
                netVoidAmt.setText(CurrencyConverter.convert(0 - loytalTotal.getLysOkNetVoidAmt()));
            } else {
                viewHolder.getExpandView().findViewById(R.id.normal_trans_total_include).setVisibility(View.VISIBLE);
                viewHolder.getExpandView().findViewById(R.id.loyalty_trans_total_include).setVisibility(View.GONE);
                TransTotal total = FinancialApplication.getTransTotalDbHelper().calcTotal(acquirer);
                String saleAmtStr = CurrencyConverter.convert(total.getSaleAmt());
                String refundAmtStr = CurrencyConverter.convert(0 - total.getRefundTotalAmt());
                String voidSaleAmtStr = CurrencyConverter.convert(0 - (total.getSaleVoidAmt()));
                String voidRefundAmtStr = CurrencyConverter.convert(0 - total.getRefundVoidTotalAmt());
                saleSum.setText(String.valueOf(total.getSaleNum()));
                saleAmt.setText(saleAmtStr);
                refundSum.setText(String.valueOf(total.getRefundTotalNum()));
                refundAmt.setText(refundAmtStr);
                voidSaleSum.setText(String.valueOf(total.getSaleVoidNum()));
                voidSaleAmt.setText(voidSaleAmtStr);
                voidRefundSum.setText(String.valueOf(total.getRefundVoidTotalNum()));
                voidRefundAmt.setText(voidRefundAmtStr);
            }
        }

        /**
         * find acquirer
         * @param position int
         * @return String
         */
        private String findAcquirer(int position) {
            String acqName = acquirerListAdapter.getDataBeanList().get(position).get(ACQ_NAME);
            if (acqName.contains("INS")) {
                acqName = acqName.split("-")[0];
            }
            return acqName;
        }
    }

    /**
     * confirm button change
     */
    private void confirmBtnChange() {
        mSettle.setEnabled(!checkedAcqs.isEmpty());
    }
}
