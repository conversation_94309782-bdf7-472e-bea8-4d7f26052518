/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-7-31
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.task;

import android.content.Context;
import android.content.DialogInterface;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionPrintPreview;
import com.pax.pay.trans.action.ActionPrintTransReceipt;
import com.pax.pay.trans.action.ActionSendEmail;
import com.pax.pay.trans.action.ActionSendSMS;
import com.pax.pay.trans.action.activity.PrintPreviewActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;

public class PrintTask extends BaseTask {
    private TransData transData;
    private BaseTrans transaction;

    /**
     *
     */
    public PrintTask(Context context, TransData transData, TransEndListener transListener) {
        super(context, transListener);
        this.transData = transData;
    }

    /**
     *
     */
    public PrintTask(Context context, BaseTrans transaction, TransEndListener transListener) {
        super(context, transListener);
        this.transaction = transaction;
    }

    public static TransEndListener genTransEndListener(final BaseTask task, final String state) {
        return new TransEndListener() {

            @Override
            public void onEnd(final ActionResult result) {
                FinancialApplication.getApp().runOnUiThread(new Runnable() {

                    @Override
                    public void run() {
                        task.onActionResult(state, result);
                    }
                });
            }
        };
    }

    enum State {
        PRINT_PREVIEW,
        PRINT_TICKET,
        ENTER_PHONE_NUM,
        ENTER_EMAIL,
        SEND_SMS,
        SEND_EMAIL,
    }

    @Override
    protected void bindStateOnAction() {
        //print preview action
        ActionPrintPreview printPreviewAction = new ActionPrintPreview(
                new AAction.ActionStartListener() {

                    @Override
                    public void onStart(AAction action) {
                        if (transaction != null) {       //Jerry add
                            transData = transaction.getTransData();
                        }
                        ((ActionPrintPreview) action).setParam(getCurrentContext(), transData);
                    }
                });
        bind(State.PRINT_PREVIEW.toString(), printPreviewAction);

        // get Telephone num
        ActionInputTransData phoneAction = new ActionInputTransData(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionInputTransData) action).setParam(getCurrentContext(), getString(R.string.paperless)).setInputLine(
                        getString(R.string.prompt_phone_number), ActionInputTransData.EInputType.PHONE, 20, false);
            }
        });
        bind(State.ENTER_PHONE_NUM.toString(), phoneAction);

        // get email address
        ActionInputTransData emailAction = new ActionInputTransData(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionInputTransData) action).setParam(getCurrentContext(), getString(R.string.paperless))
                        .setInputLine(getString(R.string.prompt_email_address), ActionInputTransData.EInputType.EMAIL, 100, false);
            }
        });
        bind(State.ENTER_EMAIL.toString(), emailAction);

        // print action
        ActionPrintTransReceipt printTransReceiptAction = new ActionPrintTransReceipt(
                new AAction.ActionStartListener() {

                    @Override
                    public void onStart(AAction action) {
                        if (transaction != null) {
                            transData = transaction.getTransData();
                        }
                        ((ActionPrintTransReceipt) action).setParam(getCurrentContext(), transData);
                    }
                });
        bind(State.PRINT_TICKET.toString(), printTransReceiptAction, true);

        ActionSendSMS sendSMSAction = new ActionSendSMS(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {

                ((ActionSendSMS) action).setParam(getCurrentContext(), transData);
            }
        });
        bind(State.SEND_SMS.toString(), sendSMSAction);

        ActionSendEmail sendEmailAction = new ActionSendEmail(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionSendEmail) action).setParam(getCurrentContext(), transData);
            }
        });
        bind(State.SEND_EMAIL.toString(), sendEmailAction);

        if (transData != null) {
            increaseTraceNo(transData);
        } else if (transaction != null) {
            increaseTraceNo(transaction.getTransData());
        }

        //showReversalDebug();

        gotoState(State.PRINT_TICKET.toString());
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);

        switch (state) {
            case PRINT_PREVIEW:
                goPrintBranch(result);
                break;
            case ENTER_PHONE_NUM:
                onEnterSMSBranch(result);
                break;
            case ENTER_EMAIL:
                onEnterEmailBranch(result);
                break;
            case SEND_SMS:
            case SEND_EMAIL:
                onSendBranch(result);
                break;
            case PRINT_TICKET:
            default:
                transEnd(result);
                break;
        }
    }

    private void onEnterSMSBranch(ActionResult result) {
        if (result.getRet() == TransResult.SUCC) {
            transData.setPhoneNum((String) result.getData());
            gotoState(State.SEND_SMS.toString());
        } else {
            gotoState(State.PRINT_PREVIEW.toString());
        }
    }

    private void onEnterEmailBranch(ActionResult result) {
        if (result.getRet() == TransResult.SUCC) {
            transData.setEmail((String) result.getData());
            gotoState(State.SEND_EMAIL.toString());
        } else {
            gotoState(State.PRINT_PREVIEW.toString());
        }
    }

    private void onSendBranch(ActionResult result) {
        if (result.getRet() == TransResult.SUCC) {
            // end trans
            transEnd(result);
        } else {
            dispResult(transData.getTransType().getTransName(), result, null);
            gotoState(State.PRINT_PREVIEW.toString());
        }
    }

    private void goPrintBranch(ActionResult result) {
        String string = (String) result.getData();
        if (string != null && string.equals(PrintPreviewActivity.PRINT_BUTTON)) {
            //print ticket
            gotoState(State.PRINT_TICKET.toString());
        } else if (string != null && string.equals(PrintPreviewActivity.SMS_BUTTON)) {
            gotoState(State.ENTER_PHONE_NUM.toString());
        } else if (string != null && string.equals(PrintPreviewActivity.EMAIL_BUTTON)) {
            gotoState(State.ENTER_EMAIL.toString());
        } else {
            //end trans directly, not print
            transEnd(new ActionResult(TransResult.SUCC, transData));
        }
    }

    private void increaseTraceNo(TransData transData) {
        ETransType transType = transData.getTransType();
        //AET-32、AET31
        // AET-126只要发送成功保存了交易就要增加流水号，避免流水号复用
        // 冲正交易不需要增加流水号
        if (transData.getReversalStatus() == TransData.ReversalStatus.REVERSAL) {
            return;
        }

        if (transType == ETransType.VOID
                || transType == ETransType.PREAUTHCMVOID
                || transType == ETransType.ADJUST
                || transType == ETransType.LYS_SALE_ADJUST
                || transType == ETransType.VOID_WITH_CASH_DOLLAR) {
            return;
        }

        if ((transType != ETransType.OFFLINE_TRANS_SEND) &&
                (transType != ETransType.EMV_SETTLE) &&
                (transType != ETransType.EMV_SETTLE_END) &&
                (transType != ETransType.EMV_BATCH_UP)) {
            Component.incInvoiceNo();
        }
    }

    private void showReversalDebug() {
        if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.ALLOW_REVERSAL_DEBUG)) {
            return;
        }

        CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);
        dialog.setCancelClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                //transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            }
        });
        dialog.setConfirmClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                transData.setReversalStatus(TransData.ReversalStatus.PENDING);
                FinancialApplication.getTransDataDbHelper().updateTransData(transData);
            }
        });
        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                //transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            }
        });
        dialog.show();
        dialog.setNormalText(getString(R.string.produce_reversal));
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
        dialog.setCanceledOnTouchOutside(false);
    }

}
