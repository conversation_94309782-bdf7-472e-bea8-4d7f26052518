/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         caowb                   Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.activity.SettlePreviewActivity;

/**
 * select acquirers when settlement
 */
public class ActionSettlePreview extends AAction {
    private Context context;
    private String title;

    /**
     * 子类构造方法必须调用super设置ActionStartListener
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionSettlePreview(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String title) {
        this.context = context;
        this.title = title;
    }

    @Override
    protected void process() {
        //return if there are no transaction
        if(!FinancialApplication.isAutoSettlement() && GreendaoHelper.getTransDataHelper().existUnknownBqrTransData()){
            setResult(new ActionResult(TransResult.ERR_UNKNOWN_QR, null));
            return;
        }else if (GreendaoHelper.getTransDataHelper().countOf() == 0) {
            setResult(new ActionResult(TransResult.ERR_NO_TRANS, null));
            return;
        }

        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                Intent intent = new Intent(context, SettlePreviewActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
                bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intent.putExtras(bundle);
                context.startActivity(intent);
            }
        });

    }
}
