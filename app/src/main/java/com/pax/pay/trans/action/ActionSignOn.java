package com.pax.pay.trans.action;

import android.content.Context;
import android.os.AsyncTask;
import android.text.TextUtils;
import android.util.Log;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.dal.exceptions.PedDevException;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.transmit.Online;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.ResponseCode;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.util.Calendar;

import static com.pax.pay.constant.Constants.MASTERCARD_TYPE;

/**
 * Created by liliang on 2018/1/4.
 */

public class ActionSignOn extends AAction {

    private Context mContext;
    private BaseTrans mTrans;
    private String signOnType;

    /**
     * derived classes must call super(listener) to set
     */
    public ActionSignOn(Context context, BaseTrans trans, String signOnType) {
        super(null);
        mContext = context;
        mTrans = trans;
        this.signOnType = signOnType;
    }

    @Override
    protected void process() {
        new SignOnTask().execute();
    }

    class SignOnTask extends AsyncTask<Void, Void, Integer> {
        private TransProcessListenerImpl mListener;

        @Override
        protected void onPreExecute() {
            if (mListener == null) {
                mListener = new TransProcessListenerImpl(mContext);
                if (signOnType.equals(MASTERCARD_TYPE)) {
                    mListener.onUpdateProgressTitle(mContext.getString(R.string.mastercard_sign_in_wait));
                } else {
                    mListener.onUpdateProgressTitle(mContext.getString(R.string.pos_sign_in_wait));
                }
            }
        }

        @Override
        protected Integer doInBackground(Void[] objects) {
            TransData transData = mTrans.getTransData();
            if(Component.isDemo()){
                byte tpkIndex = Constants.INDEX_TPK;
                int tmkIndex = Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam.NumberParam.MK_INDEX));
                if (signOnType.equals(MASTERCARD_TYPE)) {
                    tpkIndex = Constants.INDEX_TPK_MASTERCARD;
                    tmkIndex =
                            Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam.NumberParam.MASTERCARD_MK_INDEX));
                }
                try {
                    byte[] pinKeyTpk = {
                            (byte) 0x00, (byte) 0x01, (byte) 0x02, (byte) 0x03,
                            (byte) 0x04, (byte) 0x05, (byte) 0x06, (byte) 0x07,
                            (byte) 0x08, (byte) 0x09, (byte) 0x0A, (byte) 0x0B,
                            (byte) 0x0C, (byte) 0x0D, (byte) 0x0E, (byte) 0x0F};
                    Device.writeTPK(pinKeyTpk, null, (byte) tmkIndex, tpkIndex);
                    return TransResult.SUCC;
                } catch (PedDevException e) {
                    Log.e(TAG, "", e);
                    return TransResult.ERR_WRITE_TPK;
                }
            }
            int result = new Online().online(transData, mListener);
            if (result != TransResult.SUCC) {
                return result;
            }

            result = checkRspCode(transData, mListener);
            if (result != TransResult.SUCC) {
                return result;
            }

            // 保存批次号
            TransData data = mTrans.getTransData();
            Acquirer acquirer = data.getAcquirer();
            acquirer.setCurrBatchNo((int) transData.getBatchNo());
            FinancialApplication.getAcqManager().updateAcquirer(acquirer);

            Calendar calender = Calendar.getInstance();
            int year = calender.get(Calendar.YEAR);
            String timestamp = String.valueOf(year).substring(2) + transData.getDateTime();
            // 更新系统时间
            Device.setSystemTime(timestamp);

            return savePinKey(transData);
        }

        @Override
        protected void onPostExecute(Integer result) {
            if (mListener != null) {
                mListener.onHideProgress();
            }

            setResult(new ActionResult(result, null));
            mListener = null;
        }
    }

    /**
     * 检查Respnse code
     * @param transData 数据
     * @param listener 监听器
     * @return SUCC 0
     */
    private int checkRspCode(TransData transData, TransProcessListener listener) {
        if (!"00".equals(transData.getResponseCode())) {
            if (listener != null) {
                listener.onHideProgress();
                ResponseCode responseCode = ResponseCode.getInstance().parse(transData
                        .getResponseCode());
                listener.onShowErrMessage(Utils.getString(R.string.prompt_err_code)
                                + responseCode.toString(),
                        2, true);
            }
            return TransResult.ERR_HOST_REJECT;
        }
        return TransResult.SUCC;
    }

    /**
     * 保存Pin key
     * @param transData 数据
     * @return SUCC 0
     */
    private int savePinKey(TransData transData) {
        if (TextUtils.isEmpty(transData.getField62())) {
            return TransResult.ERR_WRITE_TPK;
        }

        IConvert convert = FinancialApplication.getConvert();
        byte[] f62 = convert.strToBcd(transData.getField62(), IConvert.EPaddingPosition.PADDING_LEFT);
        Log.d("PINKEY", new String(f62));

        // 工作密钥，若长度域不为24或40或56或60或84,格式有误
        if (f62.length != 24 && f62.length != 40 && f62.length != 56 && f62.length != 60 && f62.length != 84) {
            return TransResult.ERR_TPK_LENGTH;
        }

        // PINKEY
        byte[] pinKey = new byte[16];
        byte[] pinKeyKCV = new byte[4];

        System.arraycopy(f62, 0, pinKey, 0, pinKey.length);
        System.arraycopy(f62, 16, pinKeyKCV, 0, pinKeyKCV.length);
        try {
            //upi tmkInkdex,pinkeyIndex
            byte tpkIndex = Constants.INDEX_TPK;
            int tmkIndex = Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam.NumberParam.MK_INDEX));
            if (signOnType.equals(MASTERCARD_TYPE)) {
                tpkIndex = Constants.INDEX_TPK_MASTERCARD;
                tmkIndex =
                        Utils.getMainKeyIndex(FinancialApplication.getSysParam().get(SysParam.NumberParam.MASTERCARD_MK_INDEX));
            }
            Device.writeTPK(pinKey, pinKeyKCV, (byte) tmkIndex, tpkIndex);
            return TransResult.SUCC;
        } catch (PedDevException e) {
            Log.e(TAG, "", e);
            return TransResult.ERR_WRITE_TPK;
        }
    }

}
