/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.pax.abl.core.ipacker.IPacker;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.entity.DUKPTResult;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.device.Device;
import com.pax.eemv.utils.Tools;
import com.pax.gl.pack.IIso8583;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.impl.GL;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.util.Calendar;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class PackIso8583 implements IPacker<TransData, byte[]> {

    protected static final String TAG = "PackIso8583";
    private static final String NUMBER_PATTERN = "\\d+$";
    private static final String OTHER_PATTERN = "^\\d\\.\\d{3}$";
    private static final Map<TransData.EnterMode, String> enterModeMap = new EnumMap<>(TransData.EnterMode.class);

    static {
        enterModeMap.put(TransData.EnterMode.MANUAL, "001");
        enterModeMap.put(TransData.EnterMode.SWIPE, "090");
        enterModeMap.put(TransData.EnterMode.INSERT, "005");
        enterModeMap.put(TransData.EnterMode.CLSS, "007");
        enterModeMap.put(TransData.EnterMode.FALLBACK, "090");
        enterModeMap.put(TransData.EnterMode.QR, "003");
        enterModeMap.put(TransData.EnterMode.CLSS_MST, "091");
        enterModeMap.put(TransData.EnterMode.BHARAT_QR, "010");
    }

    protected PackListener listener;
    private IIso8583 iso8583;
    private IIso8583.IIso8583Entity entity;

    public PackIso8583(PackListener listener) {
        this.listener = listener;
        initEntity();
    }

    /**
     * 获取打包entity
     */
    private void initEntity() {
        iso8583 = GL.getGL().getPacker().getIso8583();
        try {
            entity = iso8583.getEntity();
            entity.loadTemplate(FinancialApplication.getApp().getResources().getAssets().open("edc8583.xml"));
        } catch (Iso8583Exception | IOException | XmlPullParserException e) {
            LogUtils.e(TAG, "", e);
        }
    }

    protected final void setBitData(String field, String value) throws Iso8583Exception {
        if (value != null && !value.isEmpty()) {
            entity.setFieldValue(field, value);
        }
    }

    protected final void setBitData(String field, byte[] value) throws Iso8583Exception {
        if (value != null && value.length > 0) {
            entity.setFieldValue(field, value);
        }
    }

    protected abstract int[] getRequiredFields();

    @Override
    @NonNull
    public byte[] pack(@NonNull TransData transData) {
        LogUtils.i(TAG, "enter PackIso8583 pack");
        int[] fields = getRequiredFields();
        try {
            for (int i : fields) {
                addField(i, transData);
            }

            // h
            String pHeader = transData.getTpdu() + transData.getHeader();
            entity.setFieldValue("h", pHeader); //设置域值
            // m
            ETransType transType = transData.getTransType();
            if (transData.getReversalStatus() == TransData.ReversalStatus.REVERSAL) {
                entity.setFieldValue("m", transType.getDupMsgType());
            } else {
                entity.setFieldValue("m", transType.getMsgType());
            }

            byte[] packData = iso8583.pack(); //基于ISO8583实体的设置和值进行组包
            if (packData == null || packData.length == 0) {
                return new byte[0];
            }

            if (entity.hasField("64")) {
                int len = packData.length;
                byte[] calMacBuf = new byte[len - 11 - 8];// 去掉header和mac
                System.arraycopy(packData, 11, calMacBuf, 0, len - 11 - 8);
                byte[] mac = listener.onCalcMac(calMacBuf);
                if (mac.length == 0) {
                    return new byte[0];
                }
                System.arraycopy(mac, 0, packData, len - 8, 8);
            }
            if (SysParam.getInstance().getBoolean(R.string.EDC_EMV_DEBUG_REPORT)) {
                entity.dump();
            }
            return packData;
        } catch (Iso8583Exception e) {
            LogUtils.e(TAG, "", e);
        }

        return new byte[0];
    }

    @Override
    public int unpack(@NonNull TransData transData, final byte[] rsp) throws PedDevException {

        HashMap<String, byte[]> map;
        try {

            map = iso8583.unpack(rsp, true);
            // 调试信息， 日志输入解包后数据
            if (SysParam.getInstance().getBoolean(R.string.EDC_EMV_DEBUG_REPORT)) {
                entity.dump();
            }

        } catch (Iso8583Exception e) {
            LogUtils.e(TAG, "Debug Report", e);
            return TransResult.ERR_UNPACK;
        }

        // 报文头
        byte[] header = map.get("h");
        // TPDU检查
        String rspTpdu = new String(header).substring(0, 10);
        String reqTpdu = transData.getTpdu();
        if (!rspTpdu.substring(2, 6).equals(reqTpdu.substring(6, 10))
                || !rspTpdu.substring(6, 10).equals(reqTpdu.substring(2, 6))) {
            return TransResult.ERR_UNPACK;
        }
        transData.setHeader(new String(header).substring(10));

        ETransType transType = transData.getTransType();

        byte[] buff;
        // 检查39域应答码
        buff = map.get("39");
        if (buff == null) {
            return TransResult.ERR_PACKET;
        }
        transData.setResponseCode(FinancialApplication.getRspCode().parse(new String(buff)));

        // 检查返回包的关键域， 包含field4
        boolean isCheckAmt = transType != ETransType.SETTLE && transType != ETransType.SALE
                && transType != ETransType.SALE_CASH && transType != ETransType.TRANS_INQUIRY && transType != ETransType.ADJUST
                && transType != ETransType.PREAUTH_COMPLETE_ADVICE;
        int ret = checkRecvData(map, transData, isCheckAmt);
        if (ret != TransResult.SUCC) {
            return ret;
        }

        // field 2 主账号
        buff = map.get("2");
        if (buff != null && buff.length > 0 && transType == ETransType.TRANS_INQUIRY) {
            String pan = new String(buff);
            // 解析返回的卡号，Inquiry会返回，需要设置上卡号和卡组织
            transData.setPan(pan);
            Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(pan);
            transData.setIssuer(issuer);
        }

        // field 3 交易处理码
        buff = map.get("3");
        if (buff != null && buff.length > 0) {
            String origField3 = transData.getField3();
            if (origField3 != null && !origField3.isEmpty() && !origField3.equals(new String(buff))) {
                return TransResult.ERR_PROC_CODE;
            }
        }
        // field 4 交易金额
        buff = map.get("4");
        //由于DCC情况下会上送转换后的金额，并且收到转换后的金额的响应，因此此处不用再重新设置金额，防止金额错误设置成转换后的金额
        if (buff != null && buff.length > 0 && transData.getTransType() != ETransType.VOID) {
            if (!transData.isDcc()) {
                transData.setAmount(new String(buff));
            } else {
                transData.setInternationalCurrencyAmount(new String(buff));
            }
        }

        // field 11 流水号
        buff = map.get("11");
        if (buff != null && buff.length > 0 && transData.getTransType() != ETransType.VOID) {
            transData.setTraceNo(Utils.parseLongSafe(new String(buff), -1));
        }

        // field 13 受卡方所在地日期
        String dateTime = "";
        buff = map.get("13");
        if (buff != null) {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            dateTime = year + new String(buff);
        }

        // field 12 受卡方所在地时间
        buff = map.get("12");
        // Inquiry和Complete Advice后台返回的原始交易时间，因此不更新时间，防止时间错误
        if (buff != null && buff.length > 0 &&
                transData.getTransType() != ETransType.TRANS_INQUIRY && transData.getTransType() != ETransType.PREAUTH_COMPLETE_ADVICE
                && transData.getTransType() != ETransType.ADJUST) {
            transData.setDateTime(dateTime + new String(buff));
        }

        // field 14 卡有效期
        buff = map.get("14");
        if (buff != null && buff.length > 0) {
            String expDate = new String(buff);
            if (!"0000".equals(expDate)) {
                transData.setExpDate(expDate);
            }
        }

        // field 22
        buff = map.get("22");
        if (buff != null && buff.length > 0) {
            String enterModeString = new String(buff);
            // 解析寻卡模式，Inquiry会返回，需要设置上
            for (Map.Entry<TransData.EnterMode, String> et : enterModeMap.entrySet()) {
                if (enterModeString.contains(et.getValue())) {
                    TransData.EnterMode enterMode = et.getKey();
                    transData.setEnterMode(enterMode);
                    break;
                }
            }

        }

        // Transaction Inquiry必须返回卡号和寻卡模式，否则完成之后历史记录详情页会空指针崩溃
        if (transType == ETransType.TRANS_INQUIRY && (transData.getEnterMode() == null || transData.getIssuer() == null || transData.getPan() == null)) {
            return TransResult.ERR_PACKET;
        }

        // field 23 卡片序列号
        buff = map.get("23");
        if (buff != null && buff.length > 0) {
            transData.setCardSerialNo(new String(buff));
        }
        // field 25
        // field 26

        // field 35
        // field 36

        // field 37 检索参考号
        buff = map.get("37");
        if (buff != null && buff.length > 0) {
            transData.setRefNo(new String(buff));
            LogUtils.i(TAG, "Response RRN :  " + new String(buff));
        }

        // field 38 授权码
        buff = map.get("38");
        if (buff != null && buff.length > 0) {
            transData.setAuthCode(new String(buff));
        }

        // field 44
        buff = map.get("44");
        if (buff != null && buff.length > 11) {
            String temp = new String(buff).substring(0, 11).trim();
            transData.setIssuerCode(temp);
            if (buff.length > 11) {
                temp = new String(buff).substring(11).trim();
                transData.setAcqCode(temp);
            }
        }
        // field 48
        buff = map.get("48");
        if (buff != null && buff.length > 0) {
            if (transData.getTransType() == ETransType.LOGON) {
                transData.setField48(Tools.bcd2Str(buff));
            } else {
                transData.setField48(new String(buff));
            }
            unpackField48(transData);
        }

        // field 52

        // field 53

        // field 54

        // field 55
        buff = map.get("55");
        if (buff != null && buff.length > 0) {
            transData.setRecvIccData(ConvertHelper.getConvert().bcdToStr(buff));
        }

        // field 58

        // field 60
        buff = map.get("60");
        if (buff != null && buff.length > 0) {
            String buffStr = new String(buff);
            if (buffStr.length() >= 8) { // 确保字符串长度至少为8，避免StringIndexOutOfBoundsException
                transData.setBatchNo(Utils.parseLongSafe(buffStr.substring(2, 8), -1));
            } else {
                return TransResult.ERR_UNPACK;
            }
        }

        // field 61
        buff = map.get("61");
        if (buff != null && buff.length > 0
                && null != transData.getIssuer() && transData.getIssuer().getName().equals("AMEX")) {
            transData.setAmexSeNum(new String(buff));
        }

        // field 62
        buff = map.get("62");
        if (buff != null && buff.length > 0) {
            transData.setField62(ConvertHelper.getConvert().bcdToStr(buff));
            // 如果是PreAuth的查询，则把联网请求的62域代表的invoiceNo存下来
            if (transData.getTransType() == ETransType.TRANS_INQUIRY) {
                transData.setOrigInvoiceNo(Utils.parseLongSafe(new String(buff), transData.getInvoiceNo()));
            }
        }

        // field 63
        buff = map.get("63");
        if (buff != null && buff.length > 0) {
            if (transData.getTransType() == ETransType.QR || transData.isDcc() || transData.getTransType() == ETransType.TRANS_INQUIRY) {
                transData.setField63(new String(buff));
            } else if (transData.getTransType() == ETransType.SALE ||
                    transData.getTransType() == ETransType.SALE_CASH) {
                transData.setField63(Tools.bcd2Str(buff));
                String result = Utils.hexToAscii(Tools.bcd2Str(buff));
                if (result.length() >= 28) {
                    String tableID = result.substring(0, 2);
                    if ("44".equals(tableID)) {
                        String surcharge = result.substring(2, 14);
                        transData.setSurchargeAmount(surcharge);
                    }
                    tableID = result.substring(14, 16);
                    if ("39".equals(tableID)) {
                        String tax = result.substring(16, 28);
                        transData.setTaxAmount(tax);
                    }
                }
            } else {
                transData.setField63(new String(buff));
            }
        }
        //设置完63域之后开始检查各个域的内容并设值,并且排除冲正时的情况
        if (transData.getReversalStatus() != TransData.ReversalStatus.REVERSAL) {
            switch (transType) {
                case SALE:
                case PREAUTH:
                    // SALE交易后台未返回V8表，因此不校验Tip
                    if ((transData.isDcc() && !checkCcTableFormat(transData))) {
                        transData.setDcc(false);
                        transData.setRate(null);
                    }
                    break;
                case PREAUTH_COMPLETE_ADVICE:
                    if ((transData.isDcc() && !checkCcTableFormat(transData))) {
                        return TransResult.ERR_PACKET;
                    }
                    break;
                case SALE_ADVICE:
                    if (!checkTipFormat(transData) || !check58TableFormat(transData)) {
                        return TransResult.ERR_PACKET;
                    }
                    break;
                case PREAUTH_ADVICE:
                    if (!check58TableFormat(transData) || !checkSDTableFormat(transData)) {
                        return TransResult.ERR_PACKET;
                    }
                    break;
                case ADJUST:
                    if (!checkTipFormat(transData)) {
                        return TransResult.ERR_PACKET;
                    }
                    break;
                case PREAUTHCANCEL:
                    if (transData.isDcc() && !check58TableFormat(transData)) {
                        return TransResult.ERR_PACKET;
                    }
                    break;
                case TRANS_INQUIRY:
                    if (!checkCcTableFormat(transData) || !checkSDTableFormat(transData)) {
                        return TransResult.ERR_PACKET;
                    }
                    break;
            }
        }

        // field 64
        // 解包校验mac
        byte[] data = new byte[rsp.length - 11 - 8];
        System.arraycopy(rsp, 11, data, 0, data.length);
        buff = map.get("64");
        if (buff != null && buff.length > 0 && listener != null) {
            byte[] mac = listener.onCalcMac(data);
            if (!ConvertHelper.getConvert().isByteArrayValueSame(buff, 0, mac, 0, 8)) {
                return TransResult.ERR_MAC;
            }
        }

        return TransResult.SUCC;
    }

    private void addField(int fieldId, TransData transData) throws Iso8583Exception {
        if (fieldId < 1 || fieldId > 128) {
            throw new IllegalArgumentException("filedId == " + fieldId + ", fieldId should be 1-64");
        }
        switch (fieldId) {
            case 1:
                setBitData1(transData);
                break;
            case 2:
                setBitData2(transData);
                break;
            case 3:
                setBitData3(transData);
                break;
            case 4:
                setBitData4(transData);
                break;
            case 5:
                setBitData5(transData);
                break;
            case 6:
                setBitData6(transData);
                break;
            case 7:
                setBitData7(transData);
                break;
            case 8:
                setBitData8(transData);
                break;
            case 9:
                setBitData9(transData);
                break;
            case 10:
                setBitData10(transData);
                break;
            case 11:
                setBitData11(transData);
                break;
            case 12:
                setBitData12(transData);
                break;
            case 13:
                setBitData13(transData);
                break;
            case 14:
                setBitData14(transData);
                break;
            case 15:
                setBitData15(transData);
                break;
            case 16:
                setBitData16(transData);
                break;
            case 17:
                setBitData17(transData);
                break;
            case 18:
                setBitData18(transData);
                break;
            case 19:
                setBitData19(transData);
                break;
            case 20:
                setBitData20(transData);
                break;
            case 21:
                setBitData21(transData);
                break;
            case 22:
                setBitData22(transData);
                break;
            case 23:
                setBitData23(transData);
                break;
            case 24:
                setBitData24(transData);
                break;
            case 25:
                setBitData25(transData);
                break;
            case 26:
                setBitData26(transData);
                break;
            case 27:
                setBitData27(transData);
                break;
            case 28:
                setBitData28(transData);
                break;
            case 29:
                setBitData29(transData);
                break;
            case 30:
                setBitData30(transData);
                break;
            case 31:
                setBitData31(transData);
                break;
            case 32:
                setBitData32(transData);
                break;
            case 33:
                setBitData33(transData);
                break;
            case 34:
                setBitData34(transData);
                break;
            case 35:
                setBitData35(transData);
                break;
            case 36:
                setBitData36(transData);
                break;
            case 37:
                setBitData37(transData);
                break;
            case 38:
                setBitData38(transData);
                break;
            case 39:
                setBitData39(transData);
                break;
            case 40:
                setBitData40(transData);
                break;
            case 41:
                setBitData41(transData);
                break;
            case 42:
                setBitData42(transData);
                break;
            case 43:
                setBitData43(transData);
                break;
            case 44:
                setBitData44(transData);
                break;
            case 45:
                setBitData45(transData);
                break;
            case 46:
                setBitData46(transData);
                break;
            case 47:
                setBitData47(transData);
                break;
            case 48:
                setBitData48(transData);
                break;
            case 49:
                setBitData49(transData);
                break;
            case 50:
                setBitData50(transData);
                break;
            case 51:
                setBitData51(transData);
                break;
            case 52:
                setBitData52(transData);
                break;
            case 53:
                setBitData53(transData);
                break;
            case 54:
                setBitData54(transData);
                break;
            case 55:
                setBitData55(transData);
                break;
            case 56:
                setBitData56(transData);
                break;
            case 57:
                setBitData57(transData);
                break;
            case 58:
                setBitData58(transData);
                break;
            case 59:
                setBitData59(transData);
                break;
            case 60:
                setBitData60(transData);
                break;
            case 61:
                setBitData61(transData);
                break;
            case 62:
                setBitData62(transData);
                break;
            case 63:
                setBitData63(transData);
                break;
            case 64:
                setBitData64(transData);
                break;
            default:
                break;
        }
    }

    /**
     * 检查请求和返回的关键域field4, field11, field41, field42
     *
     * @param map        解包后的map
     * @param transData  请求
     * @param isCheckAmt 是否检查field4
     */
    protected int checkRecvData(@NonNull HashMap<String, byte[]> map, @NonNull TransData transData, boolean isCheckAmt) {
        // 交易金额
        if (isCheckAmt && !checkAmount(map, transData)) {
            return TransResult.ERR_TRANS_AMT;
        }

        // 校验11域
        if (!checkTraceNo(map, transData)) {
            return TransResult.ERR_TRACE_NO;
        }

        // 校验终端号
        if (!checkTerminalId(map, transData)) {
            return TransResult.ERR_TERM_ID;
        }

        // 校验商户号
        if (!checkMerchantId(map, transData)) {
            return TransResult.ERR_MERCH_ID;
        }

        return TransResult.SUCC;
    }

    protected void setBitData1(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        String data = transData.getPan();
        int len = data.length();
        if ((len % 8) != 0) {
            len += 8 - (len % 8);
            data = Component.getPaddedString(data, len, '0');
        }

        //byte[] block = DesUtils.enTdesCBC(Constants.INDEX_TDK, new byte[] { 0, 0, 0, 0, 0, 0, 0, 0 }, data.getBytes());
        //if (block != null && block.length != 0) {
        //    setBitData("2", Tools.bcd2Str(block));
        //    LogUtils.i(TAG, "PAN: " + Tools.bcd2Str(data.getBytes()));
        //    LogUtils.i(TAG, "Encrypted PAN: " + Tools.bcd2Str(block));
        //}

        //Roy,修改为Dukpt，用作临时版本，TODO
        try {
            DUKPTResult dukptResult = Device.calDukptData(Constants.INDEX_DUKPT_DATA, new byte[]{0, 0, 0, 0, 0, 0, 0, 0}, data.getBytes());
            if ((dukptResult.getResult() != null && dukptResult.getResult().length != 0)) {
                transData.setDataKsn(ConvertHelper.getConvert().bcdToStr(dukptResult.getKsn()));
                setBitData("2", Tools.bcd2Str(dukptResult.getResult()));
                LogUtils.i(TAG, "PAN: " + Tools.bcd2Str(data.getBytes()));
                LogUtils.i(TAG, "Encrypted PAN: " + Tools.bcd2Str(dukptResult.getResult()));
            }
        } catch (PedDevException e) {
            throw new RuntimeException(e);
        }
    }

    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("3", transData.getTransType().getProcCode());
    }

    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        String temp;
        if ((transData.getTransType() == ETransType.SALE_ADVICE ||
                transData.getTransType() == ETransType.PREAUTH_ADVICE ||
                transData.getTransType() == ETransType.PREAUTHCANCEL || transData.getTransType() == ETransType.PREAUTH_COMPLETE_ADVICE) && transData.isDcc()) {
            temp = transData.getInternationalCurrencyAmount();
        } else {
            temp = transData.getAmount();
        }
        if (TextUtils.isEmpty(temp)) {
            temp = "0";
        }
        setBitData("4", temp);
    }

    protected void setBitData5(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData6(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData7(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData8(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData9(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData10(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData11(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("11", Component.getPaddedNumber(transData.getTraceNo(), 6));
    }

    protected void setBitData12(@NonNull TransData transData) throws Iso8583Exception {
        String temp = transData.getDateTime();
        if (temp != null && !temp.isEmpty()) {
            String date = temp.substring(4, 8);
            String time = temp.substring(8);
            setBitData("12", time);
            setBitData("13", date);
        }
    }

    protected void setBitData13(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("14", transData.getExpDate());
    }

    protected void setBitData15(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData16(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData17(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData18(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData19(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData20(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData21(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        Issuer issuer = transData.getIssuer();
        String issuerName = issuer == null ? "" : issuer.getName();
        setBitData("22", getInputMethod(transData.getEnterMode(), issuerName));
    }

    protected void setBitData23(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("23", transData.getCardSerialNo());
    }

    protected void setBitData24(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("24", transData.getAcquirer().getNii());
    }

    protected void setBitData25(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("25", transData.getTransType().getServiceCode());
    }

    protected void setBitData26(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData27(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData28(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData29(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData30(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData31(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData32(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData33(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData34(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() != TransData.EnterMode.MANUAL) {
            String data = transData.getTrack2();
            int len = data.length();
            if ((len % 8) != 0) {
                len += 8 - (len % 8);
                data = Component.getPaddedString(data, len, '0');
            }

            data = data.replace('=', 'D');
            //byte[] block = DesUtils.enTdesCBC(Constants.INDEX_TDK, new byte[]{0, 0, 0, 0, 0, 0, 0, 0}, data.getBytes());
            //if (block != null && block.length != 0) {
            //    setBitData("35", Tools.bcd2Str(block));
            //    LogUtils.i(TAG, "Track2: " + Tools.bcd2Str(data.getBytes()));
            //    LogUtils.i(TAG, "Encrypted Track2: " + Tools.bcd2Str(block));
            //}
            //Roy,修改为Dukpt，用作临时版本，TODO
            try {
                DUKPTResult dukptResult = Device.calDukptData(Constants.INDEX_DUKPT_DATA, new byte[]{0, 0, 0, 0, 0, 0, 0, 0}, data.getBytes());
                if ((dukptResult.getResult() != null && dukptResult.getResult().length != 0)) {
                    transData.setDataKsn(ConvertHelper.getConvert().bcdToStr(dukptResult.getKsn()));
                    setBitData("35", Tools.bcd2Str(dukptResult.getResult()));
                    LogUtils.i(TAG, "Track2: " + Tools.bcd2Str(data.getBytes()));
                    LogUtils.i(TAG, "Encrypted Track2: " + Tools.bcd2Str(dukptResult.getResult()));
                }
            } catch (PedDevException e) {
                throw new RuntimeException(e);
            }
        }
    }

    protected void setBitData36(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("36", transData.getTrack3());
    }

    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType().equals(ETransType.VOID)) {
            setBitData("37", transData.getOrigRefNo());
        } else {
            setBitData("37", transData.getRefNo());
        }
    }

    protected void setBitData38(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("38", transData.getOrigAuthCode());
    }

    protected void setBitData39(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData40(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData41(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("41", transData.getAcquirer().getTerminalId());
    }

    protected void setBitData42(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("42", transData.getAcquirer().getMerchantId());
    }

    protected void setBitData43(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData44(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData45(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData46(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData47(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData48(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("48", transData.getField48());
    }

    protected void setBitData49(@NonNull TransData transData) throws Iso8583Exception {
    }

    protected void setBitData50(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData51(@NonNull TransData transData) throws Iso8583Exception {

    }

    protected void setBitData52(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isHasPin() && transData.getPin() != null) {
            setBitData("52", ConvertHelper.getConvert().strToBcdPaddingLeft(transData.getPin()));
        }
    }

    protected void setBitData53(@NonNull TransData transData) throws Iso8583Exception {
        //Roy,修改为Dukpt，用作临时版本，TODO
        String ksn = "0020";
        if (TextUtils.isEmpty(transData.getPinKsn())) {
            ksn += "00000000000000000000";
        } else {
            ksn += transData.getPinKsn();
        }
        if (TextUtils.isEmpty(transData.getDataKsn())) {
            ksn += "00000000000000000000";
        } else {
            ksn += transData.getDataKsn();
        }
        setBitData("53", ConvertHelper.getConvert().strToBcdPaddingLeft(ksn));
    }

    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        String temp = transData.getTipAmount();
        if (!TextUtils.isEmpty(temp) && Utils.parseLongSafe(temp) > 0) {
            CurrencyCode localCurrency = transData.getLocalCurrency();
            String code = localCurrency.getCode();
            if (!TextUtils.isEmpty(code)) {
                temp = "1056" + code + "C" + Component.getPaddedString(temp, 12, '0');
                setBitData("54", temp);
            }
        }
    }

    protected void setBitData55(@NonNull TransData transData) throws Iso8583Exception {
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (TransData.EnterMode.INSERT == enterMode || TransData.EnterMode.CLSS == enterMode) {
            String temp = transData.getSendIccData();
            if (temp != null && temp.length() > 0) {
                setBitData("55", ConvertHelper.getConvert().strToBcdPaddingLeft(temp));
            }
        }
    }

    protected void setBitData56(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("56", Component.getPaddedNumber(transData.getBatchNo(), 6));
    }

    protected void setBitData57(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData58(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData59(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {

    }

    protected void setBitData61(@NonNull TransData transData) throws Iso8583Exception {
    }

    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType().equals(ETransType.VOID) ||
                transData.getTransType().equals(ETransType.E_CHARGE_SLIP)) {
            setBitData("62", Component.getPaddedNumber(transData.getOrigInvoiceNo(), 6));
        } else {
            setBitData("62", Component.getPaddedNumber(transData.getInvoiceNo(), 6));
        }
    }

    // set field 63
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("63", transData.getField63());
    }

    protected void setBitData64(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("64", new byte[]{
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00
        });
    }

    /**
     *
     */
    protected final String getInputMethod(TransData.EnterMode enterMode, String issuerName) {
        if (enterMode == null) { //AET-40
            return null;
        }

        String inputMethod;
        try {
            //So Fallback  "90" is only for the VISA/MAXSTRECARD/AMEX， “801” is for Rupay/DPAS/JCB/CUP
            if (enterMode == TransData.EnterMode.FALLBACK && !issuerName.equals(Constants.ISSUER_VISA)
                    && !issuerName.equals(Constants.ISSUER_MASTER)
                    && !issuerName.equals(Constants.ISSUER_AMEX)) {
                inputMethod = "080";
            } else {
                inputMethod = enterModeMap.get(enterMode);
            }
        } catch (Exception e) {
            LogUtils.w(TAG, "", e);
            return null;
        }
        //0     Unknown
        //1     Terminal can accept PIN
        //2     Terminal cannot accept PIN
        inputMethod += "1";

        return inputMethod;
    }

    private boolean checkAmount(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("4");

        if (data != null && data.length > 0) {
            String temp = new String(data);
            if (transData.getTransType() == ETransType.VOID) {
                return Utils.parseLongSafe(temp, 0) == 0L;
            }
            String transDataTemp;
            if ((transData.getTransType() == ETransType.SALE_ADVICE ||
                    transData.getTransType() == ETransType.PREAUTH_ADVICE ||
                    transData.getTransType() == ETransType.PREAUTHCANCEL) && transData.isDcc()) {
                transDataTemp = transData.getInternationalCurrencyAmount();
            } else {
                transDataTemp = transData.getAmount();
            }
            return Utils.parseLongSafe(temp, 0) == Utils.parseLongSafe(transDataTemp, 0);
        }
        return true;
    }

    private boolean checkTraceNo(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("11");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            //当冲正void的时候还是使用traceNo，因为Reversla的记录是新增的记录
            if (transData.getTransType().equals(ETransType.VOID) && transData.getReversalStatus() != TransData.ReversalStatus.REVERSAL) {
                return temp.equals(Component.getPaddedNumber(transData.getOrigTransNo(), 6));
            }
            return temp.equals(Component.getPaddedNumber(transData.getTraceNo(), 6));
        }
        return true;
    }

    private boolean checkTerminalId(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("41");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(transData.getAcquirer().getTerminalId());
        }
        return true;
    }

    private boolean checkMerchantId(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("42");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(transData.getAcquirer().getMerchantId());
        }
        return true;
    }

    /*********************************************
     unpack
     ********************************************/
    public int unpackField48(TransData transData) {
        return TransResult.SUCC;
    }

    protected String getLength(String strIn) {
        //如果是空内容，则返回两位长度00
        if (TextUtils.isEmpty(strIn)) {
            return "00";
        }
        return Component.getPaddedNumber(strIn.length(), 2);
    }


    private boolean checkCcTableFormat(TransData transData) {
        String field63 = transData.getField63();
        //由于CC表是第一个，所以直接用startWith来判断，防止其他内容是包含CC的导致判断失误
        if (field63 == null || field63.length() < 38 || !field63.startsWith("CC")) {
            return transData.getTransType() == ETransType.TRANS_INQUIRY;
        }

        String currencyCode = field63.substring(5, 8);
        CurrencyCode byCode = CurrencyCode.getByCode(currencyCode);
        if (byCode == null) {
            return false;
        }
        CurrencyCode.updateCurrencyCodeList(byCode);
        String internationalCurrencyAmount = field63.substring(8, 20);
        //校验金额是否合格
        if (!Utils.checkValueWithRegular(NUMBER_PATTERN, internationalCurrencyAmount)) {
            return false;
        }
        String rate = field63.substring(20, 28);
        //检查汇率是否合格
        try {
            Utils.initRate(rate);
        } catch (StringIndexOutOfBoundsException stringIndexOutOfBoundsException) {
            LogUtils.e(TAG, stringIndexOutOfBoundsException);
            return false;
        }
        String commission = field63.substring(28, 33);
        String makeUp = field63.substring(33, 38);
        if (!Utils.checkValueWithRegular(NUMBER_PATTERN, internationalCurrencyAmount) ||
                !Utils.checkValueWithRegular(OTHER_PATTERN, commission) ||
                !Utils.checkValueWithRegular(OTHER_PATTERN, makeUp)) {
            return false;
        }
        transData.setInternationalCurrencyAmount(internationalCurrencyAmount);
        transData.setInternationalCurrency(byCode);
        transData.setRate(rate);
        transData.setCommission(commission);
        transData.setMakeUp(makeUp);
        if (transData.getTransType() == ETransType.TRANS_INQUIRY) {
            transData.setDcc(true);
        }
        return true;
    }

    private boolean checkTipFormat(TransData transData) {
        String field63 = transData.getField63();
        if (TextUtils.isEmpty(transData.getTipAmount()) || "0".equals(transData.getTipAmount())) {
            return true;
        }
        if (transData.isDcc()) {
            if (field63 == null) {
                return false;
            }
            int v8Index = field63.indexOf("V8");
            if (v8Index == -1) {
                return false;
            }
            String tipFiled = field63.substring(v8Index);
            if (tipFiled.length() < 44) {
                return false;
            }
            String tipAmount = tipFiled.substring(2, 14);
            String totalAmount = tipFiled.substring(14, 26);
            String rate = tipFiled.substring(26, 34);
            // 检查汇率是否合格
            try {
                Utils.initRate(rate);
            } catch (StringIndexOutOfBoundsException stringIndexOutOfBoundsException) {
                LogUtils.e(TAG, stringIndexOutOfBoundsException);
                return false;
            }
            String commission = tipFiled.substring(34, 39);
            String makeUp = tipFiled.substring(39, 44);
            if (!Utils.checkValueWithRegular(NUMBER_PATTERN, tipAmount) ||
                    !Utils.checkValueWithRegular(NUMBER_PATTERN, totalAmount) ||
                    !Utils.checkValueWithRegular(OTHER_PATTERN, commission) ||
                    !Utils.checkValueWithRegular(OTHER_PATTERN, makeUp)) {
                return false;
            }
            transData.setTipAmount(tipAmount);
            transData.setAmount(totalAmount);
            transData.setRate(rate);
            transData.setCommission(commission);
            transData.setMakeUp(makeUp);
        } else if (transData.getTransType() == ETransType.ADJUST) {
            if (field63 == null || !field63.startsWith("38") || field63.length() < 14) {
                return false;
            }
            String tipAmount = field63.substring(2, 14);
            if (!Utils.checkValueWithRegular(NUMBER_PATTERN, tipAmount)) {
                return false;
            } else {
                transData.setTipAmount(tipAmount);
            }
        }
        return true;
    }

    private boolean check58TableFormat(TransData transData) {
        String field63 = transData.getField63();
        String customTableId = "";
        if (field63 == null) {
            return false;
        }
        switch (transData.getTransType()) {
            case SALE_ADVICE:
                customTableId = "TT";
                break;
            case PREAUTHCANCEL:
            case PREAUTH_ADVICE:
                customTableId = "TP";
                break;
            default:
                return false;
        }
        int tableIndex = field63.indexOf(customTableId + "DCC58");
        if (tableIndex == -1 || field63.substring(tableIndex).length() < 40) {
            return false;
        }

        String currencyCode = field63.substring(tableIndex + 7, tableIndex + 10);
        CurrencyCode byCode = CurrencyCode.getByCode(currencyCode);
        if (byCode == null) {
            return false;
        }
        CurrencyCode.updateCurrencyCodeList(byCode);
        String localCurrencyAmount = field63.substring(tableIndex + 10, tableIndex + 22);
        String rate = field63.substring(tableIndex + 22, tableIndex + 30);
        try {
            Utils.initRate(rate);
        } catch (StringIndexOutOfBoundsException stringIndexOutOfBoundsException) {
            LogUtils.e(TAG, stringIndexOutOfBoundsException);
            return false;
        }
        String commission = field63.substring(tableIndex + 30, tableIndex + 35);
        String makeUp = field63.substring(tableIndex + 35, tableIndex + 40);

        if (!Utils.checkValueWithRegular(NUMBER_PATTERN, localCurrencyAmount) ||
                !Utils.checkValueWithRegular(OTHER_PATTERN, commission) ||
                !Utils.checkValueWithRegular(OTHER_PATTERN, makeUp)) {
            return false;
        }
        transData.setAmount(localCurrencyAmount);
        transData.setLocalCurrency(byCode);
        transData.setRate(rate);
        transData.setCommission(commission);
        transData.setMakeUp(makeUp);
        return true;
    }


    private boolean checkSDTableFormat(TransData transData) {
        String field63 = transData.getField63();
        // Transaction Inquiry需要解析SD表内容，打印在小票上
        List<String> originalCardData = Utils.parseData("SD", 4, field63);
        if (originalCardData.size() < 6) {
            return false;
        } else {
            transData.setCardHolderName(originalCardData.get(0));
            transData.setEmvAppName(originalCardData.get(1));
            transData.setAid(originalCardData.get(2));
            transData.setTc(originalCardData.get(3));
            transData.setTvr(originalCardData.get(4));
            transData.setTsi(originalCardData.get(5));
        }
        return true;
    }
}
