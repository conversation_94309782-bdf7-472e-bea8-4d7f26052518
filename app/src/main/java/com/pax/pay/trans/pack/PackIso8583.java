package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;
import com.pax.abl.core.ipacker.IPacker;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.abl.utils.TrackUtils;
import com.pax.edc.opensdk.TransResult;
import com.pax.gl.pack.IIso8583;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.jemv.clcommon.TransactionPath;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.Issuer;
import com.pax.pay.emv.CardBin;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.RedeemConfig;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.PrintUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.xmlpull.v1.XmlPullParserException;

/**
 * Created by liliang on 2017/12/12.
 */

public abstract class PackIso8583 implements IPacker<TransData, byte[]> {
    private static final String TAG = "PackIso8583";

    protected IIso8583 iso8583;
    protected IIso8583.IIso8583Entity entity;
    protected PackListener listener;

    // define different enter  mode with number
    private static final Map<TransData.EnterMode, String> enterModeMap = new EnumMap<>(TransData
            .EnterMode.class);

    static {
        enterModeMap.put(TransData.EnterMode.MANUAL, "01");
        enterModeMap.put(TransData.EnterMode.SWIPE, "90");
        enterModeMap.put(TransData.EnterMode.INSERT, "05");
        enterModeMap.put(TransData.EnterMode.CLSS, "07");
        enterModeMap.put(TransData.EnterMode.FALLBACK, "80");
        enterModeMap.put(TransData.EnterMode.QR, "03");
    }

    // define different redemption  mode with number
    protected static final Map<Integer, String> redemptionFlagMap =
            new HashMap<Integer, String>();

    static {
        redemptionFlagMap.put(RedeemConfig.REDEEM_NONE, "000");
        redemptionFlagMap.put(RedeemConfig.REDEEM_M2, "001");
        redemptionFlagMap.put(RedeemConfig.REDEEM_M1, "010");
        redemptionFlagMap.put(RedeemConfig.REDEEM_MD, "011");
        redemptionFlagMap.put(RedeemConfig.REDEEM_PRIMARY, "100");
        redemptionFlagMap.put(RedeemConfig.REDEEM_ALL, "111");
    }

    public PackIso8583(PackListener listener) {
        this.listener = listener;
        initEntity();
    }

    private void initEntity() {
        iso8583 = FinancialApplication.getPacker().getIso8583();
        try {
            entity = iso8583.getEntity();
            entity.loadTemplate(FinancialApplication.getApp().getResources().getAssets().open("edc8583.xml"));
        } catch (Iso8583Exception | IOException | XmlPullParserException e) {
            Log.e(TAG, "", e);
        }
    }

    protected abstract int[] getRequiredFields();

    @Override
    public byte[] pack(TransData transData) {
        int[] fields = getRequiredFields();
        try {
            // h
            setHeader(transData);
            // m
            setMessageType(transData);

            for (int i : fields) {
                addField(i, transData);
            }

            byte[] packData = iso8583.pack(); //基于ISO8583实体的设置和值进行组包
            if (packData == null || packData.length == 0) {
                return new byte[0];
            }

            if (entity.hasField("64")) {
                int len = packData.length;
                byte[] calMacBuf = new byte[len - 11 - 8];// 去掉header和mac
                System.arraycopy(packData, 11, calMacBuf, 0, len - 11 - 8);
                byte[] mac = listener.onCalcMac(calMacBuf);
                if (mac.length == 0) {
                    return new byte[0];
                }
                System.arraycopy(mac, 0, packData, len - 8, 8);
            }

            return packData;
        } catch (Iso8583Exception e) {
            Log.e(TAG, "", e);
        } catch (Exception e) {
            Log.e(TAG, "", e);
        }

        return new byte[0];
    }

    /**
     * @param isNeedMac isNeedMac
     * @return byte[]
     */
    @NonNull
    protected byte[] pack(boolean isNeedMac) {
        try {
            if (isNeedMac) {
                setBitData("64", new byte[] { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 });
            }
            // for debug entity.dump();
            byte[] packData = iso8583.pack();

            if (isNeedMac) {
                if (packData == null || packData.length == 0) {
                    return "".getBytes();
                }

                int len = packData.length;

                byte[] calMacBuf = new byte[len - 11 - 8];//去掉header和mac
                System.arraycopy(packData, 11, calMacBuf, 0, len - 11 - 8);
                byte[] mac = listener.onCalcMac(calMacBuf);
                if (mac.length == 0) {
                    return "".getBytes();
                }
                System.arraycopy(mac, 0, packData, len - 8, 8);
            }

            if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_PRINT_LOG)) {
                PrintUtils.printLogs(packData, true);
            }

            return packData;
        } catch (Iso8583Exception e) {
            Log.e(TAG, "", e);
        }
        return "".getBytes();
    }

    /**
     * @param fieldId int
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    private void addField(int fieldId, TransData transData) throws Iso8583Exception {
        if (fieldId < 1 || fieldId > 64) {
            throw new IllegalArgumentException("filedId == " + fieldId +
                    ", fieldId should be 1-64");
        }
        switch (fieldId) {
            case 1:
                setBitData1(transData);
                break;
            case 2:
                setBitData2(transData);
                break;
            case 3:
                setBitData3(transData);
                break;
            case 4:
                setBitData4(transData);
                break;
            case 5:
                setBitData5(transData);
                break;
            case 6:
                setBitData6(transData);
                break;
            case 7:
                setBitData7(transData);
                break;
            case 8:
                setBitData8(transData);
                break;
            case 9:
                setBitData9(transData);
                break;
            case 10:
                setBitData10(transData);
                break;
            case 11:
                setBitData11(transData);
                break;
            case 12:
                setBitData12(transData);
                break;
            case 13:
                setBitData13(transData);
                break;
            case 14:
                setBitData14(transData);
                break;
            case 15:
                setBitData15(transData);
                break;
            case 16:
                setBitData16(transData);
                break;
            case 17:
                setBitData17(transData);
                break;
            case 18:
                setBitData18(transData);
                break;
            case 19:
                setBitData19(transData);
                break;
            case 20:
                setBitData20(transData);
                break;
            case 21:
                setBitData21(transData);
                break;
            case 22:
                setBitData22(transData);
                break;
            case 23:
                setBitData23(transData);
                break;
            case 24:
                setBitData24(transData);
                break;
            case 25:
                setBitData25(transData);
                break;
            case 26:
                setBitData26(transData);
                break;
            case 27:
                setBitData27(transData);
                break;
            case 28:
                setBitData28(transData);
                break;
            case 29:
                setBitData29(transData);
                break;
            case 30:
                setBitData30(transData);
                break;
            case 31:
                setBitData31(transData);
                break;
            case 32:
                setBitData32(transData);
                break;
            case 33:
                setBitData33(transData);
                break;
            case 34:
                setBitData34(transData);
                break;
            case 35:
                setBitData35(transData);
                break;
            case 36:
                setBitData36(transData);
                break;
            case 37:
                setBitData37(transData);
                break;
            case 38:
                setBitData38(transData);
                break;
            case 39:
                setBitData39(transData);
                break;
            case 40:
                setBitData40(transData);
                break;
            case 41:
                setBitData41(transData);
                break;
            case 42:
                setBitData42(transData);
                break;
            case 43:
                setBitData43(transData);
                break;
            case 44:
                setBitData44(transData);
                break;
            case 45:
                setBitData45(transData);
                break;
            case 46:
                setBitData46(transData);
                break;
            case 47:
                setBitData47(transData);
                break;
            case 48:
                setBitData48(transData);
                break;
            case 49:
                setBitData49(transData);
                break;
            case 50:
                setBitData50(transData);
                break;
            case 51:
                setBitData51(transData);
                break;
            case 52:
                setBitData52(transData);
                break;
            case 53:
                setBitData53(transData);
                break;
            case 54:
                setBitData54(transData);
                break;
            case 55:
                setBitData55(transData);
                break;
            case 56:
                setBitData56(transData);
                break;
            case 57:
                setBitData57(transData);
                break;
            case 58:
                setBitData58(transData);
                break;
            case 59:
                setBitData59(transData);
                break;
            case 60:
                setBitData60(transData);
                break;
            case 61:
                setBitData61(transData);
                break;
            case 62:
                setBitData62(transData);
                break;
            case 63:
                setBitData63(transData);
                break;
            case 64:
                setBitData64(transData);
                break;
            default:
                break;
        }
    }

    /**
     * unpack
     *
     * @param transData transData
     * @param rsp rsp
     */
    @Override
    public int unpack(@NonNull TransData transData, final byte[] rsp) {

        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_PRINT_LOG)) {
            PrintUtils.printLogs(rsp, false);
        }

        HashMap<String, byte[]> map;
        try {
            map = iso8583.unpack(rsp, true);
            // 调试信息， 日志输入解包后数据
            entity.dump();
        } catch (Iso8583Exception e) {
            Log.e(TAG, "", e);
            return TransResult.ERR_UNPACK;
        }

        // 报文头
        byte[] header = map.get("h");
        // TPDU检查
        String rspTpdu = new String(header).substring(0, 10);
        String reqTpdu = "600" + transData.getAcquirer().getNii() + "0000";
        if (!rspTpdu.substring(2, 6).equals(reqTpdu.substring(6, 10))
                || !rspTpdu.substring(6, 10).equals(reqTpdu.substring(2, 6))) {
            return TransResult.ERR_UNPACK;
        }

        ETransType transType = transData.getTransType();

        byte[] buff;
        // 检查39域应答码
        buff = map.get("39");
        if (buff == null) {
            return TransResult.ERR_PACKET;
        }

        transData.setResponseCode(new String(buff));

        // 检查返回包的关键域， 包含field4
        boolean isCheckAmt = true;
        if (transType == ETransType.EMV_SETTLE || transType == ETransType.LYS_SETTLE || transType == ETransType.CUP_SETTLE || transType == ETransType.ONLINE_ENQUIRY) {
            isCheckAmt = false;
        }

//        int ret = checkRecvData(map, transData, isCheckAmt);
//        if (ret != TransResult.SUCC) {
//            return ret;
//        }

        // field 2 主账号

        // field 3 交易处理码
        buff = map.get("3");
        if (buff != null && buff.length > 0) {
            String origField3 = transData.getField3();
            if (origField3 != null && !origField3.isEmpty() && !origField3.equals(new String(buff))) {
                return TransResult.ERR_PROC_CODE;
            }
        }
        // field 4 交易金额
        buff = map.get("4");
        if (buff != null && buff.length > 0) {
            if (transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR) {   //Jerry Modify
                transData.setNetAmount(Long.parseLong(new String(buff)));
            } else {
                transData.setAmount(Long.parseLong(new String(buff)));
            }
        }

        //field 10 rate
        buff = map.get("10");
        if (buff != null && buff.length > 0) {
            transData.setRate((new String(buff)));
        }

        // field 11 流水号
        buff = map.get("11");
        if (buff != null && buff.length > 0) {
            transData.setTraceNo(Utils.parseLongSafe(new String(buff), -1));
        }

        // field 13 受卡方所在地日期
        String dateTime = "";
        buff = map.get("13");
        if (buff != null) {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            dateTime = year + new String(buff);
        }

        // field 12 受卡方所在地时间
        buff = map.get("12");
        if (buff != null && buff.length > 0) {
            transData.setDateTime(dateTime + new String(buff));
        }

        // field 14 卡有效期
        buff = map.get("14");
        if (buff != null && buff.length > 0) {
            String expDate = new String(buff);
            if (!"0000".equals(expDate)) {
                transData.setExpDate(expDate);
            }
        }

        // field 22

        // field 23 卡片序列号
        buff = map.get("23");
        if (buff != null && buff.length > 0) {
            transData.setCardSerialNo(new String(buff));
        }
        // field 25
        // field 26

        // field 35
        // field 36

        // field 37 检索参考号
        buff = map.get("37");
        if (buff != null && buff.length > 0 && transType != ETransType.ENQUIRY &&
                (transData.isDcc() || (transData.getRefNo() == null || transData.getRefNo().isEmpty()))) {
            transData.setRefNo(new String(buff));
            if (transData.getTransType() == ETransType.PREAUTH){
                transData.setTraceRrnNo(transData.getRefNo());
            }
        }

        // field 38 授权码
        buff = map.get("38");
        if (buff != null && buff.length > 0 && transType != ETransType.ENQUIRY &&
                (transData.isDcc() || (transData.getAuthCode() == null || transData.getAuthCode().isEmpty()))) {
            transData.setAuthCode(new String(buff));
        }

        if (transData.getResponseCode() != null && transData.getResponseCode().equalsIgnoreCase("00")) {
            if (transData.getRefNo() == null && (transData.getTransType().getMsgType() == "0100" || transData.getTransType().getMsgType() == "0200")
                    && transData.getReversalStatus() != TransData.ReversalStatus.REVERSAL && transData.getTransType() != transType.ENQUIRY) {
                return TransResult.ERR_UNPACK;
            }
        }

        // field 44
        buff = map.get("44");
        if (buff != null && buff.length > 11) {
            String temp = new String(buff).substring(0, 11).trim();
            transData.setIssuerCode(temp);
            if (buff.length > 11) {
                temp = new String(buff).substring(11).trim();
                transData.setAcqCode(temp);
            }
        }
        // field 48
        buff = map.get("48");
        if (buff != null && buff.length > 0) {
            transData.setField48(new String(buff));
        }

        //field 49
        buff = map.get("49");
        if (buff != null && buff.length > 0) {
            CurrencyCode currencyCode = CurrencyCode.getByCode(new String(buff).substring(1));
            transData.setLocalCurrency(currencyCode);
        }

        // field 52

        // field 53

        // field 54

        // field 55
        buff = map.get("55");
        if (buff != null && buff.length > 0) {
            transData.setRecvIccData(Utils.bcd2Str(buff));
        }

        // field 58

        // field 60
        buff = map.get("60");
        if (buff != null && buff.length > 0) {
            if (transType == ETransType.LYS_SETTLE
                    || transType == ETransType.LYS_SETTLE_END_AFE_REJECT
                    || transType == ETransType.LYS_SETTLE_END_LYS_REJECT
                    || transType == ETransType.LYS_SETTLE_END_AFE_LYS_REJECT) {
                transData.setBatchNo(Utils.parseLongSafe(new String(buff), -1));
            } else {
                transData.setBatchNo(Utils.parseLongSafe(new String(buff).substring(2, 8), -1));
            }
        }
        // field 61
        buff = map.get("61");
        if (buff != null && buff.length > 0) {
            transData.setCupRRN(processCupRRN(Utils.bcd2Str(buff).substring(0, 24)));
        }
        // field 62
        buff = map.get("62");
        if (buff != null && buff.length > 0) {
            transData.setField62(Utils.bcd2Str(buff));
        }

        // field 63
        buff = map.get("63");
        if (buff != null && buff.length > 0) {
            if (transData.isDcc()) { // for trans data is dcc
                transData.setField63(FinancialApplication.getConvert().bcdToStr(buff));
                String field63 = transData.getField63();
                if (!transData.isDcc() && transType != ETransType.DOWNLOAD_CARD_BIN && transType != ETransType.DOWNLOAD_RATE_REPORT && transType != ETransType.ENQUIRY) {
                    Component.parseField63(field63, transData);
                }
                //currency info
                if (transType == ETransType.ENQUIRY) {
                    String tag32 = Component.parseDccField63(field63, "3332");
                    if (tag32 != null && !tag32.isEmpty()) {
                        //currency code : 608
                        String currencyCode = new String(FinancialApplication.getConvert().strToBcd(tag32.substring(42, 48), IConvert.EPaddingPosition.PADDING_LEFT));
                        //currency symbol 48-54
                        String currencySymbol = new String(FinancialApplication.getConvert().strToBcd(tag32.substring(48, 54), IConvert.EPaddingPosition.PADDING_LEFT));
                        //digital 56-58
                        int digital = Integer.parseInt(tag32.substring(56, 58));
                        int flag = CurrencyCode.getFlagIdBySymbol(currencySymbol);
                        CurrencyCode code = new CurrencyCode(currencyCode, currencySymbol, digital, flag);
                        transData.setHomeCurrency(code);
                        //update currency code list
                        CurrencyCode.updateCurrencyCodeList(code);
                    }
                }

                //markup rate
                String dccMarkupRate = Component.parseDccField63(field63, "3334");
                if (dccMarkupRate != null && !dccMarkupRate.isEmpty()) {
                    if (dccMarkupRate.length() < 6) {
                        transData.setDccMarkupRate(dccMarkupRate);
                    } else {
                        String temp = dccMarkupRate;
                        //2B : + for a positive markup
                        if ("2B".equalsIgnoreCase(dccMarkupRate.substring(0, 2))) {
                            temp = dccMarkupRate.substring(2, 6);
                        }
                        transData.setDccMarkupRate(temp);
                    }
                }
            } else{
                int ret = UnpackF63(transData, buff); //origin
                if (ret != TransResult.SUCC) {
                    return ret;
                }
            }
        }

        //field 6 外币金额
        buff = map.get("6");
        if (buff != null && buff.length > 0) {
            String amount = new String(buff);
            if (transData.getHomeCurrency().getCode().equals("392") || transData.getHomeCurrency().getCode().equals("410")||
                    transData.getHomeCurrency().getCode().equals("704")|| transData.getHomeCurrency().getCode().equals("152")) { // for currency JPY, KRW, VND, CLP
                amount = amount.substring(0, amount.length() - 2); // amount without two leading zero
            }
            boolean adjustNotSent = transData.getTransState() == TransData.ETransStatus.ADJUSTED &&
                    transData.getOfflineSendState() != null &&
                    transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT;
            if ((transData.getTransType() == ETransType.VOID || transData.getTransType() == ETransType.PREAUTHVOID) && adjustNotSent) {
                transData.setOrigHomeCurrencyAmount(amount);
            } else {
                transData.setHomeCurrencyAmount(amount);
            }
        }

        // field 64
        // 解包校验mac
        byte[] data = new byte[rsp.length - 11 - 8];
        System.arraycopy(rsp, 11, data, 0, data.length);
        buff = map.get("64");
        if (buff != null && buff.length > 0 && listener != null) {
            byte[] mac = listener.onCalcMac(data);
            if (!FinancialApplication.getConvert().isByteArrayValueSame(buff, 0, mac, 0, 8)) {
                return TransResult.ERR_MAC;
            }
        }

        return TransResult.SUCC;
    }

    protected int UnpackF63(@NonNull TransData transData) {
        return TransResult.SUCC;
    }

    protected int UnpackF63(@NonNull TransData transData, byte[] buffer) {
        return TransResult.SUCC;
    }


    protected final void setBitData(String field, String value) throws Iso8583Exception {
        if (!TextUtils.isEmpty(value)) {
            entity.setFieldValue(field, value);
        }
    }

    protected final void setBitData(String field, byte[] value) throws Iso8583Exception {
        if (value != null && value.length > 0) {
            entity.setFieldValue(field, value);
        }
    }

    protected void setHeader(@NonNull TransData transData) throws Iso8583Exception {
        String pHeader = "600" + transData.getAcquirer().getNii() + "0000";
        entity.setFieldValue("h", pHeader);
    }

    protected void setMessageType(@NonNull TransData transData) throws Iso8583Exception {
        ETransType transType = transData.getTransType();
        if (transData.getReversalStatus() == TransData.ReversalStatus.REVERSAL) {
            entity.setFieldValue("m", transType.getDupMsgType());
        } else {
            entity.setFieldValue("m", transType.getMsgType());
        }
    }

    protected void setBitData1(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        ETransType transType = transData.getTransType();
        if (transType == ETransType.OFFLINE_TRANS_SEND || transType == ETransType.VOID
                || transData.getReversalStatus() == TransData.ReversalStatus.REVERSAL) {
            setBitData("2", transData.getPan());
        } else {
            TransData.EnterMode enterMode = transData.getEnterMode();
            if (enterMode == TransData.EnterMode.MANUAL || transData.getEnterMode() == TransData.EnterMode.QR && (transData.getIssuer().getName().equals("MASTER") || transData.getIssuer().getName().equals("HASE_MC")) && !transData.getPan().equals(TrackUtils.getPan(transData.getTrack2()))) {
                setBitData("2", transData.getPan());
            }
        }
    }

    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("3", transData.getTransType().getProcCode());
    }

    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("4", String.format("%012d", transData.getAmount()));
    }

    protected void setBitData5(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData6(@NonNull TransData transData) throws Iso8583Exception{
        if (transData.isDcc()) {
            String amount = transData.getHomeCurrencyAmount();
            if (transData.getHomeCurrency().getCode().equals("392") || transData.getHomeCurrency().getCode().equals("410")) { // for currency JPY, KWR
                amount = amount + "00"; // add two zero
            }
            if (transData.isFullOutSource()) {
                amount = String.valueOf(transData.getAmount());
            }
            setBitData("6", amount);
        }
    }

    protected void setBitData7(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData8(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData9(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData10(@NonNull TransData transData) throws Iso8583Exception{
        if (transData.isDcc()) {
            setBitData("10", transData.getRate()); // set dcc rate
        }
    }

    protected void setBitData11(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("11", String.valueOf(transData.getTraceNo()));
    }

    protected void setBitData12(@NonNull TransData transData) throws Iso8583Exception {
        String temp = transData.getDateTime();
        if (temp != null && !temp.isEmpty()) {
            String date = temp.substring(4, 8);
            String time = temp.substring(8);
            setBitData("12", time);
            setBitData("13", date);
        }
    }

    protected void setBitData13(@NonNull TransData transData) throws Iso8583Exception {
        String Date_MMDD = transData.getDateTime();
        setBitData("13", Date_MMDD.substring(4, 8));
    }

    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        ETransType transType = transData.getTransType();
        if (transType == ETransType.OFFLINE_TRANS_SEND || transType == ETransType.VOID
                || transData.getReversalStatus() == TransData.ReversalStatus.REVERSAL) {
            setBitData("14", transData.getExpDate());
        } else {
            TransData.EnterMode enterMode = transData.getEnterMode();
            if (enterMode == TransData.EnterMode.MANUAL || transType == ETransType.VOID_WITH_CASH_DOLLAR || transData.getEnterMode() == TransData.EnterMode.QR && (transData.getIssuer().getName().equals("MASTER") || transData.getIssuer().getName().equals("HASE_MC")) && !transData.getExpDate().equals(TrackUtils.getExpDate(transData.getTrack2()))) {
                setBitData("14", transData.getExpDate());
            }
        }
    }

    protected void setBitData15(@NonNull TransData transData) {

    }

    protected void setBitData16(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData17(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData18(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData19(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData20(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData21(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        String enterMode = getInputMethod(transData);
        setBitData("22", Component.getPaddedString(enterMode, 4, '0'));
    }

    protected void setBitData23(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("23", transData.getCardSerialNo());
    }

    protected void setBitData24(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("24", transData.getAcquirer().getNii());
    }

    protected void setBitData25(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("25", "00");
            /*Raymond 20220714: "OFFLINE_TRANS_SEND" replace the obsolete type "PREAUTH_COMP" of DCC, F25 = "06" only for Sale Completion
            if ( transData.getTransType().equals(ETransType.PREAUTH_COMP)){*/
            if ( transData.getTransType().equals(ETransType.OFFLINE_TRANS_SEND) &&
                !transData.getOrigTransType().equals(ETransType.SALE)){
                setBitData("25", "06");
            }
            return;
        }
        setBitData("25", transData.getTransType().getServiceCode());
    }

    protected void setBitData26(@NonNull TransData transData) throws Iso8583Exception  {

    }

    protected void setBitData27(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData28(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData29(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData30(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData31(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData32(@NonNull TransData transData) {

    }

    protected void setBitData33(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData34(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (enterMode != TransData.EnterMode.MANUAL) {
            setBitData("35", transData.getTrack2());
        }
    }

    protected void setBitData36(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("36", transData.getTrack3());
    }

    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("37", transData.getRefNo());
    }

    protected void setBitData38(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.OFFLINE_TRANS_SEND) {
            setBitData("38", transData.getAuthCode());
        } else {
            setBitData("38", transData.getOrigAuthCode());
        }
    }

    protected void setBitData39(@NonNull TransData transData) throws Iso8583Exception {

    }

    protected void setBitData40(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData41(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("41", transData.getAcquirer().getTerminalId());
    }

    protected void setBitData42(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("42", transData.getAcquirer().getMerchantId());
    }

    protected void setBitData43(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData44(@NonNull TransData transData) {

    }

    protected void setBitData45(@NonNull TransData transData) throws Iso8583Exception {
        //do nothing
    }

    protected void setBitData46(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData47(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData48(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("48", transData.getField48());
    }

    protected void setBitData49(@NonNull TransData transData) throws Iso8583Exception{
        if (transData.isDcc()) {
            setBitData("49", transData.getLocalCurrency().getCode()); // set local currency
        }
    }

    protected void setBitData50(@NonNull TransData transData) {
        //do nothing
    }

    protected void setBitData51(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("51", transData.getHomeCurrency().getCode()); // set home currency
        }
    }

    protected void setBitData52(@NonNull TransData transData) throws Iso8583Exception {
        if (TransData.EnterMode.CLSS == transData.getEnterMode() && transData.getTransactionPath() == TransactionPath.CLSS_MC_MAG) {
            setBitData("52", new byte[]{(byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00});
        }
        if (transData.isHasPin() && transData.getPin() != null) {
            setBitData("52", FinancialApplication.getConvert().strToBcd(transData.getPin(),
                    IConvert.EPaddingPosition.PADDING_LEFT));
        }
    }

    protected void setBitData53(@NonNull TransData transData) {

    }

    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        String TempStr = String.format("%012d", transData.getTipAmount());
        String field;
        if (!transData.isDcc()) {
            field = TempStr;
        } else {
            String homeTipAmount = transData.getHomeCurrencyTipAmount();
            if (homeTipAmount == null || homeTipAmount.isEmpty()) {
                return;
            }
            if (TempStr.equals("0") && homeTipAmount.equals("0")) {
                return;
            }
            field = TempStr;
            field += FinancialApplication.getConvert().stringPadding(homeTipAmount, '0', 12, IConvert.EPaddingPosition.PADDING_LEFT);
        }
        setBitData("54", field);
    }

    protected void setBitData55(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()){
            if (TransData.EnterMode.CLSS == transData.getEnterMode() && transData.getTransactionPath() == TransactionPath.CLSS_MC_MAG) {
                return;
            }
            if (transData.getEnterMode() == TransData.EnterMode.SWIPE) {
                return;
            }
            String temp = transData.getSendIccData();
            if (temp != null && temp.length() > 0) {
                setBitData("55", FinancialApplication.getConvert().strToBcd(temp, IConvert
                        .EPaddingPosition.PADDING_LEFT));
            }
        }else {
            if (TransData.EnterMode.CLSS == transData.getEnterMode() && transData.getTransactionPath() == TransactionPath.CLSS_MC_MAG) {
                return;
            }
            if (transData.getEnterMode() == TransData.EnterMode.SWIPE) {
                return;
            }
            String temp = transData.getSendIccData();
            if (temp != null && temp.length() > 0) {
                setBitData("55", FinancialApplication.getConvert().strToBcd(temp, IConvert
                        .EPaddingPosition.PADDING_LEFT));
            } else if (transData.getEnterMode() == TransData.EnterMode.SWIPE ||
                    transData.getEnterMode() == TransData.EnterMode.FALLBACK) {
                byte[] data = new byte[]{(byte) 0xDF, 0x5A, 0x01, 0x01, (byte) 0xDF, 0x39, 0x01, 0x01};
                if (!transData.getIssuer().getName().equals("HASE_CUP") &&
                        !transData.getIssuer().getName().equals("UnionPay")) {
                    data = new byte[]{(byte) 0xDF, 0x5A, 0x01, 0x01, (byte) 0xDF, 0x39, 0x01, 0x02};
                }

                setBitData("55", data);
            }
        }
    }

    protected void setBitData56(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()){
            //do nothing
        }else {
            if (transData.getEnterMode() == TransData.EnterMode.MANUAL || transData.getEnterMode() == TransData.EnterMode.SWIPE || transData.getEnterMode() == TransData.EnterMode.QR) {
                return;
            }

            if (transData.getEnterMode() == TransData.EnterMode.CLSS) {
                if (transData.getTransactionPath() == TransactionPath.CLSS_MC_MAG) {
                    return;
                }
                if (transData.getTransType() != ETransType.VOID_WITH_CASH_DOLLAR //VOID_WITH_CASH_DOLLAR at the OrigTrans Clss Moulde have Feild56.
                        && !"HASE_CUP".equals(transData.getAcquirer().getName())) {
                    return;
                }
            }

            List<TransData> allTrans = FinancialApplication.getTransDataDbHelper().findAllTransDataExceptClss(transData.getAcquirer());
            if (!allTrans.isEmpty()) {
                String f56 = allTrans.get(allTrans.size() - 1).getSendField56(); //the previous transaction f56 data
                if (ETransType.VOID == transData.getTransType() || ETransType.VOID_WITH_CASH_DOLLAR == transData.getTransType()) {
                    transData.setSendField56(f56);
                }
                if (f56 != null && f56.length() > 0) {
                    setBitData("56", FinancialApplication.getConvert().strToBcd(f56, IConvert.EPaddingPosition.PADDING_LEFT));
                    return;
                }
            }
            byte[] data = new byte[]{(byte) 0xDF, 0x5C, 0x07, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20};
            if (ETransType.VOID == transData.getTransType() || ETransType.VOID_WITH_CASH_DOLLAR == transData.getTransType()) {
                transData.setSendField56(FinancialApplication.getConvert().bcdToStr(data));
            }
            setBitData("56", data);
        }
    }


    protected void setBitData57(@NonNull TransData transData) {

    }

    protected void setBitData58(@NonNull TransData transData) {

    }

    protected void setBitData59(@NonNull TransData transData) {

    }

    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("60", Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6));
    }

    protected void setBitData61(@NonNull TransData transData) throws Iso8583Exception {

    }

    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("62", Component.getPaddedNumber(transData.getInvoiceNo(), 6));
    }

    // set field 63
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            String temp = "";
            if (transData.getReversalStatus() == TransData.ReversalStatus.REVERSAL) {
                temp = dccDF63CommonSetting(transData, temp);
                setBitData("63", temp);
                return;
            }
            ETransType transType = transData.getTransType();
            switch (transType) {
                case ENQUIRY:
                    temp += setLtvData("27", new byte[]{0x00, 0x03}, "R");
                    temp += setLtvData("33", new byte[]{0x00, 0x03}, new byte[]{0x21});
                    temp = setCardTag(transData, temp);
                    break;
                case SALE:
                    temp = getPPSaleField63(transData);
                    setBitData("63", FinancialApplication.getConvert().strToBcd(temp, IConvert.EPaddingPosition.PADDING_LEFT));
                    return;
                case AUTHORIZATION:
                case PREAUTH:
                    temp = getPPAuthField63(transData);
                    setBitData("63", FinancialApplication.getConvert().strToBcd(temp, IConvert.EPaddingPosition.PADDING_LEFT));
                    return;
                case OFFLINE_TRANS_SEND:
                /*Raymond 20220714: remove the obsolete ETransType
                case PREAUTH_COMP:*/
                case AUTH_REVERSAL:
                case EMV_BATCH_UP:
                    temp = dccDF63CommonSetting(transData, temp);
                    temp = setCardTag(transData, temp);
                    break;
                case PREAUTHVOID:
                case REFUND:
                case VOID:
                    temp = dccDF63CommonSetting(transData, temp);
                    temp += setLtvData("33", new byte[]{0x00, 0x03}, new byte[]{0x21});
                    temp = setCardTag(transData, temp);
                    break;
                case DOWNLOAD_CARD_BIN:
                    String value = "00123031000000000000";
                    CardBin cardBin = FinancialApplication.getCardBinDb().findLastCardBin();
                    value += (cardBin != null && cardBin.getTotalBlock() != null && !cardBin.getTotalBlock().isEmpty()) ? cardBin.getTotalBlock() : "0000";
                    value += Component.getPaddedString(transData.getLocalCurrency().getCode(), 4, '0');
                    setBitData("63", FinancialApplication.getConvert().strToBcd(value, IConvert.EPaddingPosition.PADDING_LEFT));
                    return;
                case DOWNLOAD_RATE_REPORT:
                    String tag04 = "0005303401";
                    tag04 += (transData.getTag05() != null && !transData.getTag05().isEmpty()) ? transData.getTag05() : "0000";
                    setBitData("63", FinancialApplication.getConvert().strToBcd(tag04, IConvert.EPaddingPosition.PADDING_LEFT));
                    return;
                case INC_AUTHORIZATION:
                    temp = dccDF63CommonSetting(transData, temp);
                    temp += setLtvData("25", new byte[]{0x00, 0x03}, "I");
                    temp = setCardTag(transData, temp);
                    break;
                default:
                    return;
            }
            setBitData("63", temp);
          }else {
            if (transData.getIssuer().getName().equals("JCB") && !transData.getAcquirer().getName().equals("HASE_CUP")) {
                return;
            }
            String field63 = getField63(transData);
            transData.setField63(field63);
            setBitData("63", field63.getBytes(StandardCharsets.ISO_8859_1));
        }
    }

    protected void setBitData64(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("64", new byte[]{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00});
    }

    private String getPPSaleField63(TransData transData) {
        String temp = "";
        boolean isVisa = FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer());
        boolean isMc = FinancialApplication.getAcqManager().isMasterCard(transData.getIssuer());
        //12
        temp += transData.isFullOutSource()? setLtvData("3132", "0003", "58") : setLtvData("3132", "0003", "44");
        //13
        temp += setLtvData("3133", "0003", "4E");
        //33
        temp += setLtvData("3333", "0003", "37");
        //TC
        if (isVisa) {
            temp += setLtvData("5443", "0003", "35"); //for Visa tag
        } else if (isMc){
            temp += setLtvData("5443", "0003", "33"); //for MC tag
        }
        return temp;
    }

    private String getPPAuthField63(TransData transData) {
        String temp = "";
        boolean isVisa = FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer());
        boolean isMc = FinancialApplication.getAcqManager().isMasterCard(transData.getIssuer());
        //12
        temp += transData.isFullOutSource()? setLtvData("3132", "0003", "58") : setLtvData("3132", "0003", "44");
        //13
        temp += setLtvData("3133", "0003", "4E");
        //33
        temp += setLtvData("3333", "0003", "21");
        //TC
        if (isVisa) {
            temp += setLtvData("5443", "0003", "35"); //for Visa tag
        } else if (isMc){
            temp += setLtvData("5443", "0003", "38"); //for MC tag
        }
        return temp;
    }

    protected String getField63(TransData transData) {
        String f63 = "";
        ETransType transType = transData.getTransType();
        String cupRrn;
        if (transData.getCupRRN() == null) {
            cupRrn = new String(new byte[12]);
        } else {
            cupRrn = transData.getCupRRN();
            if (cupRrn.length() < 12) {
                cupRrn = cupCompletion(cupRrn);
            }
        }
        String tipFlag;
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP)) {
            tipFlag = "Y";
        } else {
            tipFlag = "N";
        }

        switch (transType) {
            case SALE:
                f63 = Component.getPaddedString(" ", 16, ' ') + new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                if (transData.getAcquirer().getName().equals("HASE_CUP")) {
                    f63 = f63 + cupRrn + tipFlag;
                }
                break;
            case VOID:
                f63 = Component.getPaddedString(" ", 16, ' ');
                if (transData.getOrigTransType() == ETransType.INSTALMENT) {
                    f63 = f63 + new String(new byte[]{0x00, 0x04}) + "20" + new String(new byte[]{0x03, 0x44}) +
                            new String(new byte[]{0x00, 0x03}) + "23" +
                            new String(Utils.str2Bcd(String.format("%02d", transData.getInstalment())));
                } else {
                    //if It is the Cash$ Void Refund，the pack is defferent！
                    f63 = f63 + new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                }
                if (transData.getAcquirer().getName().equals("HASE_CUP")) {
                    f63 = f63 + cupRrn + tipFlag;
                }
                break;
            case REFUND:
                String field63 = new String(new byte[]{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00});
                if (transData.getOrigTransType() != ETransType.SALES_WITH_CASH_DOLLAR) {  //??
                    field63 = Component.getPaddedString(" ", 16, ' ');
                }
                f63 = field63 + new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                break;
            case INSTALMENT:
                f63 = Component.getPaddedString(" ", 16, ' ');
                f63 = f63 + new String(new byte[] { 0x00, 0x04 }) + "20" + new String(
                        new byte[] { 0x03, 0x44 }) +
                        new String(new byte[] { 0x00, 0x03 }) + "23" +
                        new String(Utils.str2Bcd(String.format("%02d", transData.getInstalment())),
                                StandardCharsets.ISO_8859_1);
                break;
            case ONLINE_ENQUIRY:
                f63 = new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                break;
            case PREAUTHCM:
            case PREAUTHCMVOID:
            case PREAUTHVOID:
            case PREAUTH:
                f63 = Component.getPaddedString(" ", 16, ' ');
                f63 = f63 + new String(new byte[]{0x00, 0x03}) + "249" + cupRrn + tipFlag;
                break;
            case AUTHORIZATION:
                f63 = Component.getPaddedString(" ", 16, ' ');
                f63 = f63 + new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                break;
            case OFFLINE_TRANS_SEND:
                f63 = Component.getPaddedString(" ", 16, ' ');
                if (transData.getAcquirer().getName().equals("HASE_OLS") && transData.getTransState() == TransData.ETransStatus.ADJUSTED) {
                    f63 = new String(new byte[]{0x4C, 0x30, 0x00, 0x03}) + "24" + getTermEntryCap(transData);
                } else {
                    f63 = f63 + new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                }
                break;
            case YUU_ENQUIRY:
            case YUU_REGISTRATION:
                f63 = Component.getPaddedString(" ", 16, ' ');
                f63 = f63 + new String(new byte[]{0x00, 0x03}) + "24" + getTermEntryCap(transData);
                f63 = f63 + transData.getEcrYUUCiam() + new String(new byte[]{0x20, 0x20, 0x20,
                        0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
                        0x20,0x20, 0x20, 0x20, 0x20});
                break;
            default:
                break;
        }

        return f63;
    }

    protected String setCardTag(TransData transData, String temp) {
        boolean isVisa = FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer());
        boolean isMc = FinancialApplication.getAcqManager().isMasterCard(transData.getIssuer());
        if (isVisa) {
            //temp += setLtvData("TC", new byte[]{0x00, 0x03}, "5"); //for Visa tag
        } else if (isMc){
            //temp += setLtvData("TC", new byte[]{0x00, 0x03}, "3"); //for MC tag
        }
        return temp;
    }

    protected String dccDF63CommonSetting(TransData transData, String temp) {
        temp += transData.isFullOutSource()? setLtvData("12", new byte[]{0x00, 0x03}, "X") : setLtvData("12", new byte[]{0x00, 0x03}, "D");
        temp += setLtvData("13", new byte[]{0x00, 0x03}, "N");
        return temp;
    }

    private String cupCompletion(String cupRrn) {
        StringBuilder cupRrnBuilder = new StringBuilder(cupRrn);
        for (int i = cupRrnBuilder.length(); i < 12; i++) {
            cupRrnBuilder.append(' ');
        }
        return cupRrnBuilder.toString();
    }

    //By Jerry 20180108
    protected String getTermEntryCap(@NonNull TransData transData) {
        String termEntryCap = null;
        if (transData.getIssuer().getName().equals("VISA")
                || transData.getIssuer().getName().equals("HASE_VISA")
                || transData.getIssuer().getName().equals("HASE_ENJ")
                || transData.getIssuer().getName().equals("HASE_ENJ_E")) {
            //VISA通知HASE,VISA 交易報文 F63 的 Terminal Capability 值为 5
            termEntryCap = "5";
        } else if (transData.getIssuer().getName().equals("MASTER")
                || transData.getIssuer().getName().equals("HASE_MC")) {
            termEntryCap = "3";
        } else if (transData.getIssuer().getName().equals("UnionPay")
                || transData.getIssuer().getName().equals("HASE_CUP")) {
            termEntryCap = "9";
        }
        if (transData.getAcquirer().getName().equals("HASE_CUP")) {
            termEntryCap = "9";
        }
        return termEntryCap;
    }

    private String setLtvData(String tag, byte[] length, String value) {
        return new String(length) + tag + value;
    }

    private String setLtvData(String tag, byte[] length, byte[] value) {
        return setLtvData(tag, length, new String(value));
    }

    private String setLtvData(String tag, String length, String value) {
        return length + tag + value;
    }

    protected String getAmtString(byte[] buf) {
        StringBuilder temp = new StringBuilder();
        for (byte aBuf : buf) {
            temp.append(String.format("%02x", aBuf));
        }
        return temp.toString();
    }

    /**
     * 检查请求和返回的关键域field4, field11, field41, field42
     *
     * @param map        解包后的map
     * @param transData  请求
     * @param isCheckAmt 是否检查field4
     * @return
     */
    protected int checkRecvData(@NonNull HashMap<String, byte[]> map, @NonNull TransData transData, boolean isCheckAmt) {
        // 交易金额
        if (isCheckAmt && !checkAmount(map, transData) && !transData.getTransType().equals(ETransType.AUTH_REVERSAL)) { //allow auth reversal use amount "0"
            return TransResult.ERR_TRANS_AMT;
        }

        // 校验11域
        if (!checkTraceNo(map, transData))
            return TransResult.ERR_TRACE_NO;

        // 校验终端号
        if (!checkTerminalId(map, transData))
            return TransResult.ERR_TERM_ID;

        // 校验商户号
        if (!checkMerchantId(map, transData))
            return TransResult.ERR_MERCH_ID;

        return TransResult.SUCC;
    }

    /**
     * @param transData
     * @return
     */
    protected final String getInputMethod(TransData transData) {
        TransData.EnterMode enterMode = transData.getEnterMode();
        boolean hasPin = transData.isHasPin();
        Issuer issuer = transData.getIssuer();
        Acquirer acquirer = transData.getAcquirer();
        String track2 = transData.getTrack2();
        if (enterMode == null) { //AET-40
            return null;
        }
        String inputMethod;
        try {
            inputMethod = enterModeMap.get(enterMode);
            if ("HASE_CUP".equals(acquirer.getName()) || issuer.getName().equals("HASE_CUP")) {
                if (TransData.EnterMode.FALLBACK == enterMode) {
                    //銀聯後台已不再接受fallback交易, 所以若採用95,會導致後台拒絕該交易.所以,非银联卡的Fallback交易, 需將交易辨別為SWIPE交易處理
                    if (issuer.getName().equals("UnionPay") || issuer.getName()
                            .equals("HASE_CUP")) {
                        inputMethod = "95";
                    } else {
                        inputMethod = "02";
                    }
                } else if (TransData.EnterMode.SWIPE == enterMode) {
                    inputMethod = "02";
                }
            } else if (TransData.EnterMode.SWIPE == enterMode
                    && ("VISA".equals(issuer.getName()) || "MASTER".equals(issuer.getName())
                    || "HASE_VISA".equals(issuer.getName()) || "HASE_MC".equals(issuer.getName())
                    || "HASE_ENJ".equals(issuer.getName()) || "HASE_ENJ_E".equals(
                    issuer.getName()))) {
                inputMethod = processInputMethod(track2);
            } else if (transData.getTransactionPath() == TransactionPath.CLSS_MC_MAG) {
                inputMethod = "91";
            } else if (transData.getIssuer().getName().equals("JCB")
                    && TransData.EnterMode.FALLBACK == enterMode
                    && !transData.isDcc()) {
                inputMethod = "97";
            }
        } catch (Exception e) {
            Log.w(TAG, "", e);
            return null;
        }

        if (TransData.EnterMode.QR == enterMode && !FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_IS_PRODUCTION_MODE)) {
            inputMethod = "07";
        }

        if (issuer.getName().equals("HASE_CUP") || acquirer.getName().equals("HASE_CUP")) {
            if (hasPin) {
                inputMethod += "1";
            } else {
                inputMethod += "2";
            }
        } else {
            inputMethod += "1";
        }

        return inputMethod;
    }

    private String processInputMethod(String track2) {
        //For SWIPE卡交易, 如果TRACK 2 SERVICE CODE是2或6開頭, F22應該設為0021. 如果SERVICE CODE不是2或6開頭則F22設為0901
        char serviceCode = track2.charAt(track2.indexOf('=') + 5);
        if (serviceCode == '2' || serviceCode == '6') {
            return "02";
        }
        return "90";
    }

    private boolean checkAmount(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("4");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            long acqAmount = transData.getAmount();
            if (ETransType.VOID_WITH_CASH_DOLLAR == transData.getTransType()) {
                acqAmount = transData.getNetAmount();
            } else if (ETransType.LYS_BATCH_UP == transData.getTransType()) {
                acqAmount = transData.getNetAmount() + transData.getTipAmount();
            } else if (ETransType.OFFLINE_TRANS_SEND == transData.getTransType() && "HASE_OLS".equals(transData.getAcquirer().getName())
                    && TransData.ETransStatus.ADJUSTED == transData.getTransState()) {
                acqAmount = transData.getNetAmount() + transData.getTipAmount();
            }
            return Utils.parseLongSafe(temp, 0) == acqAmount;
        }

        return true;
    }

    private boolean checkTraceNo(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("11");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(Component.getPaddedNumber(transData.getTraceNo(), 6));
        }
        return true;
    }

    private boolean checkTerminalId(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("41");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(transData.getAcquirer().getTerminalId());
        }
        return true;
    }

    private boolean checkMerchantId(@NonNull final HashMap<String, byte[]> map, @NonNull final TransData transData) {
        byte[] data = map.get("42");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(transData.getAcquirer().getMerchantId());
        }
        return true;
    }

    private String processCupRRN(String cupRRN) {
        StringBuilder temp = new StringBuilder();
        char[] charArray = cupRRN.toCharArray();
        for (int i = 1; i < cupRRN.length(); i += 2) {
            temp.append(charArray[i]);
        }
        return temp.toString();
    }

}
