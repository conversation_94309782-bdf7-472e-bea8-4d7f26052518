/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.abl.utils.TrackUtils;
import com.pax.edc.opensdk.TransResult;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;

public class PackSale extends PackIso8583 {

    public PackSale(PackListener listener) {
        super(listener);
    }

    protected int[] getRequiredFields() {
        return new int[] {
                2, 3, 4, 6, 11, 14, 22, 23, 24, 25, 26, 35, 37, 41, 42, 45, 49, 51, 52, 54, 55, 56, 61, 62, 63
        };    //normal CUP add 23,35,54, and cut 56,62
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (enterMode == TransData.EnterMode.MANUAL
                || transData.getEnterMode() == TransData.EnterMode.QR
                && (transData.getIssuer()
                .getName()
                .equals("MASTER") || transData.getIssuer().getName().equals("HASE_MC"))
                && !transData.getPan().equals(TrackUtils.getPan(transData.getTrack2()))) {
            setBitData("2", transData.getPan());
        }
    }

    /**
     * setBitData14
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (enterMode == TransData.EnterMode.MANUAL
                || transData.getEnterMode() == TransData.EnterMode.QR
                && (transData.getIssuer()
                .getName()
                .equals("MASTER") || transData.getIssuer().getName().equals("HASE_MC"))
                && !transData.getExpDate().equals(TrackUtils.getExpDate(transData.getTrack2()))) {
            setBitData("14", transData.getExpDate());
        }
    }

    /**
     * setBitData23
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData23(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().getName().equals("HASE_CUP")) {
            setBitData("23", transData.getCardSerialNo());
        }
    }

    @Override
    protected void setBitData26(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getIssuer() != null && transData.getPin() != null &&
                ("MASTER".equals(transData.getIssuer().getName()) || "HASE_MC".equals(transData.getIssuer().getName()))) {
            setBitData("26", "12");
        }
    }


    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getTransType() == ETransType.SALE) && transData.isDcc()) {
            setBitData("37", transData.getRefNo());
        }
    }

    /**
     * setBitData45
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData45(@NonNull TransData transData) throws Iso8583Exception {
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (enterMode != TransData.EnterMode.CLSS
                && enterMode != TransData.EnterMode.FALLBACK
                && enterMode != TransData.EnterMode.SWIPE) {
            String Track1 = transData.getTrack1();
            if (Track1 != null && !Track1.isEmpty()) {
                setBitData("45", Track1);
            }
        }
    }

    /**
     * setBitData54
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        long TempChange = transData.getTipAmount();
        String TempStr = String.format("%012d", TempChange);
        String field;
        if (TempChange > 0) {
            if (!transData.isDcc()) {
                setBitData("54", TempStr);
            }else{
                String homeTipAmount = transData.getHomeCurrencyTipAmount();
                if (homeTipAmount == null || homeTipAmount.isEmpty()) {
                    return;
                }
                if (TempStr.equals("0") && homeTipAmount.equals("0")) {
                    return;
                }
                field = TempStr;
                field += FinancialApplication.getConvert().stringPadding(homeTipAmount, '0', 12, IConvert.EPaddingPosition.PADDING_LEFT);
                setBitData("54", field);
            }
        }
    }

    /**
     * setBitData61
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData61(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().isEnableRefNo() && !transData.isDcc() && !TextUtils.isEmpty(
                transData.getAcqReferenceNo())) {
            setBitData("61", Component.getPaddedString(transData.getAcqReferenceNo(), 21, ' '));
        }
    }

    /**
     * setBitData62
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        if (!transData.getAcquirer().getName().equals("HASE_CUP") && !transData.isDcc()) {
            setBitData("62", Component.getPaddedNumber(Component.getInvoiceNo(), 6));   //???
        }
    }

    @Override
    protected int UnpackF63(@NonNull TransData transData, byte[] buffer) {
        if (transData.getAcquirer().getName().equals("HASE_CUP") && buffer.length == 1) {
            transData.setCupCardType(new String(buffer));
        }
        return TransResult.SUCC;
    }

}
