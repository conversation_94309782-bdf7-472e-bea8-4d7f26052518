package com.pax.pay.trans;

public class EMIProcessID {
    public static final int NO_PIN_ENTERED = 1;
    public static final int TRANSACTION_TIME_OUT = 2;
    public static final int TRANSACTION_SWITCH_DECLINED = 3;
    public static final int VOID = 8;
    public static final int TRANSACTION_FAILED = 10;
    public static final int NOTIFY_TRANSACTION_TIMEOUT = 11;
    public static final int AUTH_REVERSAL_FAILED = 12;
    public static final int AUTH_REVERSAL_SUCCESS = 13;
    public static final int NOTIFY_TRANSACTION_FAILED = 15;

    private EMIProcessID() {
    }
}
