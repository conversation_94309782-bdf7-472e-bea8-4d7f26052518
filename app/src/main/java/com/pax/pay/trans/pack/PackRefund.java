/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;

public class PackRefund extends PackIso8583 {

    public PackRefund(PackListener listener) {
        super(listener);
    }

    protected int[] getRequiredFields() {
        return new int[]{1, 2, 3, 4, 11, 14, 22, 24, 25, 35, 37, 41, 42, 49, 52, 61, 62, 63};
    }

    /**
     * setBitData61
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData61(@NonNull TransData transData) throws Iso8583Exception {
        if (!transData.getAcquirer().isEnableRefNo() || TextUtils.isEmpty(
                transData.getAcqReferenceNo())) {
            return;
        }
        setBitData("61", Component.getPaddedString(transData.getAcqReferenceNo(), 21, ' '));
    }

    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("37", transData.getRefNo());
        }
    }

    /**
     * setBitData62
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        if (!transData.getAcquirer().getName().equals("HASE_CUP") && !transData.isDcc()) {
            setBitData("62", Component.getPaddedNumber(Component.getInvoiceNo(), 6));
        }
    }
}
