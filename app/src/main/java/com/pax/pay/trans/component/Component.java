/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.component;

import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Environment;
import android.os.StatFs;

import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.entity.EPedDesMode;
import com.pax.dal.entity.EPedType;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.ClssParam;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransData.ETransStatus;
import com.pax.data.entity.TransData.EnterMode;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.eemv.entity.ClssInputParam;
import com.pax.eemv.entity.Config;
import com.pax.eemv.entity.InputParam;
import com.pax.eemv.enums.ECvmResult;
import com.pax.eemv.enums.EFlowType;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionSearchCard.SearchMode;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.CountryCode;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.sdk.Sdk;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;

import java.io.File;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

public class Component {

    private static final String TAG = "Component";

    private static final long MAX_TRANS_NO = 999999;
    private static final long MAX_BATCH_NO = 999999;
    private static final long MAX_INVOICE_NO = 999999;

    private Component() {
        //do nothing
    }

    /**
     * 交易预处理，检查是否签到， 是否需要结束， 是否继续批上送， 是否支持该交易， 是否需要参数下载
     *
     * @param context   unused
     * @param transType {@link ETransType}
     * @return {@link TransResult}
     */
    public static int transPreDeal(final Context context, ETransType transType) {
        if (!isNeedPreDeal(transType)) {
            return TransResult.SUCC;
        }
        // TODO Battery level check

        // 判断是否需要结算
        int ret = checkSettle();
        if (ret != TransResult.SUCC) {
            return ret;
        }
        // 批上送断点
        if (isNeedBatchUp()) {
            return TransResult.ERR_BATCH_UP_NOT_COMPLETED;
        }
        // 根据交易类型判断是否支持此交易
        if (!isSupportTran(transType)) {
            return TransResult.ERR_NOT_SUPPORT_TRANS;
        }
        // 判断是否有参数下载

        return ret;
    }


    /**
     * 获取读卡方式
     *
     * @param transType ：交易类型{@link com.pax.pay.trans.model.ETransType}
     * @return {@link SearchMode}
     */
    public static byte getCardReadMode(ETransType transType) {
        byte mode = transType.getReadMode();

        if (mode == 0) {
            return mode;
        }
        // does not support manual input
        if (!FinancialApplication.getAcqManager().getCurAcq().isEnableKeyIn()) {
            mode &= ~SearchMode.KEYIN;
        }

        //A50D do not support SWIPE CARD
        if (Utils.isA50()) {
            mode &= ~SearchMode.SWIPE;
        }

        return mode;
    }

    /**
     * 根据交易类型、冲正标识确认当前交易是否预处理
     *
     * @param transType {@link ETransType}
     * @return true:需要预处理 false:不需要预处理 备注：签到，签退，结算，参数下发，公钥下载，冲正类不需要预处理,新增交易类型时，需修改添加交易类型判断
     */
    private static boolean isNeedPreDeal(ETransType transType) {
        return transType != ETransType.SETTLE;
    }

    /**
     * 检查是否达结算要求
     *
     * @return 0：不用结算 1：结算提醒,立即 2：结算提醒，稍后 3：结算提醒,空间不足
     */
    private static int checkSettle() {
        // 获取交易笔数
        long cnt = GreendaoHelper.getTransDataHelper().countOf();
        // 获取允许的最大交易笔数
        long maxCnt = SysParam.getInstance().getInt(R.string.MAX_TRANS_COUNT, Integer.MAX_VALUE);

        // 判断交易笔数是否超限
        if (cnt >= maxCnt) {
            if (cnt >= maxCnt + 10) {
                return TransResult.ERR_NEED_SETTLE_NOW; // 结算提醒,立即
            } else {
                return TransResult.ERR_NEED_SETTLE_LATER; // 结算提醒,稍后
            }
        }
        // 判断存储空间大小
        if (!hasFreeSpace()) {
            return TransResult.ERR_NO_FREE_SPACE; // 存储空间不足,需要结算
        }
        return TransResult.SUCC; // 不用结算
    }

    /**
     * 判断是否有剩余空间
     *
     * @return true: 有空间 false：无空间
     */
    @SuppressWarnings("deprecation")
    private static boolean hasFreeSpace() {
        File dataPath = Environment.getDataDirectory();
        StatFs dataFs = new StatFs(dataPath.getPath());
        long sizes = (long) dataFs.getFreeBlocks() * (long) dataFs.getBlockSize();
        long available = sizes / (1024 * 1024);
        return available > 1;
    }

    private static boolean isNeedBatchUp() {
        return FinancialApplication.getController().get(Controller.BATCH_UP_STATUS) == Controller.Constant.BATCH_UP;
    }

    /**
     * 判断是否支持该交易
     *
     * @param transType {@link ETransType}
     */
    private static boolean isSupportTran(ETransType transType) {
        switch (transType) {
            case SALE:
                return SysParam.getInstance().getBoolean(R.string.TTS_SALE);
            case VOID:
                return SysParam.getInstance().getBoolean(R.string.TTS_VOID);
            case REFUND:
                return SysParam.getInstance().getBoolean(R.string.TTS_REFUND);
            case PREAUTH:
                return SysParam.getInstance().getBoolean(R.string.TTS_PREAUTH);
            default:
                break;
        }

        return true;
    }

    /**
     * convert {@link TransData} to {@link InputParam} for EMV and CLSS
     *
     * @param transData {@link TransData}
     * @return {@link InputParam}
     */
    public static InputParam toInputParam(TransData transData) {
        InputParam inputParam = new InputParam();
        convertTransData2InputParam(transData, inputParam);
        return inputParam;
    }

    public static ClssInputParam toClssInputParam(TransData transData) {
        ClssInputParam inputParam = new ClssInputParam();
        if (ETransType.QSPARC_MONEY_ADD_ACCOUNT.equals(transData.getTransType())) {
            //Money ADD AFA 认证需求
            inputParam.setMoneyAddByAccount(true);
            ClssParam findClssParam = FinancialApplication.getClssParamDbHelper().findClssParam("RUPAY");
            inputParam.setAfarAmount(findClssParam.getAfaLimit());
            long transAmount = Long.parseLong(transData.getAmount());
            if (transAmount > findClssParam.getAfaLimit()) {
                inputParam.setExceedAfaAmount(true);
            }
        }
        convertTransData2InputParam(transData, inputParam);
        inputParam.setAmtZeroNoAllowedFlg(1);
        inputParam.setCrypto17Flg(true);
        inputParam.setStatusCheckFlg(false);
        inputParam.setReaderTTQ("3620C000");//********
        inputParam.setDomesticOnly(false);
        List<ECvmResult> list = new ArrayList<>();
        list.add(ECvmResult.REQ_SIG);
        list.add(ECvmResult.REQ_ONLINE_PIN);
        inputParam.setCvmReq(list);
        inputParam.setEnDDAVerNo((byte) 0);
        return inputParam;
    }

    private static void convertTransData2InputParam(TransData transData, InputParam inputParam) {
        ETransType transType = transData.getTransType();
        String amount = transData.getAmount();
        if (amount == null || amount.isEmpty()) {
            amount = "0";
        }
        inputParam.setAmount(amount);

        String cashBackAmount = transData.getCashAmount();
        if (cashBackAmount == null || cashBackAmount.isEmpty()) {
            cashBackAmount = "0";
        }
        inputParam.setCashBackAmount(cashBackAmount);
        inputParam.setPciTimeout(60 * 1000);
        if ((transData.getTransType().equals(ETransType.SALE))
                || (transData.getTransType().equals(ETransType.SALE_CASH))
                || (transData.getTransType().equals(ETransType.CASH_ONLY))
                || (transData.getTransType().equals(ETransType.PREAUTH))
                || (transData.getTransType().equals(ETransType.REFUND))
                || (transData.getTransType().equals(ETransType.QSPARC_MONEY_ADD_CASH))
                || (transData.getTransType().equals(ETransType.QSPARC_MONEY_ADD_ACCOUNT))
                || (transData.getTransType().equals(ETransType.QSPARC_BALANCE_UPDATE))
                || (transData.getTransType().equals(ETransType.PREAUTHCOMPLETE)
                || (transData.getTransType().equals(ETransType.BANK_EMI_SALE))
                || (transData.getTransType().equals(ETransType.BRAND_EMI_SALE))
                || (transData.getTransType().equals(ETransType.PREAUTHCANCEL)))) {
            if (transData.getEnterMode() == EnterMode.INSERT) {
                inputParam.setFlowType(EFlowType.COMPLETE);
            } else {
                inputParam.setFlowType(EFlowType.QPBOC);
            }
            inputParam.setEnableCardAuth(true);
            inputParam.setSupportCVM(true);

        } else {
            // 联机Q非消费，余额查询，预授权均走简单Q流程
            inputParam.setFlowType(EFlowType.SIMPLE);

            inputParam.setEnableCardAuth(false);
            inputParam.setSupportCVM(false);
        }
        byte[] procCode = ConvertHelper.getConvert()
                .strToBcdPaddingRight(transType.getProcCode());

        if (transData.getTransType().equals(ETransType.QSPARC_MONEY_ADD_CASH)
                || transData.getTransType().equals(ETransType.QSPARC_MONEY_ADD_ACCOUNT)) {
            inputParam.setTag9CValue((byte) 0x28);
        } else if (transData.getTransType().equals(ETransType.QSPARC_BALANCE_UPDATE)) {
            inputParam.setTag9CValue((byte) 0x29);
        } else if (transData.getTransType().equals(ETransType.QSPARC_BALANCE_ENQUIRY)) {
            inputParam.setTag9CValue((byte) 0x31);
        } else if (transData.getTransType().equals(ETransType.CASH_ONLY)) {
            inputParam.setTag9CValue((byte) 0x01);
        } else {
            inputParam.setTag9CValue(procCode[0]);
        }

        //EMI交易不支持脱机交易强制联机
        if (ETransType.BANK_EMI_SALE.equals(transData.getTransType()) ||
                ETransType.BRAND_EMI_SALE.equals(transData.getTransType())) {
            // 根据交易类型判断是否强制联机
            inputParam.setForceOnline(true);
        }

        inputParam.setTransDate(transData.getDateTime().substring(0, 8));
        inputParam.setTransTime(transData.getDateTime().substring(8));
        inputParam.setTransTraceNo(Component.getPaddedNumber(transData.getTraceNo(), 6));

    }

    public static Config genCommonEmvConfig() {
        Config cfg = new Config();
        Currency current = Currency.getInstance(CurrencyConverter.getDefCurrency());
        String currency = String.valueOf(
                CountryCode.getByCode(CurrencyConverter.getDefCurrency().getCountry()).getCurrencyNumeric());
        String country = String.valueOf(
                CountryCode.getByCode(CurrencyConverter.getDefCurrency().getCountry()).getNumeric());

        cfg.setCountryCode(country);
        cfg.setForceOnline(false);
        cfg.setGetDataPIN(true);
        cfg.setMerchCateCode("0000");
        cfg.setReferCurrCode(currency);
        cfg.setReferCurrCon(1000);
        cfg.setReferCurrExp((byte) current.getDefaultFractionDigits());
        cfg.setSurportPSESel(true);
        cfg.setTermType((byte) 0x22);
        cfg.setTransCurrCode(currency);
        cfg.setTransCurrExp((byte) current.getDefaultFractionDigits());
        cfg.setTermId(FinancialApplication.getAcqManager().getCurAcq().getTerminalId());
        cfg.setMerchId(FinancialApplication.getAcqManager().getCurAcq().getMerchantId());
        cfg.setMerchName(SysParam.getInstance().getString(R.string.EDC_MERCHANT_NAME));
        cfg.setTermAIP("0800");
        cfg.setBypassPin(true); // 输密码支持bypass
        cfg.setBatchCapture((byte) 1);
        cfg.setUseTermAIPFlag(true);
        cfg.setBypassAllFlag(true);
        return cfg;
    }

    // 仅仅获取小票号/不+1
    public static int getInvoiceNo() {
        int invoiceNo = SysParam.getInstance().getInt(R.string.EDC_INVOICE_NO);
        if (invoiceNo == 0) {
            invoiceNo += 1;
            SysParam.getInstance().set(R.string.EDC_INVOICE_NO, invoiceNo);
        }
        return invoiceNo;
    }

    /**
     * 小票号+1
     */
    public static void incInvoiceNo() {
        int invoiceNo = SysParam.getInstance().getInt(R.string.EDC_INVOICE_NO);
        if (invoiceNo >= MAX_INVOICE_NO) {
            invoiceNo = 0;
        }
        invoiceNo++;
        SysParam.getInstance().set(R.string.EDC_INVOICE_NO, invoiceNo);
    }

    /**
     * 小票号-1
     */
    public static void decInvoiceNo() {
        int invoiceNo = SysParam.getInstance().getInt(R.string.EDC_INVOICE_NO);
        if (invoiceNo > 1) {
            invoiceNo--;
            SysParam.getInstance().set(R.string.EDC_INVOICE_NO, invoiceNo);
        }
    }

    /**
     * 流水号+1
     */
    public static void incTransNo() {
        long transNo = SysParam.getInstance().getInt(R.string.EDC_TRACE_NO);
        if (transNo >= MAX_TRANS_NO) {
            transNo = 0;
        }
        transNo++;
        SysParam.getInstance().set(R.string.EDC_TRACE_NO, transNo);
    }

    /**
     * 批次号+1
     */
    public static void incBatchNo() {
        int batchNo = SysParam.getInstance().getInt(R.string.EDC_BATCH_NO);
        if (batchNo >= MAX_BATCH_NO) {
            batchNo = 0;
        }
        batchNo++;
        SysParam.getInstance().set(R.string.EDC_BATCH_NO, batchNo);
    }

    public static String getPaddedNumber(long num, int digit) {
        NumberFormat nf = NumberFormat.getInstance(Locale.US);
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(digit);
        nf.setMinimumIntegerDigits(digit);
        return nf.format(num);
    }

    public static String getPaddedString(String str, int maxLen, char ch) {
        return ConvertHelper.getConvert().stringPadding(str, ch, maxLen, com.pax.commonlib.utils.convert.IConvert.EPaddingPosition.PADDING_LEFT);
    }

    /**
     * 交易初始化
     *
     * @return {@link TransData}
     */
    public static TransData transInit() {
        TransData transData = new TransData();
        transInit(transData);
        return transData;
    }

    /**
     * 交易初始化
     *
     * @param transData {@link TransData}
     */
    public static void transInit(TransData transData) {
        Acquirer acquirer = FinancialApplication.getAcqManager().getCurAcq();

        transData.setTraceNo(getTransNo());
        transData.setInvoiceNo(getInvoiceNo());
        transData.setBatchNo(SysParam.getInstance().getInt(R.string.EDC_BATCH_NO));
        transData.setDateTime(Device.getTime(Constants.TIME_PATTERN_TRANS));
        transData.setHeader("");
        transData.setTpdu("600" + acquirer.getNii() + "0304");
        // 冲正原因
        transData.setDupReason("22");
        transData.setTransState(ETransStatus.NORMAL);
        transData.setAcquirer(acquirer);
        Locale defCurrency = CurrencyConverter.getDefCurrency();
        transData.setCurrency(defCurrency);
        String currencyCode = Currency.getInstance(defCurrency).getCurrencyCode();
        transData.setLocalCurrency(CurrencyCode.getBySymbol(currencyCode));
    }

    // 获取流水号
    public static long getTransNo() {
        long transNo = SysParam.getInstance().getInt(R.string.EDC_TRACE_NO);
        if (transNo == 0) {
            transNo += 1;
            SysParam.getInstance().set(R.string.EDC_TRACE_NO, transNo);
        }
        return transNo;
    }

    /**
     * 是否免签
     *
     * @param transData {@link TransData}
     * @return true: 免签 false: 需要签名
     */
    public static boolean isSignatureFree(TransData transData) {

        if (!SysParam.getInstance().getBoolean(R.string.QUICK_PASS_TRANS_SIGN_FREE_FLAG)) {
            return false;
        }

        int limitAmount = SysParam.getInstance().getInt(R.string.QUICK_PASS_TRANS_SIGN_FREE_AMOUNT);
        String amount = transData.getAmount().replace(".", "");
        ETransType transType = transData.getTransType();
        if (!(ETransType.SALE.equals(transType) || ETransType.PREAUTH.equals(transType))) {
            return false;
        }
        return Integer.parseInt(amount) <= limitAmount;
    }

    /**
     * 磁道加密
     *
     * @param trackData track data
     * @return encrypted track data
     */
    public static String encryptTrack(String trackData) {
        if (trackData == null || trackData.isEmpty()) {
            return null;
        }
        String temp = trackData;
        int len = temp.length();
        if (len % 2 > 0) {
            temp += "0";
        }
        byte[] tb = new byte[8];
        byte[] bTrack = ConvertHelper.getConvert().strToBcdPaddingLeft(temp);
        System.arraycopy(bTrack, bTrack.length - 9, tb, 0, 8);
        byte[] block = new byte[8];
        try {
            block = FinancialApplication.getDal().getPed(EPedType.INTERNAL).calcDes(Constants.INDEX_TDK, tb,
                    EPedDesMode.ENCRYPT);
        } catch (PedDevException e) {
            LogUtils.e(TAG, "", e);
        }
        System.arraycopy(block, 0, bTrack, bTrack.length - 9, 8);
        return ConvertHelper.getConvert().bcdToStr(bTrack).substring(0, len);
    }

    /**
     * check whether the Neptune is installed, if not, display prompt
     */
    public static boolean neptuneInstalled(Context context, DialogInterface.OnDismissListener noNeptuneDismissListener) {
        if (!Sdk.isPaxDevice()) {
            return true;
        }
        PackageInfo packageInfo = null;
        try {
            packageInfo = context.getPackageManager().getPackageInfo("com.pax.ipp.neptune", 0);
        } catch (NameNotFoundException e) {
            LogUtils.e(TAG, "", e);
        }

        if (packageInfo == null) {
            CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.ERROR_TYPE, 5);
            dialog.setContentText(context.getString(R.string.please_install_neptune));
            dialog.setCanceledOnTouchOutside(true);
            dialog.show();
            dialog.setOnDismissListener(noNeptuneDismissListener);
            return false;
        }
        return true;
    }

    public static boolean isDemo() {
        String commType = SysParam.getInstance().getString(R.string.COMM_TYPE);
        return SysParam.CommType.DEMO.equals(commType);
    }

    public static boolean isSupportCash(TransData transData) {
        if ((transData.getTransType() == ETransType.SALE_CASH || transData.getTransType() == ETransType.CASH_ONLY) && Constants.ISSUER_AMEX.equalsIgnoreCase(transData.getIssuer().getName())) {
            return false;
        } else {
            return true;
        }
    }

    public static boolean isQsparcTrans(TransData transData) {
        ETransType transType = transData.getTransType();
        return (transType == ETransType.QSPARC_BALANCE_ENQUIRY || transType == ETransType.QSPARC_BALANCE_UPDATE ||
                transType == ETransType.QSPARC_MONEY_ADD_CASH || transType == ETransType.QSPARC_MONEY_ADD_ACCOUNT);
    }

    //根据AID判断卡片是否为银联卡
    public static boolean isUPICard(String aid) {
        if (aid == null) {
            return false;
        }

        if (aid.contains("A000000333")) {
            return true;
        }
        return false;
    }
}
