/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.view.Gravity;

import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.IssuerTransTotal;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransTotal;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.impl.GL;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * total generator
 *
 * <AUTHOR>
 */

public class ReceiptGeneratorTotal implements IReceiptGenerator {
    private String title;
    private String result;
    private TransTotal total;
    private final String LINE = "————————————————";
    private final boolean isHistoryPrint;

    /**
     * @param title      ：title
     * @param result     : result
     * @param transTotal ：transTotal
     */
    public ReceiptGeneratorTotal(String title, String result, TransTotal transTotal, boolean isHistoryPrint) {
        this.title = title;
        this.result = result;
        this.total = transTotal;
        this.isHistoryPrint = isHistoryPrint;
    }

    @Override
    public Bitmap generateBitmap() {
        IPage page = Device.generatePage();

        // title
        Bitmap logo = getImageFromAssetsFile("pax_logo_boid_slogan.png");
        // title
        page.addLine()
                .addUnit(page.createUnit()
                        .setBitmap(logo)
                        .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        //merchant information
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_NAME))
                        .setTextStyle(Typeface.BOLD)
                        .setGravity(Gravity.CENTER_HORIZONTAL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_ADDRESS))
                        .setTextStyle(Typeface.BOLD)
                        .setGravity(Gravity.CENTER_HORIZONTAL));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        // date time 获取当前系统时间
        String time1 = Device.getTime(Constants.TIME_PATTERN_TRANS);
        // 统一使用Settlement指定格式进行格式化
        String date = TimeConverter.convert(time1, Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN_CUSTOM);

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_transaction_datetime))
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(date)
                        .setGravity(Gravity.END)
                        .setFontSize(FONT_SMALL));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // terminal ID/operator ID
        page.addLine()
                .addUnit(page.createUnit()
                        .setFontSize(FONT_SMALL)
                        .setText(Utils.getString(R.string.receipt_terminal_id)))
                .addUnit(page.createUnit()
                        .setFontSize(FONT_SMALL)
                        .setText(total.getTerminalID())
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // merchant ID
        page.addLine()
                .addUnit(page.createUnit()
                        .setFontSize(FONT_SMALL)
                        .setText(Utils.getString(R.string.receipt_merchant_id)))
                .addUnit(page.createUnit()
                        .setFontSize(FONT_SMALL)
                        .setText(total.getMerchantID())
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // batch
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_batch_no))
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Component.getPaddedNumber(total.getBatchNo(), 6))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //title
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(isHistoryPrint ? R.string.receipt_history_settle_report : R.string.receipt_settle_report))
                        .setGravity(Gravity.CENTER_HORIZONTAL)
                        .setFontSize(FONT_BIG));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        List<IssuerTransTotal> issuerTransTotals = total.getIssuerTransTotals();
        for (IssuerTransTotal issuerTransTotal : issuerTransTotals) {
            genSaleAndRefundForIssuer(page, issuerTransTotal);
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_grand_total)));

        String saleNum = Long.toString(total.getSaleTotalNum() + total.getEmiSaleTotalNum());
        String saleAmt = CurrencyConverter.convert(total.getSaleTotalAmt() + total.getEmiSaleTotalAmount());
        String refundNum = Long.toString(total.getRefundTotalNum());
        String refundAmt = CurrencyConverter.convert(-total.getRefundTotalAmt());
        String voidNum = Long.toString(total.getVoidTotalNum() + total.getEmiVoidTotalNum());
        String voidAmt = CurrencyConverter.convert(-(total.getVoidTotalAmt() + total.getEmiVoidTotalAmount()));
        String tipNum = Long.toString(total.getTipTotalNum());
        String tipAmt = CurrencyConverter.convert(total.getTipTotalAmt());
        //bqr 的number
        String qrNum = Long.toString(total.getBqrTotalNum());
        //bqr 的amount
        String qrAmt = CurrencyConverter.convert(total.getBqrTotalAmount());
        // sale，refund，void 的总数量
        String totalNum = Long.toString(total.getSaleTotalNum() + total.getRefundTotalNum() + total.getSaleVoidTotalNum()
                + total.getEmiSaleTotalNum() + total.getEmiVoidTotalNum());
        // sale，refund和void的总金额，由于void之后sale交易就会消失，因此仅统计sale扣除refund即可
        String totalAmt = CurrencyConverter.convert(total.getSaleTotalAmt() + total.getEmiSaleTotalAmount()
                - total.getRefundTotalAmt());
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        //生成Settlement Report中的实体卡片以及yono的交易合计，暂时未统计yono
        genSaleAndRefund(page, saleNum, saleAmt, refundNum, refundAmt, voidNum, voidAmt, tipNum, tipAmt, totalNum, totalAmt);
        page.addLine().addUnit(page.createUnit().setText(LINE).setFontSize(FONT_NORMAL));
        //生成Settlement Report中的bqr交易合计
        genQr(page, qrNum, qrAmt);
        if (Component.isDemo()) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.demo_mode))
                            .setGravity(Gravity.CENTER));
        }

        List<ETransType> list = new ArrayList<>();
        list.add(ETransType.PREAUTH);
        List<TransData> preAuthData = GreendaoHelper.getTransDataHelper().findTransData(list);
        if (!preAuthData.isEmpty()) {
            page.addLine().addUnit(page.createUnit().setText(LINE).setFontSize(FONT_NORMAL));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.settle_preauth_sale))
                            .setGravity(Gravity.CENTER)
                            .setFontSize(FONT_NORMAL));
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getTerminalAndAppVersion())
                        .setGravity(Gravity.CENTER)
                        .setFontSize(FONT_SMALL));

        page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        IImgProcessing imgProcessing = GL.getGL().getImgProcessing();
        return imgProcessing.pageToBitmap(page, 384);
    }

    @Override
    public void clear() {
        // nothing
    }

    private void genSaleAndRefundForIssuer(IPage page, IssuerTransTotal issuerTransTotal) {
        String saleNum = Long.toString(issuerTransTotal.getSaleTotalNum());
        String saleAmt = CurrencyConverter.convert(issuerTransTotal.getSaleTotalAmt());
        String refundNum = Long.toString(issuerTransTotal.getRefundTotalNum());
        String refundAmt = CurrencyConverter.convert(-issuerTransTotal.getRefundTotalAmt());
        String voidNum = Long.toString(issuerTransTotal.getVoidTotalNum());
        String voidAmt = CurrencyConverter.convert(-issuerTransTotal.getVoidTotalAmt());
        String tipNum = Long.toString(issuerTransTotal.getTipTotalNum());
        String tipAmt = CurrencyConverter.convert(issuerTransTotal.getTipTotalAmt());
        String totalNum = Long.toString(issuerTransTotal.getSaleTotalNum() + issuerTransTotal.getRefundTotalNum());
        String totalAmt = CurrencyConverter.convert(issuerTransTotal.getSaleTotalAmt() - issuerTransTotal.getRefundTotalAmt());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_type) + " " + issuerTransTotal.getIssuer()));

        genSaleAndRefund(page, saleNum, saleAmt, refundNum, refundAmt, voidNum, voidAmt, tipNum, tipAmt, totalNum, totalAmt);
    }

    private void genSaleAndRefund(IPage page, String saleNum, String saleAmt, String refundNum, String refundAmt,
                                  String voidNum, String voidAmt, String tipNum, String tipAmt, String totalNum, String totalAmt) {
        // type/count/amount
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(" ")
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_count))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_total))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        // sale
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.trans_sale))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(saleNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(saleAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // refund
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.trans_refund))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(refundNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(refundAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // void
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.settle_total_void_sale))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(voidNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(voidAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(LINE).setFontSize(FONT_NORMAL));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.settle_card_totals))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(totalNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(totalAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.settle_tip_totals))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(tipNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(tipAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
    }

    private void genQr(IPage page, String qrNum, String qrAmt) {
        //title
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_qr_settle_report))
                        .setGravity(Gravity.CENTER_HORIZONTAL)
                        .setFontSize(FONT_BIG));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        //bqr 无卡片类型，因此统一写NA
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_type) + " NA"));
        // type/count/amount
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(" ")
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_count))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_total))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        // Bharat QR
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.trans_sale))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(qrNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(qrAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(LINE).setFontSize(FONT_NORMAL));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        //bqr总计的金额和数量
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.settle_totals))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3f))
                .addUnit(page.createUnit()
                        .setText(qrNum)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER)
                        .setWeight(1.5f))
                .addUnit(page.createUnit()
                        .setText(qrAmt)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
    }


    private Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }

        return image;
    }
}

