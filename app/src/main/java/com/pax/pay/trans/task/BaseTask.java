/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.pay.trans.task;

import android.content.Context;
import android.content.DialogInterface.OnDismissListener;
import android.text.TextUtils;

import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionEndListener;
import com.pax.abl.core.ATransaction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.TransData;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.TransResultUtils;
import com.pax.view.dialog.DialogUtils;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;

public abstract class BaseTask extends ATransaction {
    protected Context context;
    /**
     * transaction listener
     */
    protected TransEndListener transListener;

    private String currentState;

    public BaseTask(Context context, TransEndListener transListener) {
        super();
        this.context = context;
        this.transListener = transListener;
    }

    /**
     * transaction result prompt
     */
    protected void transEnd(final ActionResult result) {
        clear(); // no memory leak
        TransContext.getInstance().setCurrentAction(null);
        if (transListener != null) {
            transListener.onEnd(result);
        }
    }

    /**
     * transaction result prompt and deal with remove card
     *
     * @param transData
     * @param result
     * @param dismissListener
     */
    protected void dispResult(TransData transData, final ActionResult result, OnDismissListener dismissListener) {
        String transName = null;
        ETransType transType = transData.getTransType();
        boolean isBQRTrans = transType == ETransType.QR || transType == ETransType.QR_INQUIRY;
        if (transData.getTransType() != null) {
            transName = transData.getTransType().getTransName();
            if (TransData.ETransStatus.VOIDED.equals(transData.getTransState())) {
                transName = ETransType.VOID.getTransName();
            }
        }
        if ((result.getRet() == TransResult.ERR_QR_MISMATCH_RESPONSE) && (ETransType.QR_INQUIRY == transType ||
                ETransType.QR == transType)) {
            DialogUtils.showSingleConfirmDialog(getCurrentContext(), TransResultUtils.getMessage(
                    TransResult.ERR_QR_MISMATCH_RESPONSE
            ), dismissListener);
        } else if (result.getRet() == TransResult.SUCC) {
            String authCode = "";
            if (!TextUtils.isEmpty(transData.getAuthCode()) && !isBQRTrans) {
                authCode = "APP.CODE " + transData.getAuthCode();
            }

            if (isBQRTrans && (transData.getResponseCode()== null || !"00".equals(transData.getResponseCode().getCode()))) {
                DialogUtils.showErrMessage(getCurrentContext(), transName, "DECLINED", dismissListener, Constants.FAILED_DIALOG_SHOW_TIME);
            } else {
                DialogUtils.showSuccMessage(getCurrentContext(), transName, authCode, dismissListener, Constants.SUCCESS_DIALOG_SHOW_TIME);
            }


        } else if (result.getRet() == TransResult.ERR_ABORTED) {
            // ERR_ABORTED not prompt error message
            if (dismissListener != null) {
                dismissListener.onDismiss(null);
            }
        } else {
            if (ETransType.BANK_EMI_SALE.equals(transType) || ETransType.BRAND_EMI_SALE.equals(transType) ||
                    ETransType.INSTA_EMI_SALE.equals(transType)) {
                // 如果是EMI API失败的包含返回码的，则提示返回的相应错误，否则提示自定义的通用错误
                String errMsg = (String) result.getData();
                errMsg = TextUtils.isEmpty(errMsg) ? TransResultUtils.getMessage(result.getRet()) : errMsg;
                DialogUtils.showErrMessage(getCurrentContext(), transName, errMsg, dismissListener, Constants.FAILED_DIALOG_SHOW_TIME);
            } else {
                DialogUtils.showErrMessage(getCurrentContext(), transName, TransResultUtils.getMessage(result.getRet()), dismissListener, Constants.FAILED_DIALOG_SHOW_TIME);
            }
        }
    }

    protected void bind(String state, AAction action, final boolean forceEndWhenFail) {
        super.bind(state, action);
        if (action != null) {
            action.setEndListener(new ActionEndListener() {

                @Override
                public void onEnd(AAction action, final ActionResult result) {
                    FinancialApplication.getApp().runOnUiThread(new ActionEndRunnable(forceEndWhenFail, result));
                }
            });
        }
    }

    @Override
    protected void bind(String state, AAction action) {
        this.bind(state, action, false);
    }

    @Override
    public void gotoState(String state) {
        this.currentState = state;
        super.gotoState(state);
    }

    @Override
    public void resetState(String state) {
        this.currentState = state;
        super.resetState(state);
    }

    @NonNull
    protected String getString(@StringRes int redId) {
        return context.getString(redId);
    }

    /**
     * deal action result
     *
     * @param currentState ：current State
     * @param result       ：current action result
     */
    public abstract void onActionResult(String currentState, ActionResult result);

    protected Context getCurrentContext() {
        return ActivityStack.getInstance().top();
    }

    private class ActionEndRunnable implements Runnable {
        final boolean forceEndWhenFail;
        final ActionResult result;

        ActionEndRunnable(final boolean forceEndWhenFail, final ActionResult result) {
            this.forceEndWhenFail = forceEndWhenFail;
            this.result = result;
        }

        @Override
        public void run() {
            onEndRun(forceEndWhenFail, result);
        }

        private void onEndRun(final boolean forceEndWhenFail, final ActionResult result) {
            try {
                if (forceEndWhenFail && result.getRet() != TransResult.SUCC) {
                    transEnd(result);
                } else {
                    onActionResult(currentState, result);
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "", e);
                LogUtils.w(TAG, "", e);
                transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            }
        }
    }
}
