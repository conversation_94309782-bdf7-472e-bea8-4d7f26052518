/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.receipt;

import android.graphics.Bitmap;
import androidx.annotation.StringRes;

import android.view.Gravity;
import com.pax.abl.utils.PanUtils;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.eemv.enums.ETransResult;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransData.EnterMode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import java.util.Map;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

/**
 * receipt generator
 *
 * <AUTHOR>
 */
public class ReceiptGeneratorTrans implements IReceiptGenerator {

    private int receiptNo = 0;
    private TransData transData;
    private boolean isRePrint = false;
    private int receiptMax = 0;
    private boolean isPrintPreview = false;

    /**
     * @param transData        ：transData
     * @param currentReceiptNo : currentReceiptNo
     * @param receiptMax       ：generate which one, start from 0
     * @param isReprint        ：is reprint?
     */
    public ReceiptGeneratorTrans(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
    }

    /**
     * @param transData ：transData
     * @param currentReceiptNo : currentReceiptNo
     * @param receiptMax ：generate which one, start from 0
     * @param isReprint ：is reprint?
     * @param isPrintPreview : isPrintPreview?
     */
    public ReceiptGeneratorTrans(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
        this.isPrintPreview = isPrintPreview;
    }

    @Override
    public Bitmap generateBitmap() {
        boolean isPrintSign = false;
        // 生成第几张凭单不合法时， 默认0
        if (receiptNo > receiptMax) {
            receiptNo = 0;
        }
        // the first copy print signature, if three copies, the second copy should print signature too
        if (receiptNo == 0 || ((receiptMax == 3) && receiptNo == 1)) {
            isPrintSign = true;
        }

        IPage page = Device.generatePage();
        // transaction type
        ETransType transType = transData.getTransType();
        TransData.ETransStatus transStatus = transData.getTransState();

        SysParam sysParam = FinancialApplication.getSysParam();
        Acquirer acquirer = transData.getAcquirer();

        String temp;

        //Reprint
        if (isRePrint) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("----" + Utils.getString(R.string.receipt_print_again) + "----")
                            .setFontSize(FONT_BIG)
                            .setGravity(Gravity.CENTER)
                            .setTextStyle(BOLD));
        }

        //Line break
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));


        // title
        page.addLine()
                .addUnit(page.createUnit()
                        .setBitmap(Utils.getImageFile("logo.bmp"))
                        .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit().setText(" "));

        // merchant name
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(sysParam.get(SysParam.StringParam.EDC_MERCHANT_NAME_EN))
                        .setGravity(Gravity.LEFT)
                        .setFontSize(FONT_NORMAL));
        // merchant Address
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(sysParam.get(SysParam.StringParam.EDC_MERCHANT_ADDRESS))
                        .setGravity(Gravity.LEFT));

        // terminal ID/operator ID
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_terminal_code_space)))
                .addUnit(page.createUnit()
                        .setText(acquirer.getTerminalId())
                        .setGravity(Gravity.END));

        // merchant ID
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_merchant_code))
                        .setWeight(2.0f))
                .addUnit(page.createUnit()
                        .setText(acquirer.getMerchantId())
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

//        page.addLine()
//                .addUnit(page.createUnit()
//                        .setText(Utils.getString(R.string.receipt_trans_type))
//                        .setFontSize(FONT_SMALL));
        temp = transStatus.equals(TransData.ETransStatus.NORMAL) ? "" : " (" + transStatus.toString() + ")";
        if (transData.getOrigTransType() != null) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(transType.getTransName() + " " + transData.getOrigTransType().getTransName() + temp)
                            .setFontSize(FONT_BIG)
                            .setTextStyle(BOLD));
        } else {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(transType.getTransName() + temp)
                            .setFontSize(FONT_BIG)
                            .setTextStyle(BOLD));
        }
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        // date/time
        String formattedDate = TimeConverter.convert(Device.getTime(Constants.TIME_PATTERN_TRANS), Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN_DISPLAY2);
        page.addLine().addUnit(page.createUnit().setText(formattedDate).setFontSize(FONT_NORMAL));

        // issue
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(transData.getIssuer().getName())
                        .setFontSize(FONT_NORMAL));

        // card NO
        if (transType == ETransType.PREAUTH || transType == ETransType.AUTHORIZATION) {
            if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_MASK_CARD_NO)) {
                temp = transData.getPan();
            } else {
                temp = PanUtils.maskCardNo(transData.getPan(), transData.getIssuer().getPanMaskPattern());
            }
        } else {
            if (!transData.isOnlineTrans()) {
                temp = transData.getPan();
            } else {
                temp = PanUtils.maskCardNo(transData.getPan(), transData.getIssuer().getPanMaskPattern());
            }
        }

        TransData.EnterMode enterMode = transData.getEnterMode();
        temp += "  " + enterMode.toString();
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("CARD#")
                        .setWeight(4.0f)
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        //expiry date
        String temp1 = transData.getExpDate();
        if (temp1 != null && !temp1.isEmpty()) {
            if (transData.getIssuer().isRequireMaskExpiry()) {
                temp1 = "**/**";
            } else {
                temp1 = temp1.substring(2) + "/" + temp1.substring(0, 2);// 将yyMM转换成MMyy
            }
        } else {
            temp1 = "";
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("EXP DATE:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp1)
                        .setGravity(Gravity.END));


        // batch NO
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("BATCH NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6))
                        .setGravity(Gravity.END));

        //transaction N0
//        temp = Component.getPaddedNumber(transData.getTraceNo(), 6);
        temp = Component.getPaddedNumber(transData.getInvoiceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("TRACE NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END));

        // reference NO
        temp = transData.getRefNo();
        if (temp == null) {
            temp = "";
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("RRN:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END));

        //APP Code
        temp1 = transData.getAuthCode();
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("APP CODE:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp1)
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        //prn instalment
        if (transData.getInstalment() != 0) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_month) + ":" + Component.getPaddedNumber(transData.getInstalment(), 2))
                            .setFontSize(FONT_NORMAL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }


        if (transType == ETransType.VOID) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_orig_trans_no)
                                    + Component.getPaddedNumber(transData.getOrigTransNo(), 6)));
        }

/*        if ((enterMode == EnterMode.INSERT || enterMode == EnterMode.CLSS) &&
                (transType == ETransType.SALE || transType == ETransType.PREAUTH)) {
            if (transData.getEmvResult() == ETransResult.OFFLINE_APPROVED) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TC:"))
                        .addUnit(page.createUnit()
                                .setText(transData.getTc())
                                .setGravity(Gravity.END));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("ARQC:")
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(transData.getArqc())
                                .setGravity(Gravity.END)
                                .setWeight(7.0f));
            }

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TSI:  " + transData.getTsi()))
                    .addUnit(page.createUnit()
                            .setText("ATC:  " + transData.getAtc())
                            .setGravity(Gravity.END));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TVR:"))
                    .addUnit(page.createUnit()
                            .setText(transData.getTvr())
                            .setGravity(Gravity.END));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("APP:")
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getEmvAppLabel())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("AID:")
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getAid())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        }*/

        //Jerry modify
        if (enterMode == EnterMode.INSERT || enterMode == EnterMode.CLSS) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("APP:")
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getEmvAppLabel())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("AID:")
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getAid())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            if (!transData.getAcquirer().getName().equals("HASE_CUP")) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TC:"))
                        .addUnit(page.createUnit()
                                .setText(transData.getTc())
                                .setGravity(Gravity.END));
            } else {
                if (transData.getEmvResult() == ETransResult.OFFLINE_APPROVED) {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText("TC:"))
                            .addUnit(page.createUnit()
                                    .setText(transData.getTc())
                                    .setGravity(Gravity.END));
                } else {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText("ARQC:")
                                    .setWeight(4.0f))
                            .addUnit(page.createUnit()
                                    .setText(transData.getArqc())
                                    .setGravity(Gravity.END)
                                    .setWeight(7.0f));
                }

                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TVR:"))
                        .addUnit(page.createUnit()
                                .setText(transData.getTvr())
                                .setGravity(Gravity.END));
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TSI:  " + transData.getTsi()))
                        .addUnit(page.createUnit()
                                .setText("ATC:  " + transData.getAtc())
                                .setGravity(Gravity.END));
            }
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        }

        String RedeemAmountName = FinancialApplication.getSysParam().get(SysParam.StringParam
                .HASE_CASH_DOL_NAME);
        if (transData.getAcquirer().getName().equals("HASE_OLS")) {
            RedeemAmountName = getRedeemName(transData, sysParam);
        }
        //base & tip
        if(transData.isDcc()) {
            String tipAmopunt = String.valueOf(transData.getTipAmount());
            boolean hasTip = !(tipAmopunt == null || tipAmopunt.isEmpty() || tipAmopunt.equals("0"));
            boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP);
            String localCurrencySymbol = transData.getLocalCurrency().getSymbol();
            //rate
            String homeCurrencySymbol = transData.isFullOutSource()? transData.getReceiptHomeCurrency().getSymbol() : transData.getHomeCurrency().getSymbol();
            String rate;
            if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_INVERSE_RATE) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC)) {
                rate = "1 " + homeCurrencySymbol + " = " + Utils.inverseRate(transData.getRate(), 8) + " " + localCurrencySymbol;
            } else {
                rate = "1 " + localCurrencySymbol + " = " + Utils.initRate(transData.getRate()) + " " + homeCurrencySymbol;
            }
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_rate))
                            .setFontSize(FONT_SMALL)
                            .setWeight(8.0f))
                    .addUnit(page.createUnit()
                            .setText(rate)
                            .setFontSize(FONT_NORMAL)
                            .setGravity(Gravity.END)
                            .setWeight(20.0f));
            if (Utils.getString(R.string.issuer_visa).equalsIgnoreCase(transData.getIssuer().getName())) {
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit()
                        .setText("(" + Utils.initMarkupRate(transData.getDccMarkupRate()) + " Markup Included)")
                        .setFontSize(FONT_SMALL));
            }

            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

            // total
            long amount = transData.getAmount();
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("SELECT[X]TRANSACTION CURRENCY")
                            .setFontSize(FONT_SMALL));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

            long homeAmount = Utils.parseLongSafe(transData.getHomeCurrencyAmount(), 0);
            //VOID REFUND and REFUND(VOIDED) and VOID INSTALLMENT REFUND not print "-"
            if ((transType.isSymbolNegative() || transData.getTransState().equals(TransData.ETransStatus.VOIDED)) &&
                    transData.getOrigTransType() == ETransType.REFUND) {
                amount = -amount;
                homeAmount = -homeAmount;
            }
            temp = CurrencyConverter.convert(amount, transData.getLocalCurrency());
            String temp2 = CurrencyConverter.convert(homeAmount, transData.getHomeCurrency());

            String amountLab = " AMOUNT[X]";
            if (((transType == ETransType.PREAUTH || transType == ETransType.AUTHORIZATION) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_DCC_AUTH_NEW_FLOW)) || ((transType == ETransType.SALE || transType == ETransType.OFFLINE_TRANS_SEND) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_DCC_SALE_NEW_FLOW))) {
                amountLab = " AMOUNT[  ]";
            }
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("[  ]" + transData.getLocalCurrency().getSymbol() + " AMOUNT")
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.START))
                    .addUnit(page.createUnit()
                            .setText(transData.getHomeCurrency().getSymbol() + amountLab)
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END));
            if (hasTip && transType.isAdjustAllowed() && transData.getTransState() != TransData.ETransStatus.VOIDED) {
                String localTip = CurrencyConverter.convert(transData.getTipAmount(), transData.getLocalCurrency());
                String homeTip = CurrencyConverter.convert(Utils.parseLongSafe(transData.getHomeCurrencyTipAmount(), 0), transData.getHomeCurrency());
                String localBase = CurrencyConverter.convert(transData.getAmount() - transData.getTipAmount(), transData.getLocalCurrency());
                String homeBase = CurrencyConverter.convert(Utils.parseLongSafe(transData.getHomeCurrencyAmount(), 0) - Utils.parseLongSafe(transData.getHomeCurrencyTipAmount(), 0), transData
                        .getHomeCurrency());

                //base
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(localBase)
                                .setWeight(2.5f)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.START))
                        .addUnit(page.createUnit()
                                .setText("")
                                .setFontSize(FONT_SMALL)
                                .setWeight(1f))
                        .addUnit(page.createUnit()
                                .setText(homeBase)
                                .setFontSize(FONT_SMALL)
                                .setWeight(2.5f)
                                .setGravity(Gravity.END));

                //tip
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(localTip)
                                .setWeight(2f)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.START))
                        .addUnit(page.createUnit()
                                .setFontSize(FONT_SMALL)
                                .setText(Utils.getString(R.string.receipt_amount_tip))
                                .setWeight(1f))
                        .addUnit(page.createUnit()
                                .setText(homeTip)
                                .setFontSize(FONT_SMALL)
                                .setWeight(2f)
                                .setGravity(Gravity.END));

                //total
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_SMALL)
                                .setWeight(2f)
                                .setGravity(Gravity.START))
                        .addUnit(page.createUnit()
                                .setFontSize(FONT_SMALL)
                                .setText(Utils.getString(R.string.receipt_amount_total))
                                .setWeight(1f))
                        .addUnit(page.createUnit()
                                .setText(temp2)
                                .setFontSize(FONT_SMALL)
                                .setWeight(2f)
                                .setGravity(Gravity.END));

                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.START))
                        .addUnit(page.createUnit()
                                .setText(temp2)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.END));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            }

            //when support tip, print tip line
            //only for sale and offline
            if (enableTip && transType.isAdjustAllowed() && !hasTip && transData.getTransState() != TransData.ETransStatus.VOIDED) {
                page.addLine().addUnit(page.createUnit().setText("TIPS IN TXN CURRENCY _________________").setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit().setText("TOTAL IN TXN CURRENCY _______________").setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            }

            if (Utils.getString(R.string.issuer_visa).equalsIgnoreCase(transData.getIssuer().getName())) {
                page.addLine().addUnit(page.createUnit().setText("------------------------------------------").setFontSize(FONT_SMALL));
                if (transData.getLocalCurrency().getCode().equals("446")) {
                    page.addLine().addUnit(page.createUnit().setText(Utils.getString(R.string.receipt_dcc_visa_claim_MOP)).setFontSize(FONT_SMALL).setGravity(Gravity.CENTER));
                } else {
                    page.addLine().addUnit(page.createUnit().setText(Utils.getString(R.string.receipt_dcc_visa_claim_HKD)).setFontSize(FONT_SMALL).setGravity(Gravity.CENTER));
                }
            }
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        }else{
            if (transType.isAdjustAllowed()) {
                String AmtTotalName = Utils.getString(R.string.receipt_amount_total);
                long base = transData.getAmount() - transData.getTipAmount();
                long TotalAmount = base;

                temp = CurrencyConverter.convert(base, transData.getCurrency());
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_amount_base))
                                .setFontSize(FONT_NORMAL)
                                .setWeight(8.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(10.0f));

                if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                    AmtTotalName = "NET TOTAL";
                    long CashAmount = 0;
                    if (transData.getLysStatus() == TransData.LYSStatus.LYS_OK) {
                        CashAmount = transData.getCashDolRedeemed();
                        temp = CurrencyConverter.convert(CashAmount, transData.getCurrency());
                        page.addLine()
                                .addUnit(page.createUnit()
                                        .setText(RedeemAmountName)
                                        .setFontSize(FONT_NORMAL)
                                        .setWeight(2.0f))
                                .addUnit(page.createUnit()
                                        .setText(temp)       //getCashRedeem()
                                        .setFontSize(FONT_NORMAL)
                                        .setGravity(Gravity.END)
                                        .setWeight(3.0f));

                        // bonus amount
                        if (transData.getCashDolBonusRedeemed() != 0) {
                            long CashDolBonusRedeemed = transData.getCashDolBonusRedeemed();
                            temp = CurrencyConverter.convert(CashDolBonusRedeemed, transData.getCurrency());
                            page.addLine()
                                    .addUnit(page.createUnit()
                                            .setText(sysParam.get(SysParam.StringParam.BONUS_DOL_NAME))
                                            .setFontSize(FONT_NORMAL)
                                            .setWeight(2.0f))
                                    .addUnit(page.createUnit()
                                            .setText(temp)
                                            .setFontSize(FONT_NORMAL)
                                            .setGravity(Gravity.END)
                                            .setWeight(3.0f));
                        }
                    }
                    page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')).setFontSize(FONT_NORMAL));

                    //NET AMOUNT
                    long NetAmt = base - CashAmount;
                    temp = CurrencyConverter.convert(NetAmt, transData.getCurrency());
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText("NET AMT")
                                    .setFontSize(FONT_NORMAL)
                                    .setWeight(2.0f))
                            .addUnit(page.createUnit()
                                    .setText(temp)
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.END)
                                    .setWeight(3.0f));

                    TotalAmount = NetAmt;

                }
                long tips = transData.getTipAmount();
                temp = CurrencyConverter.convert(tips, transData.getCurrency());
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_amount_tip))
                                .setFontSize(FONT_NORMAL)
                                .setWeight(2.0f))
                        .addUnit(page.createUnit().setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
                page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')).setFontSize(FONT_NORMAL));

                TotalAmount += tips;
                temp = CurrencyConverter.convert(TotalAmount, transData.getCurrency());
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(AmtTotalName)
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(9.0f));
            } else {
                long BaseAmount = transData.getAmount();
                String BaseName = Utils.getString(R.string.receipt_amount_base);
                if (transType.isSymbolNegative() && transData.getOrigTransType() != ETransType.REFUND) {
                    BaseAmount = -BaseAmount;
                    //fix add Cash$ about Redeem VOID.
                    BaseName = Utils.getString(R.string.receipt_amount_total);
                } else {
                    if (!transData.getAcquirer().getName().equals("HASE_OLS"))
                        BaseName = Utils.getString(R.string.receipt_amount_total);
                }

                temp = CurrencyConverter.convert(BaseAmount, transData.getCurrency());
                //Base/total amount
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(BaseName)
                                .setFontSize(FONT_NORMAL)
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(9.0f));

                if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                    //NET AMOUNT
                    long NetAmt = transData.getNetAmount();
                    long CashAmount = 0;
                    if (transData.getLysStatus() == TransData.LYSStatus.LYS_OK) {
                        if (transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR) {
                            CashAmount = BaseAmount - (-NetAmt);
                        } else {
                            CashAmount = transData.getCashDolRedeemed();
                        }
                        temp = CurrencyConverter.convert(CashAmount, transData.getCurrency());
                        if (transData.getTransType() != ETransType.REFUND &&
                                transData.getOrigTransType() != ETransType.REFUND) {
                            page.addLine()
                                    .addUnit(page.createUnit()
                                            .setText(RedeemAmountName)
                                            .setFontSize(FONT_NORMAL)
                                            .setWeight(2.0f))
                                    .addUnit(page.createUnit()
                                            .setText(temp)       //getCashRedeem()
                                            .setFontSize(FONT_NORMAL)
                                            .setGravity(Gravity.END)
                                            .setWeight(3.0f));
                        }
                    }

                    if (transData.getTransType() == ETransType.SALES_WITH_CASH_DOLLAR) {
                        page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')).setFontSize(FONT_NORMAL));
                        temp = CurrencyConverter.convert(NetAmt, transData.getCurrency());
                        page.addLine()
                                .addUnit(page.createUnit()
                                        .setText("NET AMT")
                                        .setFontSize(FONT_NORMAL)
                                        .setWeight(2.0f))
                                .addUnit(page.createUnit()
                                        .setText(temp)
                                        .setFontSize(FONT_NORMAL)
                                        .setGravity(Gravity.END)
                                        .setWeight(3.0f));
                    }
                    if (transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR) {
                        page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')).setFontSize(FONT_NORMAL));
                        //NET TOTAL
                        long NetTotal = -NetAmt;
                        temp = CurrencyConverter.convert(NetTotal, transData.getCurrency());
                        page.addLine()
                                .addUnit(page.createUnit()
                                        .setText("NET TOTAL")
                                        .setFontSize(FONT_NORMAL)
                                        .setWeight(2.0f))
                                .addUnit(page.createUnit()
                                        .setText(temp)
                                        .setFontSize(FONT_NORMAL)
                                        .setGravity(Gravity.END)
                                        .setWeight(3.0f));
                    }

                    if (transData.getTransType() == ETransType.REDEEM_INST) {
                        page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')).setFontSize(FONT_NORMAL));
                        //REMAINING
                        page.addLine()
                                .addUnit(page.createUnit()
                                        .setText("REMAINING")
                                        .setFontSize(FONT_NORMAL)
                                        .setWeight(2.0f))
                                .addUnit(page.createUnit()
                                        .setText("xxx")
                                        .setFontSize(FONT_NORMAL)
                                        .setGravity(Gravity.END)
                                        .setWeight(3.0f));
                    }
                    if (transData.getTransType() != ETransType.VOID_WITH_CASH_DOLLAR &&
                            transData.getOrigTransType() != ETransType.SALES_WITH_CASH_DOLLAR) {

                    }
                }
            }
        }

        page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 28, '=')).setFontSize(FONT_NORMAL));

        if (transData.getAcquirer().getName().equals("HASE_OLS")) {      //print bal amount
            if (transData.getLysStatus() == TransData.LYSStatus.LYS_OK) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("EXP")
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.CENTER)
                                .setWeight(7.0f))
                        .addUnit(page.createUnit()
                                .setText("BAL")       //getCashRedeem()
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
                //Need add Cash Expriy and cash ballance
                temp1 = transData.getCashDolExpdate();
                if (temp1 != null && !temp1.isEmpty()) {
                    temp1 = temp1.substring(4, 6) + "/" + temp1.substring(2, 4);// 将yyMM转换成MMyy
                } else {
                    temp1 = "";
                }

                long CashBalance = transData.getCashDolBalance();
                temp = CurrencyConverter.convert(CashBalance, transData.getCurrency());
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(RedeemAmountName)
                                .setFontSize(FONT_NORMAL)
                                .setWeight(5.0f))
                        .addUnit(page.createUnit()
                                .setText(temp1)
                                .setFontSize(FONT_NORMAL)
                                .setWeight(3.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));

            } else {
                page.addLine().addUnit(page.createUnit().setText("* + FUN$ FUNCTION IS       *").setFontSize(FONT_NORMAL));
                page.addLine().addUnit(page.createUnit().setText("* TEMPORARY UNAVAILABLE   *").setFontSize(FONT_NORMAL));
            }
        }
        page.addLine().addUnit(page.createUnit().setText(" "));

        //prn instalment terms
        if (transData.getInstalment() != 0) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_prompt))
                            .setFontSize(FONT_NORMAL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }

        boolean isSignFree = transData.isSignFree();

        if (!isSignFree) {
            String tmp = getSignaturePrompts(transData);
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(tmp)
                            .setFontSize(FONT_SMALL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }

        if (isSignFree) {// only sign free
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_amount_prompt_end_sign)));

        }
        if (!isSignFree && isPrintSign) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_sign))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.CENTER));
            Bitmap bitmap = loadSignature(transData);
            if (bitmap != null) {
                page.addLine().addUnit(page.createUnit()
                        .setBitmap(loadSignature(transData))
                        .setGravity(Gravity.CENTER));
            } else {
                page.addLine().addUnit(page.createUnit().setText(" "));
            }

        }

        if (isSignFree) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.CENTER));
        }

        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        if (receiptMax == 3) {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("*---  " + Utils.getString(R.string.receipt_stub_acquire) + "   ---*")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else if (receiptNo == 1) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("*---  " + Utils.getString(R.string.receipt_stub_merchant) + "   ---*")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("*---  " + Utils.getString(R.string.receipt_stub_user) + "   ---*")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        } else {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("*---  " + Utils.getString(R.string.receipt_stub_merchant) + "   ---*")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("*---  " + Utils.getString(R.string.receipt_stub_user) + "   ---*")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        }
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("Cardholder acknowledges receipt of goods and services and agrees to pay the total shown herein.")
                        .setFontSize(FONT_SMALL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(getTerminalAndAppVersion())
                        .setFontSize(FONT_NORMAL));

        if (Component.isDemo()) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        if (!isPrintPreview) {
            page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        }

        IImgProcessing imgProcessing = FinancialApplication.getGl().getImgProcessing();
        return imgProcessing.pageToBitmap(page, 384);
    }

    private String genFreePrompt(@StringRes int amountPrompt, long amount, @StringRes int resultPrompt) {
        return Utils.getString(amountPrompt)
                + CurrencyConverter.convert(amount, transData.getCurrency())
                + ", "
                + Utils.getString(resultPrompt);
    }

//    private Bitmap getImageFromAssetsFile(String fileName) {
//        Bitmap image = null;
//        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
//        try {
//            InputStream is = am.open(fileName);
//            image = BitmapFactory.decodeStream(is);
//            is.close();
//        } catch (IOException e) {
//            Log.e(TAG, "", e);
//        }
//
//        return image;
//
//    }


    private Bitmap loadSignature(TransData transData) {
        byte[] signData = transData.getSignData();
        if (signData == null) {
            return null;
        }
        return FinancialApplication.getGl().getImgProcessing().jbigToBitmap(signData);
    }

    /***
     * // generate bar code
     * private void generateBarCode(TransData transData, IPage page) {
     * <p>
     * ETransType transType = ETransType.valueOf(transData.getTransType());
     * if (transType == ETransType.AUTH || transType == ETransType.SALE) {
     * if (transData.getTransState().equals(ETransStatus.VOIDED)) {
     * return;
     * }
     * try {
     * JSONObject json = new JSONObject();
     * <p>
     * json.put("authCode", transData.getAuthCode());
     * json.put("date", transData.getDateTime().substring(4, 8));
     * json.put("transNo", transData.getTraceNo());
     * json.put("refNo", transData.getRefNo());
     * <p>
     * JSONArray array = new JSONArray();
     * array.put(json);
     * <p>
     * page.addLine().addUnit(
     * FinancialApplication.gl.getImgProcessing().generateBarCode(array.toString(), 230, 230,
     * BarcodeFormat.QR_CODE), Gravity.CENTER);
     * } catch (JSONException e) {
     * e.printStackTrace();
     * }
     * <p>
     * }
     * }
     ***/

    private String getTerminalAndAppVersion() {
        Map<ETermInfoKey, String> map = FinancialApplication.getDal().getSys().getTermInfo();
        return map.get(ETermInfoKey.MODEL) + " " + FinancialApplication.getVersion();
    }

    @Override
    public String generateString() {
        return "Card No:" + transData.getPan() + "\nTrans Type:" + transData.getTransType().toString()
                + "\nAmount:" + CurrencyConverter.convert(transData.getAmount(), transData.getCurrency())
                + "\nTip:" + CurrencyConverter.convert(transData.getTipAmount(), transData.getCurrency())
                + "\nTransData:" + transData.getDateTime();
    }

    private String getSignaturePrompts(TransData transData) {
        String tmp = "";
        if (transData.getInstalment() != 0) {
            tmp = Utils.getString(R.string.receipt_signature_instalment_prompt);
        } else {
            tmp = Utils.getString(R.string.receipt_signature_prompt);
        }
        return tmp;
    }

    private String getRedeemName(TransData transData, SysParam sysParam) {
        String RedeemAmountName = FinancialApplication.getSysParam().get(SysParam.StringParam
                .HASE_CASH_DOL_NAME);
        if (transData.getLysStatus() == TransData.LYSStatus.LYS_OK) {
            String redemptionFlag = transData.getRedemptionFlag();
            if (redemptionFlag == null) {
                return RedeemAmountName;
            }

            if (redemptionFlag.equals("010")) {      //M1$
                RedeemAmountName = sysParam.get(SysParam.StringParam.MERCHANT1_DOL_NAME);
            } else if (redemptionFlag.equals("001")) {     //M2$
                RedeemAmountName = sysParam.get(SysParam.StringParam.MERCHANT2_DOL_NAME);
            } else {
            } //HASE CASH$
        }
        return RedeemAmountName;
    }
}
