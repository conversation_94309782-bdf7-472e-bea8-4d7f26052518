package com.pax.pay.trans.pack;

import com.pax.abl.core.ipacker.PackListener;

public class PackEnquiry extends PackIso8583 {

    /**
     * @param listener packlistener
     */
    public PackEnquiry(PackListener listener) {
        super(listener);
    }

    /**
     * required iso8583 fields
     */
    @Override
    protected int[] getRequiredFields() {
        return new int[] { 2, 3, 4, 11, 14, 22, 24, 25, 35, 41, 42, 49, 63 };
    }
}
