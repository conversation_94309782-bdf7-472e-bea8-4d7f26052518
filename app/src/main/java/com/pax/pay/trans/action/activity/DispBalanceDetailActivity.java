package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;

import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.LoyaltyProgram;
import com.pax.pay.utils.Utils;
import com.pax.view.RcvCashBalanceAdapter;

import java.util.ArrayList;

import static com.pax.pay.utils.Utils.subBytes;

/**
 * Created by z<PERSON><PERSON> on 2017/12/27.
 */

public class DispBalanceDetailActivity extends BaseActivityWithTickForAction {
    String navTitle;
    private byte printFlag = 1;

    private byte[] contentBalanceData;
    ArrayList<LoyaltyProgram> loyaltyProgramList;

    private RecyclerView mRvcCashBalance;
    private RcvCashBalanceAdapter mRcvCashBalAdapter;

    /**
     * load param
     */
    @Override
    protected void loadParam() {
        Bundle bundle = getIntent().getExtras();
        navTitle = bundle.getString(EUIParamKeys.NAV_TITLE.toString());
        contentBalanceData = bundle.getByteArray(EUIParamKeys.CONTENT_BALANCEDATA.toString());
        if (contentBalanceData != null && contentBalanceData.length != 0) {
            int ret = UnpackBalanceData(contentBalanceData);
            if (ret != TransResult.SUCC) {
                finish(new ActionResult(TransResult.ERR_LYS_UNAVAILABLE, null));
            }
        } else if (Component.isDemo()) {
            createDummyData();
        } else {
            finish(new ActionResult(TransResult.ERR_LYS_UNAVAILABLE, null));
            return;
        }
    }

    /**
     * get layout id
     * @return int
     */
    @Override
    protected int getLayoutId() {
        return R.layout.activity_disp_balance_detail;
    }

    /**
     * get title string
     * @return String
     */
    @Override
    protected String getTitleString() {
        return navTitle;
    }

    /**
     * init views
     */
    @Override
    protected void initViews() {
        mRvcCashBalance = (RecyclerView) findViewById(R.id.rcv_cash_balance);
        mRcvCashBalAdapter = new RcvCashBalanceAdapter(this, loyaltyProgramList);
        mRvcCashBalance.setAdapter(mRcvCashBalAdapter);

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mRvcCashBalance.setLayoutManager(linearLayoutManager);

        DividerItemDecoration divider = new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
        divider.setDrawable(getResources().getDrawable(R.drawable.recyclerview_divider));
        mRvcCashBalance.addItemDecoration(divider);
    }

    @Override
    protected void setListeners() {
    }

    /**
     * 使用自定义Menu替代默认的Menu
     * @param menu Menu
     * @return boolean
     */
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.loyalty_print, menu);
        return super.onCreateOptionsMenu(menu);
    }

    /**
     * on options item selected
     * @param item MenuItem
     * @return boolean
     */
    @Override
    protected boolean onOptionsItemSelectedSub(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish(new ActionResult(TransResult.SUCC, null));
                return true;
            case R.id.lyt_query_print:
                finish(new ActionResult(TransResult.SUCC, printFlag));
                return true;
        }
        return super.onOptionsItemSelectedSub(item);
    }

    /**
     * on key back down
     * @return boolean
     */
    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.SUCC, null));
        return true;
    }

    /**
     * Unpack Balance Data
     * @param balanceData byte[]
     * @return int
     */
    public int UnpackBalanceData(@NonNull byte[] balanceData) {
        loyaltyProgramList = new ArrayList<>();

        if (balanceData == null || balanceData.length == 2 || balanceData.length != 77) {
            return TransResult.ERR_LYS_UNAVAILABLE;
        }

        String lysStatus = new String(Utils.subBytes(balanceData, 4, 5));
        if (!"1".equals(lysStatus)) {
            return TransResult.ERR_LYS_UNAVAILABLE;
        }

        for (int i = 0; i < 3; i++) {
            int index = 5 + i * 24;

            String programId = new String(subBytes(balanceData, index, index + 3));
            if ("000".equals(programId) || "   ".equals(programId)) {
                continue;
            }
            String balance = Utils.bcd2Str(subBytes(balanceData, index + 3, index + 9));
            String balanceSign = new String(subBytes(balanceData, index + 9, index + 10));
            String expireDate = Utils.bcd2Str(subBytes(balanceData, index + 10, index + 14));
            String expiredBalance = Utils.bcd2Str(subBytes(balanceData, index + 14, index + 20));
            String expiredBalanceDate = Utils.bcd2Str(subBytes(balanceData, index + 20, index + 24));
            LoyaltyProgram loyaltyProgram = new LoyaltyProgram();
            loyaltyProgram.setProgramId(programId);
            loyaltyProgram.setCashBalance(balance);
            loyaltyProgram.setCashSign(balanceSign);
            loyaltyProgram.setCashExpDate(expireDate);
            loyaltyProgram.setExpCashBalance(expiredBalance);
            loyaltyProgram.setExpCashExpiry(expiredBalanceDate);
            loyaltyProgramList.add(loyaltyProgram);
        }

        return TransResult.SUCC;
    }

    /**
     * create dummy data
     */
    private void createDummyData() {
        loyaltyProgramList = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            LoyaltyProgram loyaltyProgram = new LoyaltyProgram();
            loyaltyProgram.setProgramId("00" + i);
            loyaltyProgram.setCashBalance(String.format("%012d", 10000));
            loyaltyProgram.setCashSign("0");
            loyaltyProgram.setCashExpDate("20181231");
            loyaltyProgram.setExpCashBalance(String.format("%012d", 10000));
            loyaltyProgram.setExpCashExpiry("20171231");
            loyaltyProgramList.add(loyaltyProgram);
        }
    }
}
