/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.transmit;

import android.os.SystemClock;
import android.util.Log;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.gl.commhelper.exception.CommException;
import com.pax.glwrapper.comm.ICommHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.utils.Utils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static com.pax.pay.utils.Utils.getString;

class TcpNoSsl extends ATcp {

    private String ip;
    private int port;

    /**
     * 建立连接
     */
    @Override
    public int onConnect(boolean isReversal, boolean ecrMode) {
        int ret = setCommParam();
        if (ret != TransResult.SUCC) {
            return ret;
        }

        if (!isEnableBalance()) {
            ip = getHostIp();
            port = getHostPort();
            onShowMsg(FinancialApplication.getApp().getString(R.string.wait_connect),
                    getConnectTimeout() / 1000);
            ret = connect(ip, port);
            if (ret != TransResult.ERR_CONNECT) {
                return ret;
            }
            ip = getBackupIp();
            port = getBackupPort();
            onHideMsg();
            onShowMsg(FinancialApplication.getApp().getString(R.string.wait_connect_other), getConnectTimeout() /
                    1000);
            return connect(ip, port);
        } else {
            //如果上一笔交易也是reversal则说明上一次reversal失败，再次进行reversal需更换ip
            if (isReversal) {
                if (isLastTransReversal()) {
                    balanceHost();
                }
                setLastTransReversal(true);
            } else {
                setLastTransReversal(false);
            }
            ip = balancedIp();
            port = balancedPort();
            //如果是reversal则reversal和下次正常交易数据在同一个ip内上送
            if (!isReversal) {
                balanceHost();
            }
            onShowMsg(FinancialApplication.getApp().getString(R.string.wait_connect),
                    getConnectTimeout() / 1000);
            ret = connect(ip, port);
            if (ret == TransResult.SUCC) {
                return ret;
            }
            //若是reversal交易，前面未切换ip，此时连接失败需要切换ip
            if (isReversal) {
                balanceHost();
            }
            ip = balancedIp();
            port = balancedPort();
            //如果是reversal则reversal和下次正常交易数据在同一个ip内上送
            if (!isReversal) {
                balanceHost();
            }
            onHideMsg();
            onShowMsg(FinancialApplication.getApp().getString(R.string.wait_connect_other),
                    getConnectTimeout() / 1000);
            return connect(ip, port);
        }
    }

    /**
     * 发送数据
     */
    @Override
    public int onSend(byte[] data, String acquirerName) {
        try {
            onShowMsg(Utils.getString(R.string.wait_send), getSendTimeout() / 1000);
            client.send(data);
            return TransResult.SUCC;
        } catch (CommException e) {
            Log.e(TAG, "", e);
        }
        return TransResult.ERR_SEND;
    }

    /**
     * 接收数据
     */
    @Override
    public TcpResponse onRecv(String acqName) {
        try {
            onShowMsg(Utils.getString(R.string.wait_recv), getReceiveTimeout() / 1000);
            byte[] lenBuf = client.recv(2);
            if (lenBuf == null || lenBuf.length != 2) {
                onHideMsg();
                return new TcpResponse(TransResult.ERR_RECV, null);
            }
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int len = (((lenBuf[0] << 8) & 0xff00) | (lenBuf[1] & 0xff));
            byte[] rsp = client.recv(len);
            if (rsp == null || rsp.length != len) {
                onHideMsg();
                return new TcpResponse(TransResult.ERR_RECV, null);
            }
            baos.write(rsp);
            rsp = baos.toByteArray();
            onHideMsg();
            return new TcpResponse(TransResult.SUCC, rsp);
        } catch (IOException | CommException e) {
            Log.e(TAG, "", e);
        }

        onHideMsg();
        return new TcpResponse(TransResult.ERR_RECV, null);
    }

    /**
     * 关闭连接
     */
    @Override
    public void onClose() {
        try {
            client.disconnect();
        } catch (Exception e) {
            Log.e(TAG, "", e);
        }
    }

    private int connect(String hostIp, int port) {
        if (hostIp == null || hostIp.length() == 0 || getString(R.string.acq_ip_hint).equals(
                hostIp)) {
            //增加1秒延时，否则回复太快会跳过正在连接的对话框显示直接显示连接失败
            SystemClock.sleep(1000);
            return TransResult.ERR_CONNECT;
        }

        ICommHelper commHelper = FinancialApplication.getGl().getCommHelper();
        connectSub(commHelper, hostIp, port);
        client.setConnectTimeout(getConnectTimeout());
        client.setSendTimeout(getSendTimeout());
        client.setRecvTimeout(getReceiveTimeout());
        try {
            client.connect();
            return TransResult.SUCC;
        } catch (CommException e) {
            Log.e(TAG, "", e);
        }
        return TransResult.ERR_CONNECT;
    }

    protected void connectSub(ICommHelper commHelper, String hostIp, int port) {
        client = commHelper.createTcpClient(hostIp, port);
    }

}
