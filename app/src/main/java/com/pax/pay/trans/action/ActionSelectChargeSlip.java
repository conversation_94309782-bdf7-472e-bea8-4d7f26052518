package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.SelectAccountActivity;
import com.pax.pay.trans.action.activity.SelectChargeActivity;

import java.lang.ref.WeakReference;

public class ActionSelectChargeSlip extends AAction {
    private WeakReference<Context> weakReference;
    private String title;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionSelectChargeSlip(ActionStartListener listener) {
        super(listener);
    }


    public void setParam(Context context, String title) {
        this.weakReference = new WeakReference<>(context);
        this.title = title;
    }

    @Override
    protected void process() {
        if (weakReference.get() != null) {
            Intent intent = new Intent(weakReference.get(), SelectChargeActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
            intent.putExtras(bundle);
            weakReference.get().startActivity(intent);
        } else{
            Log.d(TAG, "actionSelectChargeSlip: weakReference is null");
        }
    }
}
