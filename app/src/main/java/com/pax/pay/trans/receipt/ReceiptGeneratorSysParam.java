/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.Context;
import android.view.Gravity;

import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.utils.FontCache;
import com.pax.device.Device;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * receipt generator
 *
 * <AUTHOR>
 */
public class ReceiptGeneratorSysParam extends ReceiptGeneratorParam implements IReceiptGenerator {

    public ReceiptGeneratorSysParam() {
        //do nothing
    }

    @Override
    protected List<IPage> generatePages(Context context) {
        String[] keyArray = BaseApplication.getAppContext().getResources().getStringArray(R.array.config_menu_edc_title);
        String[] valueArray = BaseApplication.getAppContext().getResources().getStringArray(R.array.config_menu_edc_key);
        // 存储key和value的匹配表，便于后面匹配具体的键值打印
        Map<String,String> itemMap = new HashMap<>();
        // 遍历移除所有特殊符号，防止影响后面字符串匹配
        for(int i = 0;i<keyArray.length;i++){
            keyArray[i] = keyArray[i].replaceAll("[.:#]","");
            itemMap.put(keyArray[i],valueArray[i]);
        }
        List<IPage> pages = new ArrayList<>();
        List<String[]> sysPrList = SysParam.getInstance().getAllParam();

        IPage page = Device.generatePage();
        page.setTypeFace(FontCache.get(Constants.PAID_FONT_NAME, FinancialApplication.getApp()));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("\nSysParam\n")
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER));
        pages.add(page);
        page = Device.generatePage();
        for (String key : keyArray) {
            // 关闭transaction limit则不用打印transaction number参数
            if(Utils.getString(R.string.edc_max_transaction_number).equals(key) &&
                    !SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_TRANSACTION_NUMBER_LIMIT))){
                continue;
            }
            for (String[] i : sysPrList) {
                String fstr = i[0];
                if((fstr != null && fstr.equals(itemMap.get(key)))){
                    fstr = key;
                    float fWeight = (float) fstr.length() / (float) (fstr.length() + i[1].length()) * 5.0f;
                    float eWeight = (float) i[1].length() / (float) (fstr.length() + i[1].length()) * 5.0f + 0.1f;

                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(fstr)
                                    .setFontSize(FONT_SMALL)
                                    .setWeight(fWeight))
                            .addUnit(page.createUnit()
                                    .setText(i[1])
                                    .setFontSize(FONT_SMALL)
                                    .setGravity(Gravity.END)
                                    .setWeight(eWeight));
                    page.adjustLineSpace(10);
                    //匹配成功则直接跳出当前循环，节省资源
                    break;
                }
            }
        }

        pages.add(page);
        page = Device.generatePage();
        page.addLine().addUnit(page.createUnit().setText("\n\n"));
        pages.add(page);
        return pages;
    }

}
