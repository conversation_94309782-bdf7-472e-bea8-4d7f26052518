/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.Utils;

public class PackPreAuthCancel extends PackIso8583 {

    public PackPreAuthCancel(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{2, 3, 4, 11, 14, 22, 24, 25, 37, 41, 42, 49, 53, 55, 62, 63};
    }

    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        // RRN情况下可能是空，因此判断如果不为空才发，否则不发，防止崩溃
        if(transData.getPan() != null){
            super.setBitData2(transData);
        }
    }

    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        String accountType = transData.getAccountType();
        if (!TextUtils.isEmpty(accountType)) {
            setBitData("3", "00" + accountType + "00");
            return;
        }
        super.setBitData3(transData);
    }

    @Override
    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        // RRN情况下可能是空，因此判断如果不为空才发，否则不发，防止崩溃
        if (transData.getEnterMode() != null && transData.getTrack2() != null) {
            super.setBitData35(transData);
        }
    }

    @Override
    protected void setBitData49(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("49", Component.getPaddedNumber(Integer.parseInt(transData.getInternationalCurrency().getCode()), 3));
        }
    }

    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("62", Component.getPaddedNumber(transData.getOrigInvoiceNo(), 6));
    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            StringBuilder adviceData = new StringBuilder("TPDCC");
            adviceData.append("58");
            adviceData.append(Component.getPaddedNumber(Integer.parseInt(transData.getLocalCurrency().getCode()), 3));
            adviceData.append(Component.getPaddedString(transData.getAmount(), 12, '0'));
            adviceData.append(transData.getRate());
            adviceData.append(transData.getCommission());
            adviceData.append(transData.getMakeUp());
            setBitData("63", adviceData.toString());
        }
    }

}
