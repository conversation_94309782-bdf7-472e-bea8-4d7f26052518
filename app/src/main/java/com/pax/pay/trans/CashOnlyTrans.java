/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans;

import android.content.Context;
import android.text.TextUtils;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.entity.DUKPTResult;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransData.EnterMode;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ECvmResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.action.ActionClssPreProc;
import com.pax.pay.trans.action.ActionClssProcess;
import com.pax.pay.trans.action.ActionEmvProcess;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionEnterPin;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionSearchCard;
import com.pax.pay.trans.action.ActionSearchCard.CardInformation;
import com.pax.pay.trans.action.ActionSearchCard.SearchMode;
import com.pax.pay.trans.action.ActionSelectAccount;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class CashOnlyTrans extends BaseTrans {
    private byte searchCardMode = -1; // search card mode
    private String inputAmount;
    private boolean needFallBack = false;
    private byte currentMode;
    private PrintTask printTask;


    /**
     * @param context     :context
     * @param inputAmount :total inputAmount
     * @param mode        {@link SearchMode}, 如果等于-1，
     */
    public CashOnlyTrans(Context context, String inputAmount, byte mode, TransEndListener transListener) {
        super(context, ETransType.CASH_ONLY, transListener);
        setParam(inputAmount, mode);
    }


    private void setParam(String inputAmount, byte mode) {
        this.searchCardMode = mode;
        this.inputAmount = inputAmount;
        if (searchCardMode == -1) { // 待机银行卡消费入口
            searchCardMode = Component.getCardReadMode(ETransType.CASH_ONLY);
            this.transType = ETransType.CASH_ONLY;
        }
    }

    @Override
    public void bindStateOnAction() {
        if (!TextUtils.isEmpty(inputAmount)) {
            transData.setAmount(inputAmount.replace(".", ""));
        }

        transData.setProcCode(ETransType.CASH_ONLY.getProcCode());

        ActionEnterAmount cashAmountAction = new ActionEnterAmount(action -> ((ActionEnterAmount) action).setParam(getCurrentContext(),
                getString(R.string.prompt_cash_amount), false, ETransType.CASH_ONLY));
        bind(State.ENTER_CASH_AMOUNT.toString(), cashAmountAction, true);
        // search card action
        ActionSearchCard searchCardAction = new ActionSearchCard(action -> ((ActionSearchCard) action).setParam(getCurrentContext(), getString(R.string.trans_cash_only), searchCardMode, transData.getAmount(),
                null, "", needFallBack));

        bind(State.CHECK_CARD.toString(), searchCardAction, true);

        // enter pin action
        ActionEnterPin enterPinAction = new ActionEnterPin(action -> ((ActionEnterPin) action).setParam(getCurrentContext(), getString(R.string.trans_cash_only),
                transData.getPan(), Constants.ISSUER_UNIONPAY.equalsIgnoreCase(transData.getIssuer().getName()), getString(R.string.prompt_pin),
                getString(R.string.prompt_no_pin), transData.getAmount(), transData.getTipAmount(),
                ActionEnterPin.EEnterPinType.ONLINE_PIN));
        bind(State.ENTER_PIN.toString(), enterPinAction, true);

        // emv process action
        ActionEmvProcess emvProcessAction = new ActionEmvProcess(action -> ((ActionEmvProcess) action).setParam(getCurrentContext(), emv, transData));
        bind(State.EMV_PROC.toString(), emvProcessAction);

        //clss process action
        ActionClssProcess clssProcessAction = new ActionClssProcess(action -> ((ActionClssProcess) action).setParam(getCurrentContext(), clss, transData));
        bind(State.CLSS_PROC.toString(), clssProcessAction);

        //clss preprocess action
        ActionClssPreProc clssPreProcAction = new ActionClssPreProc(action -> ((ActionClssPreProc) action).setParam(clss, transData));
        bind(State.CLSS_PREPROC.toString(), clssPreProcAction);

        //select account
        ActionSelectAccount selectAccount = new ActionSelectAccount(action ->
                ((ActionSelectAccount) action).setParam(context, getString(R.string.trans_cash_only)));
        bind(SaleTrans.State.SELECT_ACCOUNT.toString(), selectAccount);

        //Enter digits Number action
        ActionInputTransData inputFourDigits = new ActionInputTransData(action -> {
            String pan = transData.getPan();
            Context context = getCurrentContext();
            ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_cash_only))
                    .setInputLine(getString(R.string.prompt_four_digits), ActionInputTransData.EInputType.NUM, 4, 4,
                            pan.substring(pan.length() - 4), getString(R.string.trans_invalid_digits));
        });
        bind(SaleTrans.State.INPUT_DIGITS.toString(), inputFourDigits, true);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(action -> ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0));

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);

        // signature action
        ActionSignature signatureAction = new ActionSignature(action ->
                ((ActionSignature) action).setParam(getCurrentContext(), transData.isDcc() ? transData.getInternationalCurrencyAmount() : transData.getAmount(),
                        transData.isDcc() ? transData.getInternationalCurrency().getCode() : transData.getLocalCurrency().getCode()));
        bind(State.SIGNATURE.toString(), signatureAction);

        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(action -> ((ActionOfflineSend) action).setParam(getCurrentContext()));
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);


        //e charge slip
        EChargeSlipTask chargeSlipTask = new EChargeSlipTask(getCurrentContext(), getString(R.string.trans_cash_only), transData, EChargeSlipTask.genTransEndListener(CashOnlyTrans.this, State.E_CHARGE_SLIP.toString()));
        bind(State.E_CHARGE_SLIP.toString(), chargeSlipTask);

        //print preview action
        printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(CashOnlyTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        // perform the first action

        gotoState(State.ENTER_CASH_AMOUNT.toString());


    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        int ret = result.getRet();
        State state = State.valueOf(currentState);
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType, true);
            if (f55Dup.length > 0) {
                TransData dupTransData = GreendaoHelper.getTransDataHelper().findFirstDupRecord();
                if (dupTransData != null) {
                    dupTransData.setDupIccData(ConvertHelper.getConvert().bcdToStr(f55Dup));
                    GreendaoHelper.getTransDataHelper().update(dupTransData);
                }
            }
            if (ret == TransResult.NEED_FALL_BACK) {
                needFallBack = true;
                searchCardMode &= 0x01;
                gotoState(State.CHECK_CARD.toString());
                return;
            } else if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }


        switch (state) {
            case CHECK_CARD: // subsequent processing of check card
                onCheckCard(result);
                break;
            case ENTER_CASH_AMOUNT:
                if (result.getRet() != TransResult.SUCC) {
                    transEnd(result);
                    return;
                }
                onCashResult(result);
                break;
            case SELECT_ACCOUNT:
                onSelectAccount(result);
                break;
            case INPUT_DIGITS:
                onInput4Digits(result);
                break;
            case ENTER_PIN: // subsequent processing of enter pin
                onEnterPin(result);
                break;
            case MAG_ONLINE: // subsequent processing of online
                // determine whether need electronic signature or print
                toSignOrPrint();
                break;
            case EMV_PROC: // emv后续处理
                //get trans result
                CTransResult transResult = (CTransResult) result.getData();
                // EMV完整流程 脱机批准或联机批准都进入签名流程
                afterEMVProcess(transResult.getTransResult());
                break;
            case CLSS_PREPROC:
                gotoState(State.CHECK_CARD.toString());
                break;
            case CLSS_PROC:
                CTransResult clssResult = (CTransResult) result.getData();
                afterClssProcess(clssResult);
                break;
            case SIGNATURE:
                // save signature data
                byte[] signData = (byte[]) result.getData();
                byte[] signPath = (byte[]) result.getData1();

                if (signData != null && signData.length > 0 &&
                        signPath != null && signPath.length > 0) {
                    transData.setSignData(signData);
                    transData.setSignPath(signPath);
                    // update trans data，save signature
                    GreendaoHelper.getTransDataHelper().update(transData);
                }

                //check offline trans
                checkOfflineTrans();
                break;
            case OFFLINE_SEND:
                toChargeSlipOrPrint();
                break;
            case E_CHARGE_SLIP:
                String chooseResult = (String) result.getData();
                if (Constants.E_CHARGE_SLIP.equals(chooseResult)) {
                    transEnd(new ActionResult(TransResult.SUCC, null));
                } else if (Constants.NO_CHARGE_SLIP.equals(chooseResult)) {
                    printTask.setParam(true);
                    gotoState(State.PRINT.toString());
                } else {
                    gotoState(State.PRINT.toString());
                }
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }


    private void toChargeSlipOrPrint() {
        if (!Utils.isA50() || SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            gotoState(State.E_CHARGE_SLIP.toString());
        } else {
            gotoState(State.PRINT.toString());
        }
    }

    private void onCheckCard(ActionResult result) {
        CardInformation cardInfo = (CardInformation) result.getData();
        saveCardInfo(cardInfo, transData);

        if (needFallBack) {
            transData.setEnterMode(EnterMode.FALLBACK);
        }
        // enter card number manually
        currentMode = cardInfo.getSearchMode();
        if (SearchMode.isWave(currentMode)) {
            needRemoveCard = true;
            // AET-15
            gotoState(State.CLSS_PROC.toString());
        } else if (currentMode == SearchMode.SWIPE || currentMode == SearchMode.KEYIN) {
            if (!Component.isSupportCash(transData)) {
                Device.beepErr();
                transEnd(new ActionResult(TransResult.ERR_CARD_UNSUPPORTED, null));
                return;
            }
            gotoState(SaleTrans.State.SELECT_ACCOUNT.toString());
        } else if (currentMode == SearchMode.INSERT) {
            needRemoveCard = true;
            // EMV process
            gotoState(State.EMV_PROC.toString());
        }
    }

    private void onCashResult(ActionResult result) {
        //get total inputAmount
        String cashAmountStr = result.getData().toString();
        transData.setAmount(cashAmountStr);
        transData.setCashAmount(cashAmountStr);
        gotoState(State.CLSS_PREPROC.toString());

    }

    private void onEnterPin(ActionResult result) {
        DUKPTResult dukptResult = (DUKPTResult) result.getData();
        if (dukptResult.getResult() != null && dukptResult.getResult().length != 0) {
            transData.setPin(ConvertHelper.getConvert().bcdToStr(dukptResult.getResult()));
            transData.setHasPin(true);
        }
        if (dukptResult.getKsn() != null && dukptResult.getKsn().length != 0) {
            transData.setPinKsn(ConvertHelper.getConvert().bcdToStr(dukptResult.getKsn()));
        }
        gotoState(State.MAG_ONLINE.toString());
    }

    private void checkOfflineTrans() {
        //get offline trans data list
        List<TransData.OfflineStatus> filter = new ArrayList<>();
        filter.add(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        List<TransData> offlineTransList = GreendaoHelper.getTransDataHelper().findOfflineTransData(filter);
        //AET-150
        if ((transData.getTransType().equals(ETransType.CASH_ONLY) && transData.isOnlineTrans() &&
                !offlineTransList.isEmpty() &&
                !offlineTransList.get(0).getId().equals(transData.getId()))) { //AET-92
            if (transData.getEnterMode() == EnterMode.CLSS && transData.getEmvResult() != ETransResult.ONLINE_APPROVED) {
                toChargeSlipOrPrint();
                return;
            }
            //offline send
            gotoState(State.OFFLINE_SEND.toString());
        } else {
            // if terminal does not support signature ,card holder does not sign or time out，print preview directly.
            toChargeSlipOrPrint();
        }
    }

    // need electronic signature or send
    private void toSignOrPrint() {
        Component.incInvoiceNo();  //交易成功后小票号+1
        if (transData.isHasPin()) {// signature free
            // print preview
            transData.setSignFree(true);
            checkOfflineTrans();
        } else {
            transData.setSignFree(false);
            gotoState(State.SIGNATURE.toString());
        }
        //after change the sign stats should update the transdata
        GreendaoHelper.getTransDataHelper().update(transData);
    }

    private void afterEMVProcess(ETransResult transResult) {
        EmvTransProcess.emvTransResultProcess(transResult, emv, transData);
        if (transResult == ETransResult.ONLINE_APPROVED) {// 联机批准
            toSignOrPrint();
        } else if (transResult == ETransResult.OFFLINE_APPROVED) {//脱机批准处理
            transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
            transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            GreendaoHelper.getTransDataHelper().insert(transData);
            // increase trans no.
            Component.incTransNo();
            toSignOrPrint();
        } else if (transResult == ETransResult.SIMPLE_FLOW_END) { // simplify the process
            // trans not support simplified process
            // end trans
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        } else if (transResult == ETransResult.ONLINE_DENIED) { // refuse online
            // end trans
            transEnd(new ActionResult(TransResult.ERR_HOST_REJECT, null));
        } else if (transResult == ETransResult.ONLINE_CARD_DENIED) {// 平台批准卡片拒绝
            transEnd(new ActionResult(TransResult.ERR_CARD_DENIED, null));
        } else if (transResult == ETransResult.ABORT_TERMINATED ||
                transResult == ETransResult.OFFLINE_DENIED) { // emv interrupt
            Device.beepErr();
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        }
    }

    private void onSelectAccount(ActionResult result) {
        if (result.getRet() == TransResult.ERR_TIMEOUT) {
            transEnd(result);
            return;
        }
        String accountType = "00";//default type
        if (result.getData() != null) {
            accountType = result.getData().toString();
        }
        transData.setAccountType(accountType);
        gotoState(SaleTrans.State.INPUT_DIGITS.toString());
    }

    private void onInput4Digits(ActionResult result) {
        //get auth code
        String digits = (String) result.getData();
        String pan = transData.getPan();

        if (TextUtils.isEmpty(pan) || pan.length() < 4 || digits.length() != 4) {
            transEnd(new ActionResult(TransResult.ERR_FOUR_DIGITS, null));
            return;
        }

        //输入的卡号后四位和获取到的不相同
        if (Utils.parseLongSafe(pan.substring(pan.length() - 4), 1) != Utils.parseIntSafe(digits)) {
            transEnd(new ActionResult(TransResult.ERR_FOUR_DIGITS, null));
            return;
        }

        if (currentMode == SearchMode.SWIPE) {
            //floorlimit for swipe card
            if (transData.isPinFree() && transData.getIssuer().getFloorLimit() > Utils.parseLongSafe(transData.getAmount(), 0)) {
                // save trans data
                transData.setTransType(ETransType.OFFLINE_TRANS_SEND);
                transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
                transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
                GreendaoHelper.getTransDataHelper().insert(transData);
                //increase trans no.
                Component.incTransNo();
                toSignOrPrint();
            } else {
                if (!transData.isPinFree()) {
                    // enter pin
                    gotoState(SaleCashTrans.State.ENTER_PIN.toString());
                } else {
                    // online process
                    gotoState(SaleCashTrans.State.MAG_ONLINE.toString());
                }
            }
        } else if (currentMode == SearchMode.KEYIN) {
            if (transData.getIssuer().isRequirePIN()) {
                //enter pin
                gotoState(SaleCashTrans.State.ENTER_PIN.toString());
            } else {
                // online process
                gotoState(SaleCashTrans.State.MAG_ONLINE.toString());
            }
        }
    }

    private void afterClssProcess(CTransResult transResult) {

        if (transResult.getTransResult() == ETransResult.CLSS_OC_SEE_PHONE) {
            //
            searchCardMode = 0x04;
            gotoState(State.CLSS_PREPROC.toString());
            return;
        }

        if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_AGAIN) {
            DialogUtils.showErrMessage(getCurrentContext(), transType.getTransName(), getString(R.string.prompt_please_retry), null, Constants.FAILED_DIALOG_SHOW_TIME);
            //AET-175
            gotoState(State.CLSS_PREPROC.toString());
            return;
        }

        // 设置交易结果
        transData.setEmvResult(transResult.getTransResult());
        if (transResult.getTransResult() == ETransResult.ABORT_TERMINATED ||
                transResult.getTransResult() == ETransResult.CLSS_OC_DECLINED) { // emv interrupt
            Device.beepErr();
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            return;
        }

        if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_ANOTHER_INTERFACE) {
            //todo: use contact
            //此处应当结束整个交易，重新发起交易时，客户自然会使用接触式
            ToastUtils.showMessage("Please use contact");
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            return;
        }

        ClssTransProcess.clssTransResultProcess(transResult, clss, transData);

        if (transResult.getCvmResult() == ECvmResult.SIG) {
            //do signature after online
            transData.setSignFree(false);
            transData.setPinFree(true);
        } else if (transResult.getCvmResult() == ECvmResult.ONLINE_PIN_SIG) {
            transData.setSignFree(false);
            transData.setPinFree(false);
        } else if (transResult.getCvmResult() == ECvmResult.ONLINE_PIN) {
            transData.setSignFree(true);
            transData.setPinFree(false);
        } else {
            transData.setSignFree(true);
            transData.setPinFree(true);
        }
        GreendaoHelper.getTransDataHelper().update(transData);

        if (transResult.getTransResult() == ETransResult.CLSS_OC_APPROVED || transResult.getTransResult() == ETransResult.ONLINE_APPROVED) {
            transData.setOnlineTrans(transResult.getTransResult() == ETransResult.ONLINE_APPROVED);
            Component.incInvoiceNo();  //非接增加小票号
            if (!transData.isSignFree()) {
                gotoState(State.SIGNATURE.toString());
            } else {
                checkOfflineTrans();
            }
        } else {
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        }
        // ETransResult.CLSS_OC_ONLINE_REQUEST, online is handled in the module
    }


    enum State {
        ENTER_CASH_AMOUNT,
        CHECK_CARD,
        ENTER_PIN,
        MAG_ONLINE,
        EMV_PROC,
        CLSS_PREPROC,
        CLSS_PROC,
        SELECT_ACCOUNT,
        INPUT_DIGITS,
        SIGNATURE,
        OFFLINE_SEND,
        PRINT,
        E_CHARGE_SLIP
    }
}
