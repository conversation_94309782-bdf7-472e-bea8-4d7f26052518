/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Alex                    Create
 * ===========================================================================================
 */

package com.pax.pay.trans.btprinter.viewholders;

import android.bluetooth.BluetoothDevice;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.pax.commonlib.utils.LogUtils;
import com.pax.sbipay.R;


/**
 * DeviceViewHolder
 * Created by Alex on 2016/6/22.
 */
public class DeviceViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private OnHolderListener mListener;
    private BluetoothDevice mDevice;

    public DeviceViewHolder(ViewGroup parent, OnHolderListener listener) {
        super(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_printer_device, parent, false));
        mListener = listener;
        itemView.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        LogUtils.e("print", "DeviceViewHolder onClicked");
        if (mListener != null)
            mListener.onItemClicked(mDevice);
        view.setSelected(true);
    }

    public void setData(BluetoothDevice device, boolean isSelected) {
        itemView.setSelected(isSelected);
        mDevice = device;
        if (itemView instanceof TextView && mDevice != null) {
            TextView tvName = (TextView) itemView;
            tvName.setText(mDevice.getName());
        }
    }

    public interface OnHolderListener {
        void onItemClicked(BluetoothDevice device);
    }
}
