/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-4-23
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.ShowQrCodeActivity;

public class ActionShowQr extends AAction {
    private Context context;
    private String title;
    private String amount;
    private String qrCode;
    private String purchaseId;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionShowQr(ActionStartListener listener) {
        super(listener);
    }

    /**
     * Sets param.
     *
     * @param context the context
     * @param title   the title
     * @param amount  the amount
     */
    public void setParam(Context context, String title, String amount, String qrCode, String purchaseId) {
        this.context = context;
        this.title = title;
        this.amount = amount;
        this.qrCode = qrCode;
        this.purchaseId = purchaseId;
    }

    @Override
    protected void process() {
        Intent intent = new Intent(context, ShowQrCodeActivity.class);
        intent.putExtra(EUIParamKeys.NAV_TITLE.toString(), title);
        intent.putExtra(EUIParamKeys.TRANS_AMOUNT.toString(), amount);
        intent.putExtra(EUIParamKeys.QR_CODE.toString(), qrCode);
        intent.putExtra(EUIParamKeys.QR_PURCHASE_ID.toString(), purchaseId);
        context.startActivity(intent);
    }
}
