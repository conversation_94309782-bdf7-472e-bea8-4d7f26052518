/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2024-4-25
 * Module Author: wangyq
 * Description:
 *
 * ============================================================================
 */

package com.pax.pay.trans;

import android.content.Context;
import android.text.TextUtils;

import com.pax.abl.core.ActionResult;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.model.ETransType;
import com.pax.sbipay.R;

import java.lang.ref.WeakReference;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
public class HelpTrans extends BaseTrans {
    String helpCode = "";

    public HelpTrans(Context context, String helpCode, TransEndListener transListener) {
        super(context, ETransType.CALL_HELP, transListener);
        this.helpCode = helpCode;
    }

    @Override
    protected void bindStateOnAction() {
        transData.setHelpCode(helpCode);

        //Enter Phone Number action
        ActionInputTransData enterPhoneNo = new ActionInputTransData(action -> {
            Context context = getCurrentContext();
            ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_call_help))
                    .setInputLine(getString(R.string.prompt_input_mobile_number), ActionInputTransData.EInputType.NUM, 10, 0, false);
        });
        bind(State.ENTER_PHONE_NO.toString(), enterPhoneNo);

        //Enter Paper Roll Num action
        ActionInputTransData enterPaperRollNo = new ActionInputTransData(action -> {
            Context context = getCurrentContext();
            ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_call_help))
                    .setInputLine(getString(R.string.prompt_input_paper_roll_number), ActionInputTransData.EInputType.NUM, 10, 0, false);
        });
        bind(State.ENTER_PAPER_ROLL_NUM.toString(), enterPaperRollNo);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(action -> ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0));

        bind(String.valueOf(State.ONLINE), transOnlineAction, true);

        gotoState(State.ENTER_PHONE_NO.toString());
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        if (result.getRet() != TransResult.SUCC) {
            transEnd(result);
            return;
        }
        State state = State.valueOf(currentState);
        switch (state) {
            case ENTER_PHONE_NO:
                if (result.getData() != null && !TextUtils.isEmpty(result.getData().toString())) {
                    //电话号码 和纸卷数量放在63域上送
                    transData.setField63(result.getData().toString());
                    if ("7010".equalsIgnoreCase(helpCode)) {
                        gotoState(State.ENTER_PAPER_ROLL_NUM.toString());
                    } else {
                        gotoState(State.ONLINE.toString());
                    }
                } else {
                    gotoState(State.ENTER_PHONE_NO.toString());
                }
                break;
            case ENTER_PAPER_ROLL_NUM:
                if (result.getData() != null && !TextUtils.isEmpty(result.getData().toString())) {
                    //电话号码 和纸卷数量放在63域上送
                    transData.setField63(transData.getField63() + "|" + result.getData().toString());
                    gotoState(State.ONLINE.toString());
                } else {
                    gotoState(State.ENTER_PAPER_ROLL_NUM.toString());
                }
                break;
            case ONLINE:
                transEnd(result);
                break;
        }
    }

    enum State {
        ENTER_PHONE_NO,
        ENTER_PAPER_ROLL_NUM,
        ONLINE
    }
}
