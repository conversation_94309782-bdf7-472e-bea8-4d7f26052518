package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.model.TransData;

public class PackCupBatchUp extends PackIso8583 {

    public PackCupBatchUp(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[] { 2, 3, 4, 11, 12, 13, 14, 22, 24, 25, 37, 38, 39, 41, 42 };
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("2", transData.getPan());
    }

    /**
     * setBitData3
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("3", transData.getProcessCode());
    }

    /**
     * setBitData11
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData11(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("11", String.valueOf(transData.getOrigTransNo()));
    }

    /**
     * setBitData14
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("14", transData.getExpDate());
    }

    /**
     * setBitData39
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData39(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("39", transData.getResponseCode());
    }

    /**
     * setBitData25
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData25(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("25", transData.getOrigTransType().getServiceCode());
    }

/*    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        String f60 = transData.getOrigTransType().getMsgType() + Component.getPaddedNumber(transData.getTraceNo(), 6);
        setBitData("60", f60 + String.format("%1$-12s", ""));
    }

    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("62", Component.getPaddedNumber(transData.getInvoiceNo(), 6));
    }*/
}
