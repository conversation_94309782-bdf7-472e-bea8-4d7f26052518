package com.pax.pay.trans.action;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;

import static com.pax.pay.utils.Utils.getString;

public class ActionStartSubApp extends AAction {

    private Context context;
    private String appPkg;
    private String appActivity;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionStartSubApp(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String appPkg, String appActivity) {
        this.context = context;
        this.appPkg = appPkg;
        this.appActivity = appActivity;
    }

    @Override
    protected void process() {
        try {
            if (appActivity != null) {
                ComponentName componetName = new ComponentName(appPkg, appActivity);
                Intent intent = new Intent();
                intent.setComponent(componetName);
                context.startActivity(intent);
            } else {
                //第三方应用SPAY,采用以下方式启动
                Intent launchIntent = context.getPackageManager()
                        .getLaunchIntentForPackage(getString(R.string.spay_package_name));
                context.startActivity(launchIntent);
            }

            setResult(new ActionResult(TransResult.SUCC, null));
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            Toast.makeText(context, getString(R.string.err_no_match_app), Toast.LENGTH_SHORT).show();
        }
    }
}
