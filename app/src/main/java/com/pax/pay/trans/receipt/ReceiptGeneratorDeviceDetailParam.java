package com.pax.pay.trans.receipt;

import android.content.Context;
import android.view.Gravity;
import android.view.View;

import com.pax.commonlib.utils.FontCache;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.DeviceDetails;
import com.pax.device.Device;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.impl.GL;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.utils.DeviceInfoInitUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

public class ReceiptGeneratorDeviceDetailParam extends ReceiptGeneratorParam implements IReceiptGenerator {

    private static final String TAG = "ReceiptGeneratorDeviceDetailParam";

    public interface OnDeviceInfoLoadedListener {
        void onDeviceInfoLoaded(View view);
    }

    @Override
    protected List<IPage> generatePages(Context context) {
        return generateDeviceInfoPage();
    }

    protected List<IPage> generateDeviceInfoPage() {
        List<IPage> pages = new ArrayList<>();
        IPage page = getDeviceInfoPage();
        pages.add(page);
        return pages;
    }

    // 获取用于预览的view
    public void getDeviceDetailView(OnDeviceInfoLoadedListener onDeviceInfoLoadedListener) {
        FinancialApplication.getApp().runInBackground(() -> {
            IImgProcessing imgProcessing = GL.getGL().getImgProcessing();
            onDeviceInfoLoadedListener.onDeviceInfoLoaded(imgProcessing.pageToView(getDeviceInfoPage(), 384));
        });
    }

    private IPage getDeviceInfoPage() {
        DeviceDetails deviceInfo = null;
        Future<DeviceDetails> future = DeviceInfoInitUtils.initDeviceInfo(FinancialApplication.getApp());
        try {
            deviceInfo = future.get();
        } catch (ExecutionException e) {
            LogUtils.e(TAG, "", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态。
            LogUtils.d(TAG, "Thread interrupted");
        }
        IPage page = Device.generatePage();
        page.setTypeFace(FontCache.get(Constants.PAID_FONT_NAME, FinancialApplication.getApp()));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("Device Details")
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.CENTER));

        addLineToPage(page, "TID", deviceInfo.getTid());
        addLineToPage(page, "MID", deviceInfo.getMid());
        addLineToPage(page, "Mer Name", deviceInfo.getMerchantName());
        addLineToPage(page, "Terminal IP", deviceInfo.getTerminalIp());
        addLineToPage(page, "Host IP", deviceInfo.getHostIp());
        addLineToPage(page, "Host Port", String.valueOf(deviceInfo.getHostPort()));
        addLineToPage(page, "Host NII", deviceInfo.getHostNii());
        addLineToPage(page, "Host APN", deviceInfo.getHostApn());
        addLineToPage(page, "SW Ver", deviceInfo.getSoftwareVersion());
        addLineToPage(page, "Build Num", deviceInfo.getBuildNum());
        addLineToPage(page, "Build Type", deviceInfo.getBuildType());
        addLineToPage(page, "Build Date", deviceInfo.getBuildDate());
        addLineToPage(page, "FW Ver", deviceInfo.getFirmwareVersion());
        addLineToPage(page, "ResPack Ver", deviceInfo.getResourcePackageVersion());
        addLineToPage(page, "NeptSerVer", deviceInfo.getNeptuneServiceVersion());
        addLineToPage(page, "HW Ver", deviceInfo.getHardwareVersion());
        addLineToPage(page, "SP Version", deviceInfo.getSpVersion());
        addLineToPage(page, "Model Name", deviceInfo.getModelName());
        addLineToPage(page, "Serial Number", deviceInfo.getSerialNumber());
        addLineToPage(page, "Selected Media", deviceInfo.getSelectedMedia());
        addLineToPage(page, "SIM Status", deviceInfo.getSimStatus());
        addLineToPage(page, "SIM#", deviceInfo.getSimNumber());
        addLineToPage(page, "Network Name", deviceInfo.getNetworkName());
        addLineToPage(page, "Signal Strength", deviceInfo.getSignalStrength() + " dBm");
        addLineToPage(page, "SSL Enable", deviceInfo.getSslEnable());
        addLineToPage(page, "Mac Address", deviceInfo.getMacAddress());
        addLineToPage(page, "MQTT IP", deviceInfo.getMqttIp());
        addLineToPage(page, "MQTT Port", deviceInfo.getMqttPort());
        addLineToPage(page, "MQTT QOS", deviceInfo.getMqttQos());
        addLineToPage(page, "MQTT ConTimeout", deviceInfo.getMqttConnectTimeout());
        addLineToPage(page, "Paper Usage", deviceInfo.getPaperUsage() + " km");
        addLineToPage(page, "Battery Healthy", deviceInfo.getBatteryStatus());
        addLineToPage(page, "Battery Level", deviceInfo.getBatteryLeve());

        page.addLine().addUnit(page.createUnit().setText("\n"));
        return page;
    }

    private void addLineToPage(IPage page, String label, String value) {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("     " + label + ":")
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.START))
                .addUnit(page.createUnit()
                        .setText(value + "     ")
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.START));
        page.adjustLineSpace(0);
    }
}
