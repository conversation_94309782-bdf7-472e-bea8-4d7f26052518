package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import java.nio.charset.StandardCharsets;

public class PackLoyaltyBatchUp extends PackIso8583 {
    public PackLoyaltyBatchUp(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[] { 2, 3, 4, 11, 12, 13, 14, 22, 24, 25, 37, 38, 39, 41, 42, 55, 60, 63 };
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("2", transData.getPan());
    }

    /**
     * setBitData3
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("3", transData.getProcessCode());
    }

    /**
     * setBitData4
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("4",
                String.format("%012d", transData.getNetAmount() + transData.getTipAmount()));
        if (ETransType.REFUND == transData.getOrigTransType()) {
            //Refund batchupload报文中F4用原Refund交易金额
            setBitData("4", String.format("%012d", transData.getAmount()));
        }
    }

    /**
     * setBitData11
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData11(@NonNull TransData transData) throws Iso8583Exception {
        transData.setTraceNo(Component.getTransNo());
        super.setBitData11(transData);
    }

    /**
     * setBitData14
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("14", transData.getExpDate());
    }

    /**
     * setBitData39
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData39(@NonNull TransData transData) throws Iso8583Exception {
        if (ETransType.OFFLINE_TRANS_SEND == transData.getOrigTransType()) {
            transData.setResponseCode("00");
        }
        setBitData("39", transData.getResponseCode());
    }

    /**
     * setBitData55
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    protected void setBitData55(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == TransData.EnterMode.SWIPE) {
            return;
        }
        if (ETransType.OFFLINE_TRANS_SEND == transData.getOrigTransType()) {
            return;
        }
        String temp = transData.getSendIccDataForBatchUp();
        if (temp != null && temp.length() > 0) {
            setBitData("55", FinancialApplication.getConvert().strToBcd(temp, IConvert
                    .EPaddingPosition.PADDING_LEFT));
        } else if (transData.getEnterMode() == TransData.EnterMode.SWIPE ||
                transData.getEnterMode() == TransData.EnterMode.FALLBACK) {
            byte[] data =
                    new byte[] { (byte) 0xDF, 0x5A, 0x01, 0x01, (byte) 0xDF, 0x39, 0x01, 0x01 };
            setBitData("55", data);
        }
    }

    /**
     * setBitData25
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData25(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("25", transData.getOrigTransType().getServiceCode());
    }

    /**
     * setBitData60
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        String f60;
        String origMessageType = transData.getOrigTransType().getMsgType();
        if (origMessageType.equals("0265")) {
            origMessageType = "0200";
        }
        f60 = origMessageType + Component.getPaddedNumber(transData.getOrigTransNo(), 6);
        setBitData("60", f60 + String.format("%1$-12s", ""));
    }

    /**
     * setBitData63
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        String acquirerName = transData.getAcquirer().getName();
        if (!acquirerName.equalsIgnoreCase("HASE_OLS")) {
            return;
        }
        if (transData.getOrigTransType() == ETransType.REFUND) {
            return;
        }
        String str = "";
        if (transData.getLysStatus() != TransData.LYSStatus.LYS_OK) {
            //LYS Status is not 1
            if(transData.getPromotionCode() != null){
                str += transData.getPromotionCode();
            }
            str += "L";
            str += transData.getLysStatus().toString();
        } else {
            str += "L";
            str += transData.getLysStatus().toString();
            str += new String(Utils.str2Bcd(String.format("%012d", transData.getAmount())), StandardCharsets.ISO_8859_1);
            str += new String(Utils.str2Bcd(String.format("%012d", transData.getNetAmount())), StandardCharsets.ISO_8859_1);
            str += new String(Utils.str2Bcd(String.format("%012d", transData.getCashDolBonusRedeemed())), StandardCharsets.ISO_8859_1);

            final String threeSpace = new String(new byte[]{0x20, 0x20, 0x20});
            String defProgDetails = threeSpace + new String(new byte[]{0x00, 0x00, 0x00, 0x00, 0x00, 0x00});
            String cashDolProgramId = transData.getCashDolProgramId();
            String mer1ProgramId = transData.getMer1ProgramId();
            String mer2ProgramId = transData.getMer2ProgramId();

            if (!TextUtils.isEmpty(cashDolProgramId)) {
                str += cashDolProgramId + new String(Utils.str2Bcd(String.format("%012d", transData
                        .getCashDolRedeemed())), StandardCharsets.ISO_8859_1);
            } else {
                str += defProgDetails;
            }

            if (!TextUtils.isEmpty(mer1ProgramId)) {
                str += mer1ProgramId + new String(Utils.str2Bcd(String.format("%012d", transData
                        .getMer1CashDolRedeemed())), StandardCharsets.ISO_8859_1);
            } else {
                str += defProgDetails;
            }

            if (!TextUtils.isEmpty(mer2ProgramId)) {
                str += mer2ProgramId + new String(Utils.str2Bcd(String.format("%012d", transData.getMer2CashDolRedeemed())), StandardCharsets.ISO_8859_1);
            } else {
                str += defProgDetails;
            }

            String issuerName = transData.getIssuer().getName();
            if ("HASE_VISA".equalsIgnoreCase(issuerName) || "HASE_ENJ".equalsIgnoreCase(issuerName)) {
                str += new String(new byte[]{0x00, 0x03, 0x32, 0x34, 0x36});
            } else if ("HASE_MC".equalsIgnoreCase(issuerName)) {
                str += new String(new byte[]{0x00, 0x03, 0x32, 0x34, 0x33});
            } else if ("HASE_CUP".equalsIgnoreCase(issuerName)) {
                str += new String(new byte[]{0x00, 0x03, 0x32, 0x34, 0x39});
            }
        }
        setBitData("63", str.getBytes(StandardCharsets.ISO_8859_1));
    }
}
