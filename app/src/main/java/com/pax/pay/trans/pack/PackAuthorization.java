package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;

/**
 * Created by xub on 2017/12/13.
 */

public class PackAuthorization extends PackIsoBase {
    public PackAuthorization(PackListener listener) {
        super(listener);
    }

    protected int[] getRequiredFields() {
        /*Raymond 20220708: Fix DCC AUTHORIZATION, miss F49 which is mandatory, the set F49 will add the field only if it is the DCC Transaction
        return new int[] { 1, 2, 3, 4, 11, 14, 22, 24, 25, 35, 41, 42, 55, 61, 62, 63 };*/
        return new int[] { 1, 2, 3, 4, 11, 14, 22, 24, 25, 26, 35, 41, 42, 49, 55, 61, 62, 63 };
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == TransData.EnterMode.MANUAL) {
            setBitData("2", transData.getPan());
        }
    }

    /**
     * setBitData14
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == TransData.EnterMode.MANUAL) {
            setBitData("14", transData.getExpDate());
        }
    }

    @Override
    protected void setBitData26(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getIssuer() != null && transData.getPin() != null &&
                ("MASTER".equals(transData.getIssuer().getName()) || "HASE_MC".equals(transData.getIssuer().getName()))) {
            setBitData("26", "12");
        }
    }

    /**
     * setBitData35
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("35", transData.getTrack2());
    }

    /**
     * setBitData61
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData61(@NonNull TransData transData) throws Iso8583Exception {
        if (!transData.getAcquirer().isEnableRefNo() || TextUtils.isEmpty(
                transData.getAcqReferenceNo())) {
            return;
        }
        if (transData.getAcquirer().getName().equals("HASE_OLS") || transData.getAcquirer()
                .getName()
                .equals("HASE") || transData.getAcquirer().getName().equals("HASE_EMV")) {
            setBitData("61", Component.getPaddedString(transData.getAcqReferenceNo(), 21, ' '));
        }
    }
}
