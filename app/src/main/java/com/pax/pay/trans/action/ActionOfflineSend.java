/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-1-12
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.os.AsyncTask;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.transmit.TransOnline;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;

public class ActionOfflineSend extends AAction {
    private TransProcessListener transProcessListenerImpl;
    private Context context;
    private TransData mTransData;

    /**
     *
     * @param listener Action start listener
     */
    public ActionOfflineSend(ActionStartListener listener) {
        super(listener);
    }

    /**
     *
     * @param context 上下文
     * @param transData 交易数据
     */
    public void setParam(Context context, TransData transData) {
        this.context = context;
        mTransData = transData;
    }

    @Override
    protected void process() {
        new OfflineUploadTask().execute();
    }

    private class OfflineUploadTask extends AsyncTask<Void, Void, Integer> {

        @Override
        protected void onPreExecute() {
            transProcessListenerImpl = new TransProcessListenerImpl(context);
        }

        @Override
        protected Integer doInBackground(Void... voids) {
            return new TransOnline().offlineUpload(mTransData.getAcquirer(), false, transProcessListenerImpl);
        }

        @Override
        protected void onPostExecute(Integer result) {
            transProcessListenerImpl.onHideProgress();
            setResult(new ActionResult(result, null));
        }
    }
}
