/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.exceptions.PrinterDevException;
import com.pax.data.entity.TransData;
import com.pax.pay.mqtt.CurrentBatchSummary;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.ArrayList;

/**
 * print receipt
 *
 * <AUTHOR>
 */
public class ReceiptPrintTrans extends AReceiptPrint {

    public <T> int print(T transData, boolean isRePrint, PrintListener listener, String title) {
        if (!SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_PRINT))
            return 0;

        this.listener = listener;
        int ret = 0;
        int receiptNum = getVoucherNum();
        if (listener != null)
            listener.onShowMessage(null, Utils.getString(R.string.wait_print));

        for (int i = 0; i < receiptNum; i++) {
            if (receiptNum == 2 || receiptNum == 3) {
                if (listener != null) {
                    listener.onEnd();
                    //the first one is customer receipt,the second one is merchant receipt
                    String note = "";
                    if (receiptNum == 3) {
                        if (i == 0) {
                            note = Utils.getString(R.string.receipt_print_acquire);
                        } else if (i == 1) {
                            note = Utils.getString(R.string.receipt_print_merchant_prompt);
                        } else {
                            note = Utils.getString(R.string.receipt_print_cardholder_prompt);
                        }
                    } else {
                        if (i == 0) {
                            note = Utils.getString(R.string.receipt_print_merchant_prompt);
                        } else {
                            note = Utils.getString(R.string.receipt_print_cardholder_prompt);
                        }
                    }
                    if (i != 0) {
                        PrintListener.Status result = listener.onConfirm(null, note);
                        if (result != PrintListener.Status.CONTINUE) {
                            listener.onEnd();
                            return ret;
                        }
                    }
                }
            }
            IReceiptGenerator receiptGenerator = null;
            if (transData instanceof TransData) {
                TransData transDataCast = (TransData) transData;
                if (ETransType.BANK_EMI_SALE == transDataCast.getTransType()
                        || ETransType.BRAND_EMI_SALE == transDataCast.getTransType()) {
                    receiptGenerator = new ReceiptGeneratorEMITrans(transDataCast, i, receiptNum, isRePrint);
                } else if (ETransType.INSTA_EMI_SALE == transDataCast.getTransType()) {
                    receiptGenerator = new ReceiptGeneratorInstaTrans(transDataCast, i, receiptNum, isRePrint);
                } else {
                    receiptGenerator = new ReceiptGeneratorTrans(transDataCast, i, receiptNum, isRePrint);
                }
            } else if (transData instanceof SQRData) {
                receiptGenerator = new ReceiptGeneratorSQR((SQRData) transData, i, receiptNum, isRePrint);
            } else if (transData instanceof ArrayList) {
                receiptGenerator = new ReceiptGeneratorSQRBatch((ArrayList<CurrentBatchSummary>) transData, i, receiptNum, title);
            }

            try {
                ret = printBitmap(receiptGenerator.generateBitmap());

                // 如果是TTSPrintListenerImpl，通知当前小票打印完成
                if (listener instanceof TTSPrintListenerImpl) {
                    ((TTSPrintListenerImpl) listener).onReceiptPrinted();
                }

            } catch (PrinterDevException e) {
                LogUtils.e(TAG, "", e);
                if (listener != null) {
                    listener.onConfirm(null, String.format("%s %s", e.getErrModule(), e.getErrMsg()));

                    // 如果是TTSPrintListenerImpl，强制完成打印流程
                    if (listener instanceof TTSPrintListenerImpl) {
                        ((TTSPrintListenerImpl) listener).forceComplete();
                    } else {
                        listener.onEnd();
                    }
                }
                return -1;
            } finally {
                receiptGenerator.clear();
            }
            if (ret == -1) {
                break;
            }


        }
        if (listener != null) {
            listener.onEnd();
        }

        return ret;
    }

    private int getVoucherNum() {
        int receiptNum = SysParam.getInstance().getInt(R.string.EDC_RECEIPT_NUM);
        if (receiptNum < 1 || receiptNum > 3) // receipt copy number is 1-3
        {
            receiptNum = 2;
        }
        return receiptNum;
    }

}
