package com.pax.pay.trans.model;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/1/10.
 */
public class RSAKeyData {
    //RSA PUK RID
    private String rsaPukRID;

    //RSA PUK ID
    private int rsaPukID;

    //RSA PUK Modul
    private String rsaPukModul;

    //Modul length
    private int rsaPukModulLen;

    //RSA PUK Exponent
    private String rsaPukExponent;

    //Random key
    private String randomKey;

    /**
     * Gets rsa puk rid.
     *
     * @return the rsa puk rid
     */
    public String getRsaPukRID() {
        return rsaPukRID;
    }

    /**
     * Sets rsa puk rid.
     *
     * @param rsaPukRID the rsa puk rid
     */
    public void setRsaPukRID(String rsaPukRID) {
        this.rsaPukRID = rsaPukRID;
    }

    /**
     * Gets rsa puk id.
     *
     * @return the rsa puk id
     */
    public int getRsaPukID() {
        return rsaPukID;
    }

    /**
     * Sets rsa puk id.
     *
     * @param rsaPukID the rsa puk id
     */
    public void setRsaPukID(int rsaPukID) {
        this.rsaPukID = rsaPukID;
    }

    /**
     * Gets rsa puk modul.
     *
     * @return the rsa puk modul
     */
    public String getRsaPukModul() {
        return rsaPukModul;
    }

    /**
     * Sets rsa puk modul.
     *
     * @param rsaPukModul the rsa puk modul
     */
    public void setRsaPukModul(String rsaPukModul) {
        this.rsaPukModul = rsaPukModul;
    }

    /**
     * Gets rsa puk modul len.
     *
     * @return the rsa puk modul len
     */
    public int getRsaPukModulLen() {
        return rsaPukModulLen;
    }

    /**
     * Sets rsa puk modul len.
     *
     * @param rsaPukModulLen the rsa puk modul len
     */
    public void setRsaPukModulLen(int rsaPukModulLen) {
        this.rsaPukModulLen = rsaPukModulLen;
    }

    /**
     * Gets rsa puk exponent.
     *
     * @return the rsa puk exponent
     */
    public String getRsaPukExponent() {
        return rsaPukExponent;
    }

    /**
     * Sets rsa puk exponent.
     *
     * @param rsaPukExponent the rsa puk exponent
     */
    public void setRsaPukExponent(String rsaPukExponent) {
        this.rsaPukExponent = rsaPukExponent;
    }

    /**
     * Gets random key.
     *
     * @return the random key
     */
    public String getRandomKey() {
        return randomKey;
    }

    /**
     * Sets random key.
     *
     * @param randomKey the random key
     */
    public void setRandomKey(String randomKey) {
        this.randomKey = randomKey;
    }
}
