package com.pax.pay.trans.receipt;

import com.pax.glwrapper.page.IPage;
import com.pax.pay.trans.model.TransData;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

public class PureRedeemReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     */
    public PureRedeemReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isPrintPreview isPrintPreview
     */
    public PureRedeemReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addReferenceNO();
        addAppCode();
        addEMVInfo();
        addSystemTrace();
        addNewline(1);
        addEcrRef();
        addNewline(1);
        addAmount();
        addNewline(1);
        addEnd();
    }

    /**
     * add trans type
     */
    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("REDEEM 換領")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

    /**
     * add amount
     */
    @Override
    public void addAmount() {
        addBaseAmount();
        addMer1CashDollar();
        addMer2CashDollar();
        addHaseCashDollar();
        addBonus();
        addDoubleLine();
        if (!TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
            addNewline(1);
            addRedeemUnavailableMsg(transData.getLysStatus());
            addNewline(1);
            return;
        }
        addNewline(1);
        addAvailableRedeemBalance();
        addNewline(1);
        addExpiredRedeemBalance();
    }
}
