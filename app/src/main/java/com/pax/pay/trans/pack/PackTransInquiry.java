/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;

public class PackTransInquiry extends PackIso8583 {

    public PackTransInquiry(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{ 3, 11, 12, 13, 14, 22, 24, 25, 37, 41, 42, 53, 62, 63};
    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        StringBuilder adviceData = new StringBuilder("TTDCC");
        setBitData("63", adviceData.toString());
    }
}
