/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import com.pax.dal.exceptions.PrinterDevException;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransTotal;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

/**
 * print total
 *
 * <AUTHOR>
 */
public class ReceiptPrintTotal extends AReceiptPrint {

    public int print(String title, TransTotal transTotal, PrintListener listener, boolean isHistoryPrint) {
        this.listener = listener;

        if (listener != null) {
            listener.onShowMessage(null, Utils.getString(R.string.wait_print));
        }
        ReceiptGeneratorTotal receiptGeneratorTotal = new ReceiptGeneratorTotal(title, null, transTotal, isHistoryPrint);
        try {
            printBitmap(receiptGeneratorTotal.generateBitmap());
        } catch (PrinterDevException e) {
            if (listener != null) {
                listener.onConfirm(null, String.format("%s %s", e.getErrModule(), e.getErrMsg()));
            }
        }
        if (listener != null) {
            listener.onEnd();
        }
        return 0;
    }

    public View getTransTotalView(Context context, Acquirer acquirer) {
        // 创建一个 ScrollView 作为容器，使得内容可以滚动
        ScrollView scrollView = new ScrollView(context);
        scrollView.setScrollbarFadingEnabled(false); // 禁用滚动条消失的效果
        scrollView.setVerticalScrollBarEnabled(false); // 隐藏垂直滚动条

        // 创建一个 LinearLayout 作为子容器
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL); // 设置垂直排列

        TransTotal total;
        ReceiptGeneratorTotal receiptGeneratorTotal;
        if (acquirer == null) {
            total = GreendaoHelper.getTransTotalHelper().findLastTransTotal(null, true);
            receiptGeneratorTotal = new ReceiptGeneratorTotal(context.getString(R.string.print_last_total), null, total, false);
        } else {
            total = GreendaoHelper.getTransTotalHelper().calcTotal(acquirer);
            receiptGeneratorTotal = new ReceiptGeneratorTotal(context.getString(R.string.print_history_total), null, total, true);
        }

        container.addView(Utils.createImageView(context, receiptGeneratorTotal.generateBitmap()));

        // 将 LinearLayout 添加到 ScrollView 中
        scrollView.addView(container);
        // 返回包含所有 Bitmap 的可滚动视图
        return scrollView;
    }

}
