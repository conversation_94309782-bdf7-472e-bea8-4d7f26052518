package com.pax.pay.trans;

import android.content.Context;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.entity.DUKPTResult;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.action.ActionDispTransDetail;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionEnterPin;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.lang.ref.WeakReference;
import java.util.LinkedHashMap;

/**
 * ===========================================================================================
 * = COPYRIGHT
 * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or nondisclosure
 * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 * disclosed except in accordance with the terms in that agreement.
 * Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 * // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                         Author	                Action
 * 2020/8/31 9:35  	         Kael           	Create/Add/Modify/Delete
 * ===========================================================================================
 */

public class YONOTrans extends BaseTrans {
    private PrintTask printTask;


    public YONOTrans(Context context, ETransType transType, TransEndListener transListener) {
        super(context, transType, transListener);
    }

    @Override
    protected void bindStateOnAction() {
        transData.setEnterMode(TransData.EnterMode.YONO);
        // enter trans
        String title = transType.getTransName();
        ActionEnterAmount amountAction = new ActionEnterAmount(action -> ((ActionEnterAmount) action).setParam(getCurrentContext(),
                title, false, transType));
        bind(State.ENTER_AMOUNT.toString(), amountAction, true);

        ActionInputTransData enterTransNoAction = new ActionInputTransData(action -> ((ActionInputTransData) action).setParam(new WeakReference<>(getCurrentContext()), title)
                .setInputLine(getString(R.string.prompt_input_yono_no), ActionInputTransData.EInputType.NUM, 6, 6));
        bind(State.ENTER_TRANSNO.toString(), enterTransNoAction, true);

        ActionEnterPin enterPinAction = new ActionEnterPin(action -> ((ActionEnterPin) action).setParam(getCurrentContext(), getString(R.string.trans_yono),
                transData.getPan(), false, getString(R.string.prompt_pin),
                getString(R.string.prompt_no_pin), transData.getAmount(), transData.getTipAmount(),
                ActionEnterPin.EEnterPinType.ONLINE_PIN));

        bind(State.ENTER_PIN.toString(), enterPinAction, true);

        // confirm information
        ActionDispTransDetail confirmInfoAction = new ActionDispTransDetail(action -> {

            String transType = transData.getTransType().getTransName();
            String amount = CurrencyConverter.convert(Utils.parseLongSafe(transData.getAmount(), 0), transData.getCurrency());
            // date and time
            //AET-95
            String formattedDate = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                    Constants.TIME_PATTERN_CUSTOM);

            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put(getString(R.string.history_detail_type), transType);
            map.put(getString(R.string.history_detail_amount), amount);
            map.put(getString(R.string.history_detail_invoice_no), Component.getPaddedNumber(transData.getInvoiceNo(), 6));
            map.put(getString(R.string.detail_yono_no), Component.getPaddedString(transData.getYonoNum(), 6, '0'));
            map.put(getString(R.string.dateTime), formattedDate);
            ((ActionDispTransDetail) action).setParam(getCurrentContext(),
                    title, map);
        });
        bind(State.TRANS_DETAIL.toString(), confirmInfoAction, true);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(action -> ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0));

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);

        //e charge slip
        EChargeSlipTask chargeSlipTask = new EChargeSlipTask(getCurrentContext(), getString(R.string.trans_yono), transData, EChargeSlipTask.genTransEndListener(YONOTrans.this, State.E_CHARGE_SLIP.toString()));
        bind(State.E_CHARGE_SLIP.toString(), chargeSlipTask);


        //print preview action
        printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(YONOTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);
        gotoState(State.ENTER_AMOUNT.toString());
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        int ret = result.getRet();
        State state = State.valueOf(currentState);
        switch (state) {
            case ENTER_AMOUNT:
                // save trans amount
                transData.setAmount(result.getData().toString());
                gotoState(State.ENTER_TRANSNO.toString());
                break;
            case ENTER_TRANSNO:
                String yono = result.getData().toString();
                if (yono.length() < 6) {
                    transEnd(new ActionResult(TransResult.ERR_CARD_NO, null));
                }
                transData.setYonoNum(yono);
                transData.setPan("9001001010" + yono);
                Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(transData.getPan());
                transData.setIssuer(issuer);
                gotoState(State.ENTER_PIN.toString());
                break;
            case ENTER_PIN:
                DUKPTResult dukptResult = (DUKPTResult) result.getData();
                if (dukptResult.getResult() != null && dukptResult.getResult().length != 0) {
                    transData.setPin(ConvertHelper.getConvert().bcdToStr(dukptResult.getResult()));
                    transData.setHasPin(true);
                }
                if (dukptResult.getKsn() != null && dukptResult.getKsn().length != 0) {
                    transData.setPinKsn(ConvertHelper.getConvert().bcdToStr(dukptResult.getKsn()));
                }
                gotoState(State.TRANS_DETAIL.toString());
                break;
            case TRANS_DETAIL:
                if (ret != TransResult.SUCC) {
                    transEnd(result);
                    return;
                }
                gotoState(State.MAG_ONLINE.toString());
                break;
            case MAG_ONLINE:
                toChargeSlipOrPrint();
                break;
            case E_CHARGE_SLIP:
                String chooseResult = (String) result.getData();
                if (Constants.E_CHARGE_SLIP.equals(chooseResult)) {
                    transEnd(new ActionResult(TransResult.SUCC, null));
                } else if (Constants.NO_CHARGE_SLIP.equals(chooseResult)) {
                    printTask.setParam(true);
                    gotoState(State.PRINT.toString());
                } else {
                    gotoState(State.PRINT.toString());
                }
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }

    enum State {
        ENTER_AMOUNT,
        ENTER_TRANSNO,
        ENTER_PIN,
        MAG_ONLINE,
        TRANS_DETAIL,
        PRINT,
        E_CHARGE_SLIP
    }

    private void toChargeSlipOrPrint() {
        Component.incInvoiceNo();  //交易成功后小票号+1
        GreendaoHelper.getTransDataHelper().update(transData);
        if (!Utils.isA50() || SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            gotoState(State.E_CHARGE_SLIP.toString());
        } else {
            gotoState(State.PRINT.toString());
        }
    }
}
