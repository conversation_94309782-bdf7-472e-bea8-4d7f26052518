/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.transmit;

import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.os.ConditionVariable;
import android.os.SystemClock;
import android.util.Log;
import android.view.KeyEvent;

import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionEndListener;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.commonlib.utils.convert.IConvert;
import com.pax.dal.entity.DUKPTResult;
import com.pax.data.entity.TransData;
import com.pax.device.Device;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionEnterPin;
import com.pax.pay.trans.action.activity.EnterPinActivity;
import com.pax.pay.trans.action.activity.SearchCardActivity;
import com.pax.pay.trans.action.activity.SelectCurrencyActivity;
import com.pax.pay.trans.action.activity.TransPreviewActivity;
import com.pax.pay.trans.action.activity.emi.BankEMICheckCardActivity;
import com.pax.pay.trans.action.activity.emi.BankEMINotifyReverseActivity;
import com.pax.sbipay.R;
import com.pax.view.dialog.CustomAlertDialog;

import androidx.annotation.NonNull;

public class TransProcessListenerImpl implements TransProcessListener {

    private static final String TAG = "TransProcessListener";

    private Context context;
    private CustomAlertDialog dialog;

    private IConvert convert = ConvertHelper.getConvert();
    private ConditionVariable cv;
    private boolean isShowMessage;
    private String title;
    private int result;
    private CustomAlertDialog cfmDialog;

    public TransProcessListenerImpl(Context context) {
        this.context = context;
        this.isShowMessage = true;
    }

    public TransProcessListenerImpl(Context context, boolean isShowMessage) {
        this.context = context;
        this.isShowMessage = isShowMessage;
    }

    private void showDialog(final String message, final int timeout, final int alertType) {
        LogUtils.logTemp("7 showDialog : "+isShowMessage);
        if (!isShowMessage) {
            return;
        }
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                LogUtils.logTemp("8 dialog runOnUiThread: "+dialog);
                if (dialog == null) {
                    LogUtils.logTemp("9 dialog ActivityStack.getInstance().top(): "+ActivityStack.getInstance().top());
                    if (ActivityStack.getInstance().top() != null) {
                        dialog = new CustomAlertDialog(ActivityStack.getInstance().top(), alertType);
                        // 防止选择汇率界面不显示弹窗
                        if (ActivityStack.getInstance().top() != context && !(ActivityStack.getInstance().top() instanceof SelectCurrencyActivity)) {
                            LogUtils.logTemp("10 enter if");
                            dialog.dismiss();
                        } else {
                            LogUtils.logTemp( "11 enter else");
                            dialog.show();
                            dialog.setCancelable(false);
                            if (alertType == CustomAlertDialog.WARN_TYPE) {
                                dialog.setImage(R.drawable.ic16);
                            }
                            dialog.setTimeout(timeout);
                            dialog.setTitleText(title);
                            dialog.setContentText(message);
                        }
                    }
                } else {
                    LogUtils.logTemp("12 dialog.isShowing(): "+dialog.isShowing());
                    //progress style ,update message and title
                    if (!dialog.isShowing()) {
                        dialog.show();
                        dialog.setCancelable(false);
                    }
                    dialog.setTimeout(timeout);
                    dialog.setTitleText(title);
                    dialog.setContentText(message);
                }
                if (cv != null) {
                    cv.open();
                }

            }
        });
    }

    @Override
    public void onShowProgress(final String message, final int timeout) {
        showDialog(message, timeout, CustomAlertDialog.PROGRESS_TYPE);
    }

    @Override
    public void onShowProgress(String message, int timeout, ConditionVariable cv){
        showDialog(message, timeout, CustomAlertDialog.PROGRESS_TYPE);
        this.cv = cv;
    }

    @Override
    public void onShowWarning(String message, int timeout) {
        showDialog(message, timeout, CustomAlertDialog.WARN_TYPE);
    }

    private int onShowMessage(final String message, final int timeout, final int alertType, final boolean confirmable) {
        if (!isShowMessage) {
            return -1;
        }
        onHideProgress();
        cv = new ConditionVariable();
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                // 使用当前页面的activity上下文来创建弹窗
                cfmDialog = new CustomAlertDialog(ActivityStack.getInstance().top(), alertType, timeout);
                LogUtils.d(TAG, "run:onShowMessage " + context);
                LogUtils.d(TAG, "current context " + ActivityStack.getInstance().top());
                if (alertType == CustomAlertDialog.ERROR_TYPE && FinancialApplication.getCurrentETransType() != null) {
                    cfmDialog.setTitleText(FinancialApplication.getCurrentETransType().getTransName());
                }
                cfmDialog.setContentText(message);
                if (context == ActivityStack.getInstance().top() ||
                        (context != ActivityStack.getInstance().top() && ActivityStack.getInstance().top() instanceof SelectCurrencyActivity)
                        || (context != ActivityStack.getInstance().top() && ActivityStack.getInstance().top() instanceof BankEMICheckCardActivity)
                        || (context != ActivityStack.getInstance().top() && ActivityStack.getInstance().top() instanceof BankEMINotifyReverseActivity)
                        || (context != ActivityStack.getInstance().top() && ActivityStack.getInstance().top() instanceof TransPreviewActivity)
                        || (context != ActivityStack.getInstance().top() && ActivityStack.getInstance().top() instanceof EnterPinActivity)) {
                    cfmDialog.show();
                    cfmDialog.showConfirmButton(confirmable);
                    cfmDialog.setOnDismissListener(arg0 -> cv.open());
                } else {
                    cfmDialog.dismiss();
                }

            }
        });

        cv.block();
        return 0;
    }

    private void onShowRemoveMessage(final String message, final int timeout, final int alertType, final boolean confirmable) {
        LogUtils.i("onShowRemoveMessage", "message:" + message);
        if (!isShowMessage) {
            return;
        }
        onHideProgress();
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                if (cfmDialog == null) {
                    cfmDialog = new CustomAlertDialog(ActivityStack.getInstance().top(), alertType, timeout);
                }
                LogUtils.d(TAG, "run:onShowMessage " + context);
                LogUtils.d(TAG, "current context " + ActivityStack.getInstance().top());

                cfmDialog.setContentText(message);
                if (context == ActivityStack.getInstance().top()) {
                    cfmDialog.show();
                    cfmDialog.showConfirmButton(confirmable);
                    cfmDialog.setOnDismissListener(new OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface arg0) {
                        }
                    });
                } else {
                    cfmDialog.dismiss();
                }

            }
        });
    }

    @Override
    public int onShowNormalMessage(final String message, final int timeout, boolean confirmable) {
        return onShowMessage(message, timeout, CustomAlertDialog.NORMAL_TYPE, confirmable);
    }

    @Override
    public int onShowErrMessage(final String message, final int timeout, boolean confirmable) {
        Device.beepErr();
        return onShowMessage(message, timeout, CustomAlertDialog.ERROR_TYPE, confirmable);
    }

    @Override
    public int onShowErrMessage(final String title, final String message, final int timeout, final boolean confirmable) {
        onHideProgress();
        cv = new ConditionVariable();
        FinancialApplication.getApp().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                CustomAlertDialog customDialog = new CustomAlertDialog(ActivityStack.getInstance().top(), CustomAlertDialog.ERROR_TYPE, true, timeout);
                customDialog.setTitleText(title);
                customDialog.setContentText(message);
                customDialog.setCanceledOnTouchOutside(true);
                customDialog.show();
                customDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                    @Override
                    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                        return keyCode == KeyEvent.KEYCODE_BACK;
                    }
                });
                customDialog.setOnDismissListener(new OnDismissListener() {

                    @Override
                    public void onDismiss(DialogInterface arg0) {
                        cv.open();
                    }
                });
            }
        });

        cv.block();
        return 0;
    }

    @Override
    @NonNull
    public byte[] onCalcMac(byte[] data) {
        return Device.getMac(data);
    }

    @Override
    public void onHideProgress() {
        if (dialog != null) {
            SystemClock.sleep(200);
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public int onShowOKMessage(String message, int timeout, boolean confirmable) {
        Device.beepOk();
        return onShowMessage(message, timeout, CustomAlertDialog.SUCCESS_TYPE, confirmable);
    }

    @Override
    public void onHideMessage() {
        if (cfmDialog != null) {
            SystemClock.sleep(200);
            cfmDialog.dismiss();
            cfmDialog = null;
        }
    }

    @Override
    public void onUpdateProgressTitle(String title) {
        LogUtils.logTemp("isShowMessage : " + isShowMessage, true);
        if (!isShowMessage) {
            return;
        }
        this.title = title;
    }

    @Override
    public int onInputOnlinePin(final TransData transData) {
        cv = new ConditionVariable();
        result = 0;

        final String totalAmount = transData.getTransType().isSymbolNegative() ? "-" + transData.getAmount() : transData.getAmount();
        final String tipAmount = transData.getTransType().isSymbolNegative() ? null : transData.getTipAmount();

        ActionEnterPin actionEnterPin = new ActionEnterPin(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEnterPin) action).setParam(context,
                        transData.getTransType().getTransName(), transData.getPan(), Constants.ISSUER_UNIONPAY.equalsIgnoreCase(transData.getIssuer().getName()),
                        context.getString(R.string.prompt_pin),
                        context.getString(R.string.prompt_no_pin),
                        totalAmount, tipAmount,
                        ActionEnterPin.EEnterPinType.ONLINE_PIN);

            }
        });

        actionEnterPin.setEndListener(new ActionEndListener() {

            @Override
            public void onEnd(AAction action, ActionResult actionResult) {
                int ret = actionResult.getRet();
                if (ret == TransResult.SUCC) {
                    DUKPTResult dukptResult = (DUKPTResult) actionResult.getData();
                    if (dukptResult.getResult() != null && dukptResult.getResult().length != 0) {
                        transData.setPin(ConvertHelper.getConvert().bcdToStr(dukptResult.getResult()));
                        transData.setHasPin(true);
                    }
                    if (dukptResult.getKsn() != null && dukptResult.getKsn().length != 0) {
                        transData.setPinKsn(ConvertHelper.getConvert().bcdToStr(dukptResult.getKsn()));
                    }
                    result = 0;
                    cv.open();
                } else {
                    result = -1;
                    cv.open();
                }
                ActivityStack.getInstance().pop();
            }
        });
        actionEnterPin.execute();

        cv.block();
        return result;
    }

    @Override
    public void onShowRemoveMessage(String message, int timeout, boolean conformable) {
        onShowRemoveMessage(message, timeout, CustomAlertDialog.NORMAL_TYPE, conformable);
    }
}
