package com.pax.pay.trans.receipt;

import com.pax.glwrapper.page.IPage;
import com.pax.pay.trans.model.TransData;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

/**
 * Created by xub on 2018/3/23.
 */

public class AuthorizationReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isReprint isReprint
     */
    public AuthorizationReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isPrintPreview isPrintPreview
     */
    public AuthorizationReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addReferenceNO();
        addAppCode();
        addEMVInfo();
        addSystemTrace();
        addNewline(1);
        addAmount();
        addDoubleLine();
        addNewline(1);
        addAcquirerRefNo();
        addEnd();
    }

    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("AUTH 授權")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

}
