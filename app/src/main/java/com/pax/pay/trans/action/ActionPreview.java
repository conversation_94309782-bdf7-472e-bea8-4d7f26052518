/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         lixc                    Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransData;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.mqtt.CurrentBatchSummary;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.action.activity.TransPreviewActivity;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

public class ActionPreview extends AAction {
    private TransData transData;
    private SQRData sqrData;
    private ArrayList<CurrentBatchSummary> currentBatchSummaryList;

    private String transType;

    private Acquirer acquirer;

    private WeakReference<Context> contextRef;
    private String title;

    private static final String TAG = "ActionPreview";

    public ActionPreview(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, TransData transData) {
        this.contextRef = new WeakReference<>(context);
        this.transData = transData;
    }

    public void setParam(Context context, SQRData sqrData) {
        this.contextRef = new WeakReference<>(context);
        this.sqrData = sqrData;
    }

    public void setParam(Context context, ArrayList<CurrentBatchSummary> currentBatchSummaryList, String title) {
        this.contextRef = new WeakReference<>(context);
        this.currentBatchSummaryList = currentBatchSummaryList;
        this.title = title;
    }


    public void setParam(Context context, TransData transData, String transType) {
        this.contextRef = new WeakReference<>(context);
        this.transData = transData;
        this.transType = transType;
    }

    public void setParam(Context context, Acquirer acquirer, String transType) {
        this.contextRef = new WeakReference<>(context);
        this.acquirer = acquirer;
        this.transType = transType;
    }

    @Override
    protected void process() {
        Context context = contextRef.get();
        if (context == null || (context instanceof Activity && ((Activity) context).isFinishing())) {
            LogUtils.d(TAG, "context is null or activity is finishing");
            // 清理引用，防止内存泄漏
            resetStartListener();
            return;
        }
        Intent intent = new Intent(context, TransPreviewActivity.class);
        if (transData != null) {
            intent.putExtra(EUIParamKeys.TRANSDATA.toString(), transData);
        }
        if (sqrData != null) {
            intent.putExtra(EUIParamKeys.SQRDATA.toString(), sqrData);
        }
        if (currentBatchSummaryList != null && !currentBatchSummaryList.isEmpty()) {
            intent.putExtra(EUIParamKeys.SQR_BATCH_SUMMARY.toString(), currentBatchSummaryList);
            intent.putExtra(EUIParamKeys.PRINT_PREVIEW_TITLE.toString(), title);
        }
        if (acquirer != null) {
            intent.putExtra(EUIParamKeys.ACQUIRER.toString(), acquirer);
        }
        intent.putExtra(EUIParamKeys.TRANS_TYPE.toString(), transType);
        context.startActivity(intent);

        // 启动Activity后清理引用，防止内存泄漏
        contextRef.clear();
        transData = null;
        sqrData = null;
        currentBatchSummaryList = null;
        acquirer = null;
        transType = null;
    }
}
