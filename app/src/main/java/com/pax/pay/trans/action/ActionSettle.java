/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.SettleActivity;
import java.util.ArrayList;
import java.util.List;

public class ActionSettle extends AAction {
    private Context context;
    private String title;
    private List<String> list;

    public ActionSettle(ActionStartListener listener) {
        super(listener);
    }

    /**
     *
     */
    public void setParam(Context context, String title, List<String> list) {
        this.context = context;
        this.title = title;
        this.list = list;
    }

    @Override
    protected void process() {
        FinancialApplication.getApp().runOnUiThread(new Runnable() {

            @Override
            public void run() {
                List<Acquirer> acquirerList = new ArrayList<>();
                try{
                    for(String acquirerName:list){
                        Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(acquirerName);
                        acquirerList.add(acquirer);
                    }
                }catch (RuntimeException e){
                    //解决部分收单行无法查询的返回
                    setResult(new ActionResult(TransResult.ERR_NO_MATCH_ACQUIRER,null));
                }
                //增加Zero Settlement，若无交易记录，也需要进行打印
/*                if (FinancialApplication.getTransDataDbHelper().countOfSelectedAcqTrans(acquirerList) == 0) {
                    setResult(new ActionResult(TransResult.ERR_NO_TRANS, null));
                    return;
                }*/

                Intent intent = new Intent(context, SettleActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(EUIParamKeys.NAV_TITLE.toString(), title);
                bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                bundle.putStringArrayList(EUIParamKeys.ARRAY_LIST_2.toString(), new ArrayList<>(list));
                intent.putExtras(bundle);
                context.startActivity(intent);
            }
        });
    }
}
