/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Base64;

import com.pax.dal.exceptions.PrinterDevException;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

/**
 * print bitmap
 */
public class ReceiptPrintBitmap extends AReceiptPrint {

    public int print(String bitmapStr, PrintListener listener) {
        this.listener = listener;

        if (listener != null) {
            listener.onShowMessage(null, Utils.getString(R.string.wait_process));
        }

        // 将json传入的String转换成Bitmap
        byte[] bitmapArray;
        bitmapArray = Base64.decode(bitmapStr, Base64.DEFAULT);
        Bitmap bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);

        if (listener != null) {
            listener.onShowMessage(null, Utils.getString(R.string.wait_print));
        }

        try {
            printBitmap(bitmap);
        } catch (PrinterDevException e) {
            if (listener != null) {
                listener.onConfirm(null, String.format("%s %s", e.getErrModule(), e.getErrMsg()));
            }
        }
        if (listener != null) {
            listener.onEnd();
        }
        return 0;
    }

}
