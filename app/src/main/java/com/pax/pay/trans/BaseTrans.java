/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.os.PowerManager;

import com.pax.commonlib.ActivityStack;
import com.pax.data.local.GreendaoHelper;
import com.pax.eventbus.TransStateEvent;
import com.pax.pay.trans.action.ActionAutoSettle;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.trans.transmit.Transmit;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.BuildConfig;
import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionEndListener;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.commonbusiness.card.TrackUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.IPicc;
import com.pax.dal.entity.EPiccType;
import com.pax.dal.exceptions.PedDevException;
import com.pax.dal.exceptions.PiccDevException;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransData.EnterMode;
import com.pax.device.Device;
import com.pax.eemv.EmvImpl;
import com.pax.eemv.IClss;
import com.pax.eemv.IEmv;
import com.pax.eemv.clss.ClssImpl;
import com.pax.eventbus.OnTransSuccessEvent;
import com.pax.pay.MainActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.action.ActionRemoveCard;
import com.pax.pay.trans.action.ActionSearchCard.CardInformation;
import com.pax.pay.trans.action.ActionSearchCard.SearchMode;
import com.pax.pay.trans.action.ActionTransPreDeal;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.BaseTask;
import com.pax.sbipay.R;
import com.pax.sdk.Sdk;
import com.pax.settings.SysParam;

import org.greenrobot.eventbus.EventBus;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public abstract class BaseTrans extends BaseTask {
    /**
     * whether transaction is running, it's global for all transaction, if insert a transaction in one transaction, control the status itself
     */
    private static boolean isTransRunning = false;
    // 当前交易类型
    protected ETransType transType;
    protected IEmv emv;
    protected IClss clss;
    protected TransData transData;
    //AET-160
    protected boolean needRemoveCard = false;
    //AET-199
    private Activity old;
    private boolean backToMain = false;
    private Disposable disposable;
    private PowerManager.WakeLock wakeLock;


    public BaseTrans(Context context, ETransType transType, TransEndListener transListener) {
        super(context, transListener);
        this.transType = transType;
    }

    /**
     * get transaction running status
     */
    public static boolean isTransRunning() {
        return isTransRunning;
    }

    /**
     * set transaction running status
     */
    public static void setTransRunning(boolean isTransRunning) {
        BaseTrans.isTransRunning = isTransRunning;
        // 发布事件，通知交易状态变化
        EventBus.getDefault().post(new TransStateEvent(isTransRunning));
    }

    protected void reverseEMITrans() {
        TransProcessListenerImpl transProcessListener = new TransProcessListenerImpl(getCurrentContext());
        FinancialApplication.getApp().runInBackground(() -> {
            try {
                int reversalResult = new Transmit().sendReversal(transProcessListener);
                if (reversalResult == TransResult.ERR_EMI_REVERSAL_EXECUTED) {
                    transData.setReversalStatus(TransData.ReversalStatus.REVERSAL_COM);
                    GreendaoHelper.getTransDataHelper().update(transData);
                    reversalResult = TransResult.ERR_EMI_REVERSAL_EXECUTED;
                }
                transEnd(new ActionResult(reversalResult, null));
            } catch (PedDevException e) {
                LogUtils.e(e);
            }
        });
    }

    /**
     * set transaction type
     */
    public void setTransType(ETransType transType) {
        this.transType = transType;
    }

    protected void setTransListener(TransEndListener transListener) {
        this.transListener = transListener;
    }

    // AET-251
    public BaseTrans setBackToMain(boolean backToMain) {
        this.backToMain = backToMain;
        return this;
    }

    /**
     * transaction result prompt
     */
    @Override
    protected void transEnd(final ActionResult result) {
        LogUtils.i(TAG, transType.toString() + " TRANS--END--");
        //清楚屏幕常量的标志位
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
        clear(); // no memory leak
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        dispResult(transData, result, new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface arg0) {
                FinancialApplication.getApp().runInBackground(new DismissRunnable(result));
            }
        });
        //dispatch To the end of MessageQueue,you must use runOnUiThread.
        FinancialApplication.getApp().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                FinancialApplication.setCurrentETransType(null);
            }
        });
        if (this instanceof PreAuthCompleteTrans || this instanceof PreAuthCancelTrans) {
            //取消EventBus注册
            FinancialApplication.getApp().unregister(this);
        }
    }

    /**
     * override execute， add function to judge whether transaction check is running and add transaction pre-deal
     */
    @Override
    public void execute() {
        LogUtils.i(TAG, transType.toString() + " TRANS--START--");
        //交易过程保持屏幕常亮
        PowerManager pm = (PowerManager) getCurrentContext().getSystemService(Context.POWER_SERVICE);
        wakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "MyApp:TransactionWakeLock");
        wakeLock.acquire();
        //禁用电源按键
        Device.enablePhysicalPowerButton(false);
        FinancialApplication.setCurrentETransType(transType);
        if (isTransRunning()) {
            setTransRunning(false);
            return;
        }
        setTransRunning(true);
        old = ActivityStack.getInstance().top();
        if (Sdk.isPaxDevice()) {
            emv = new EmvImpl().getEmv();
            clss = new ClssImpl().getClss();
        }

        // transData initial
        transData = Component.transInit();
        disposable = Observable.create((ObservableOnSubscribe<ActionResult>) emitter -> {
                    ActionResult actionResult = new ActionResult(TransResult.SUCC, null);
                    try {
                        Device.increaseKSN(Constants.INDEX_DUKPT_PIN);
                        Device.increaseKSN(Constants.INDEX_DUKPT_DATA);
                    } catch (PedDevException e) {
                        LogUtils.e(TAG, "", e);
                        isTransRunning = false;
                        transEnd(new ActionResult(TransResult.ERR_PED_EXCEPTION, null));
                        return;
                    }
                    if (GreendaoHelper.getTransDataHelper().existUnknownBqrTransData()) {
                        // 有未知的BQR交易需要先查询，不允许交易
                        actionResult = new ActionResult(TransResult.ERR_UNKNOWN_QR, null);
                        emitter.onNext(actionResult);
                    } else if (FinancialApplication.isAutoSettlement() && TransContext.getInstance().getCurrentAction() instanceof ActionAutoSettle) {
                        emitter.onNext(actionResult);
                    } else {
                        boolean maxTransactionsLimit = SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_TRANSACTION_NUMBER_LIMIT));
                        int maxTransactionsNumber = SysParam.getInstance().getInt(Utils.getString(R.string.EDC_MAX_TRANSACTION_NUMBER));
                        if (!(this instanceof SettleTrans) && maxTransactionsLimit && maxTransactionsNumber > 0
                                && GreendaoHelper.getTransDataHelper().countOfIncludeInsta() >= maxTransactionsNumber) {
                            // 交易数量超出，不允许交易
                            actionResult = new ActionResult(TransResult.ERR_MAX_TRANSACTION_NUMBER, null);
                        }
                        emitter.onNext(actionResult);
                    }
                    emitter.onComplete();
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(actionResult -> {
                    if (actionResult.getRet() != TransResult.SUCC) {
                        transEnd(actionResult);
                    } else {
                        ActionTransPreDeal preDealAction = new ActionTransPreDeal(new ActionStartListener() {
                            @Override
                            public void onStart(AAction action) {
                                ((ActionTransPreDeal) action).setParam(getCurrentContext(), transType);
                            }
                        });
                        preDealAction.setEndListener(new ActionEndListener() {
                            @Override
                            public void onEnd(AAction action, ActionResult result) {
                                if (result.getRet() != TransResult.SUCC) {
                                    transEnd(result);
                                    return;
                                }
                                transData.setTransType(transType);
                                Device.enableStatusBar(false);
                                Device.enableHomeRecentKey(false);
                                exe();
                            }
                        });
                        preDealAction.execute();
                    }
                });
    }

    /**
     * execute father execute()
     */
    private void exe() {
        super.execute();
    }

    /**
     * save card information and input type after search card
     */
    public void saveCardInfo(CardInformation cardInfo, TransData transData) {
        // manual input card number
        byte mode = cardInfo.getSearchMode();
        if (mode == SearchMode.INSERT || SearchMode.isWave(mode)) {
            transData.setEnterMode(mode == SearchMode.INSERT ? EnterMode.INSERT : EnterMode.CLSS);
        } else if (mode == SearchMode.KEYIN) {
            transData.setPan(cardInfo.getPan());
            transData.setExpDate(cardInfo.getExpDate());
            transData.setEnterMode(EnterMode.MANUAL);
            transData.setIssuer(cardInfo.getIssuer());
        } else if (mode == SearchMode.SWIPE) {
            transData.setTrack1(cardInfo.getTrack1());
            transData.setTrack2(cardInfo.getTrack2());
            transData.setTrack3(cardInfo.getTrack3());
            transData.setCardHolderName(Utils.getHolderName(cardInfo.getTrack1()));
            transData.setPan(cardInfo.getPan());
            transData.setExpDate(TrackUtils.getExpDate(cardInfo.getTrack2()));
            transData.setEnterMode(EnterMode.SWIPE);
            transData.setIssuer(cardInfo.getIssuer());

            String serviceCode = TrackUtils.getServiceCode(cardInfo.getTrack2());
            boolean isFreePin;
            if (serviceCode != null && serviceCode.length() >= 3 && !transData.getIssuer().isRequirePIN()) {
                String third = serviceCode.substring(2, 3);
                isFreePin = !(third.equals("0") || third.equals("5") || third.equals("7"));
            } else {
                isFreePin = !transData.getIssuer().isRequirePIN();
            }
            transData.setPinFree(isFreePin);
        }
    }

    @Override
    public void gotoState(String state) {
        LogUtils.i(TAG, transType.toString() + " ACTION--" + state + "--start");
        super.gotoState(state);
    }

    private class DismissRunnable implements Runnable {
        private final ActionResult result;

        DismissRunnable(ActionResult result) {
            this.result = result;
        }

        @Override
        public void run() {
            //弹窗消失之后发送事件播报语音且仅在不失败时才播报
            boolean needAudio = result != null && result.getRet() == TransResult.SUCC;
            // BQR交易当服务器拒绝时失败也会返回SUCC返回码，因此需要单独排除
            if (result != null && result.getData() instanceof TransData) {
                TransData bqrTransData = (TransData) result.getData();
                if (bqrTransData.getTransType() == ETransType.QR && bqrTransData.getTransState() == TransData.ETransStatus.FAILED) {
                    needAudio = false;
                }
            } else if (result != null && result.getData() instanceof String && Constants.E_CHARGE_SLIP.equals(result.getData())) {
                // e-charge slip无论结果如何都需要播报
                needAudio = true;
            }
            if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO) && needAudio) {
                EventBus.getDefault().post(new OnTransSuccessEvent(""));
            }
            if (BuildConfig.needRemoveCard) {
                removeCard();
            }

            try {
                IPicc picc = FinancialApplication.getDal().getPicc(EPiccType.INTERNAL);
                picc.close();
            } catch (PiccDevException e) {
                LogUtils.e(TAG, "", e);
            }

            setTransRunning(false);

            if (backToMain) {
                ActivityStack.getInstance().popTo(MainActivity.class);
            } else if (old == null) {
                ActivityStack.getInstance().pop();
            } else {
                ActivityStack.getInstance().popTo(old);
            }

            TransContext.getInstance().setCurrentAction(null);
            if (transListener != null) {
                transListener.onEnd(result);
            }
        }

        /**
         * remove card check, need start thread when call this function
         */
        private void removeCard() {
            // avoid prompting warning message for some no card transaction, like settlement
            //AET-160
            if (!needRemoveCard) {
                return;
            }

            new ActionRemoveCard(new ActionStartListener() {
                @Override
                public void onStart(AAction action) {
                    ((ActionRemoveCard) action).setParam(getCurrentContext(), transType.getTransName());
                }
            }).execute();
        }
    }
}
