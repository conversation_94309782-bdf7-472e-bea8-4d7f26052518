/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-10
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import android.annotation.SuppressLint;
import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;

public class PackPreAuthCompVoid extends PackIso8583 {

    public PackPreAuthCompVoid(PackListener listener) {
        super(listener);
    }

    /**
     * PackPreAuthCompVoid required iso8583 fields
     */
    @Override
    protected int[] getRequiredFields() {
        return new int[] { 2, 3, 4, 11, 12, 13, 14, 22, 24, 25, 37, 38, 41, 42, 54, 60, 62, 63 };
    }

    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("3", "020000");
        } else {
            super.setBitData3(transData);
        }
    }


    @Override
    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        String enterMode = getInputMethod(transData);
        if (transData.getIssuer().getName().equals("UnionPay")) {
            enterMode = "12";
        }
        setBitData("22", Component.getPaddedString(enterMode, 4, '0'));
    }

    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("37", transData.getOrigRefNo());
    }

    @Override
    protected void setBitData38(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("38", transData.getOrigAuthCode());
    }

    @SuppressLint("DefaultLocale")
    @Override
    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.VOID && transData.getTransState() == TransData.ETransStatus.ADJUSTED) {
            String tempStr = null;
            String homeTipAmount = null;
            if (transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT) {
                tempStr = String.valueOf(transData.getOrgTipAmount());
                homeTipAmount = transData.getOrigHomeCurrencyTipAmount();
                //transData.setTipAmount(transData.getOrgTipAmount());
            } else if (transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_SENT) {
                tempStr = String.format("%012d", transData.getTipAmount());
                homeTipAmount = transData.getHomeCurrencyTipAmount();
            }

            if (tempStr == null || homeTipAmount == null) {
                return;
            }

            if (transData.isDcc()) {
                homeTipAmount += FinancialApplication.getConvert().stringPadding(homeTipAmount, '0', 12, IConvert.EPaddingPosition.PADDING_LEFT);
                setBitData("54", homeTipAmount);
                return;
            }

            setBitData("54", tempStr);
        }
    }
}