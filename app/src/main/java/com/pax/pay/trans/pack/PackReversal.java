/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import java.nio.charset.StandardCharsets;

public class PackReversal extends PackIso8583 {

    private static final String TAG = "PackReversal";

    public PackReversal(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{2, 3, 4, 11, 14, 22, 23, 24, 25, 37, 38, 41, 42, 49, 54, 55, 56, 62, 63};
    }

    /**
     * setBitData3
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getOrigTransType() == ETransType.REFUND) {
            setBitData("3", "220000");
            return;
        }
        super.setBitData3(transData);
    }

    /**
     * setBitData4
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR) {
            setBitData("4", String.format("%012d", transData.getNetAmount()));
            return;
        }
        setBitData("4", String.format("%012d", transData.getAmount()));
    }

    /**
     * setBitData22
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        String enterMode = getInputMethod(transData);
        if (transData.getTransType() == ETransType.VOID && transData.getIssuer()
                .getName()
                .equals("UnionPay")) {
            enterMode = "12";
        }
        setBitData("22", Component.getPaddedString(enterMode, 4, '0'));
    }

    /**
     * setBitData23
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData23(@NonNull TransData transData) throws Iso8583Exception {
        if ((transData.getIssuer().getName().equals("HASE_CUP") || (transData.getIssuer()
                .getName()
                .equals("UnionPay")))) {
            setBitData("23", transData.getCardSerialNo());
        }
    }

    /**
     * setBitData25
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData25(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.PREAUTH
                || transData.getTransType() == ETransType.PREAUTHVOID) {
            setBitData("25", "07");
            return;
        }
        super.setBitData25(transData);
    }

    /**
     * setBitData37
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().getName().equals("HASE_OLS")
                && transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR) {
            setBitData("37", transData.getOrigRefNo());
        }
    }

    /**
     * setBitData38
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData38(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().getName().equals("HASE_CUP") && (transData.getTransType()
                == ETransType.VOID
                || transData.getTransType() == ETransType.PREAUTHVOID
                || transData.getTransType() == ETransType.PREAUTHCM
                || transData.getTransType() == ETransType.PREAUTHCMVOID)) {
            super.setBitData38(transData);
        }
    }

    /**
     * setBitData54
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.VOID
                && transData.getTransState() == TransData.ETransStatus.ADJUSTED) {
            String TempStr = null;
            if (transData.getOrgTipAmount() > 0) {
                TempStr = String.format("%012d", transData.getOrgTipAmount());
            } else if (transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_SENT) {
                TempStr = String.format("%012d", transData.getTipAmount());
            }
            if (TempStr != null) {
                setBitData("54", TempStr);
            }
        }
    }

    /**
     * setBitData55
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData55(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.INSTALMENT) {
            return;
        }

        //Only Indirect CUP Sale/PreAuth need F55 in Reversal
        if (!"HASE_CUP".equals(transData.getAcquirer().getName())) {
            return;
        }

        if (transData.getTransType() == ETransType.SALE
                || transData.getTransType() == ETransType.PREAUTH) {
            super.setBitData55(transData);
        }
    }

    /**
     * setBitData56
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData56(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == TransData.EnterMode.SWIPE
                || transData.getEnterMode() == TransData.EnterMode.MANUAL) {
            return;
        }

        if (transData.getOrigTransType() == ETransType.INSTALMENT) {
            return;
        }

        if (transData.getOrigTransType() == ETransType.REFUND) {
            return;
        }

        if (transData.getEnterMode() == TransData.EnterMode.CLSS && !(transData.getAcquirer()
                .getName()
                .equals("HASE_CUP"))) {
            return;
        }

        if (transData.getTransType() == ETransType.VOID
                || transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR) {
            if (transData.getSendField56() != null) {
                setBitData("56", FinancialApplication.getConvert()
                        .strToBcd(transData.getSendField56(),
                                IConvert.EPaddingPosition.PADDING_LEFT));
            }
            return;
        }
        super.setBitData56(transData);
    }

    /**
     * setBitData62
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().getName().equals("HASE_CUP") || transData.getAcquirer()
                .getName()
                .equals("HASE_OLS")) {
            return;
        }
        if (transData.getTransType().getMsgType().equals("0100") || transData.getTransType()
                .getMsgType()
                .equals("0200")
                || transData.getTransType().getMsgType().equals("0220") || transData.getTransType()
                .getMsgType()
                .equals("0320")) {
            super.setBitData62(transData);
        }
    }

    /**
     * setBitData63
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().getName().equals("HASE_OLS")) {
            String field63 = transData.getField63();
            setBitData("63", field63.getBytes(StandardCharsets.ISO_8859_1));
            return;
        }
        super.setBitData63(transData);
    }

}
