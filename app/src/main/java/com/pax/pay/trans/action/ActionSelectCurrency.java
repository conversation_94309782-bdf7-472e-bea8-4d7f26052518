/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-10-25
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import com.pax.abl.core.AAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.SelectCurrencyActivity;
import com.pax.pay.trans.action.activity.ShowCurrencyActivity;
import com.pax.pay.utils.CurrencyCode;
import com.pax.settings.SysParam;

public class ActionSelectCurrency extends AAction {

    private Context context;
    private String title;
    private String amount;
    private String rate;
    private String homeCurrencyAmount;
    private CurrencyCode homeCurrency;
    private CurrencyCode localCurrency;
    private boolean isVisaCard; // check if visa or not
    private String dccMarkupRate; // visa dcc charge
    private boolean isSymbolNegative = false;//only for refund
    private boolean isAuth = false;//for pre Auth, Auth
    private boolean isSale = false;//for online sale, offline sale

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionSelectCurrency(ActionStartListener listener) {
        super(listener);
    }

    /**
     * Sets param.
     *
     * @param context the context
     * @param title the title
     * @param amount the local total amount
     * @param rate the rate
     * @param homeCurrencyAmount the home currency amount
     * @param localCurrency the local currency
     * @param homeCurrency the home currency
     * @param isVisaCard true : is visa card
     * @param dccMarkupRate the dcc markup rate
     */
    public void setParam(Context context, String title, String amount, String rate, String homeCurrencyAmount,
            CurrencyCode localCurrency, CurrencyCode homeCurrency, boolean isVisaCard, String dccMarkupRate) {
        this.context = context;
        this.title = title;
        this.amount = amount;
        this.rate = rate;
        this.homeCurrencyAmount = homeCurrencyAmount;
        this.homeCurrency = homeCurrency;
        this.localCurrency = localCurrency;
        this.isVisaCard = isVisaCard;
        this.dccMarkupRate = dccMarkupRate;
    }

    /**
     * Sets param.
     *
     * @param context the context
     * @param title the title
     * @param amount the amount
     * @param rate the rate
     * @param homeCurrencyAmount the home currency amount
     * @param localCurrency the local currency
     * @param homeCurrency the home currency
     * @param isVisaCard the is visa card
     * @param dccMarkupRate the dcc markup rate
     * @param isSymbolNegative the is symbol negative
     */
    public void setParam(Context context, String title, String amount, String rate, String homeCurrencyAmount,
            CurrencyCode localCurrency, CurrencyCode homeCurrency, boolean isVisaCard, String dccMarkupRate
            , boolean isSymbolNegative) {
        this.isSymbolNegative = isSymbolNegative;
        setParam(context, title, amount, rate, homeCurrencyAmount, localCurrency, homeCurrency, isVisaCard,
                dccMarkupRate);
    }

    /**
     * Sets param.
     *
     * @param context the context
     * @param title the title
     * @param amount the local total amount
     * @param rate the rate
     * @param homeCurrencyAmount the home currency amount
     * @param localCurrency the local currency
     * @param homeCurrency the home currency
     * @param isVisaCard true : is visa card
     * @param isAuth the is symbol preAuth/auth
     * @param dccMarkupRate the dcc markup rate
     */
    public void setParam(Context context, String title, String amount, String rate, String homeCurrencyAmount,
            CurrencyCode localCurrency, CurrencyCode homeCurrency, boolean isVisaCard, boolean isAuth,
            boolean isSale, String dccMarkupRate) {
        this.context = context;
        this.title = title;
        this.amount = amount;
        this.rate = rate;
        this.homeCurrencyAmount = homeCurrencyAmount;
        this.homeCurrency = homeCurrency;
        this.localCurrency = localCurrency;
        this.isVisaCard = isVisaCard;
        this.isAuth = isAuth;
        this.isSale = isSale;
        this.dccMarkupRate = dccMarkupRate;
    }

    @Override
    protected void process() {
        Intent intent;
        if ((isAuth && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_DCC_AUTH_NEW_FLOW))
                || (isSale && FinancialApplication.getSysParam()
                .get(SysParam.BooleanParam.EDC_DCC_SALE_NEW_FLOW))) {
            intent = new Intent(context, ShowCurrencyActivity.class);
        } else {
            intent = new Intent(context, SelectCurrencyActivity.class);
        }

        intent.putExtra(EUIParamKeys.NAV_TITLE.toString(), title);
        intent.putExtra(EUIParamKeys.TRANS_AMOUNT.toString(), amount);
        intent.putExtra(EUIParamKeys.TRANS_RATE.toString(), rate);
        intent.putExtra(EUIParamKeys.TRANS_LOCAL_CURRENCY_SYMBOL.toString(), localCurrency.getSymbol());
        intent.putExtra(EUIParamKeys.TRANS_LOCAL_FLAG_IMAGE_ID.toString(),
                localCurrency.getNationalFlagImageId());
        intent.putExtra(EUIParamKeys.TRANS_HOME_FLAG_IMAGE_ID.toString(),
                homeCurrency.getNationalFlagImageId());
        intent.putExtra(EUIParamKeys.TRANS_HOME_CURRENCY_SYMBOL.toString(), homeCurrency.getSymbol());
        intent.putExtra(EUIParamKeys.TRANS_HOME_CURRENCY_AMOUNT.toString(), homeCurrencyAmount);
        intent.putExtra(EUIParamKeys.IS_VISA_CARD.toString(), isVisaCard);
        intent.putExtra(EUIParamKeys.DCC_MARKUP_RATE.toString(), dccMarkupRate);
        intent.putExtra(EUIParamKeys.IS_SYMBOL_NEGATIVE.toString(), isSymbolNegative);
        context.startActivity(intent);
    }
}
