package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.BankEMICheckCardRequestData;
import com.pax.data.emi.BankEMICheckCardResponseData;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;

public class BankEMICheckCardActivity extends BaseEMIActivity<BankEMICheckCardRequestData, BankEMICheckCardResponseData> {

    private BankEMICheckCardRequestData bankEMICheckCardRequestData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestData = bankEMICheckCardRequestData;
        try {
            handleRequest();
        } catch (JsonProcessingException e) {
            handleError(getGeneralErrorCode(),null);
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected void loadParam() {
        bankEMICheckCardRequestData = (BankEMICheckCardRequestData) getIntent().getSerializableExtra(EUIParamKeys.BANK_EMI_CHECK_CARD.toString());
    }

    @Override
    protected String getTitleString() {
        return "Bank EMI Check Card";
    }

    @Override
    protected String getRequestType() {
        return "CHECK_CARD_BIN";
    }

    @Override
    protected int getRequestCode() {
        return 120;
    }

    @Override
    protected int getGeneralErrorCode() {
        return TransResult.ERR_CHECK_CARD_BIN;
    }

    @Override
    protected Class<BankEMICheckCardResponseData> getResponseClass() {
        return BankEMICheckCardResponseData.class;
    }
}