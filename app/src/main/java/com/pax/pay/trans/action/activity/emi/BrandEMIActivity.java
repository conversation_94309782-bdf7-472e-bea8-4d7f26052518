package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.BrandEMIRequestData;
import com.pax.data.emi.BrandEMIResponseData;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;

public class BrandEMIActivity extends BaseEMIActivity<BrandEMIRequestData, BrandEMIResponseData> {
    private BrandEMIRequestData brandEMIRequestData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestData = brandEMIRequestData;
        try {
            handleRequest();
        } catch (JsonProcessingException e) {
            handleError(getGeneralErrorCode(),null);
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected void loadParam() {
        brandEMIRequestData = (BrandEMIRequestData) getIntent().getSerializableExtra(EUIParamKeys.BRAND_EMI_DATA.toString());
    }

    @Override
    protected String getTitleString() {
        return "Brand EMI";
    }

    @Override
    protected String getRequestType() {
        return "BRANDEMI_REQUEST";
    }

    @Override
    protected int getRequestCode() {
        return 122;
    }

    @Override
    protected int getGeneralErrorCode() {
        return TransResult.ERR_USER_CANCEL;
    }

    @Override
    protected Class<BrandEMIResponseData> getResponseClass() {
        return BrandEMIResponseData.class;
    }

}