package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.model.TransData;


/**
 * Created by terry on 2019/01/09.
 */

public class PackAuthReversal extends PackIsoBase {
    public PackAuthReversal(PackListener listener) {
        super(listener);
    }

    /**
     * pack download auth reversal required iso8583 fields
     */
    protected int[] getRequiredFields() {
        return new int[] {
                1, 2, 3, 4, 6, 10, 11, 12, 13, 14, 22, 24, 25, 35, 37, 38, 41, 42, 49, 51, 54, 55,
                62, 63
        };
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("2", transData.getPan());
    }

    @Override
    protected void setBitData12(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getAcquirer().getName().equals("HASE_EMV") || transData.getAcquirer()
                .getName()
                .equals("HASE_OLS"))) {
            super.setBitData12(transData);
        }
    }

    @Override
    protected void setBitData13(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getAcquirer().getName().equals("HASE_EMV") || transData.getAcquirer().getName().equals("HASE_OLS"))){
            super.setBitData13(transData);
        }
    }

    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getAcquirer().getName().equals("HASE_EMV") || transData.getAcquirer().getName().equals("HASE_OLS"))){
            setBitData("14", transData.getExpDate());
        }
    }

    @Override
    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("35", transData.getTrack2());
    }

    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getAcquirer().getName().equals("HASE_EMV") || transData.getAcquirer().getName().equals("HASE_OLS"))){
            super.setBitData37(transData);
        }
    }

    @Override
    protected void setBitData38(@NonNull TransData transData) throws Iso8583Exception {
        if (!(transData.getAcquirer().getName().equals("HASE_EMV") || transData.getAcquirer().getName().equals("HASE_OLS"))){
            setBitData("38", transData.getAuthCode());
        }
    }

    @Override
    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        long TempChange = transData.getTipAmount();
        String TempStr = String.format("%012d", TempChange);
        String field;
        if (TempChange > 0) {
            if (!transData.isDcc()) {
                setBitData("54", TempStr);
            }else{
                String homeTipAmount = transData.getHomeCurrencyTipAmount();
                if (homeTipAmount == null || homeTipAmount.isEmpty()) {
                    return;
                }
                if (TempStr.equals("0") && homeTipAmount.equals("0")) {
                    return;
                }
                field = TempStr;
                field += FinancialApplication.getConvert().stringPadding(homeTipAmount, '0', 12, IConvert.EPaddingPosition.PADDING_LEFT);
                setBitData("54", field);
            }
        }
    }
}
