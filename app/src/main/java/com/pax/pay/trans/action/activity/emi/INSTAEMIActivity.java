package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.INSTAEMIRequestData;
import com.pax.data.emi.INSTAEMIResponseData;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;

public class INSTAEMIActivity extends BaseEMIActivity<INSTAEMIRequestData, INSTAEMIResponseData> {
    private INSTAEMIRequestData instaemiRequestData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestData = instaemiRequestData;
        try {
            handleRequest();
        } catch (JsonProcessingException e) {
            handleError(getGeneralErrorCode(), null);
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected void loadParam() {
        instaemiRequestData = (INSTAEMIRequestData) getIntent().getSerializableExtra(EUIParamKeys.INSTA_EMI_DATA.toString());
    }

    @Override
    protected String getTitleString() {
        return "INSTA EMI";
    }

    @Override
    protected String getRequestType() {
        return "INSTAEMI_REQUEST";
    }

    @Override
    protected int getRequestCode() {
        return 123;
    }

    @Override
    protected int getGeneralErrorCode() {
        return TransResult.ERR_USER_CANCEL;
    }

    @Override
    protected Class<INSTAEMIResponseData> getResponseClass() {
        return INSTAEMIResponseData.class;
    }
}