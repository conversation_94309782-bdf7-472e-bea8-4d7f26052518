/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-1-13
 * Module Author: qixw
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.EnterAmountTextWatcher;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.view.keyboard.CustomKeyboardEditText;
import java.math.BigDecimal;

public class InputTransDataTipActivity extends BaseActivityWithTickForAction {

    private TextView mOriTips;
    private TextView mTotalAmount;
    private CustomKeyboardEditText mEditNewTotal;

    private Button confirmBtn;

    private LinearLayout ecrRefLl;

    private TextView mEcrRef;

    private String navTitle;
    private ActionInputTransData.EInputType inputType;

    private long totalAmountLong = 0L;
    private long tipAmountLong = 0L;
    private long baseAmountLong = 0L;
    private float adjustPercent = 0.0f;
    private long oriTipAmountLong = 0L;
    private long netAmountLong = 0L;
    private long maxValue;
    private String currencySymbol;
    private boolean isDccOptOut;
    private boolean isFromThirdParty;
    private String ecrRef;
    private long newTotalAmount = 0L;

    private TextView baseAmountLabelText;


    private TextView totalAmountLabelText;
    private TextView newTotalAmountLabelText;
    private boolean isHaseOls;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setEditText();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_input_new_tip;
    }

    /**
     * load parameter
     */
    @Override
    protected void loadParam() {
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        inputType = (ActionInputTransData.EInputType) getIntent().getSerializableExtra(EUIParamKeys.INPUT_TYPE.toString());

        String oriTransAmount = getIntent().getStringExtra(EUIParamKeys.TRANS_AMOUNT.toString());
        String netAmount = getIntent().getStringExtra(EUIParamKeys.NET_AMOUNT.toString());
        String oriTips = getIntent().getStringExtra(EUIParamKeys.ORI_TIPS.toString());
        isHaseOls = getIntent().getBooleanExtra(EUIParamKeys.IS_HASE_OLS.toString(), false);
        totalAmountLong = Utils.parseLongSafe(oriTransAmount, 0);
        netAmountLong = Utils.parseLongSafe(netAmount, 0);
        tipAmountLong = Utils.parseLongSafe(oriTips, 0);
        oriTipAmountLong = tipAmountLong;
        baseAmountLong = totalAmountLong - tipAmountLong;
        adjustPercent = getIntent().getFloatExtra(EUIParamKeys.TIP_PERCENT.toString(), 0.0f);
        currencySymbol = getIntent().getStringExtra(EUIParamKeys.TRANS_CURRENCY_SYMBOL.toString());
        isDccOptOut = getIntent().getBooleanExtra(EUIParamKeys.IS_DCC_OPT_OUT.toString(), false);
        isFromThirdParty = getIntent().getBooleanExtra(EUIParamKeys.IS_THIRD_PARTY.toString(), false);
        ecrRef = getIntent().getStringExtra(EUIParamKeys.ECR_REF.toString());
        newTotalAmount = Utils.parseLongSafe(
                getIntent().getStringExtra(EUIParamKeys.NEW_TOTAL_AMOUNT.toString()), 0);
    }

    /**
     * get title string
     * @return String
     */
    @Override
    protected String getTitleString() {
        return navTitle;
    }

    /**
     * init views
     */
    @Override
    protected void initViews() {
        TextView mBaseAmount = (TextView) findViewById(R.id.value_base_amount);
        mOriTips = (TextView) findViewById(R.id.value_ori_tips);
        mTotalAmount = (TextView) findViewById(R.id.value_total_amount);
        baseAmountLabelText = (TextView) findViewById(R.id.value_base_amount_label);
        totalAmountLabelText = (TextView) findViewById(R.id.value_total_amount_label);
        newTotalAmountLabelText = (TextView) findViewById(R.id.new_total_label);

        mBaseAmount.setText(CurrencyConverter.convert(baseAmountLong,
                CurrencyCode.getBySymbol(currencySymbol)));
        mOriTips.setText(
                CurrencyConverter.convert(tipAmountLong, CurrencyCode.getBySymbol(currencySymbol)));
        mTotalAmount.setText(CurrencyConverter.convert(totalAmountLong,
                CurrencyCode.getBySymbol(currencySymbol)));

        mEditNewTotal = (CustomKeyboardEditText) findViewById(R.id.prompt_edit_new_total);
        mEditNewTotal.setText(
                CurrencyConverter.convert(0L, CurrencyCode.getBySymbol(currencySymbol)));
        mEditNewTotal.setFocusable(true);
        //mEditNewTotal.requestFocus();

        confirmBtn = (Button) findViewById(R.id.info_confirm);

        ecrRefLl = (LinearLayout) findViewById(R.id.ll_ecr_ref);
        mEcrRef = (TextView) findViewById(R.id.value_ecr_ref);

        if (isHaseOls) {
            baseAmountLabelText.setText(R.string.prompt_net_amount_label);
            totalAmountLabelText.setText(R.string.prompt_net_total_label);
            newTotalAmountLabelText.setText(R.string.prompt_new_total_label);
            mBaseAmount.setText(CurrencyConverter.convert(netAmountLong,
                    CurrencyCode.getBySymbol(currencySymbol)));
            mTotalAmount.setText(CurrencyConverter.convert(netAmountLong + oriTipAmountLong,
                    CurrencyCode.getBySymbol(currencySymbol)));
        }

        if (!TextUtils.isEmpty(ecrRef)) {
            ecrRefLl.setVisibility(View.VISIBLE);
            mEcrRef.setText(ecrRef);
            mEditNewTotal.setText(CurrencyConverter.convert(newTotalAmount,
                    CurrencyCode.getBySymbol(currencySymbol)));
            mEditNewTotal.setEnabled(false);
        }
        //如果是从三方应用调起的，则显示新金额，并设置不可点击
        if(isFromThirdParty){
            mEditNewTotal.setText(CurrencyConverter.convert(newTotalAmount,
                    CurrencyCode.getBySymbol(currencySymbol)));
            mEditNewTotal.setEnabled(false);
        }
    }

    /**
     * set edit text
     */
    private void setEditText() {
        mEditNewTotal.setHint(getString(R.string.amount_default));
        //三方调用进入的流程和ECR同理
        if (!TextUtils.isEmpty(ecrRef) || isFromThirdParty) {
            //ECR的调整金额无法编辑，故验证无效金额后直接拒绝交易
            //输入金额超过可调整金额上限，提示小费超出限额
            if ((baseAmountLong * (1 + adjustPercent / 100) < newTotalAmount)) {
                finish(new ActionResult(TransResult.ERR_TIPS_EXCEED_LIMIT, null));
                return;
            } else if (newTotalAmount <= baseAmountLong) {
                //输入金额低于原交易金额，提示无效金额
                //若ECR总金额等于原交易金额, 也提示无效金额
                finish(new ActionResult(TransResult.INVALID_AMOUNT, null));
                return;
            }
        }

        //confirmBtnChange(); //AET-19

        final EnterAmountTextWatcher amountWatcher;
        if (!TextUtils.isEmpty(ecrRef) || isFromThirdParty) {
            amountWatcher = new EnterAmountTextWatcher(0, newTotalAmount,
                    CurrencyCode.getBySymbol(currencySymbol));
        } else {
            amountWatcher =
                    new EnterAmountTextWatcher(0, 0, CurrencyCode.getBySymbol(currencySymbol));
        }

        amountWatcher.setOnTipListener(new EnterAmountTextWatcher.OnTipListener() {
            @Override
            public void onUpdateTipListener(long baseAmount, long newTotalAmount) {
                if (isHaseOls) {
                    tipAmountLong = newTotalAmount - netAmountLong;
                    totalAmountLong = baseAmountLong + tipAmountLong - oriTipAmountLong;
                } else {
                    tipAmountLong = newTotalAmount - baseAmountLong;
                    totalAmountLong = newTotalAmount;
                }
                //confirmBtnChange();
            }

            @Override
            public boolean onVerifyTipListener(long baseAmount, long newTotalAmount) {
                //AET-205
                if (isHaseOls) {
                    return netAmountLong + (baseAmountLong * adjustPercent / 100) >= newTotalAmount && newTotalAmount <= amountWatcher.getMaxValue();
                } else {
                    return baseAmountLong * (1 + adjustPercent / 100) >= newTotalAmount && newTotalAmount <= amountWatcher.getMaxValue();
                }
            }
        });
        mEditNewTotal.addTextChangedListener(amountWatcher);
        maxValue = amountWatcher.getMaxValue();
    }

    /**
     * confirm button change
     */
   /* private void confirmBtnChange() {
        long oriTotleAmount = baseAmountLong + oriTipAmountLong;
        boolean enable = totalAmountLong != oriTotleAmount;
        if (isDccOptOut) {
            enable = true;
        }
        confirmBtn.setEnabled(enable);
    }*/

    /**
     * set listeners
     */
    @Override
    protected void setListeners() {
        confirmBtn.setOnClickListener(this);
        mEditNewTotal.setOnEditorActionListener(new TipEditorActionListener());
    }

    private class TipEditorActionListener extends EditorActionListener {
        @Override
        public void onKeyOk() {
            //do nothing
//            onClick(confirmBtn);
            mEditNewTotal.clearFocus();
        }

        @Override
        public void onKeyCancel() {
//            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
            mEditNewTotal.clearFocus();
        }
    }

    /**
     * 点击事件
     * @param v View
     */
    @Override
    public void onClickProtected(View v) {

        if (v.getId() == R.id.info_confirm) {
            String content = process();
            if (content == null || content.isEmpty()) {
                ToastUtils.showMessage(R.string.invalid_amount);
                return;
            }
            if (isDccOptOut) {
                long newTotalAmount = CurrencyConverter.parse(mEditNewTotal.getText().toString(),
                        CurrencyCode.getBySymbol(currencySymbol));
                long newTipAmount = newTotalAmount - baseAmountLong;

                BigDecimal hundredBd = new BigDecimal(100);
                BigDecimal percentBd = BigDecimal.valueOf(adjustPercent);
                BigDecimal baseAmountBd = new BigDecimal(baseAmountLong);
                BigDecimal maxTipsBd = baseAmountBd.multiply(percentBd).divide(hundredBd, 2);
                BigDecimal tipAmountBd = new BigDecimal(newTipAmount);

                boolean adjustWrong = inputType == ActionInputTransData.EInputType.NUM && mTotalAmount.getText().toString().equals(mEditNewTotal.getText().toString());
                if (adjustWrong || maxTipsBd.doubleValue() < tipAmountBd.doubleValue() || newTotalAmount > maxValue || newTotalAmount < baseAmountLong) {
                    ToastUtils.showMessage(R.string.please_input_again);
                    return;
                }
            }
            finish(new ActionResult(TransResult.SUCC, totalAmountLong, tipAmountLong));
        }

    }

    /**
     * 输入数值检查
     */
    private String process() {
        String content = mEditNewTotal.getText().toString().trim();

        if (content.isEmpty()) {
            return null;
        }

        //tip can be 0, so don't need to check here
        long newAmount = CurrencyConverter.parse(mEditNewTotal.getText().toString().trim(), CurrencyCode.getBySymbol(currencySymbol));
        if ((!isHaseOls && newAmount <= baseAmountLong) || (isHaseOls && newAmount <= netAmountLong)) {
            return null;
        }
        return String.valueOf(newAmount);
    }

    /**
     * on key back down
     * @return boolean
     */
    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }
}
