/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         qixw                    Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.EnterAmountTextWatcher;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.view.keyboard.CustomKeyboardEditText;

public class InputTransDataTipActivity extends BaseActivityWithTickForAction {

    private TextView mOriTips;
    private TextView mTotalAmount;
    private CustomKeyboardEditText mEditNewTips;

    private Button confirmBtn;

    private String navTitle;

    private long totalAmountLong = 0L;
    private long tipAmountLong = 0L;
    private long baseAmountLong = 0L;
    private float adjustPercent = 0L;
    private long orgTipAmountLong = 0L;//original tip amount


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setEditText();
    }

    @Override
    protected int getLayoutId() {
        if (Utils.isA50()) {
            return R.layout.activity_input_new_tip_a50;
        } else {
            return R.layout.activity_input_new_tip;
        }
    }

    @Override
    protected void loadParam() {
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());

        String oriTransAmount = getIntent().getStringExtra(EUIParamKeys.TRANS_AMOUNT.toString());
        String oriTips = getIntent().getStringExtra(EUIParamKeys.ORI_TIPS.toString());
        totalAmountLong = Utils.parseLongSafe(oriTransAmount, 0);
        orgTipAmountLong = Utils.parseLongSafe(oriTips, 0);
        baseAmountLong = totalAmountLong - orgTipAmountLong;
        adjustPercent = getIntent().getFloatExtra(EUIParamKeys.TIP_PERCENT.toString(), 0.0f);

    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        TextView mBaseAmount = findViewById(R.id.value_base_amount);
        mOriTips = findViewById(R.id.value_ori_tips);
        mTotalAmount = findViewById(R.id.value_total_amount);
        mEditNewTips = findViewById(R.id.prompt_edit_new_tips);
        confirmBtn = findViewById(R.id.info_confirm);

        mBaseAmount.setText(CurrencyConverter.convert(baseAmountLong));
        mOriTips.setText(CurrencyConverter.convert(orgTipAmountLong));
        mTotalAmount.setText(CurrencyConverter.convert(totalAmountLong));
    }

    private void setEditText() {
        mEditNewTips.setHint(getString(R.string.amount_default));
        mEditNewTips.requestFocus();

        final EnterAmountTextWatcher amountWatcher = new EnterAmountTextWatcher(baseAmountLong, orgTipAmountLong);
        amountWatcher.setOnTipListener(new EnterAmountTextWatcher.OnTipListener() {
            @Override
            public void onUpdateTipListener(long baseAmount, long tipAmount) {
                tipAmountLong = tipAmount;
                totalAmountLong = baseAmountLong + tipAmountLong;
                mTotalAmount.setText(CurrencyConverter.convert(totalAmountLong));
                mEditNewTips.setText(CurrencyConverter.convert(tipAmount));
            }

            @Override
            public boolean onVerifyTipListener(long baseAmount, long tipAmount) {
                //AET-205
                return tipAmount >= 0 && baseAmountLong * adjustPercent / 100 >= tipAmount;
            }
        });
        mEditNewTips.addTextChangedListener(amountWatcher);
    }

    @Override
    protected void setListeners() {
        confirmBtn.setOnClickListener(this);
        mEditNewTips.setOnEditorActionListener(new TipEditorActionListener());
    }

    @Override
    public void onClickProtected(View v) {

        if (v.getId() == R.id.info_confirm) {
            processAdjust();
        }

    }

    /**
     * 输入数值检查
     */
    private String process() {
        String content = mEditNewTips.getText().toString().trim();

        if (content.isEmpty()) {
            return null;
        }
        LogUtils.i(TAG, "process: content=" + content);
        //tip can be 0, so don't need to check here
        tipAmountLong = Utils.parseLongSafe(content, 0);
        return CurrencyConverter.parse(content).toString();
    }

    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

    private class TipEditorActionListener extends EditorActionListener {
        @Override
        public void onKeyOk() {
            processAdjust();
        }

        @Override
        public void onKeyCancel() {
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        }
    }

    private void processAdjust() {
        String content = process();
        tipAmountLong = Utils.parseLongSafe(content, 0);
        if (content == null || content.isEmpty()) {
            ToastUtils.showMessage(R.string.please_input_again);
            return;
        }
        if (orgTipAmountLong == tipAmountLong) {//tip not change
            ToastUtils.showMessage(R.string.prompt_tip_not_changed);
            return;
        }
        LogUtils.i(TAG, "process: tipAmountLong=" + tipAmountLong);
        finish(new ActionResult(TransResult.SUCC, totalAmountLong, tipAmountLong));
        setForbidBack(true);

    }
}
