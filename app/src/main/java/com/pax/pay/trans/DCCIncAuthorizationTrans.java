package com.pax.pay.trans;

import android.content.Context;
import android.content.DialogInterface;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.trans.action.ActionEmvProcess;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionEnterInfo;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionSearchCard;
import com.pax.pay.trans.action.ActionSelectCurrency;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.action.activity.SelectCurrencyActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

/**
 * Created by terry on 2019/01/09.
 */

public class DCCIncAuthorizationTrans extends BaseTrans {
    private byte searchCardMode = -1;
    private boolean needFallBack = false;
    private String refNo;

    public DCCIncAuthorizationTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.INC_AUTHORIZATION, transListener);
    }

    @Override
    protected void bindStateOnAction() {
        searchCardMode = Component.getCardReadMode(ETransType.INC_AUTHORIZATION);

        // enter amount action
        ActionEnterAmount amountAction = new ActionEnterAmount(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEnterAmount) action).setParam(getCurrentContext(),
                        getString(R.string.trans_inc_authorization), false);
            }
        });
        bind(State.ENTER_AMOUNT.toString(), amountAction, true);
        // read card
        ActionSearchCard searchCardAction = new ActionSearchCard(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSearchCard) action).setParam(getCurrentContext(), getString(R.string.trans_inc_authorization),
                        searchCardMode, String.valueOf(transData.getAmount()),
                        /*raymond ********: preserve consistency of UI display, the box is for manual key-in, confusion if shown "Please Use Contact Bank Card"
                                            null, "Please Use Contact Bank Card", transData);*/
                        null, "", transData);
            }
        });
        bind(State.CHECK_CARD.toString(), searchCardAction, true);
        // enquiry action
        ActionTransOnline enquiryAction = new ActionTransOnline(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), getString(R.string.trans_inc_authorization), transData);
            }
        });

        bind(State.ENQUIRY.toString(), enquiryAction);
        //select currency
        ActionSelectCurrency selectCurrencyAction = new ActionSelectCurrency(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionSelectCurrency) action).setParam(getCurrentContext(), getString(R.string.trans_inc_authorization),
                        String.valueOf(transData.getAmount()), transData.getRate(), transData.getHomeCurrencyAmount(),
                        transData.getLocalCurrency(), transData.getHomeCurrency(),
                        FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer()),true, false, transData.getDccMarkupRate());
            }
        });
        bind(State.SELECT_CURRENCY.toString(), selectCurrencyAction, true);
        //enter RRN action
        ActionEnterInfo enterInfoAction = new ActionEnterInfo(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionEnterInfo) action).setParam(getCurrentContext(),
                        getString(R.string.trans_inc_authorization),
                        transData.isDcc() ? getString(R.string.prompt_dcc_rrn) : getString(R.string.prompt_rrn),
                        transData.isDcc() ? transData.getHomeCurrencyAmount() : String.valueOf(transData.getAmount()),
                        transData.isDcc() ? transData.getHomeCurrency().getCode() : transData.getLocalCurrency().getCode(),
                        12, true, false);
            }
        });
        bind(State.ENTER_REF_NO.toString(), enterInfoAction, true);
        // emv action
        ActionEmvProcess emvProcessAction = new ActionEmvProcess(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEmvProcess) action).setParam(getCurrentContext(), emv, transData);
            }
        });
        bind(State.EMV_PROC.toString(), emvProcessAction);
        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), transData);
            }
        });

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);
        // signature action
        ActionSignature signatureAction = new ActionSignature(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSignature) action).setParam(getCurrentContext(), transData.isDcc() ? transData.getHomeCurrencyAmount() : String.valueOf(transData.getAmount()),
                        transData.isDcc() ? transData.getHomeCurrency().getCode() : transData.getLocalCurrency().getCode(),getString(R.string.trans_inc_authorization));
            }
        });
        bind(State.SIGNATURE.toString(), signatureAction);

        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionOfflineSend) action).setParam(getCurrentContext(), transData);
            }
        });
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);

        //print preview action
        PrintTask printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(DCCIncAuthorizationTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        // execute the first action
        gotoState(State.ENTER_AMOUNT.toString());
    }

    enum State {
        ENTER_AMOUNT,
        CHECK_CARD,
        /**
         * inquiry currency ratio
         */
        ENQUIRY,
        /**
         * select currency(local currency or foreign currency)
         */
        SELECT_CURRENCY,
        ENTER_REF_NO,
        EMV_PROC,
        MAG_ONLINE,
        SIGNATURE,
        OFFLINE_SEND,
        PRINT,
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        int ret = result.getRet();
        State state = State.valueOf(currentState);
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType,transData.isDcc(), true, FinancialApplication.getAcqManager().getECardType(transData.getPan()));
            if (f55Dup.length > 0) {
                TransData dupTransData = FinancialApplication.getTransDataDbHelper().findFirstDupRecord();
                if (dupTransData != null) {
                    dupTransData.setDupIccData(Utils.bcd2Str(f55Dup));
                    FinancialApplication.getTransDataDbHelper().updateTransData(dupTransData);
                }
            }
            if (ret == TransResult.NEED_FALL_BACK) {
                needFallBack = true;
                searchCardMode &= 0x01;
                gotoState(State.CHECK_CARD.toString());
                return;
            } else if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }

        switch (state) {
            case ENTER_AMOUNT:// 输入交易金额后续处理
                // save amount
                transData.setAmount((long) result.getData());
                gotoState(State.CHECK_CARD.toString());
                break;
            case CHECK_CARD: // 检测卡的后续处理
                onCheckCard(result);
                break;
            case ENQUIRY: // check currency ratio
                onEnquiry(ret);
                break;
            case SELECT_CURRENCY: // for customer to choose use Dcc or Dcd
                onSelectCurrency(result);
                break;
            case ENTER_REF_NO: // enter rrn
                onEnterRefNo(result);
                break;
            case MAG_ONLINE: // after online
                // judge whether need signature or print
                checkOfflineTrans();
                break;
            case EMV_PROC: // emv后续处理
                afterEMVProcess(result);
                break;
            case OFFLINE_SEND: // send offline transaction
                toSignOrPrint();
                break;
            case SIGNATURE:
                onSignature(result);
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transType.getTransName(), result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }

    }

    private void onCheckCard(ActionResult result) {
        ActionSearchCard.CardInformation cardInfo = (ActionSearchCard.CardInformation) result.getData();
        saveCardInfo(cardInfo, transData);

        byte mode = cardInfo.getSearchMode();
        if (mode == ActionSearchCard.SearchMode.SWIPE || mode == ActionSearchCard.SearchMode.KEYIN) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
            transData.setAcquirer(acquirer);
            if (!Utils.checkAcquirerSettleStatus(acquirer.getName())) {
                transEnd(new ActionResult(TransResult.SETTLEMENT_PENDING, null));
                return;
            }
        }

        transData.setTransType(ETransType.INC_AUTHORIZATION);
        if (needFallBack) {
            transData.setEnterMode(TransData.EnterMode.FALLBACK);
        }

        if (mode == ActionSearchCard.SearchMode.KEYIN || mode == ActionSearchCard.SearchMode.SWIPE) {
            if (transData.getIssuer() == null || !Component.chkIssuerOption(transType, transData.getIssuer())) {
                transEnd(new ActionResult(TransResult.ERR_NOT_SUPPORT_TRANS, null));
                return;
            }
            toEnquiryOrEnterPin();
        } else if (mode == ActionSearchCard.SearchMode.INSERT || mode == ActionSearchCard.SearchMode.WAVE) {
            needRemoveCard = true;
            // EMV处理
            gotoState(State.EMV_PROC.toString());
        }
    }

    //go enquiry, check currency ratio
    private void toEnquiryOrEnterPin() {
        if (FinancialApplication.getBaseManager().checkDccAllowed(transData)) {
            transData.setOrigAcquirer(transData.getAcquirer());
            //check card bin
            int ret = Component.checkCardBin(transData.getPan());
            //local card bin can not do dcc
            if (ret < 0) {
                if (ret == TransResult.ERR_NO_CARD_BIN) {
                    DialogUtils.showErrMessage(getCurrentContext(), transData.getTransType().getTransName(), TransResultUtils.getMessage(TransResult.ERR_NO_CARD_BIN),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialogInterface) {
                                    toDoDcc();
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    transEnd(new ActionResult(ret, null));
                }
                return;
            } else if (ret == 0) {
                //for Macao transfer to PP host and change trans data setting
                if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData)){
                    Component.macaoTransSetting(transData);
                }
                //go DCD
                Component.transUpdate(transData);
                gotoState(State.MAG_ONLINE.toString());
                return;
            }
            toDoDcc();
        } else {
            //for Macao transfer to PP host and change trans data setting
            if (FinancialApplication.getBaseManager().isMacaoLocalTrans(transData)){
                Component.macaoTransSetting(transData);
            }
            // enter pin or not
            gotoState(State.MAG_ONLINE.toString());
        }
    }

    private void toDoDcc() {
        //enquiry rate
        Component.dccTransInit(transData);
        transData.setTransType(ETransType.ENQUIRY);
        gotoState(State.ENQUIRY.toString());
    }

    private void onEnquiry(int ret) {
        //enquiry failed, run dcd
        transData.setTransType(ETransType.INC_AUTHORIZATION);
        if (ret != TransResult.SUCC) {
            if (ret < 0) {
                transEnd(new ActionResult(ret, null));
                return;
            }
            //for Macao transfer to PP host and change trans data setting
            if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData)){
                Component.macaoTransSetting(transData);
            }
            Component.transUpdate(transData);
            gotoState(State.MAG_ONLINE.toString());
            return;
        }

        if (!(isEcr && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_ECR_CHOOSE_DCC))) {
            gotoState(State.SELECT_CURRENCY.toString());
            return;
        }

        //ecr
        if (transListener == null) {
            transEnd(new ActionResult(TransResult.ERR_ECR, null));
            return;
        }
        transListener.onEnd(new ActionResult(TransResult.SUCC_ECR_SELECT_CURRENCY, transData, dccListener));
        waitForEcrSelect(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                if (ecrResult != 0) {
                    //ecr err
                    transEnd(new ActionResult(TransResult.ERR_ECR, null));
                    return;
                }
                //after selecting currency, to enter pin
                gotoState(State.MAG_ONLINE.toString());
            }
        });
    }

    private void onSelectCurrency(ActionResult result) {
        // get whether customer select local currency or dcc
        String temp = (String) result.getData();
        if (temp.equals(SelectCurrencyActivity.LOCAL_CURRENCY)) { // DCD transaction
            transData.setIsFullOutSource(FinancialApplication.getBaseManager().isFullOutsorceMode(transData));
            if (transData.isFullOutSource()) {
                FinancialApplication.getBaseManager().setMacaoLocalEmvData(transData); //update macao trans data
                Component.macaoAcqUpdate(transData);
            }
            Component.transUpdate(transData);
        } else { //DCC currency
            transData.setHomeCurrencyTipAmount(Component.localAmountToHomeAmount(transData.getHomeCurrency(), String.valueOf(transData.getTipAmount()), transData.getRate()));
        }
        gotoState(State.ENTER_REF_NO.toString());
    }

    private void onEnterRefNo(ActionResult result) {
        refNo = (String) result.getData();
        transData.setRefNo(refNo);
        if (transData.getTransType() == ETransType.INC_AUTHORIZATION) {
            TransData origTransData = FinancialApplication.getTransDataDbHelper().findTransDataByRefNo(refNo);
            if (origTransData != null) {
                transData.setOrigRefNo(refNo);
                transData.setOrigAuthCode(origTransData.getAuthCode());
            }
        }
        gotoState(State.MAG_ONLINE.toString());
    }

    private void afterEMVProcess(ActionResult result) {
        ETransResult transResult = ((CTransResult) result.getData()).getTransResult();
        // EMV完整流程 脱机批准或联机批准都进入签名流程
        EmvTransProcess.emvTransResultProcess(transResult, emv, transData);
        if (transResult == ETransResult.ONLINE_APPROVED) {// 联机批准
            checkOfflineTrans();
        } else if (transResult == ETransResult.OFFLINE_APPROVED) {//脱机批准处理
            // judge whether need signature or print
            toSignOrPrint();
        } else {
            transactionFailProcess(transResult);
        }
    }

    // 判断是否需要电子签名或打印
    private void toSignOrPrint() {
        if (Component.isSignatureFree(transData)) {// 免签
            // 打印
            transData.setSignFree(true);
            gotoState(State.PRINT.toString());
        } else {
            // 电子签名
            transData.setSignFree(false);
            gotoState(State.PRINT.toString());//恒生要求移除Signature流程，但是数据库中的signFree需保留置位
        }
        FinancialApplication.getTransDataDbHelper().updateTransData(transData);
    }

    private void onSignature(ActionResult result) {
        // save signature data
        byte[] signData = (byte[]) result.getData();
        byte[] signPath = (byte[]) result.getData1();

        if (signData != null && signData.length > 0 &&
                signPath != null && signPath.length > 0) {
            transData.setSignData(signData);
            transData.setSignPath(signPath);
            // update trans data，save signature
            FinancialApplication.getTransDataDbHelper().updateTransData(transData);
        }

        // if terminal does not support signature ,card holder does not sign or time out，print preview directly.
        gotoState(State.PRINT.toString());
    }

    private void checkOfflineTrans() {
        if ("HASE_OLS".equals(transData.getAcquirer().getName())) {
            toSignOrPrint();
            return;
        }
        gotoState(State.OFFLINE_SEND.toString());
    }
}
