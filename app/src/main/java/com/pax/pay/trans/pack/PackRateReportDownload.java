/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-4-23
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import com.pax.abl.core.ipacker.PackListener;

public class PackRateReportDownload extends PackIso8583 {

    /**
     * PackRateReportDownload
     */
    public PackRateReportDownload(PackListener listener) {
        super(listener);
    }

    /**
     * pack rate report download required iso8583 fields
     */
    @Override
    protected int[] getRequiredFields() {
        return new int[] { 3, 11, 24, 41, 42, 63 };
    }
}
