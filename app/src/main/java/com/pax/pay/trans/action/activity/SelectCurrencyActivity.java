/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-10-25
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action.activity;

import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

public class SelectCurrencyActivity extends BaseActivityWithTickForAction {

    private String title;
    private String amount;
    private String rate;
    private String homeCurrencyAmount;
    private String homeCurrencySymbol;
    private String localCurrencySymbol;
    private int localFlagImageId;
    private int homeFlagImageId;
    private boolean isVisaCard;
    private String dccMarkupRate;

    private LinearLayout localCurrencyLL;
    private LinearLayout homeCurrencyLL;

    private boolean isSymbolNegative;

    public static final String LOCAL_CURRENCY = "local currency";
    public static final String HOME_CURRENCY = "home currency";

    @Override
    protected void loadParam() {
        title = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        amount = getIntent().getStringExtra(EUIParamKeys.TRANS_AMOUNT.toString());
        rate = getIntent().getStringExtra(EUIParamKeys.TRANS_RATE.toString());
        homeCurrencyAmount = getIntent().getStringExtra(EUIParamKeys.TRANS_HOME_CURRENCY_AMOUNT.toString());
        homeCurrencySymbol = getIntent().getStringExtra(EUIParamKeys.TRANS_HOME_CURRENCY_SYMBOL.toString());
        localCurrencySymbol = getIntent().getStringExtra(EUIParamKeys.TRANS_LOCAL_CURRENCY_SYMBOL.toString());
        localFlagImageId = getIntent().getIntExtra(EUIParamKeys.TRANS_LOCAL_FLAG_IMAGE_ID.toString(), 0);
        homeFlagImageId = getIntent().getIntExtra(EUIParamKeys.TRANS_HOME_FLAG_IMAGE_ID.toString(), 0);
        isVisaCard = getIntent().getBooleanExtra(EUIParamKeys.IS_VISA_CARD.toString(), false);
        dccMarkupRate = getIntent().getStringExtra(EUIParamKeys.DCC_MARKUP_RATE.toString());
        isSymbolNegative = getIntent().getBooleanExtra(EUIParamKeys.IS_SYMBOL_NEGATIVE.toString(), false);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_select_currency;
    }

    @Override
    protected String getTitleString() {
        return title;
    }

    @Override
    protected void initViews() {
        TextView localCurrencyAmount = (TextView) findViewById(R.id.local_currency_amount);
        localCurrencyAmount.setText(CurrencyConverter.convert(Utils.parseLongSafe(amount, 0), isSymbolNegative));
        TextView rateTv = (TextView) findViewById(R.id.rate);
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_INVERSE_RATE) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC)) {
            //rateTv.setText(Utils.inverseRate(rate, 8) + " " + homeCurrencySymbol + "/" + localCurrencySymbol);
            rateTv.setText("1 " + homeCurrencySymbol + " = " + Utils.inverseRate(rate, 8) + " " + localCurrencySymbol);
        } else {
            //rateTv.setText(Utils.initRate(rate) + " " + localCurrencySymbol + "/" + homeCurrencySymbol);
            rateTv.setText("1 " + localCurrencySymbol + " = " + Utils.initRate(rate) + " " + homeCurrencySymbol);
        }

        TextView feeRateTv = (TextView) findViewById(R.id.fee_rate);
        //check visa card
        if (isVisaCard && dccMarkupRate != null && !dccMarkupRate.isEmpty()) {
            feeRateTv.setText("(" + Utils.initMarkupRate(dccMarkupRate) + " " + getString(R.string.fee_rate) + ")");
            feeRateTv.setVisibility(View.VISIBLE);
        }

        TextView homeCurrencyAmountTv = (TextView) findViewById(R.id.home_currency_amount);
        long homeAmount = Utils.parseLongSafe(homeCurrencyAmount, 0);
        if (isSymbolNegative) {
            homeAmount = -homeAmount;
        }
        homeCurrencyAmountTv.setText(CurrencyConverter.convert(homeAmount, CurrencyCode.getBySymbol(homeCurrencySymbol)));
        //local currency LinearLayout
        localCurrencyLL = (LinearLayout) findViewById(R.id.local_currency_ll);
        //local currency symbol
        TextView localCurrencyButton = (TextView) findViewById(R.id.local_currency_button);
        localCurrencyButton.setText(localCurrencySymbol);
        //home currency LinearLayout
        homeCurrencyLL = (LinearLayout) findViewById(R.id.home_currency_ll);
        //home currency symbol
        TextView homeCurrencyButton = (TextView) findViewById(R.id.home_currency_button);
        homeCurrencyButton.setText(homeCurrencySymbol);
    }

    @Override
    protected void setListeners() {
        localCurrencyLL.setOnClickListener(this);
        homeCurrencyLL.setOnClickListener(this);
    }

    @Override
    public void onClickProtected(View v) {
        switch (v.getId()) {
            case R.id.local_currency_ll:
                //select local currency
                localCurrencyLL.setClickable(false);
                homeCurrencyLL.setClickable(false);
                finish(new ActionResult(TransResult.SUCC, LOCAL_CURRENCY));
                break;
            case R.id.home_currency_ll:
                //select home currency, go DCC
                localCurrencyLL.setClickable(false);
                homeCurrencyLL.setClickable(false);
                finish(new ActionResult(TransResult.SUCC, HOME_CURRENCY));
                break;
            default:
                break;
        }
    }

    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
