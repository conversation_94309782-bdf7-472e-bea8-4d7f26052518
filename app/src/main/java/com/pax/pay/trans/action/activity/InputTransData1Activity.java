/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import android.os.ConditionVariable;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.text.InputFilter;
import android.text.TextUtils;
import android.widget.TextView;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.eventbus.OnTransSuccessEvent;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.app.quickclick.QuickClickProtection;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionInputTransData.EInputType;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.RxUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.keyboard.CustomKeyboardEditText;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Locale;

public class InputTransData1Activity extends BaseActivityWithTickForAction {

    private String prompt;
    private String navTitle;

    private EInputType inputType;
    private boolean isPaddingZero;

    private int maxLen;
    private int minLen;

    private String followOrigData;
    private String errorPromMsg;

    private CustomKeyboardEditText mEditText = null;
    private boolean forbidBack;
    private TextToSpeech textToSpeech;
    ConditionVariable cv;
    private boolean needAudio;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getPreferences(MODE_PRIVATE).edit().remove("EditTextContent").apply();
        setEditText();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 保存输入框内容
        final String content = mEditText.getText().toString();
        getPreferences(MODE_PRIVATE).edit().putString("EditTextContent", content).apply();
    }


    // 每次退出当前页面都需要释放资源，防止下次进入多次播报
    @Override
    protected void onStop() {
        release();
        super.onStop();
    }

    // 重新进入页面之后需要重新加载资源
    @Override
    protected void onResume() {
        if (needAudio) {
            // 如果已经注册就不再注册
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
            FinancialApplication.getApp().runInBackground(() -> {
                if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO)) {
                    textToSpeech = new TextToSpeech(FinancialApplication.getApp(), status -> {
                        if (status != TextToSpeech.SUCCESS) {
                            // 初始化失败
                            LogUtils.d("TextToSpeech init is not success");
                            ToastUtils.showMessage(getString(R.string.trans_success_audio_init_fail));
                        } else {
                            // 配置tts
                            textToSpeech.setPitch(0.7f);
                            textToSpeech.setSpeechRate(1.2f);
                            textToSpeech.setLanguage(Locale.ENGLISH);
                            // 设置回调监听
                            textToSpeech.setOnUtteranceProgressListener(new UtteranceProgressListener() {
                                @Override
                                public void onStart(String utteranceId) {
                                    // do nothing
                                }

                                @Override
                                public void onDone(String utteranceId) {
                                    // 朗读完成之后释放资源
                                    if (textToSpeech != null) {
                                        textToSpeech.stop();
                                        textToSpeech.shutdown();
                                        cv.open();
                                    }
                                }

                                @Override
                                public void onError(String utteranceId) {
                                    if (textToSpeech != null) {
                                        textToSpeech.stop();
                                        textToSpeech.shutdown();
                                        cv.open();
                                    }
                                }
                            });
                        }
                    });
                }
            });
        }
        super.onResume();
        // 修复快速进入Pre-Auth Completion/Cancel点击返回被拦截无响应问题
        QuickClickProtection.getInstance().stop();
        // 恢复输入框内容
        final String content = getPreferences(MODE_PRIVATE).getString("EditTextContent", "");
        mEditText.setText(content);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString("EditTextContent", mEditText.getText().toString());
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        // 恢复输入框内容
        if (savedInstanceState != null) {
            String content = savedInstanceState.getString("EditTextContent");
            mEditText.setText(content);
        }
    }

    @Override
    protected int getLayoutId() {
        if (Utils.isA50()) {
            return R.layout.activity_input_trans_data1_a50;
        } else {
            return R.layout.activity_input_trans_data1;
        }
    }

    @Override
    protected void loadParam() {
        prompt = getIntent().getStringExtra(EUIParamKeys.PROMPT_1.toString());
        inputType = (EInputType) getIntent().getSerializableExtra(EUIParamKeys.INPUT_TYPE.toString());
        maxLen = getIntent().getIntExtra(EUIParamKeys.INPUT_MAX_LEN.toString(), 6);
        minLen = getIntent().getIntExtra(EUIParamKeys.INPUT_MIN_LEN.toString(), 0);
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        isPaddingZero = getIntent().getBooleanExtra(EUIParamKeys.INPUT_PADDING_ZERO.toString(), true);

        followOrigData = getIntent().getStringExtra(EUIParamKeys.ORIG_FOLLOW_DATA.toString());
        errorPromMsg = getIntent().getStringExtra(EUIParamKeys.ERROR_MSG.toString());
        needAudio = getIntent().getBooleanExtra(EUIParamKeys.NEED_AUDIO.toString(), false);
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        TextView promptText = findViewById(R.id.prompt_amount);
        promptText.setText(prompt);
    }

    private void setEditText() {
        if (EInputType.NUM == inputType) {
            setEditTextNum();
        }
        if (mEditText != null) {
            mEditText.setOnEditorActionListener(new EditorActionListener() {
                @Override
                protected void onKeyOk() {
                    // 点击OK之后开始拦截输入和更新返回的标志位，在回调方法中统一拦截
                    mEditText.setKeyboardEnable(false);
                    setForbidBack(true);
                    String content = process();
                    if (EInputType.NUM == inputType && minLen > 0 && TextUtils.isEmpty(content)) {
                        ToastUtils.showMessage(R.string.please_input_again);
                        setForbidBack(false);
                        mEditText.setKeyboardEnable(true);
                        return;
                    }
                    if (!TextUtils.isEmpty(followOrigData) && !followOrigData.equals(content)) {//有需要对照的原数据，且原数据跟输入的不相同
                        ToastUtils.showMessage(errorPromMsg);
                        setForbidBack(false);
                        mEditText.setKeyboardEnable(true);
                        return;
                    }

                    // 历史记录的查询原始订单无论输入结果如何都需要放开
                    if (Utils.getString(R.string.trans_history).equals(getTitleString())
                            || Utils.getString(R.string.sqr_history).equals(getTitleString())) {
                        setForbidBack(false);
                        mEditText.setKeyboardEnable(true);
                    }
                    finish(new ActionResult(TransResult.SUCC, content));
                }

                @Override
                protected void onKeyCancel() {
                    if (!forbidBack) {
                        forbidBack = true;
                        setForbidBack(true);
                        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
                    }
                }
            });
        }
    }

    // 数字
    private void setEditTextNum() {
        mEditText = findViewById(R.id.input_data_1);
        mEditText.requestFocus();
        mEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLen)});
    }

    @Override
    protected void setListeners() {
        // nothing
    }

    @Override
    protected boolean onKeyBackDown() {
        if (!forbidBack) {
            forbidBack = true;
            setForbidBack(true);
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        }
        return true;
    }

    /**
     * 输入数值检查
     */
    private String process() {
        String content = mEditText.getText().toString().trim();

        if (content.isEmpty()) {
            return null;
        }

        if (EInputType.NUM == inputType) {
            if (content.length() >= minLen && content.length() <= maxLen) {
                if (isPaddingZero) {
                    content = Component.getPaddedString(content, maxLen, '0');
                }
            } else {
                return null;
            }
        }
        return content;
    }

    private void release() {
        if (needAudio) {
            if (textToSpeech != null) {
                textToSpeech.setOnUtteranceProgressListener(null);
                textToSpeech.stop();
                textToSpeech.shutdown();
                textToSpeech = null;
            }
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
            RxUtils.release();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTransSuccess(OnTransSuccessEvent event) {
        if (textToSpeech != null) {
            // 播报语音内容
            textToSpeech.speak(getString(R.string.transaction_success_voice_text), TextToSpeech.QUEUE_FLUSH, null, "trans_success");
            //阻塞，等待语音播报完成之后再继续
            cv = new ConditionVariable();
            cv.close();
            cv.block();
        }
    }
}