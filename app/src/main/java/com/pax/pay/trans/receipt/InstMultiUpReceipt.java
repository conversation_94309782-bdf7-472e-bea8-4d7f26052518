package com.pax.pay.trans.receipt;

import com.pax.pay.trans.model.TransData;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

public class InstMultiUpReceipt extends RedeemInstReceipt {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     */
    public InstMultiUpReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isPrintPreview isPrintPreview
     */
    public InstMultiUpReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INST-MULTI-UP")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

}
