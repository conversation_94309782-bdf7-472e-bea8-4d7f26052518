package com.pax.pay.trans.action.activity;

import android.content.Context;
import android.os.Bundle;
import android.os.ConditionVariable;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.eventbus.OnTransSuccessEvent;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.utils.RxUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import java.util.Locale;

public class SelectChargeActivity extends BaseActivityWithTickForAction{
    private LinearLayout eChargeLayout;
    private LinearLayout physicalChargeLayout;
    private LinearLayout noChargeLayout;
    private String title;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_select_charge_slip;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        enableBackAction(false);
    }

    @Override
    protected void initViews() {
        eChargeLayout = findViewById(R.id.e_charge_slip);
        noChargeLayout = findViewById(R.id.no_charge_slip);
        physicalChargeLayout = findViewById(R.id.physical_charge_slip);
        if (Utils.isA50()) {
            physicalChargeLayout.setVisibility(View.GONE);
        }
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            eChargeLayout.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected boolean onKeyBackDown() {
        // 不允许返回
        return true;
    }

    @Override
    protected String getTitleString() {
        return title;
    }

    @Override
    protected void setListeners() {
        eChargeLayout.setOnClickListener(this);
        physicalChargeLayout.setOnClickListener(this);
        noChargeLayout.setOnClickListener(this);
    }

    @Override
    protected void loadParam() {
        title = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
    }


    protected void onClickProtected(View v) {
        switch (v.getId()){
            case R.id.e_charge_slip:
                finish(new ActionResult(TransResult.SUCC, Constants.E_CHARGE_SLIP));
                break;
            case R.id.physical_charge_slip:
                finish(new ActionResult(TransResult.SUCC, Constants.PHYSICAL_CHARGE_SLIP));
                break;
            case R.id.no_charge_slip:
                finish(new ActionResult(TransResult.SUCC, Constants.NO_CHARGE_SLIP));
                break;
        }
    }

}
