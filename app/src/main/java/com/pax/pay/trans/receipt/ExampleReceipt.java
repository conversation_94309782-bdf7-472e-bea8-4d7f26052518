package com.pax.pay.trans.receipt;


import com.pax.glwrapper.page.IPage;
import com.pax.pay.trans.model.TransData;

/**
 * Created by liliang on 3/19 0019.
 */

public class ExampleReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isPrintPreview isPrintPreview
     */
    public ExampleReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public String generateString() {
        return null;
    }

    @Override
    public void addContent(IPage page) {

    }
}
