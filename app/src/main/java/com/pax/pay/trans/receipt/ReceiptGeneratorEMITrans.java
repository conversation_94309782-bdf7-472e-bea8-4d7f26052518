/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import static com.pax.pay.constant.Constants.ISSUER_RUPAY;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import com.pax.commonbusiness.card.PanUtils;
import com.pax.commonlib.utils.FontCache;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransData.EnterMode;
import com.pax.eemv.enums.ETransResult;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.impl.GL;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.io.InputStream;

/**
 * receipt generator
 *
 * <AUTHOR>
 */
public class ReceiptGeneratorEMITrans implements IReceiptGenerator {

    private int receiptNo = 0;
    private TransData transData;
    private boolean isRePrint = false;
    private int receiptMax = 0;
    private boolean isPrintPreview = false;
    private Bitmap logo;
    private Bitmap signature;
    private boolean isVoidTrans = false;

    /**
     * @param transData        ：transData
     * @param currentReceiptNo : currentReceiptNo
     * @param receiptMax       ：generate which one, start from 0
     * @param isReprint        ：is reprint?
     */
    public ReceiptGeneratorEMITrans(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
    }

    public ReceiptGeneratorEMITrans(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint, boolean isPrintPreview) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
        this.isPrintPreview = isPrintPreview;
    }

    @Override
    public Bitmap generateBitmap() {
        View view = pageToView();
        view.setDrawingCacheEnabled(true); // 启用绘图缓存
        view.buildDrawingCache(); // 确保绘图缓存是最新的
        Bitmap bitmap = Bitmap.createBitmap(view.getDrawingCache()); // 通过复制绘图缓存来创建Bitmap，避免对原缓存的直接引用

        // 清理工作
        view.destroyDrawingCache(); // 清理绘图缓存
        view.setDrawingCacheEnabled(false); // 禁用绘图缓存

        return bitmap; // 返回创建的Bitmap
    }

    @NotNull
    public View pageToView() {
        boolean isPrintSign = false;
        // 生成第几张凭单不合法时， 默认0
        if (receiptNo > receiptMax) {
            receiptNo = 0;
        }

        // the first copy print signature, if three copies, the second copy should print signature too
        if (receiptNo == 0 || ((receiptMax == 3) && receiptNo == 1)) {
            isPrintSign = true;
        }

        IPage page = GL.getGL().getImgProcessing().createPage();
        page.adjustLineSpace(-9);
        page.setTypeFace(FontCache.get(Constants.PAID_FONT_NAME, FinancialApplication.getApp()));

        // transaction type
        ETransType transType = transData.getTransType();

        Acquirer acquirer = FinancialApplication.getAcqManager().getCurAcq();

        String temp;
        logo = getImageFromAssetsFile("pax_logo_boid_slogan.png");

        // title
        page.addLine()
                .addUnit(page.createUnit()
                        .setBitmap(logo)
                        .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //merchant information
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_NAME))
                        .setTextStyle(Typeface.BOLD)
                        .setGravity(Gravity.CENTER_HORIZONTAL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_ADDRESS))
                        .setTextStyle(Typeface.BOLD)
                        .setGravity(Gravity.CENTER_HORIZONTAL));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        if (isRePrint) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("----" + Utils.getString(R.string.receipt_print_again) + "----")
                            .setFontSize(FONT_BIG)
                            .setGravity(Gravity.CENTER));
        }

        String date = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                Constants.DATE_PATTERN);
        String time = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_transaction_date) + date)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_transaction_time) + time)
                        .setGravity(Gravity.END)
                        .setFontSize(FONT_SMALL));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        // merchant ID and TID
        String merchantId = acquirer.getMerchantId();
        String terminalId = acquirer.getTerminalId();

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_merchant_id) + merchantId)
                        .setWeight(3.0f)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_terminal_id) + terminalId)
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f)
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //Batch No and Invoice No
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_batch_no) + Component.getPaddedNumber(transData.getBatchNo(), 6))
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_invoice_no) + Component.getPaddedNumber(transData.getInvoiceNo(), 6))
                        .setGravity(Gravity.END)
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //transType
        String type = Utils.getString(R.string.emi_sale);
        TransData.ETransStatus transStatus = transData.getTransState();
        //if the transtype is void,should display the origin transdata
        if (TransData.ETransStatus.VOIDED.equals(transStatus) || ETransType.VOID.equals(transType)) {
            type = "Void " + type;
            isVoidTrans = true;
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(type)
                        .setGravity(Gravity.CENTER_HORIZONTAL)
                        .setFontSize(FONT_BIG));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        EnterMode enterMode = transData.getEnterMode();

        if ((enterMode == EnterMode.INSERT || enterMode == EnterMode.CLSS) &&
                (transType == ETransType.SALE || transType == ETransType.PREAUTH || transType == ETransType.CASH_ONLY || transType == ETransType.SALE_CASH
                        || transType == ETransType.QSPARC_MONEY_ADD_ACCOUNT || transType == ETransType.QSPARC_MONEY_ADD_CASH
                        || transType == ETransType.QSPARC_BALANCE_UPDATE || transType == ETransType.QSPARC_BALANCE_ENQUIRY
                        || transType == ETransType.PREAUTHCANCEL || transType == ETransType.REFUND || transType == ETransType.PREAUTHCOMPLETE || transType == ETransType.OFFLINE_TRANS_SEND
                        || transType == ETransType.ADJUST)) {

            //app name
            String appName = transData.getEmvAppName();
            boolean appNameEnable = !TextUtils.isEmpty(appName);
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(appNameEnable ? "APP NAME:" : "APP LABEL:")
                            .setFontSize(FONT_SMALL)
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(appNameEnable ? appName : transData.getEmvAppLabel())
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
            //appl(aid)
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_aid))
                            .setFontSize(FONT_SMALL)
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setFontSize(FONT_SMALL)
                            .setText(transData.getAid())
                            .setWeight(7.0f)
                            .setGravity(Gravity.END));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
            //TVR
            page.addLine()
                    .addUnit(page.createUnit()
                            .setFontSize(FONT_SMALL)
                            .setText("TVR:"))
                    .addUnit(page.createUnit()
                            .setFontSize(FONT_SMALL)
                            .setText(transData.getTvr())
                            .setGravity(Gravity.END));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
            //TSI
            page.addLine()
                    .addUnit(page.createUnit()
                            .setFontSize(FONT_SMALL)
                            .setText("TSI:"))
                    .addUnit(page.createUnit()
                            .setFontSize(FONT_SMALL)
                            .setText(transData.getTsi())
                            .setGravity(Gravity.END));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
            if (!(enterMode == EnterMode.CLSS && ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName()) && transData.getEmvResult() == ETransResult.ONLINE_APPROVED && !transData.isFullOnline())) {
                //Rupay 非接交易时，partial online的交易不需要打印TC
                page.addLine()
                        .addUnit(page.createUnit()
                                .setFontSize(FONT_SMALL)
                                .setText("TC:"))
                        .addUnit(page.createUnit()
                                .setFontSize(FONT_SMALL)
                                .setText(transData.getTc())
                                .setGravity(Gravity.END));
            }
        }

        //card no
        temp = PanUtils.maskCardNo(transData.getPan());

        String temp1 = "";
        if (enterMode == EnterMode.MANUAL) {
            temp1 = " MANUAL";
        } else if (enterMode == EnterMode.SWIPE) {
            temp1 = " SWIPE";
        } else if (enterMode == EnterMode.INSERT) {
            temp1 = " CHIP";
        } else if (enterMode == EnterMode.CLSS) {
            temp1 = " CLESS";
        } else if (enterMode == EnterMode.FALLBACK) {
            temp1 = " FALLBACK";
        } else if (enterMode == EnterMode.QR) {
            temp1 = " QRC";
        } else if (enterMode == EnterMode.BHARAT_QR) {
            temp1 = " MANUAL";
        }

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        page.addLine()
                .addUnit(page.createUnit()
                        .setFontSize(FONT_SMALL)
                        .setText(temp))
                .addUnit(page.createUnit()
                        .setText(temp1)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));
        //card type / entermode
        if (transData.getIssuer() != null) {
            type = transData.getIssuer().getName();
        } else {
            type = "NA";
        }
        temp1 = transData.getExpDate();
        if (!TextUtils.isEmpty(temp1)) {
            if (transData.getIssuer().isRequireMaskExpiry()) {
                temp1 = "**/**";
            } else {
                temp1 = temp1.substring(2) + "/" + temp1.substring(0, 2);// 将yyMM转换成MMyy
            }
        } else {
            temp1 = "";
        }
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_date) + temp1)
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_type) + type)
                        .setFontSize(FONT_SMALL)
                        .setWeight(3.0f)
                        .setGravity(Gravity.END));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // auth code / RRN
        temp = transData.getRefNo();
        if (temp == null) {
            temp = "";
        }

        temp1 = transData.getAuthCode();
        if (temp1 == null) {
            temp1 = "";
        }

        page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_ref_no) + temp)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_auth_code) + temp1)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        //Product Information
        if (ETransType.BRAND_EMI_SALE.equals(transType)) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_sign_line))
                    .setGravity(Gravity.CENTER));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.product_information))
                            .setGravity(Gravity.CENTER_HORIZONTAL)
                            .setFontSize(FONT_BIG));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.brand_name))
                            .setFontSize(FONT_SMALL)
                            .setWeight(3.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getBrandName() == null ? "" : transData.getBrandName())
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END)
                            .setWeight(4.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.prod_category))
                            .setFontSize(FONT_SMALL)
                            .setWeight(3.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getProdCategory() == null ? "" : transData.getProdCategory())
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END)
                            .setWeight(4.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.prod_serial))
                            .setFontSize(FONT_SMALL)
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getProdSerialNo() == null ? "" : transData.getProdSerialNo())
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END)
                            .setWeight(3.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.model_no))
                            .setFontSize(FONT_SMALL)
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getModelNo() == null ? "" : transData.getModelNo())
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END)
                            .setWeight(3.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.cust_mobile))
                            .setFontSize(FONT_SMALL)
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getCustomerMobile() == null ? "" : transData.getCustomerMobile())
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.END)
                            .setWeight(3.0f));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        }

        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));

        //EMI DETAILS
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.emi_details))
                        .setGravity(Gravity.CENTER_HORIZONTAL)
                        .setFontSize(FONT_BIG));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.tenure))
                        .setFontSize(FONT_SMALL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getTenure() == null ? "" : transData.getTenure() + " Months")
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.card_issuer))
                        .setFontSize(FONT_SMALL)
                        .setWeight(3.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getCardIssuer() == null ? "" : transData.getCardIssuer())
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.applicable_int_rate))
                        .setFontSize(FONT_SMALL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getEmiRate() == null ? "" : transData.getEmiRate() + "%")
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        if (ETransType.BRAND_EMI_SALE.equals(transType)) {
            temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getSubVentionAmt(), 0), transData.getCurrency());
            if (transData.getSubVentionAmt() != null && !"000".equals(transData.getSubVentionAmt())) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.cashback_amt))
                                .setFontSize(FONT_SMALL)
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
            }

            temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getCashBackAmount(), 0), transData.getCurrency());
            if (!transData.getCashBackAmount().isEmpty() && !"000".equals(transData.getCashBackAmount())) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.additional_cashback_discount_amt))
                                .setFontSize(FONT_SMALL)
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
            }
        }

        temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getAmount(), 0), transData.getCurrency());

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_amount_base))
                        .setFontSize(FONT_SMALL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getEmiLoanAmt(), 0), transData.getCurrency());

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.loan_amount))
                        .setFontSize(FONT_SMALL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getEmiAmt(), 0), transData.getCurrency());

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.emi_amount))
                        .setFontSize(FONT_SMALL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getEmiTotalAmt(), 0), transData.getCurrency());
        if (isVoidTrans) {
            temp = "-" + temp;
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.total_amt_with_int))
                        .setFontSize(FONT_SMALL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));

        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.emi_decaltion) + transData.getTermsCondition().replaceAll("\\n$", ""))
                .setFontSize(FONT_SMALL)
                .setGravity(Gravity.START));
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line)).setGravity(Gravity.CENTER));

        temp = CurrencyConverter.convert(Utils.parseLongSafe(transData.getAmount(), 0), transData.getCurrency());

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_amount_base))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        boolean isHasPin = transData.isHasPin();
        boolean isSignFree = transData.isSignFree();
        if (isHasPin) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_pin_verified))
                    .setGravity(Gravity.CENTER));
        }

        //signature
        if (!isSignFree && isPrintSign) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_sign))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.CENTER));
            signature = loadSignature(transData);
            if (signature != null) {
                page.addLine().addUnit(page.createUnit()
                        .setBitmap(loadSignature(transData))
                        .setGravity(Gravity.CENTER));
            } else {
                page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
            }

        }

        //verify prompt
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_verify))
                .setFontSize(18)//temporary change
                .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.greet_messsage))
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.CENTER));
        if (receiptMax == 3) {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_acquire))
                                .setGravity(Gravity.CENTER));
            } else if (receiptNo == 1) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_merchant))
                                .setGravity(Gravity.CENTER));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_user))
                                .setGravity(Gravity.CENTER));
            }
        } else {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_merchant))
                                .setGravity(Gravity.CENTER));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_user))
                                .setGravity(Gravity.CENTER));
            }
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getTerminalAndAppVersion())
                        .setGravity(Gravity.CENTER)
                        .setFontSize(FONT_SMALL));

        if (Component.isDemo()) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        if (!isPrintPreview) {
            page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        }

        IImgProcessing imgProcessing = GL.getGL().getImgProcessing();
        return imgProcessing.pageToView(page, 384);
    }

    private Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }

        return image;
    }

    private Bitmap loadSignature(TransData transData) {
        byte[] signData = transData.getSignData();
        if (signData == null) {
            return null;
        }
        return GL.getGL().getImgProcessing().jbigToBitmap(signData);
    }

    public void clear() {
        if (logo != null && !logo.isRecycled()) {
            logo.recycle();
            logo = null;
        }
        if (signature != null && !signature.isRecycled()) {
            signature.recycle();
            signature = null;
        }
    }
}
