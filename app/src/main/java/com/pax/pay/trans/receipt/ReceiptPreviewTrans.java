/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.receipt;

import android.graphics.Bitmap;
import com.pax.edc.R;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;

/**
 * generate bitmap image of print preview
 */

public class ReceiptPreviewTrans {

    /**
     * preview
     *
     * @param transData transData
     * @param listener listener
     * @return bitmap
     */
    public Bitmap preview(TransData transData, PrintListener listener) {

        Bitmap bitmap;

        if (listener != null) {
            listener.onShowMessage(null, Utils.getString(R.string.wait_receipt_generate));
        }

        switch (transData.getTransType()) {
            case PREAUTH:
                PreAuthReceipt preAuthReceipt = new PreAuthReceipt(transData, 0, 1, false, true);
                bitmap = preAuthReceipt.generateBitmap();
                break;
            case AUTHORIZATION:
                AuthorizationReceipt authorizationReceipt = new AuthorizationReceipt(transData, 0, 1, false, true);
                bitmap = authorizationReceipt.generateBitmap();
                break;
            case INC_AUTHORIZATION:
                IncAuthorizationReceipt incAuthorizationReceipt = new IncAuthorizationReceipt(transData, 0, 1, false, true);
                bitmap = incAuthorizationReceipt.generateBitmap();
                break;
            case AUTH_REVERSAL:
                AuthReversalReceipt authReversalReceipt = new AuthReversalReceipt(transData,0,1,false,true);
                bitmap = authReversalReceipt.generateBitmap();
                break;
            case OFFLINE_TRANS_SEND:
                OfflineReceipt offlineReceipt = new OfflineReceipt(transData, 0, 1, false, true);
                bitmap = offlineReceipt.generateBitmap();
                break;
            case PREAUTHVOID:
                PreAuthVoidReceipt preAuthVoidReceipt = new PreAuthVoidReceipt(transData, 0, 1, false, true);
                bitmap = preAuthVoidReceipt.generateBitmap();
                break;
            case PREAUTHCM:
            /*Raymond 20220714: remove the obsolete ETransType
            case PREAUTH_COMP:*/
                PreAuthComReceipt preAuthComReceipt = new PreAuthComReceipt(transData, 0, 1, false, true);
                bitmap = preAuthComReceipt.generateBitmap();
                break;
            case PREAUTHCMVOID:
            /*Raymond 20220714: remove the obsolete ETransType
            case PREAUTH_COMP_VOID:*/
                PreAuthComVoidReceipt preAuthComVoidReceipt = new PreAuthComVoidReceipt(transData, 0, 1, false, true);
                bitmap = preAuthComVoidReceipt.generateBitmap();
                break;
            case SALE:
                SaleReceipt saleReceipt = new SaleReceipt(transData, 0, 1, false, true);
                bitmap = saleReceipt.generateBitmap();
                break;
            case VOID:
                VoidReceipt voidReceipt = new VoidReceipt(transData, 0, 1, false, true);
                bitmap = voidReceipt.generateBitmap();
                break;
            case REFUND:
                RefundReceipt refundReceipt = new RefundReceipt(transData, 0, 1, false, true);
                bitmap = refundReceipt.generateBitmap();
                break;
            case INSTALMENT:
                InstalmentReceipt instalmentReceipt = new InstalmentReceipt(transData, 0, 1, false, true);
                bitmap = instalmentReceipt.generateBitmap();
                break;
            case SALES_WITH_CASH_DOLLAR:
                LoyaltySaleReceipt loyaltySaleReceipt = new LoyaltySaleReceipt(transData, 0, 1, false, true);
                bitmap = loyaltySaleReceipt.generateBitmap();
                break;
            case VOID_WITH_CASH_DOLLAR:
                LoyaltyVoidReceipt loyaltyVoidReceipt = new LoyaltyVoidReceipt(transData, 0, 1, false, true);
                bitmap = loyaltyVoidReceipt.generateBitmap();
                break;
            case ONLINE_ENQUIRY:
                RedeemEnquiryReceipt olineEnquiry = new RedeemEnquiryReceipt(transData, 0, 1, false, true);
                bitmap = olineEnquiry.generateBitmap();
                break;
            case PURE_REDEEM:
                PureRedeemReceipt pureRedeemReceipt = new PureRedeemReceipt(transData, 0, 1, false, true);
                bitmap = pureRedeemReceipt.generateBitmap();
                break;
            case MULTIPLE_UP:
                MultipleUpReceipt multipleUpReceipt = new MultipleUpReceipt(transData, 0, 1, false, true);
                bitmap = multipleUpReceipt.generateBitmap();
                break;
            case REDEEM_INST:
                RedeemInstReceipt redeemInstReceipt = new RedeemInstReceipt(transData, 0, 1, false, true);
                bitmap = redeemInstReceipt.generateBitmap();
                break;
            case INST_MULTI_UP:
                InstMultiUpReceipt instMultiUpReceipt = new InstMultiUpReceipt(transData, 0, 1, false, true);
                bitmap = instMultiUpReceipt.generateBitmap();
                break;
            case DCC_OPT_OUT:
                DccOptOutReceipt dccOptOutReceipt = new DccOptOutReceipt(transData, 0, 1, false, true);
                bitmap = dccOptOutReceipt.generateBitmap();
                break;
            default:
                ReceiptGeneratorTrans receiptGeneratorTrans = new ReceiptGeneratorTrans(transData, 0, 1, false, true);
                bitmap = receiptGeneratorTrans.generateBitmap();
        }

        if (listener != null) {
            listener.onEnd();
        }

        return bitmap;
    }
}
