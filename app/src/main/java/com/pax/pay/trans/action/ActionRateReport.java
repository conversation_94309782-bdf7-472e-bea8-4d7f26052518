/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-4-23
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.TransData;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.transmit.Online;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.ResponseCode;
import com.pax.pay.utils.TransResultUtils;
import com.pax.sbipay.R;
import com.pax.view.dialog.DialogUtils;

public class ActionRateReport extends AAction {
    private Context context;
    private TransData transData;
    private StringBuilder str = new StringBuilder();

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionRateReport(ActionStartListener listener) {
        super(listener);
    }

    /**
     * Sets param.
     *
     * @param context the context
     */
    public void setParam(Context context) {
        this.context = context;
    }

    @Override
    protected void process() {
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                initTransData();
                downloadRateReport();

            }
        });
    }

    private void initTransData() {
        transData = new TransData();
        transData.setAcquirer(FinancialApplication.getAcqManager().getCurAcq());
        Component.transInit(transData);
        transData.setTransType(ETransType.DOWNLOAD_DCC_MASSAGE);
        transData.setDcc(true);
    }

    private void downloadRateReport() {
        int ret;
        TransProcessListener transProcessListenerImpl = new TransProcessListenerImpl(context);
        boolean downloadResult = false;
        while (true) {
            transData.setTraceNo(Component.getTransNo());
            try {
                ret = new Online().online(transData, transProcessListenerImpl, 0);
                transProcessListenerImpl.onHideProgress();
                ResponseCode responseCode = transData.getResponseCode();
                if (ret == TransResult.SUCC && responseCode != null) {
                    //00 – if download complete
                    //05 – if download pending
                    if ("05".equals(responseCode.getCode())) {
                        parseField63();
                    } else {
                        if ("00".equals(responseCode.getCode())) {
                            downloadResult = true;
                            parseField63();
                        } else {
                            ret = TransResult.ERR_HOST_REJECT;
                        }
                        break;
                    }
                } else {
                    break;
                }
            } catch (PedDevException e) {
                LogUtils.e(TAG, "", e);
                ret = TransResult.ERR_CONNECT;
                break;
            }
        }

        if (downloadResult) {
            DialogUtils.showSuccMessage(context, context.getString(R.string.trans_dcc_download), null,
                    new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            setResult(new ActionResult(TransResult.SUCC, str));
                        }
                    }, Constants.SUCCESS_DIALOG_SHOW_TIME);
            return;
        }

        DialogUtils.showErrMessage(context, null,
                TransResultUtils.getMessage(ret),
                dialog -> setResult(new ActionResult(TransResult.ERR_ABORTED, null)), Constants.FAILED_DIALOG_SHOW_TIME);
    }

    private void parseField63() {
        String field63 = transData.getField63();
        if (TextUtils.isEmpty(field63)) {
            return;
        }
        String[] split = field63.split("\\|");
        if (split.length > 0) {
            str.append(field63);
        }
    }
}
