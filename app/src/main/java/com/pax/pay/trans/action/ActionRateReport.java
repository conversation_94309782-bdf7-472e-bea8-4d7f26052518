/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-4-23
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.DialogInterface;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.receipt.PrintListenerImpl;
import com.pax.pay.trans.receipt.ReceiptPrintRateReport;
import com.pax.pay.trans.transmit.Online;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

public class ActionRateReport extends AAction {
    private Context context;
    private TransData transData;
    private String printContent;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionRateReport(ActionStartListener listener) {
        super(listener);
    }

    /**
     * Sets param.
     *
     * @param context the context
     */
    public void setParam(Context context) {
        this.context = context;
    }

    @Override
    protected void process() {
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                initTransData();
                downloadRateReport();
            }
        });
    }

    private void initTransData() {
        transData = new TransData();
        transData.setAcquirer(FinancialApplication.getAcqManager().findAcquirer(context.getString(R.string.hase_dcc)));
        Component.transInit(transData);
        transData.setTransType(ETransType.DOWNLOAD_RATE_REPORT);
        transData.setDcc(true);
        transData.setField3("010001");
    }

    private void downloadRateReport() {
        int ret;
        int printResult = 0;
        TransProcessListener transProcessListenerImpl = new TransProcessListenerImpl(context);
        PrintListenerImpl listener = new PrintListenerImpl(context);
        boolean downloadResult;
        while (true) {
            ret = new Online().online(transData, transProcessListenerImpl);
            transProcessListenerImpl.onHideProgress();
            downloadResult = transData.getResponseCode() != null && "00".equals(transData.getResponseCode()) && ret == TransResult.ERR_PROC_CODE;
            if (ret == TransResult.SUCC || downloadResult) {
                parseField63();
                printResult = new ReceiptPrintRateReport().print(printContent, listener);
            }
            if (ret != TransResult.SUCC || printResult != 0) {
                break;
            }
        }
        if (downloadResult) {
            DialogUtils.showSuccMessage(context, context.getString(R.string.trans_rate_report_download), null,
                    new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            setResult(new ActionResult(TransResult.SUCC, null));
                        }
                    }, Constants.SUCCESS_DIALOG_SHOW_TIME);
            return;
        }
        DialogUtils.showErrMessage(context, null,
                printResult == 0 ? context.getString(R.string.dialog_download_rate_err) : "print error",
                new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        setResult(new ActionResult(TransResult.ERR_ABORTED, null));
                    }
                }, Constants.FAILED_DIALOG_SHOW_TIME);
    }

    private void parseField63() {
        String field63 = transData.getField63();
        if (field63 == null || field63.isEmpty()) {
            return;
        }

        //tag 05
        transData.setTag05(Component.parseDccField63(field63, "3035"));
        //tag 06
        String tag06 = Component.parseDccField63(field63, "3036");
        if (tag06 == null || tag06.isEmpty()) {
            return;
        }
            printContent = new String(FinancialApplication.getConvert().strToBcd(tag06.substring(0, tag06.length() - 6), IConvert.EPaddingPosition.PADDING_LEFT));
    }
}
