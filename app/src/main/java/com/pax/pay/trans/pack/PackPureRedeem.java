package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.RedeemConfig;
import com.pax.pay.trans.model.TransData;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/19.
 */

public class PackPureRedeem extends PackSalesWithCashDollar {
    public PackPureRedeem(PackListener listener) {
        super(listener);
    }

    /**
     * setBitData63
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        String airLineTicketNumber = "";
        String f63;
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (enterMode == TransData.EnterMode.INSERT
                || enterMode == TransData.EnterMode.CLSS
                || enterMode == TransData.EnterMode.FALLBACK) {
            airLineTicketNumber = Component.getPaddedString(" ", 16, ' ');
        }
        String tableLength = new String(new byte[] { 0x00, 0x03 });
        String tableID = "24";
        String terminalEntryCapability = getTermEntryCap(transData);
        String loyaltyInfoFlag = "L";
        String redemptionFlag = RedeemConfig.getPureRememptionFlag();
        String specRedmFuncFlag = transData.getSpcRedemptionFuncFlag();
        if(enterMode == TransData.EnterMode.SWIPE || enterMode == TransData.EnterMode.MANUAL){
            f63 = loyaltyInfoFlag + redemptionFlag + specRedmFuncFlag;
        }else{
            f63 = airLineTicketNumber + tableLength + tableID + terminalEntryCapability + loyaltyInfoFlag + redemptionFlag + specRedmFuncFlag;
        }
        setBitData("63", f63);
        transData.setField63(f63);
    }
}
