/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.ResponseCode;

import androidx.annotation.NonNull;

public class PackBatchUp extends PackIso8583 {

    public PackBatchUp(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{2, 3, 4, 11, 12, 13, 22, 24, 25, 37, 38, 39, 41, 42, 49, 53, 56, 62};
    }

    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("3", transData.getProcCode());
    }

    @Override
    protected void setBitData25(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("25", transData.getOrigTransType().getServiceCode());
    }

    @Override
    protected void setBitData38(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("38", transData.getAuthCode());
    }

    @Override
    protected void setBitData39(@NonNull TransData transData) throws Iso8583Exception {
        ResponseCode responseCode = transData.getResponseCode();
        if (responseCode != null && responseCode.getCode() != null) {
            setBitData("39", responseCode.getCode());
        }
    }

    @Override
    protected void setBitData49(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
            setBitData("49", Component.getPaddedNumber(Integer.parseInt(transData.getInternationalCurrency().getCode()), 3));
        }
    }

}
