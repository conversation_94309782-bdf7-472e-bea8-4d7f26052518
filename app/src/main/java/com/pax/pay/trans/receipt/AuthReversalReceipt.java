package com.pax.pay.trans.receipt;

import com.pax.pay.trans.model.TransData;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

/**
 * Created by terry on 2019/01/09.
 */

public class AuthReversalReceipt extends IncAuthorizationReceipt {

    public AuthReversalReceipt(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    public AuthReversalReceipt(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint,
            boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    /**
     * overwirte trans title
     */
    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("AUTH REV 授權撤銷")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }
}
