/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2024-4-23
 * Module Author: wangyq
 * Description:
 *
 * ============================================================================
 */

package com.pax.pay.trans.action;

import android.content.Context;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.data.local.db.helper.SQRDataDbHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.view.dialog.DialogUtils;

import java.lang.ref.WeakReference;
import java.util.List;

public class ActionClearBatch extends AAction {
    private WeakReference<Context> context;

    /**
     * 子类构造方法必须调用super设置ActionStartListener
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionClearBatch(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context) {
        this.context = new WeakReference<>(context);
    }

    @Override
    protected void process() {
        FinancialApplication.getApp().runInBackground(() -> {

            List<TransData> transDataList = GreendaoHelper.getTransDataHelper().findAllTransData(true);
            List<SQRData> sqrDataList = SQRDataDbHelper.getInstance().queryAllSQRData();
            if (transDataList.isEmpty() && sqrDataList.isEmpty()) {
                DialogUtils.showErrMessage(context.get(), "", Utils.getString(R.string.no_batch_clear), null, Constants.FAILED_DIALOG_SHOW_TIME);
            } else {
                FinancialApplication.getController().set(Controller.BATCH_UP_STATUS, Controller.Constant.WORKED);
                boolean isDone = GreendaoHelper.getTransDataHelper().deleteAllTransData();
                boolean isMQTTDeleted = SQRDataDbHelper.getInstance().deleteAllSQRData();
                FinancialApplication.getController().set(Controller.CLEAR_LOG, Controller.Constant.NO);
                boolean result = isDone && isMQTTDeleted;
                if (result) {
                    DialogUtils.showSuccMessage(context.get(), Utils.getString(R.string.om_clearTrade_menu_trade_voucher) + " Successful", null, Constants.SUCCESS_DIALOG_SHOW_TIME);
                    setResult(new ActionResult(TransResult.SUCC, null));
                } else {
                    setResult(new ActionResult(TransResult.ERR_NO_TRANS, null));
                }
            }
        });
    }
}
