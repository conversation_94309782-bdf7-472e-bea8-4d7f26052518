/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-1-6
 * Module Author: Frose.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.model;

import android.util.Log;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.AcqIssuerRelation;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.CardRange;
import com.pax.pay.base.Issuer;
import com.pax.pay.base.YUUCardRange;
import com.pax.pay.db.AcqDb;
import com.pax.pay.trans.component.Component;
import com.pax.settings.SysParam;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Acq manager.
 */
public class AcqManager {

    private static final String TAG = "AcqManager";
    private static final String ACQUIRER_DCC = "DCC";

    private static AcqManager acqmanager;
    private static AcqDb acqDbHelper;
    private static final Map<String, String> issuerAcqMap = new HashMap<>();
    private Acquirer acquirer;

    static {
        issuerAcqMap.put("HASE_VISA", "HASE_OLS");
        issuerAcqMap.put("HASE_MC", "HASE_OLS");
        issuerAcqMap.put("HASE_CUP", "HASE_OLS");

        issuerAcqMap.put("VISA", "HASE_EMV");
        issuerAcqMap.put("MASTER", "HASE_EMV");
        issuerAcqMap.put("JCB", "HASE_EMV");

        issuerAcqMap.put("AMEX", "AMEX");

        issuerAcqMap.put("UnionPay", "HASE_CUP");

        /** Normal HASE_ENJ + HASE_ENJ_E **/
        //HASE_ENJ是指能夠執行Loyalty的卡種類
        //而HASE_ENJ_E是指不能執行Loyalty的卡種類. 並因為2種Issuer都屬於HASE卡, 所以在Instalment上亦支援這2種卡種類.
        issuerAcqMap.put("HASE_ENJ", "HASE_OLS");
        issuerAcqMap.put("HASE_ENJ_E", "HASE_EMV");
    }

    private static final Map<String, String> issuerInsAcqMap = new HashMap<>();

    static {
        issuerInsAcqMap.put("HASE_VISA", "HASE_INS");
        issuerInsAcqMap.put("HASE_MC", "HASE_INS");
        issuerInsAcqMap.put("HASE_CUP", "HASE_INS");

        /** Instalment HASE_ENJ + HASE_ENJ_E **/
        issuerInsAcqMap.put("HASE_ENJ", "HASE_INS");
        issuerInsAcqMap.put("HASE_ENJ_E", "HASE_INS");
    }

    private static final Map<String, String> issuerDccAcqMap = new HashMap<>();

    static {
        issuerDccAcqMap.put("VISA", "HASE_DCC");
        issuerDccAcqMap.put("MASTER", "HASE_DCC");
        issuerDccAcqMap.put("JCB", "HASE_DCC");

        issuerDccAcqMap.put("AMEX", "AMEX");
    }

    private static final Map<String, String> issuerMacaoAcqMap = new HashMap<>();

    static {
        issuerMacaoAcqMap.put("VISA", "HASE_MACAO");
        issuerMacaoAcqMap.put("MASTER", "HASE_MACAO");
        issuerMacaoAcqMap.put("JCB", "HASE_MACAO");
        issuerMacaoAcqMap.put("HASE_VISA", "HASE_MACAO");
        issuerMacaoAcqMap.put("HASE_MC", "HASE_MACAO");
        issuerMacaoAcqMap.put("HASE_JCB", "HASE_MACAO");
        issuerMacaoAcqMap.put("HASE_ENJ", "HASE_MACAO");
        issuerMacaoAcqMap.put("HASE_ENJ_E", "HASE_MACAO");

        issuerMacaoAcqMap.put("HASE_CUP", "HASE_OLS");

        issuerMacaoAcqMap.put("UnionPay", "HASE_CUP");

        issuerMacaoAcqMap.put("AMEX", "AMEX");
    }

    /**
     * Gets instance.
     *
     * @return the instance
     */
    public static synchronized AcqManager getInstance() {
        if (acqmanager == null) {
            acqmanager = new AcqManager();
            init();
        }
        return acqmanager;
    }

    /**
     * 根据Issuer查找Acquirer,
     * 不支持{@link ETransType#INSTALMENT}, {@link ETransType#AUTHORIZATION},
     * {@link ETransType#OFFLINE_TRANS_SEND}
     *
     * @param issuer the issuer
     * @param transType the trans type
     * @return the acquirer
     */
    public Acquirer findAcquirer(Issuer issuer, ETransType transType) {
        if (transType == ETransType.INSTALMENT
                || transType == ETransType.AUTHORIZATION
                /*Raymond 20220708: bug fix INC AUTH reject chip card transaction
                || transType == ETransType.INC_AUTHORIZATION*/
                || transType == ETransType.OFFLINE_TRANS_SEND) {
            throw new RuntimeException("Method findAcquirer(Issuer issuer, ETransType transType " +
                    "does not support INSTALMENT, AUTHORIZATION, INC AUTHORIZATION, OFFLINE_TRANS_SEND");
        }
        String acqName = issuerAcqMap.get(issuer.getName());
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_FO)) {
            acqName = issuerMacaoAcqMap.get(issuer.getName());
        }

        Acquirer acquirer = acqDbHelper.findAcquirer(acqName);
        if (acquirer == null && "HASE_OLS".equals(acqName)) {
            acquirer = acqDbHelper.findAcquirer("HASE");
            if (acquirer == null && "HASE_OLS".equals(acqName)) {
                if (issuer.getName().equals("HASE_CUP")) {
                    acquirer = acqDbHelper.findAcquirer("HASE_CUP");
                } else {
                    acquirer = acqDbHelper.findAcquirer("HASE_EMV");
                }
            }
        } else if ("HASE_OLS".equals(acqName) &&
                FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_FO) &&
                issuer.getName().equals("HASE_CUP")) { // For Full Out Source Mode
            acquirer = acqDbHelper.findAcquirer("HASE_CUP");
        }
        if (acquirer == null) {
            throw new RuntimeException("Not Supported Card");
        }

        return acquirer;
    }

    /**
     * Is PP acquirer supported.
     *
     * @param issuer the issuer
     * @param transData the trans data
     * @return the boolean
     */
    public boolean isPPAcquirerSupported(Issuer issuer, TransData transData) {
        if (issuer == null) {
            return false;
        }

        if (!(issuer.getName().equals("VISA") || issuer.getName().equals("MASTER") ||
                issuer.getName().equals("HASE_VISA") || issuer.getName().equals("HASE_MC") ||
                issuer.getName().equals("JCB") || issuer.getName().equals("HASE_JCB") ||
                issuer.getName().equals("HASE_ENJ") || issuer.getName().equals("HASE_ENJ_E"))) {
            return false;
        }
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC)) {
            int ret = Component.checkCardBin(transData.getPan()); // check card pan is local card or not
            if (ret != TransResult.SUCC_NOT_LOCAL_CARD_BIN) {
                if (ret == TransResult.SUCC) {
                    try {
                        List<Acquirer> acquirers = acqDbHelper.lookupAcquirersForIssuer(issuer);
                        for (Acquirer tmp : acquirers) {
                            if (tmp.getName().toUpperCase().contains(ACQUIRER_DCC)) {
                                acqmanager.setCurAcq(tmp);
                                return true;
                            }
                        }
                        if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
                            Acquirer acquirer =
                                    FinancialApplication.getAcqManager().findAcquirer("HASE_MACAO");
                            acqmanager.setCurAcq(acquirer);
                            return true;
                        }
                        return false;
                    } catch (SQLException e) {
                        Log.e(TAG, "", e);
                    }
                } else {
                    acqmanager.setCurAcq(findAcquirer("HASE_DCC"));
                }
            } else {
                acqmanager.setCurAcq(findAcquirer("HASE_DCC"));
            }
            return true;
        } else if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_MACAO");
            acqmanager.setCurAcq(acquirer);
            return true;
        } else {
            try {
                List<Acquirer> acquirers = acqDbHelper.lookupAcquirersForIssuer(issuer);
                for (Acquirer tmp : acquirers) {
                    if (tmp.getName().toUpperCase().contains(ACQUIRER_DCC)) {
                        acqmanager.setCurAcq(tmp);
                        return true;
                    }
                }
                return false;
            } catch (SQLException e) {
                Log.e(TAG, "", e);
            }
        }
        return false;
    }

    /**
     * Is visa card.
     *
     * @param issuer the issuer
     * @return the boolean
     */
    public boolean isVisaCard(Issuer issuer) {
        return issuer != null && "VISA".equalsIgnoreCase(issuer.getName());
    }

    /**
     * Is visa card.
     *
     * @param issuer the issuer
     * @return the boolean
     */
    public boolean isMasterCard(Issuer issuer) {
        return issuer != null && "MASTER".equalsIgnoreCase(issuer.getName());
    }

    /**
     * Find issuers for acq list.
     *
     * @param acqName the acq name
     * @return the list
     */
    public List<Issuer> findIssuersForAcq(String acqName) {
        List<Issuer> issuers = new ArrayList<>();
        for (Map.Entry<String, String> entry : issuerAcqMap.entrySet()) {
            String acq = entry.getValue();
            if (acq == null) {
                continue;
            }
            //当acquirer为HASE时返回HASE_OLS对应issuer
            if (acqName.equals("HASE")) {
                acqName = "HASE_OLS";
            }
            if (acq.equals(acqName)) {
                issuers.add(findIssuer(entry.getKey()));
            }
        }
        return issuers;
    }

    /**
     * 查找Hase emv acquirer
     *
     * @param acqName String
     * @return List<Issuer> list
     */
    public List<Issuer> findIssuersForHaseEmvAcq(String acqName) {
        List<Issuer> issuers = new ArrayList<>();
        for (Map.Entry<String, String> entry : issuerAcqMap.entrySet()) {
            String acq = entry.getValue();
            if (acq == null) {
                continue;
            }
            if (acq.equals(acqName) || acq.equals("HASE_OLS")) {
                issuers.add(findIssuer(entry.getKey()));
            }
        }
        return issuers;
    }

    /**
     * Is issuer supported by instal acq boolean.
     *
     * @param issuerName the issuer name
     * @return the boolean
     */
    public boolean isIssuerSupportedByInstalAcq(String issuerName) {
        for (String key : issuerInsAcqMap.keySet()) {
            if (key.equals(issuerName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Find issuers for dcc acq list.
     *
     * @param acqName the acq name
     * @return the list
     */
    public List<Issuer> findIssuersForDccAcq(String acqName) {
        List<Issuer> issuers = new ArrayList<>();
        for (Map.Entry<String, String> entry : issuerDccAcqMap.entrySet()) {
            String acq = entry.getValue();
            if (acq == null) {
                continue;
            }
            if (acq.equals(acqName)) {
                issuers.add(findIssuer(entry.getKey()));
            }
        }
        return issuers;
    }

    /**
     * Find issuers for macao acq list.
     *
     * @param acqName the acq name
     * @return the list
     */
    public List<Issuer> findIssuersForMacaoAcq(String acqName) {
        List<Issuer> issuers = new ArrayList<>();
        for (Map.Entry<String, String> entry : issuerMacaoAcqMap.entrySet()) {
            String acq = entry.getValue();
            if (acq == null) {
                continue;
            }
            if (acq.equals(acqName)) {
                issuers.add(findIssuer(entry.getKey()));
            }
        }
        return issuers;
    }

    /**
     * Find issuers for instal acq list.
     *
     * @return the list
     */
    public List<Issuer> findIssuersForInstalAcq() {
        List<Issuer> issuers = new ArrayList<>();
        for (String issuerName : issuerInsAcqMap.keySet()) {
            issuers.add(findIssuer(issuerName));
        }
        return issuers;
    }

    /**
     * Is yuu card range boolean.
     *
     * @param cardno the cardno
     * @return the boolean
     */
    public boolean isYUUCardRange(String cardno) {
        List<YUUCardRange> YUUCardRanges = findAllYUUCardRanges();
        for (YUUCardRange YUUCardRange : YUUCardRanges) {
            if (Long.parseLong(cardno.substring(0, 10)) >= Long.parseLong(
                    YUUCardRange.getPanRangeLow())
                    && Long.parseLong(cardno.substring(0, 10)) <= Long.parseLong(
                    YUUCardRange.getPanRangeHigh())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据卡号查找Issuer
     *
     * @param pan String
     * @return Issuer issuer
     */
    public Issuer findIssuerByPan(final String pan) {
        if (pan == null || pan.isEmpty()) {
            return null;
        }
        CardRange cardRange = acqDbHelper.findCardRange(pan);
        if (cardRange == null) {
            return null;
        } else {
            return cardRange.getIssuer();
        }
    }

    /**
     * Is issuer supported.
     *
     * @param issuer the issuer
     * @return the boolean
     */
    public boolean isIssuerSupported(Issuer issuer) {
        List<Acquirer> acquirers = findAllAcquirers();
        for (Acquirer temp : acquirers) {
            try {
                List<Issuer> issuers = acqDbHelper.lookupIssuersForAcquirer(temp);
                for (Issuer tmp : issuers) {
                    if (tmp.getName().equals(issuer.getName())) {
                        return true;
                    }
                }
            } catch (SQLException e) {
                Log.e(TAG, "", e);
                return false;
            }
        }
        return false;
    }

    /**
     * Insert acquirer boolean.
     *
     * @param acquirer the acquirer
     * @return the boolean
     */
    public boolean insertAcquirer(final Acquirer acquirer) {
        return acqDbHelper.insertAcquirer(acquirer);
    }

    /**
     * Insert all acquirer boolean.
     *
     * @param acquirer the acquirer
     * @return the boolean
     */
    public boolean insertAllAcquirer(final List<Acquirer> acquirer) {
        return acqDbHelper.insertAllAcquirer(acquirer);
    }

    /**
     * Find acquirer acquirer.
     *
     * @param acquirerName the acquirer name
     * @return the acquirer
     */
    public Acquirer findAcquirer(final String acquirerName) {
        if (acqDbHelper.findAcquirer(acquirerName) == null) {
            throw new RuntimeException("Not Supported Card");
        } else {
            return acqDbHelper.findAcquirer(acquirerName);
        }
    }

    /**
     * Find all acquirers list.
     *
     * @return the list
     */
    public List<Acquirer> findAllAcquirers() {
        return acqDbHelper.findAllAcquirers();
    }

    /**
     * Update acquirer.
     *
     * @param acquirer the acquirer
     */
    public void updateAcquirer(final Acquirer acquirer) {
        acqDbHelper.updateAcquirer(acquirer);
    }

    /**
     * Delete acquirer.
     *
     * @param id the id
     */
    public void deleteAcquirer(final int id) {
        acqDbHelper.deleteAcquirer(id);
    }

    /**
     * Delete all acquirer.
     */
    public void deleteAllAcquirer() {
        acqDbHelper.deleteAllAcquirer();
    }

    /**
     * Insert issuer boolean.
     *
     * @param issuer the issuer
     * @return the boolean
     */
    public boolean insertIssuer(final Issuer issuer) {
        return acqDbHelper.insertIssuer(issuer);
    }

    /**
     * Insert all issuer boolean.
     *
     * @param issuer the issuer
     * @return the boolean
     */
    public boolean insertAllIssuer(final List<Issuer> issuer) {
        return acqDbHelper.insertAllIssuer(issuer);
    }

    /**
     * Find issuer issuer.
     *
     * @param issuerName the issuer name
     * @return the issuer
     */
    public Issuer findIssuer(final String issuerName) {
        return acqDbHelper.findIssuer(issuerName);
    }

    /**
     * Find all issuers list.
     *
     * @return the list
     */
    public List<Issuer> findAllIssuers() {
        return acqDbHelper.findAllIssuers();
    }

    /**
     * Bind.
     *
     * @param root the root
     * @param issuer the issuer
     */
    public void bind(final Acquirer root, final Issuer issuer) {
        acqDbHelper.bind(root, issuer);
    }

    /**
     * Update issuer.
     *
     * @param issuer the issuer
     */
    public void updateIssuer(final Issuer issuer) {
        acqDbHelper.updateIssuer(issuer);
    }

    /**
     * Delete issuer.
     *
     * @param id the id
     */
    public void deleteIssuer(final int id) {
        acqDbHelper.deleteIssuer(id);
    }

    /**
     * Delete all issuer.
     */
    public void deleteAllIssuer() {
        acqDbHelper.deleteIssuer();
    }

    /**
     * Insert card range.
     *
     * @param cardRange the card range
     */
    public void insertCardRange(final CardRange cardRange) {
        acqDbHelper.insertCardRange(cardRange);
    }

    /**
     * Insert all card range.
     *
     * @param cardRange the card range
     */
    public void insertAllCardRange(final List<CardRange> cardRange) {
        acqDbHelper.insertAllCardRange(cardRange);
    }

    /**
     * Update card range.
     *
     * @param cardRange the card range
     */
    public void updateCardRange(final CardRange cardRange) {
        acqDbHelper.updateCardRange(cardRange);
    }

    /**
     * Find card range card range.
     *
     * @param low the low
     * @param high the high
     * @return the card range
     */
    public CardRange findCardRange(final long low, final long high) {
        return acqDbHelper.findCardRange(low, high);
    }

    /**
     * Find card range list.
     *
     * @param issuer the issuer
     * @return the list
     */
    public List<CardRange> findCardRange(final Issuer issuer) {
        return acqDbHelper.findCardRange(issuer);
    }

    /**
     * Find all card ranges list.
     *
     * @return the list
     */
    public List<CardRange> findAllCardRanges() {
        return acqDbHelper.findAllCardRanges();
    }

    /**
     * Delete card range.
     *
     * @param id the id
     */
    public void deleteCardRange(int id) {
        acqDbHelper.deleteCardRange(id);
    }

    /**
     * Delete all card range.
     */
    public void deleteAllCardRange() {
        acqDbHelper.deleteAllCardRange();
    }

    /**
     * Insert yuu card range.
     *
     * @param YUUCardRange the yuu card range
     */
    public void insertYUUCardRange(final YUUCardRange YUUCardRange) {
        acqDbHelper.insertYUUCardRange(YUUCardRange);
    }

    /**
     * Insert all yuu card range.
     *
     * @param YUUCardRanges the yuu card ranges
     */
    public void insertAllYUUCardRange(final List<YUUCardRange> YUUCardRanges) {
        acqDbHelper.insertAllYUUCardRange(YUUCardRanges);
    }

    /**
     * Update yuu card range.
     *
     * @param YUUCardRange the yuu card range
     */
    public void updateYUUCardRange(final YUUCardRange YUUCardRange) {
        acqDbHelper.updateYUUCardRange(YUUCardRange);
    }

    /**
     * Find yuu card range yuu card range.
     *
     * @param low the low
     * @param high the high
     * @return the yuu card range
     */
    public YUUCardRange findYUUCardRange(final long low, final long high) {
        return acqDbHelper.findYUUCardRange(low, high);
    }

    /**
     * Find all yuu card ranges list.
     *
     * @return the list
     */
    public List<YUUCardRange> findAllYUUCardRanges() {
        return acqDbHelper.findAllYUUCardRanges();
    }

    /**
     * Delete yuu card range.
     *
     * @param id the id
     */
    public void deleteYUUCardRange(int id) {
        acqDbHelper.deleteYUUCardRange(id);
    }

    /**
     * Delete all yuu card range.
     */
    public void deleteAllYUUCardRange() {
        acqDbHelper.deleteAllYUUCardRange();
    }

    /**
     * Find all realtion list.
     *
     * @return the list
     */
    public List<AcqIssuerRelation> findAllRealtion() {
        return acqDbHelper.findAllRelation();
    }

    private static void init() {
        acqDbHelper = AcqDb.getInstance();
    }

    /**
     * Delete acq issuer relation.
     *
     * @param id the id
     */
    public void deleteAcqIssuerRelation(final int id) {
        acqDbHelper.deleteAcqIssuerRelation(id);
    }

    /**
     * The enum E card type.
     */
    //Jerry add
    public enum ECardType {
        /**
         * Hase pboc e card type.
         */
        HASE_PBOC,
        /**
         * Hase vis e card type.
         */
        HASE_VIS,
        /**
         * Hase mc e card type.
         */
        HASE_MC,
        /**
         * Pboc e card type.
         */
        PBOC,
        /**
         * Vis e card type.
         */
        VIS,
        /**
         * Mc e card type.
         */
        MC,
        /**
         * Amex e card type.
         */
        AMEX,
        /**
         * Hase enj e card type.
         */
        HASE_ENJ,
        /**
         * Hase enj e e card type.
         */
        HASE_ENJ_E,
        /**
         * Jcb e card type.
         */
        JCB
    }

    /**
     * Gets e card type.
     *
     * @param pan the pan
     * @return the e card type
     */
    public ECardType getECardType(final String pan) {
        if (pan == null || pan.isEmpty()) {
            return null;
        }
        //
        Map<String, ECardType> eCardTypeMap = new HashMap<>();
        eCardTypeMap.put("HASE_CUP", ECardType.HASE_PBOC);
        eCardTypeMap.put("HASE_VISA", ECardType.HASE_VIS);
        eCardTypeMap.put("HASE_MC", ECardType.HASE_MC);
        eCardTypeMap.put("UnionPay", ECardType.PBOC);
        eCardTypeMap.put("VISA", ECardType.VIS);
        eCardTypeMap.put("MASTER", ECardType.MC);
        eCardTypeMap.put("AMEX", ECardType.AMEX);
        eCardTypeMap.put("HASE_ENJ", ECardType.HASE_ENJ);
        eCardTypeMap.put("HASE_ENJ_E", ECardType.HASE_ENJ_E);
        eCardTypeMap.put("JCB", ECardType.JCB);
        return eCardTypeMap.get(findIssuerByPan(pan).getName());
    }

    /**
     * Sets cur acq.
     *
     * @param acq the acq
     */
    public void setCurAcq(Acquirer acq) {
        acquirer = acq;
    }

    /**
     * Gets cur acq.
     *
     * @return the cur acq
     */
    public Acquirer getCurAcq() {
        return acquirer;
    }
}
