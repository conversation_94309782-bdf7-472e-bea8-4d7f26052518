/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.model.TransData;

public class PackPreAuth extends PackIsoBase {

    public PackPreAuth(PackListener listener) {
        super(listener);
    }

    /**
     * get PreAuth RequiredFields
     *
     * @return int[]
     */
    protected int[] getRequiredFields() {
        /*Raymond 20220708: Fix pin field (F52) missing in CUP PREAUTH transaction
        return new int[] { 1, 2, 3, 4, 11, 14, 22, 23, 24, 25, 35, 41, 42, 49, 55, 63 };*/
        return new int[] { 1, 2, 3, 4, 11, 14, 22, 23, 24, 25, 35, 41, 42, 49, 52, 55, 63 };
    }

    /**
     * setBitData35
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData35(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("35", transData.getTrack2());
    }
}
