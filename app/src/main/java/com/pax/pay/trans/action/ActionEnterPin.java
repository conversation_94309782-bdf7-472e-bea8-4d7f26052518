/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.commonbusiness.card.PanUtils;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.EnterPinActivity;

public class ActionEnterPin extends AAction {
    private Context context;
    private String title;
    private String pan;
    private String header;
    private String subHeader;
    private String totalAmount;
    private String tipAmount;
    private boolean isSupportBypass;
    private EEnterPinType enterPinType;

    public ActionEnterPin(ActionStartListener listener) {
        super(listener);
    }

    /**
     * @param context   the context
     * @param title     the title
     * @param pan       the card number
     * @param supportBypass  if is bypass
     * @param header         the header
     * @param subHeader      the subheader
     * @param totalAmount    the total amount
     * @param tipAmount      the tip amount
     * @param enterPinType   the enter pin type
     */
    public void setParam(Context context, String title, String pan, boolean supportBypass, String header,
                         String subHeader, String totalAmount, String tipAmount, EEnterPinType enterPinType) {
        this.context = context;
        this.title = title;
        this.pan = pan;
        this.isSupportBypass = supportBypass;
        this.header = header;
        this.subHeader = subHeader;
        this.totalAmount = totalAmount;
        this.tipAmount = tipAmount; //AET-81
        this.enterPinType = enterPinType;
    }

    @Override
    protected void process() {
        Intent intent = new Intent(context, EnterPinActivity.class);
        intent.putExtra(EUIParamKeys.NAV_TITLE.toString(), title);
        intent.putExtra(EUIParamKeys.PROMPT_1.toString(), header);
        intent.putExtra(EUIParamKeys.PROMPT_2.toString(), subHeader);
        intent.putExtra(EUIParamKeys.TRANS_AMOUNT.toString(), totalAmount);
        intent.putExtra(EUIParamKeys.TIP_AMOUNT.toString(), tipAmount); //AET-81
        intent.putExtra(EUIParamKeys.ENTERPINTYPE.toString(), enterPinType);
        intent.putExtra(EUIParamKeys.PANBLOCK.toString(), PanUtils.getPanBlock(pan, PanUtils.X9_8_WITH_PAN));
        intent.putExtra(EUIParamKeys.SUPPORTBYPASS.toString(), isSupportBypass);
        context.startActivity(intent);
    }

    public enum EEnterPinType {
        ONLINE_PIN, // 联机pin
        OFFLINE_PLAIN_PIN, // 脱机明文pin
        OFFLINE_CIPHER_PIN, // 脱机密文pin
        OFFLINE_PCI_MODE, //JEMV PCI MODE, no callback for offline pin
    }

    /**
     * 脱机pin时返回的结果
     *
     * <AUTHOR>
     */
    public static class OfflinePinResult {
        // SW1 SW2
        byte[] respOut;
        int ret;

        public byte[] getRespOut() {
            return respOut;
        }

        public void setRespOut(byte[] respOut) {
            this.respOut = respOut;
        }

        public int getRet() {
            return ret;
        }

        public void setRet(int ret) {
            this.ret = ret;
        }
    }

}
