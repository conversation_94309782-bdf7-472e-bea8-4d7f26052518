/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.model;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.Issuer;
import java.io.Serializable;

/**
 * 交易总计
 *
 * <AUTHOR>
 */

@DatabaseTable(tableName = "trans_total")
public class TransTotal implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID_FIELD_NAME = "id";
    public static final String IS_CLOSED_FIELD_NAME = "closed";
    public static final String MID_FIELD_NAME = "mid";
    public static final String TID_FIELD_NAME = "tid";
    public static final String BATCHNO_FIELD_NAME = "batch_no";
    public static final String TIME_FIELD_NAME = "batch_time";


    public static final String LYS_OK_NET_AMOUNT = "LYS_OK_NET_AMOUNT";
    public static final String LYS_OK_NET_NUM = "LYS_OK_NET_NUM";
    public static final String LYS_OK_NET_VOID_NUM = "LYS_OK_NET_VOID_NUM";
    public static final String LYS_OK_NET_VOID_AMT = "LYS_OK_NET_VOID_AMT";
    public static final String VOID_AMOUNT = "VOID_AMOUNT";
    public static final String VOID_NUM = "VOID_NUM";
    public static final String REFUND_AMOUNT = "REFUND_AMOUNT";
    public static final String REFUND_NUM = "REFUND_NUM";
    public static final String REFUND_VOID_AMOUNT = "REFUND_VOID_AMOUNT";
    public static final String REFUND_VOID_NUM = "REFUND_VOID_NUM";
    public static final String SALE_VOID_AMOUNT = "SALE_VOID_AMOUNT";
    public static final String SALE_VOID_NUM = "SALE_VOID_NUM";
    public static final String AUTH_AMOUNT = "AUTH_AMOUNT";
    public static final String AUTH_NUM = "AUTH_NUM";
    public static final String OFFLINE_AMOUNT = "OFFLINE_AMOUNT";
    public static final String OFFLINE_NUM = "OFFLINE_NUM";
    public static final String OFFLINE_VOID_AMOUNT = "OFFLINE_VOID_AMOUNT";
    public static final String OFFLINE_VOID_NUM = "OFFLINE_VOID_NUM";
    public static final String TIP_AMOUNT = "TIP_AMOUNT";
    public static final String TIP_NUM = "TIP_NUM";

    //Total info related to loyalty transactions
    public static final String LYS_GROSS_NUM = "LYS_GROSS_NUM";
    public static final String LYS_GROSS_AMOUNT = "LYS_GROSS_AMOUNT";
    public static final String LYS_GROSS_VOID_NUM = "LYS_GROSS_VOID_NUM";
    public static final String LYS_GROSS_VOID_AMOUNT = "LYS_GROSS_VOID_AMOUNT";

    public static final String REDEEM_NUM = "REDEEM_NUM";
    public static final String REDEEM_AMOUNT = "REDEEM_AMOUNT";
    public static final String REDEEM_VOID_NUM = "REDEEM_VOID_NUM";
    public static final String REDEEM_VOID_AMOUNT = "REDEEM_VOID_AMOUNT";

    public static final String SALE_NUM = "SALE_NUM";
    public static final String SALE_AMOUNT = "SALE_AMOUNT";
    public static final String SALE_NET_NUM = "SALE_NET_NUM";
    public static final String SALE_NET_AMOUNT = "SALE_NET_AMOUNT";
    public static final String SALE_NET_VOID_NUM = "SALE_NET_VOID_NUM";
    public static final String SALE_NET_VOID_AMOUNT = "SALE_NET_VOID_AMOUNT";
    public static final String NON_FULL_REDEEM_NUM = "NON_FULL_REDEEM_NUM";
    public static final String NON_FULL_REDEEM_AMOUNT = "NON_FULL_REDEEM_AMOUNT";
    public static final String NON_FULL_REDEEM_VOID_NUM = "NON_FULL_REDEEM_VOID_NUM";
    public static final String NON_FULL_REDEEM_VOID_AMOUNT = "NON_FULL_REDEEM_VOID_AMOUNT";

    public static final String LYS_DOWN_NET_NUM = "LYS_DOWN_NET_NUM";
    public static final String LYS_DOWN_NET_AMOUNT = "LYS_DOWN_NET_AMOUNT";

    public static final String BOUNS_NUM = "BOUNS_NUM";
    public static final String BOUNS_AMOUNT = "BOUNS_AMOUNT";
    public static final String BOUNS_VOID_NUM = "BOUNS_VOID_NUM";
    public static final String BOUNS_VOID_AMOUNT = "BOUNS_VOID_AMOUNT";

    public static final String HASE_CASH_NUM = "HASE_CASH_NUM";
    public static final String HASE_CASH_AMOUNT = "HASE_CASH_AMOUNT";
    public static final String HASE_CASH_VOID_NUM = "HASE_CASH_VOID_NUM";
    public static final String HASE_CASH_VOID_AMOUNT = "HASE_CASH_VOID_AMOUNT";

    public static final String MER1_NUM = "MER1_NUM";
    public static final String MER1_AMOUNT = "MER1_AMOUNT";
    public static final String MER1_VOID_NUM = "MER1_VOID_NUM";
    public static final String MER1_VOID_AMOUNT = "MER1_VOID_AMOUNT";

    public static final String MER2_NUM = "MER2_NUM";
    public static final String MER2_AMOUNT = "MER2_AMOUNT";
    public static final String MER2_VOID_NUM = "MER2_VOID_NUM";
    public static final String MER2_VOID_AMOUNT = "MER2_VOID_AMOUNT";

    public static final String ZERO_NUM = "ZERO_NUM";
    public static final String VOID_ZERO_NUM = "VOID_ZERO_NUM";


    @DatabaseField(generatedId = true, columnName = ID_FIELD_NAME)
    protected int id;

    /**
     * 商户号
     */
    @DatabaseField(columnName = MID_FIELD_NAME)
    private String merchantID;
    /**
     * 终端号
     */
    @DatabaseField(columnName = TID_FIELD_NAME)
    private String terminalID;
    /**
     * 批次号
     */
    @DatabaseField(columnName = BATCHNO_FIELD_NAME)
    private int batchNo;

    /**
     * 日期时间
     */
    @DatabaseField(columnName = TIME_FIELD_NAME)
    private String dateTime;

    @DatabaseField(foreign = true, foreignAutoRefresh = true, columnName = Acquirer.ID_FIELD_NAME)
    private Acquirer acquirer;

    @DatabaseField(foreign = true, foreignAutoRefresh = true, columnName = Issuer.ID_FIELD_NAME, canBeNull = true)
    private Issuer issuer;

    @DatabaseField(columnName = IS_CLOSED_FIELD_NAME)
    private boolean isClosed;

    /**
     * 消费总金额
     */
    @DatabaseField(columnName = LYS_OK_NET_AMOUNT)
    private long lysOkNetAmt;
    /**
     * 消费总笔数
     */
    @DatabaseField(columnName = LYS_OK_NET_NUM)
    private long lysOkNetNum;

    @DatabaseField(columnName = LYS_OK_NET_VOID_NUM)
    private long lysOkNetVoidNum;

    @DatabaseField(columnName = LYS_OK_NET_VOID_AMT)
    private long lysOkNetVoidAmt;

    /**
     * 撤销总金额
     */
    @DatabaseField(columnName = VOID_AMOUNT)
    private long voidTotalAmt;
    /**
     * 撤销总笔数
     */
    @DatabaseField(columnName = VOID_NUM)
    private long voidTotalNum;
    /**
     * 退货总金额
     */
    @DatabaseField(columnName = REFUND_AMOUNT)
    private long refundTotalAmt;
    /**
     * 退货总笔数
     */
    @DatabaseField(columnName = REFUND_NUM)
    private long refundTotalNum;
    /**
     * refund void total amount
     */
    @DatabaseField(columnName = REFUND_VOID_AMOUNT)
    private long refundVoidTotalAmt;
    /**
     * refund void total num
     */
    @DatabaseField(columnName = REFUND_VOID_NUM)
    private long refundVoidTotalNum;
    /**
     * sale void total amount
     */
    @DatabaseField(columnName = SALE_VOID_AMOUNT)
    private long saleVoidAmt;
    /**
     * sale void total num
     */
    @DatabaseField(columnName = SALE_VOID_NUM)
    private long saleVoidNum;
    /**
     * 预授权总金额
     */
    @DatabaseField(columnName = AUTH_AMOUNT)
    private long authTotalAmt;
    /**
     * 预授权总笔数
     */
    @DatabaseField(columnName = AUTH_NUM)
    private long authTotalNum;
    //AET-75
    /**
     * 脱机交易总金额
     */
    @DatabaseField(columnName = OFFLINE_AMOUNT)
    private long offlineTotalAmt;
    /**
     * 脱机交易总笔数
     */
    @DatabaseField(columnName = OFFLINE_NUM)
    private long offlineTotalNum;

    @DatabaseField(columnName = OFFLINE_VOID_AMOUNT)
    private long offlineVoidTotalAmt;

    @DatabaseField(columnName = OFFLINE_VOID_NUM)
    private long offlineVoidTotalNum;

    @DatabaseField(columnName = TIP_AMOUNT)
    private long tipAmt;

    @DatabaseField(columnName = TIP_NUM)
    private long tipNum;

    @DatabaseField(columnName = LYS_GROSS_NUM)
    private long lysGrossNum;
    @DatabaseField(columnName = LYS_GROSS_AMOUNT)
    private long lysGrossAmt;

    @DatabaseField(columnName = LYS_GROSS_VOID_NUM)
    private long lysGrossVoidNum;
    @DatabaseField(columnName = LYS_GROSS_VOID_AMOUNT)
    private long lysGrossVoidAmt;

    @DatabaseField(columnName = REDEEM_NUM)
    private long redeemNum;
    @DatabaseField(columnName = REDEEM_AMOUNT)
    private long redeemAmt;

    @DatabaseField(columnName = REDEEM_VOID_NUM)
    private long redeemVoidNum;
    @DatabaseField(columnName = REDEEM_VOID_AMOUNT)
    private long redeemVoidAmt;

    @DatabaseField(columnName = SALE_NUM)
    private long saleNum;
    @DatabaseField(columnName = SALE_AMOUNT)
    private long saleAmt;
    @DatabaseField(columnName = NON_FULL_REDEEM_NUM)
    private long nonFullRedeemNum;
    @DatabaseField(columnName = NON_FULL_REDEEM_AMOUNT)
    private long nonFullRedeemAmt;
    @DatabaseField(columnName = NON_FULL_REDEEM_VOID_NUM)
    private long nonFullRedeemVoidNum;
    @DatabaseField(columnName = NON_FULL_REDEEM_VOID_AMOUNT)
    private long nonFullRedeemVoidAmt;

    @DatabaseField(columnName = SALE_NET_NUM)
    private long saleNetNum;

    @DatabaseField(columnName = SALE_NET_AMOUNT)
    private long saleNetAmt;

    @DatabaseField(columnName = SALE_NET_VOID_NUM)
    private long saleNetVoidNum;
    @DatabaseField(columnName = SALE_NET_VOID_AMOUNT)
    private long saleNetVoidAmt;

    /**
     * for net amount when LYS down(down means "LYS Status" AFE returned is not OK)
     */
    @DatabaseField(columnName = LYS_DOWN_NET_NUM)
    private long lysDownNetNum;
    @DatabaseField(columnName = LYS_DOWN_NET_AMOUNT)
    private long lysDownNetAmt;

    @DatabaseField(columnName = BOUNS_NUM)
    private long bounsNum;
    @DatabaseField(columnName = BOUNS_AMOUNT)
    private long bounsAmt;

    @DatabaseField(columnName = BOUNS_VOID_NUM)
    private long bounsVoidNum;
    @DatabaseField(columnName = BOUNS_VOID_AMOUNT)
    private long bounsVoidAmt;

    @DatabaseField(columnName = HASE_CASH_NUM)
    private long haseCashNum;
    @DatabaseField(columnName = HASE_CASH_AMOUNT)
    private long haseCashAmt;

    @DatabaseField(columnName = HASE_CASH_VOID_NUM)
    private long haseCashVoidNum;
    @DatabaseField(columnName = HASE_CASH_VOID_AMOUNT)
    private long haseCashVoidAmt;

    @DatabaseField(columnName = MER1_NUM)
    private long mer1Num;
    @DatabaseField(columnName = MER1_AMOUNT)
    private long mer1Amt;

    @DatabaseField(columnName = MER1_VOID_NUM)
    private long mer1VoidNum;
    @DatabaseField(columnName = MER1_VOID_AMOUNT)
    private long mer1VoidAmt;

    @DatabaseField(columnName = MER2_NUM)
    private long mer2Num;
    @DatabaseField(columnName = MER2_AMOUNT)
    private long mer2Amt;

    @DatabaseField(columnName = MER2_VOID_NUM)
    private long mer2VoidNum;
    @DatabaseField(columnName = MER2_VOID_AMOUNT)
    private long mer2VoidAmt;


    @DatabaseField(columnName = ZERO_NUM)
    private long zeroNum;
    @DatabaseField(columnName = VOID_ZERO_NUM)
    private long voidZeroNum;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getLysOkNetAmt() {
        return lysOkNetAmt;
    }

    public void setLysOkNetAmt(long lysOkNetAmt) {
        this.lysOkNetAmt = lysOkNetAmt;
    }

    public long getLysOkNetNum() {
        return lysOkNetNum;
    }

    public void setLysOkNetNum(long lysOkNetNum) {
        this.lysOkNetNum = lysOkNetNum;
    }

    public long getLysOkNetVoidNum() {
        return lysOkNetVoidNum;
    }

    public void setLysOkNetVoidNum(long lysOkNetVoidNum) {
        this.lysOkNetVoidNum = lysOkNetVoidNum;
    }

    public long getLysOkNetVoidAmt() {
        return lysOkNetVoidAmt;
    }

    public void setLysOkNetVoidAmt(long lysOkNetVoidAmt) {
        this.lysOkNetVoidAmt = lysOkNetVoidAmt;
    }

    public long getVoidTotalAmt() {
        return voidTotalAmt;
    }

    public void setVoidTotalAmt(long voidTotalAmt) {
        this.voidTotalAmt = voidTotalAmt;
    }

    public long getVoidTotalNum() {
        return voidTotalNum;
    }

    public void setVoidTotalNum(long voidTotalNum) {
        this.voidTotalNum = voidTotalNum;
    }

    public long getRefundVoidTotalAmt() {
        return refundVoidTotalAmt;
    }

    public void setRefundVoidTotalAmt(long refundVoidTotalAmt) {
        this.refundVoidTotalAmt = refundVoidTotalAmt;
    }

    public long getRefundVoidTotalNum() {
        return refundVoidTotalNum;
    }

    public void setRefundVoidTotalNum(long refundVoidTotalNum) {
        this.refundVoidTotalNum = refundVoidTotalNum;
    }

    public long getSaleVoidAmt() {
        return saleVoidAmt;
    }

    public void setSaleVoidAmt(long saleVoidAmt) {
        this.saleVoidAmt = saleVoidAmt;
    }

    public long getSaleVoidNum() {
        return saleVoidNum;
    }

    public void setSaleVoidNum(long saleVoidNum) {
        this.saleVoidNum = saleVoidNum;
    }

    public long getRefundTotalAmt() {
        return refundTotalAmt;
    }

    public void setRefundTotalAmt(long refundTotalAmt) {
        this.refundTotalAmt = refundTotalAmt;
    }

    public long getRefundTotalNum() {
        return refundTotalNum;
    }

    public void setRefundTotalNum(long refundTotalNum) {
        this.refundTotalNum = refundTotalNum;
    }

    public long getAuthTotalAmt() {
        return authTotalAmt;
    }

    public void setAuthTotalAmt(long authTotalAmt) {
        this.authTotalAmt = authTotalAmt;
    }

    public long getAuthTotalNum() {
        return authTotalNum;
    }

    public void setAuthTotalNum(long authTotalNum) {
        this.authTotalNum = authTotalNum;
    }

    public String getMerchantID() {
        return merchantID;
    }

    public void setMerchantID(String merchantID) {
        this.merchantID = merchantID;
    }

    public String getTerminalID() {
        return terminalID;
    }

    public void setTerminalID(String terminalID) {
        this.terminalID = terminalID;
    }

    public int getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(int batchNo) {
        this.batchNo = batchNo;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public long getOfflineTotalAmt() {
        return offlineTotalAmt;
    }

    public void setOfflineTotalAmt(long offlineTotalAmt) {
        this.offlineTotalAmt = offlineTotalAmt;
    }

    public long getOfflineTotalNum() {
        return offlineTotalNum;
    }

    public void setOfflineTotalNum(long offlineTotalNum) {
        this.offlineTotalNum = offlineTotalNum;
    }

    public long getOfflineVoidTotalAmt() {
        return offlineVoidTotalAmt;
    }

    public void setOfflineVoidTotalAmt(long offlineVoidTotalAmt) {
        this.offlineVoidTotalAmt = offlineVoidTotalAmt;
    }

    public long getOfflineVoidTotalNum() {
        return offlineVoidTotalNum;
    }

    public void setOfflineVoidTotalNum(long offlineVoidTotalNum) {
        this.offlineVoidTotalNum = offlineVoidTotalNum;
    }

    public Acquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(Acquirer acquirer) {
        this.acquirer = acquirer;
    }

    public Issuer getIssuer() {
        return issuer;
    }

    public void setIssuer(Issuer issuer) {
        this.issuer = issuer;
    }

    public boolean isClosed() {
        return isClosed;
    }

    public void setClosed(boolean closed) {
        this.isClosed = closed;
    }

    public long getLysGrossNum() {
        return lysGrossNum;
    }

    public void setLysGrossNum(long lysGrossNum) {
        this.lysGrossNum = lysGrossNum;
    }

    public long getLysGrossAmt() {
        return lysGrossAmt;
    }

    public void setLysGrossAmt(long lysGrossAmt) {
        this.lysGrossAmt = lysGrossAmt;
    }

    public long getRedeemNum() {
        return redeemNum;
    }

    public void setRedeemNum(long redeemNum) {
        this.redeemNum = redeemNum;
    }

    public long getRedeemAmt() {
        return redeemAmt;
    }

    public void setRedeemAmt(long redeemAmt) {
        this.redeemAmt = redeemAmt;
    }

    public long getSaleNum() {
        return saleNum;
    }

    public void setSaleNum(long saleNum) {
        this.saleNum = saleNum;
    }

    public long getSaleAmt() {
        return saleAmt;
    }

    public void setSaleAmt(long saleAmt) {
        this.saleAmt = saleAmt;
    }

    public long getNonFullRedeemNum() {
        return nonFullRedeemNum;
    }

    public void setNonFullRedeemNum(long nonFullRedeemNum) {
        this.nonFullRedeemNum = nonFullRedeemNum;
    }

    public long getNonFullRedeemAmt() {
        return nonFullRedeemAmt;
    }

    public void setNonFullRedeemAmt(long nonFullRedeemAmt) {
        this.nonFullRedeemAmt = nonFullRedeemAmt;
    }

    public long getNonFullRedeemVoidNum() {
        return nonFullRedeemVoidNum;
    }

    public void setNonFullRedeemVoidNum(long nonFullRedeemVoidNum) {
        this.nonFullRedeemVoidNum = nonFullRedeemVoidNum;
    }

    public long getNonFullRedeemVoidAmt() {
        return nonFullRedeemVoidAmt;
    }

    public void setNonFullRedeemVoidAmt(long nonFullRedeemVoidAmt) {
        this.nonFullRedeemVoidAmt = nonFullRedeemVoidAmt;
    }

    public long getLysDownNetNum() {
        return lysDownNetNum;
    }

    public void setLysDownNetNum(long lysDownNetNum) {
        this.lysDownNetNum = lysDownNetNum;
    }

    public long getLysDownNetAmt() {
        return lysDownNetAmt;
    }

    public void setLysDownNetAmt(long lysDownNetAmt) {
        this.lysDownNetAmt = lysDownNetAmt;
    }

    public long getBounsNum() {
        return bounsNum;
    }

    public void setBounsNum(long bounsNum) {
        this.bounsNum = bounsNum;
    }

    public long getBounsAmt() {
        return bounsAmt;
    }

    public void setBounsAmt(long bounsAmt) {
        this.bounsAmt = bounsAmt;
    }

    public long getHaseCashNum() {
        return haseCashNum;
    }

    public void setHaseCashNum(long haseCashNum) {
        this.haseCashNum = haseCashNum;
    }

    public long getHaseCashAmt() {
        return haseCashAmt;
    }

    public void setHaseCashAmt(long haseCashAmt) {
        this.haseCashAmt = haseCashAmt;
    }

    public long getMer1Num() {
        return mer1Num;
    }

    public void setMer1Num(long mer1Num) {
        this.mer1Num = mer1Num;
    }

    public long getMer1Amt() {
        return mer1Amt;
    }

    public void setMer1Amt(long mer1Amt) {
        this.mer1Amt = mer1Amt;
    }

    public long getMer2Num() {
        return mer2Num;
    }

    public void setMer2Num(long mer2Num) {
        this.mer2Num = mer2Num;
    }

    public long getMer2Amt() {
        return mer2Amt;
    }

    public void setMer2Amt(long mer2Amt) {
        this.mer2Amt = mer2Amt;
    }


    public long getLysGrossVoidNum() {
        return lysGrossVoidNum;
    }

    public void setLysGrossVoidNum(long lysGrossVoidNum) {
        this.lysGrossVoidNum = lysGrossVoidNum;
    }

    public long getLysGrossVoidAmt() {
        return lysGrossVoidAmt;
    }

    public void setLysGrossVoidAmt(long lysGrossVoidAmt) {
        this.lysGrossVoidAmt = lysGrossVoidAmt;
    }

    public long getRedeemVoidNum() {
        return redeemVoidNum;
    }

    public void setRedeemVoidNum(long redeemVoidNum) {
        this.redeemVoidNum = redeemVoidNum;
    }

    public long getRedeemVoidAmt() {
        return redeemVoidAmt;
    }

    public void setRedeemVoidAmt(long redeemVoidAmt) {
        this.redeemVoidAmt = redeemVoidAmt;
    }

    public long getSaleNetNum() {
        return saleNetNum;
    }

    public void setSaleNetNum(long saleNetNum) {
        this.saleNetNum = saleNetNum;
    }

    public long getSaleNetAmt() {
        return saleNetAmt;
    }

    public void setSaleNetAmt(long saleNetAmt) {
        this.saleNetAmt = saleNetAmt;
    }

    public long getSaleNetVoidNum() {
        return saleNetVoidNum;
    }

    public void setSaleNetVoidNum(long saleNetVoidNum) {
        this.saleNetVoidNum = saleNetVoidNum;
    }

    public long getSaleNetVoidAmt() {
        return saleNetVoidAmt;
    }

    public void setSaleNetVoidAmt(long saleNetVoidAmt) {
        this.saleNetVoidAmt = saleNetVoidAmt;
    }

    public long getBounsVoidNum() {
        return bounsVoidNum;
    }

    public void setBounsVoidNum(long bounsVoidNum) {
        this.bounsVoidNum = bounsVoidNum;
    }

    public long getBounsVoidAmt() {
        return bounsVoidAmt;
    }

    public void setBounsVoidAmt(long bounsVoidAmt) {
        this.bounsVoidAmt = bounsVoidAmt;
    }

    public long getHaseCashVoidNum() {
        return haseCashVoidNum;
    }

    public void setHaseCashVoidNum(long haseCashVoidNum) {
        this.haseCashVoidNum = haseCashVoidNum;
    }

    public long getHaseCashVoidAmt() {
        return haseCashVoidAmt;
    }

    public void setHaseCashVoidAmt(long haseCashVoidAmt) {
        this.haseCashVoidAmt = haseCashVoidAmt;
    }

    public long getMer1VoidNum() {
        return mer1VoidNum;
    }

    public void setMer1VoidNum(long mer1VoidNum) {
        this.mer1VoidNum = mer1VoidNum;
    }

    public long getMer1VoidAmt() {
        return mer1VoidAmt;
    }

    public void setMer1VoidAmt(long mer1VoidAmt) {
        this.mer1VoidAmt = mer1VoidAmt;
    }

    public long getMer2VoidNum() {
        return mer2VoidNum;
    }

    public void setMer2VoidNum(long mer2VoidNum) {
        this.mer2VoidNum = mer2VoidNum;
    }

    public long getMer2VoidAmt() {
        return mer2VoidAmt;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public void setMer2VoidAmt(long mer2VoidAmt) {
        this.mer2VoidAmt = mer2VoidAmt;
    }

    public long getZeroNum() {
        return zeroNum;
    }

    public void setZeroNum(long zeroNum) {
        this.zeroNum = zeroNum;
    }

    public long getVoidZeroNum() {
        return voidZeroNum;
    }

    public void setVoidZeroNum(long voidZeroNum) {
        this.voidZeroNum = voidZeroNum;
    }

    public long getTipAmt() {
        return tipAmt;
    }

    public void setTipAmt(long tipAmt) {
        this.tipAmt = tipAmt;
    }

    public long getTipNum() {
        return tipNum;
    }

    public void setTipNum(long tipNum) {
        this.tipNum = tipNum;
    }

    public boolean isZero() {
        return getSaleAmt() == 0
                && getSaleVoidAmt() == 0
                && getRefundTotalAmt() == 0
                && getRefundVoidTotalAmt() == 0;
    }

}
