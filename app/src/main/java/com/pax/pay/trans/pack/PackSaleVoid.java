/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import android.text.TextUtils;
import android.util.Log;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.data.entity.TransData;
import com.pax.eemv.utils.Tools;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.gl.pack.exception.TlvException;
import com.pax.glwrapper.impl.GL;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.Utils;

import androidx.annotation.NonNull;

import static com.pax.pay.constant.Constants.ISSUER_RUPAY;

public class PackSaleVoid extends PackIso8583 {

    public PackSaleVoid(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{2, 3, 4, 11, 12, 13, 14, 22, 24, 25, 37, 38, 39, 41, 42, 49, 53, 54, 55, 60, 62};
    }

    @Override
    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        String temp = "0";
        //撤销交易根据文档说明上送金额为全0
        setBitData("4", temp);
    }

    protected void setBitData11(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("11", Component.getPaddedNumber(transData.getOrigTransNo(), 6));
    }

    @Override
    protected void setBitData39(TransData transData) throws Iso8583Exception {
        //RUPAY撤销交易时，39域需上送17
        if (transData.getIssuer() != null && ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName())) {
            setBitData("39", "17");
        }
    }

    @Override
    protected void setBitData49(TransData transData) throws Iso8583Exception {
        if(transData.isDcc() && transData.getInternationalCurrency()!= null){
            setBitData("49", transData.getInternationalCurrency().getCode());
        }
    }

    @Override
    protected void setBitData55(@NonNull TransData transData) throws Iso8583Exception {
        String temp = transData.getSendIccData();
        if (temp != null && temp.length() > 0) {
            //RUPAY的撤销交易需上送脚本数据9F5B
            if (transData.getIssuerScriptResults() != null && transData.getIssuerScriptResults().length() > 0) {
                ITlv tlv = GL.getGL().getPacker().getTlv();
                ITlv.ITlvDataObjList tlvList = tlv.createTlvDataObjectList();
                ITlv.ITlvDataObj obj = tlv.createTlvDataObject();
                obj.setTag(0x9F5B);
                obj.setValue(Tools.str2Bcd(transData.getIssuerScriptResults()));
                tlvList.addDataObj(obj);

                try {
                    temp += Tools.bcd2Str(tlv.pack(tlvList));
                } catch (TlvException e) {
                    Log.e(TAG, e.toString());
                }
            }
            setBitData("55", ConvertHelper.getConvert().strToBcdPaddingLeft(temp));
        }
    }

    @Override
    protected void setBitData60(@NonNull TransData transData) throws Iso8583Exception {
        String temp = "";
        if (!TextUtils.isEmpty(transData.getOrigAmount()) && Utils.parseLongSafe(transData.getOrigAmount()) != 0) {
            Long saleAmount = Utils.parseLongSafe(transData.getOrigAmount());
            temp = saleAmount.toString();
        } else if (!TextUtils.isEmpty(transData.getAmount()) && Utils.parseLongSafe(transData.getAmount()) != 0) {
            Long saleAmount = Utils.parseLongSafe(transData.getAmount());
            temp = saleAmount.toString();
        }
        if (TextUtils.isEmpty(temp)) {
            temp = "0";
        }
        setBitData("60", Component.getPaddedString(temp, 12, '0'));
    }
}
