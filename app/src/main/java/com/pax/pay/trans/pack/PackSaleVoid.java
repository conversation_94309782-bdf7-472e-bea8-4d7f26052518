/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import android.annotation.SuppressLint;
import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;

public class PackSaleVoid extends PackIso8583 {

    public PackSaleVoid(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[] {
                1, 2, 3, 4, 6, 10, 11, 12, 13, 14, 22, 23, 24, 25, 37, 38, 41, 42, 49, 51, 54, 55, 56, 62, 63
        };       //normal CUP add 23,35,54, and cut 2,14,62  swipe add 55
    }

    /**
     * setBitData2
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData2(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("2", transData.getPan());
    }

    /**
     * setBitData3
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData3(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getOrigTransType() == ETransType.REFUND) {
            setBitData("3", "220000");
        } else {
            setBitData("3", transData.getTransType().getProcCode());
        }
    }

    /**
     * setBitData4
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        long tempChange;
        boolean isAdjusted = transData.getTransState() == TransData.ETransStatus.ADJUSTED;
        boolean isOfflineNotSent =
                transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT;
        if (isAdjusted) {
            if (isOfflineNotSent) {
                transData.setNotSendAmount(transData.getAmount()); //temp value to show on the receipt for non-piggyback
                tempChange = transData.getAmount() - transData.getTipAmount() + transData.getOrgTipAmount();
            } else {
                tempChange = transData.getAmount();
            }
            transData.setAmount(tempChange);
            //transData.setOfflineSendState(null);//Avoid transaction record to be deleted in this case(sale->adjust->void)
            @SuppressLint("DefaultLocale") String tempStr = String.format("%012d", tempChange);
            setBitData("4", tempStr);
            return;
        }
        super.setBitData4(transData);
    }

    /**
     * setBitData6
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData6(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.isDcc()) {
        String amount;
        amount = transData.getHomeCurrencyAmount();
        boolean isAdjusted = transData.getTransState() == TransData.ETransStatus.ADJUSTED;
        boolean isOfflineNotSent = transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT;
        if (transData.getTransState() == TransData.ETransStatus.ADJUSTED &&
                transData.getOfflineSendState() != null &&
                transData.getOfflineSendState() != TransData.OfflineStatus.OFFLINE_SENT) {
            amount = transData.getOrigHomeCurrencyAmount();
        }
        if (isAdjusted && isOfflineNotSent) {
            transData.setNotSendHomeCurrencyAmount(transData.getHomeCurrencyAmount()); //temp value to show on the receipt for non-piggyback
        }

        if (transData.getHomeCurrency().getCode().equals("392") || transData.getHomeCurrency().getCode().equals("410")) {
            amount = amount + "00";
        }

        setBitData("6", amount);
        }
    }

    /**
     * setBitData14
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData14(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("14", transData.getExpDate());
    }

    /**
     * setBitData22
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        String enterMode = getInputMethod(transData);
        if (transData.getIssuer().getName().equals("UnionPay")||
                (transData.getIssuer().getName().equals("HASE_CUP") &&
                        transData.getEnterMode() == TransData.EnterMode.CLSS) || ("HASE_CUP".equals(transData.getAcquirer().getName()) &&
                transData.getEnterMode() == TransData.EnterMode.FALLBACK)) {
            enterMode = "12";
        }
        setBitData("22", Component.getPaddedString(enterMode, 4, '0'));
    }

    /**
     * setBitData23
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData23(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().getName().equals("HASE_CUP")) {
            setBitData("23", transData.getCardSerialNo());
        }
    }

    /**
     * setBitData37
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData37(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("37", transData.getOrigRefNo());
    }

    /**
     * setBitData54
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @SuppressLint("DefaultLocale")
    @Override
    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getTransType() == ETransType.VOID
                && transData.getTransState() == TransData.ETransStatus.ADJUSTED) {
            String TempStr = null;
            String homeTipAmount = null;
            if (transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT) {
                TempStr = String.format("%012d", transData.getOrgTipAmount());
                homeTipAmount = transData.getOrigHomeCurrencyTipAmount();
            } else if (transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_SENT) {
                TempStr = String.format("%012d", transData.getTipAmount());
                homeTipAmount = transData.getHomeCurrencyTipAmount();
            }

            if (TempStr == null || homeTipAmount == null) {
                return;
            }

            if (transData.isDcc()) {
                /*Raymond 20220708: fix DCC void incorrect tip amount format
                homeTipAmount += FinancialApplication.getConvert().stringPadding(homeTipAmount, '0', 12, IConvert.EPaddingPosition.PADDING_LEFT);
                setBitData("54", homeTipAmount);*/
                TempStr += FinancialApplication.getConvert().stringPadding(homeTipAmount, '0', 12, IConvert.EPaddingPosition.PADDING_LEFT);
                setBitData("54", TempStr);
                return;
            }

            setBitData("54", TempStr);
        }
    }

    /**
     * setBitData55
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData55(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getOrigTransType() == ETransType.SALE
                && transData.getEnterMode() == TransData.EnterMode.FALLBACK) {
            return;
        }
        super.setBitData55(transData);
    }

    /**
     * setBitData56
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData56(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getOrigTransType() == ETransType.REFUND) {
            return;
        }
        if (transData.getOrigTransType() == ETransType.INSTALMENT) {
            return;
        }
        if (transData.getAcquirer().getName().equals("HASE_CUP")) {
            return;
        }
        super.setBitData56(transData);
    }

    /**
     * setBitData62
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData62(@NonNull TransData transData) throws Iso8583Exception {
        if (!transData.getAcquirer().getName().equals("HASE_CUP") && !transData.isDcc()) {
            //should be the original transaction invoice no
            setBitData("62", Component.getPaddedNumber(transData.getInvoiceNo(), 6));
        }
    }
}
