package com.pax.pay.trans.action.activity.emi;


import static com.pax.pay.utils.ECDSASignature.convertToPipeSeparatedString;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.TokenRequestData;
import com.pax.data.emi.TokenResponseData;
import com.pax.device.Device;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.utils.ECDSASignature;
import com.pax.sbipay.BuildConfig;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

public class GenerationTokenActivity extends BaseActivityWithTickForAction {
    private static final String TAG = "GenerationTokenActivity";
    private static final String REQUEST_TYPE = "TOKENGENERATION";
    private static final int REQUEST_CODE = 118;
    private TokenRequestData tokenRequestData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Device.enableStatusBar(false);
        Device.enableHomeRecentKey(false);
        tickTimer.stop();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(tokenRequestData);
            String pipeSeparatedString = convertToPipeSeparatedString(jsonString, null);
            LogUtils.d(TAG, "tokenRequestSeparatedString: " + pipeSeparatedString);
            if (pipeSeparatedString == null || pipeSeparatedString.isEmpty()) {
                finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, null));
                finish();
                return;
            }

            String signature = ECDSASignature.generateSignature(pipeSeparatedString);
            if (signature == null || signature.isEmpty()) {
                finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, null));
                finish();
                return;
            }
            String token = generateTokenRequest(tokenRequestData, signature);
            LogUtils.d(TAG, "request data:" + token);
            if (token == null) {
                finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, null));
                finish();
            } else {
                launchComponent(REQUEST_TYPE, token, REQUEST_CODE);
            }
        } catch (JsonProcessingException e) {
            finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, null));
            finish();
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_generation_token;
    }

    @Override
    protected void initViews() {
        enableActionBar(false);
    }

    @Override
    protected void setListeners() {
        // do nothing
    }

    @Override
    protected void loadParam() {
        tokenRequestData = (TokenRequestData) getIntent().getSerializableExtra(EUIParamKeys.TOKEN_REQUEST_DATA.toString());
    }

    @Override
    protected String getTitleString() {
        return "Token Generation";
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (REQUEST_CODE == resultCode && data != null) {
            String result = data.getStringExtra("RESULT");
            LogUtils.d(TAG, "EMI SDK Response RESULT: " + result);
            // 使用 Jackson 将 JSON 字符串转换为实体类
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                TokenResponseData tokenResponseData = objectMapper.readValue(result, TokenResponseData.class);
                LogUtils.d(TAG, "Parsed Token Response Data To Entity:\n " + tokenResponseData.toString());
                StringBuilder dataPipeSeparated = new StringBuilder();
                dataPipeSeparated.append(tokenResponseData.getToken()).append("|")
                        .append(tokenResponseData.getSdkPublicKey());
                LogUtils.d(TAG, "Response Data Pipe Separated: " + dataPipeSeparated);
                boolean isTokenRight = verifyToken(tokenResponseData, dataPipeSeparated.toString());
                LogUtils.d(TAG, "Is Verify Token Success: " + isTokenRight);
                if (!isTokenRight) {
                    finish(new ActionResult(TransResult.ERR_VERIFY_SIGNATURE, null));
                } else if ("00".equals(tokenResponseData.getStatusCode())) {
                    SysParam.getInstance().set(getString(R.string.token), tokenResponseData.getToken());
                    SysParam.getInstance().set(getString(R.string.api_ref_number), tokenResponseData.getApiRefNumber());
                    SysParam.getInstance().set(getString(R.string.emi_backend_sdk_public_key), tokenResponseData.getSdkPublicKey());
                    finish(new ActionResult(TransResult.SUCC, tokenResponseData));
                    finish();
                } else {
                    finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, tokenResponseData.getStatusMsg()));
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "", e);
                finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, null));
            }
        } else {
            finish(new ActionResult(TransResult.ERR_TOKEN_GENERATION_FAIL, null));
        }
    }

    private boolean verifyToken(TokenResponseData tokenResponseData, String data) {
        //Token的签名验证需要一个初始化的public key后续的签名验证需要使用token中返回的public key
        String publicKeyStr;
        if (BuildConfig.USE_UAT_EMI_KEY) {
            publicKeyStr =
                    "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEsgNcvRnTWequwwkHtsCWcemjqMxr6CSWe7IwS+t5q48pZkpZ8V3GAu2+vSlBLO4DJRd8jCeUtkb68VGRB2kpBA==";
        } else {
            publicKeyStr =
                    "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAErum5teUDye2V4gE1ZBAHi6DYlsgkdKfKVsqOm6/c78vlsf4yE+jwJiD5SINDb8yrhBvf93AHPEE1P6wzuJp8FA==";

        }
        if (data != null && tokenResponseData.getEmiDigiSign() != null && tokenResponseData.getSdkPublicKey() != null) {
            return ECDSASignature.verifySignature(data, tokenResponseData.getEmiDigiSign(), publicKeyStr);
        }
        return false;
    }

    private String generateTokenRequest(TokenRequestData requestData, String signature) {
        requestData.setAggregatorDigiSign(signature);
        requestData.setAggregatorCode("SBIAGGREGATOR");
        requestData.setMerchantName("Rathod Sales");
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(requestData);
        } catch (JsonProcessingException e) {
            LogUtils.e(TAG, "", e);
            return null;
        }
    }
}