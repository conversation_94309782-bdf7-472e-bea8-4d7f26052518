package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.edc.opensdk.TransResult;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.RedeemConfig;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import java.util.HashMap;
import java.util.Map;

import static com.pax.pay.utils.Utils.subBytes;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/14.
 */

public class PackSalesWithCashDollar extends PackIso8583 {
    public PackSalesWithCashDollar(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[] { 2, 3, 4, 11, 14, 22, 23, 24, 25, 35, 41, 42, 54, 55, 56, 61, 63 };
    }

    /**
     * setBitData23
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData23(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getIssuer().getName().equals("HASE_CUP")) {
            setBitData("23", transData.getCardSerialNo());
        }
    }

    /**
     * setBitData54
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    protected void setBitData54(@NonNull TransData transData) throws Iso8583Exception {
        long TempChange = transData.getTipAmount();
        if (TempChange > 0) {
            String TempStr = String.format("%012d", TempChange);
            setBitData("54", TempStr);
        }
    }

    /**
     * setBitData61
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData61(@NonNull TransData transData) throws Iso8583Exception {
        if (transData.getAcquirer().isEnableRefNo() && !TextUtils.isEmpty(
                transData.getAcqReferenceNo())) {
            setBitData("61", Component.getPaddedString(transData.getAcqReferenceNo(), 21, ' '));
        }
    }

    //to cater the document

    /**
     * setBitData63
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        String airLineTicketNumber = "";
        String f63;
        TransData.EnterMode enterMode = transData.getEnterMode();
        if (enterMode == TransData.EnterMode.INSERT
                || enterMode == TransData.EnterMode.CLSS
                || enterMode == TransData.EnterMode.FALLBACK) {
            airLineTicketNumber = Component.getPaddedString(" ", 16, ' ');
        }
        String tableLength = new String(new byte[] { 0x00, 0x03 });
        String tableID = "24";
        String terminalEntryCapability = getTermEntryCap(transData);
        String loyaltyInfoFlag = "L";
        String redemptionFlag = transData.getRedemptionFlag();
        String specRedmFuncFlag = RedeemConfig.SF_NOT_REDM_ONLY;
        if(enterMode == TransData.EnterMode.SWIPE || enterMode == TransData.EnterMode.MANUAL){
            f63 = loyaltyInfoFlag + redemptionFlag + specRedmFuncFlag;
        }else{
            f63 = airLineTicketNumber + tableLength + tableID + terminalEntryCapability + loyaltyInfoFlag + redemptionFlag + specRedmFuncFlag;
        }
        setBitData("63", f63);
        transData.setField63(f63);
    }

    @Override
    protected int UnpackF63(@NonNull TransData transData, byte[] buffer) {
        int len = buffer.length;

        if (len != 113 && len != 5 && len != 2) {
            return TransResult.ERR_UNPACK;
        }

        Map<String, TransData.LYSStatus> lysStatusMap = new HashMap<>();
        lysStatusMap.put("0", TransData.LYSStatus.LYS_NOK);
        lysStatusMap.put("1", TransData.LYSStatus.LYS_OK);
        lysStatusMap.put("2", TransData.LYSStatus.LYS_NO_HASE_CARD);
        lysStatusMap.put("3", TransData.LYSStatus.LYS_REDEEM_NOT_ALLOW);

        //LYS is not available / it is not HASE card / redemption is not allowed.
        if (len == 5) {
            String promotionCode = new String(subBytes(buffer, 0, 3));
            transData.setPromotionCode(promotionCode);
            String lytTag = new String(subBytes(buffer, 3, 4));
            if (!"L".equals(lytTag)) {
                return TransResult.ERR_UNPACK;
            }
            String lysStatus = new String(subBytes(buffer, 4, 5));
            if (!"0".equals(lysStatus) && !"2".equals(lysStatus) && !"3".equals(lysStatus)) {
                return TransResult.ERR_UNPACK;
            }
            transData.setLysStatus(lysStatusMap.get(lysStatus));
            transData.setNetAmount(transData.getAmount());
            return TransResult.SUCC;
        }

        if (len == 2) {
            String lytTag = new String(subBytes(buffer, 0, 1));
            if (!"L".equals(lytTag)) {
                return TransResult.ERR_UNPACK;
            }
            String lysStatus = new String(subBytes(buffer, 1, 2));
            if (!"0".equals(lysStatus) && !"1".equals(lysStatus) && !"2".equals(lysStatus) &&
                    !"3".equals(lysStatus)) {
                return TransResult.ERR_UNPACK;
            }
            transData.setLysStatus(lysStatusMap.get(lysStatus));
            transData.setNetAmount(transData.getAmount());
            return TransResult.SUCC;
        }

        //len == 113, LYS is available
        //Promotion Message Code
        String promotionCode = new String(subBytes(buffer, 0, 3));
        transData.setPromotionCode(promotionCode);
        //Loyalty Info Tag
        String lytTag = new String(subBytes(buffer, 3, 4));
        if (!"L".equals(lytTag)) {
            return TransResult.ERR_UNPACK;
        }
        //LYS Status
        String lysStatus = new String(subBytes(buffer, 4, 5));
        if (!"1".equals(lysStatus)) {
            return TransResult.ERR_UNPACK;
        }
        transData.setLysStatus(TransData.LYSStatus.LYS_OK);
/*        //Trans Amount
        String orgAmount = Utils.bcd2Str(subBytes(buffer, 6, 11));
        if (Long.parseLong(orgAmount) != transData.getAmount()) {
            return TransResult.ERR_UNPACK;
        }*/
        // Net amount
        long netAmount = Long.parseLong(Utils.bcd2Str(subBytes(buffer, 11, 17)));
        transData.setNetAmount(netAmount);
        // Cash$ bonus redeemed
        long bonusRedeem = Long.parseLong(Utils.bcd2Str(subBytes(buffer, 17, 23)));
        transData.setCashDolBonusRedeemed(bonusRedeem);
        //Loyalty Program Details
        for (int i = 0; i < 3; i++) {
            int index = 23 + i * 30;

            //sequence: Hang Seng Cash$, Merchant Cash$1, Merchant Cash$2
            String programId = new String(subBytes(buffer, index, index + 3));
            if ("   ".equals(programId) || "000".equals(programId)) {
                continue;
            }
            String cashRedeemed = Utils.bcd2Str(subBytes(buffer, index + 3, index + 9));
            String cashBalance = Utils.bcd2Str(subBytes(buffer, index + 9, index + 15));
            String cashBalanceSign = new String(subBytes(buffer, index + 15, index + 16));
            String cashExpDate = Utils.bcd2Str(subBytes(buffer, index + 16, index + 20));
            String expCashBalance = Utils.bcd2Str(subBytes(buffer, index + 20, index + 26));
            String expCashExpDate = Utils.bcd2Str(subBytes(buffer, index + 26, index + 30));

            if (i == 0) {
                transData.setCashDolProgramId(programId);
                transData.setCashDolRedeemed(Long.parseLong(cashRedeemed));
                transData.setCashDolBalance(Long.parseLong(cashBalance));
                transData.setCashDolBalanceSign(cashBalanceSign);
                transData.setCashDolExpdate(cashExpDate);
                transData.setCashDolExpBalance(Long.parseLong(expCashBalance));
                transData.setCashDolExpBalanceExpDate(expCashExpDate);
            } else if (i == 1) {
                transData.setMer1ProgramId(programId);
                transData.setMer1CashDolRedeemed(Long.parseLong(cashRedeemed));
                transData.setMer1CashDolBalance(Long.parseLong(cashBalance));
                transData.setMer1CashDolBalanceSign(cashBalanceSign);
                transData.setMer1CashDolExpdate(cashExpDate);
                transData.setMer1ExpiredCashDolBalance(Long.parseLong(expCashBalance));
                transData.setMer1ExpiredCashDolExpDate(expCashExpDate);
            } else {
                transData.setMer2ProgramId(programId);
                transData.setMer2CashDolRedeemed(Long.parseLong(cashRedeemed));
                transData.setMer2CashDolBalance(Long.parseLong(cashBalance));
                transData.setMer2CashDolBalanceSign(cashBalanceSign);
                transData.setMer2CashDolExpdate(cashExpDate);
                transData.setMer2ExpiredCashDolBalance(Long.parseLong(expCashBalance));
                transData.setMer2ExpiredCashDolExpDate(expCashExpDate);
            }
        }

        return TransResult.SUCC;
    }

}
