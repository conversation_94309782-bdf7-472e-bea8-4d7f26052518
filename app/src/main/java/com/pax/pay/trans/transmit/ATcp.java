/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.transmit;

import com.pax.dal.IDalCommManager;
import com.pax.dal.entity.LanParam;
import com.pax.dal.entity.MobileParam;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.gl.commhelper.IComm;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

public abstract class ATcp {

    protected static final String TAG = "ATCP";

    private static boolean balanceSwitch = true;
    private boolean enableBalance;
    protected IComm client;
    private String hostIp;
    private int hostPort;
    private String backupIp;
    private int backupPort;
    private static boolean LastTransReversal = false;


    private int connectTimeout = 20000;//ms
    private int sendTimeout = 10000;//ms
    private int receiveTimeout = 30000;//ms

    protected TransProcessListener transProcessListener;

    /**
     * 建立连接
     */
    public abstract int onConnect(boolean isReversal, boolean ecrMode);

    /**
     * 发送数据
     *
     * @param data
     * @return
     */
    public abstract int onSend(byte[] data,String acquirerName);

    /**
     * 接收数据
     *
     * @return
     */
    public abstract TcpResponse onRecv(String acqName);

    /**
     * 关闭连接
     */
    public abstract void onClose();

    public String getHostIp() {
        return hostIp;
    }

    public void setHostIp(String hostIp) {
        this.hostIp = hostIp;
    }

    public int getHostPort() {
        return hostPort;
    }

    public void setHostPort(int hostPort) {
        this.hostPort = hostPort;
    }

    public String getBackupIp() {
        return backupIp;
    }

    public void setBackupIp(String backupIp) {
        this.backupIp = backupIp;
    }

    public int getBackupPort() {
        return backupPort;
    }

    public void setBackupPort(int backupPort) {
        this.backupPort = backupPort;
    }

    public boolean isEnableBalance() {
        return enableBalance;
    }

    public void setEnableBalance(boolean enableBalance) {
        this.enableBalance = enableBalance;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getSendTimeout() {
        return sendTimeout;
    }

    public void setSendTimeout(int sendTimeout) {
        this.sendTimeout = sendTimeout;
    }

    public int getReceiveTimeout() {
        return receiveTimeout;
    }

    public void setReceiveTimeout(int receiveTimeout) {
        this.receiveTimeout = receiveTimeout;
    }

    protected void balanceHost() {
        balanceSwitch = !balanceSwitch;
    }

    protected String balancedIp() {
        return balanceSwitch ? hostIp : backupIp;
    }

    protected int balancedPort() {
        return balanceSwitch ? hostPort : backupPort;
    }

    public static boolean isLastTransReversal() {
        return LastTransReversal;
    }

    public static void setLastTransReversal(boolean lastTransReversal) {
        LastTransReversal = lastTransReversal;
    }

    /**
     * 设置监听器
     */
    protected void setTransProcessListener(TransProcessListener listener) {
        this.transProcessListener = listener;
    }

    protected void onShowMsg(String msg, int timeoutSec) {
        if (transProcessListener != null) {
            transProcessListener.onShowProgress(msg, timeoutSec);
        }
    }

    protected void onHideMsg() {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }
    }

    /**
     * 参数设置和路由选择
     *
     * @return
     */
    protected int setCommParam() {
        IDalCommManager commManager = FinancialApplication.getDal().getCommManager();
        SysParam sysParam = FinancialApplication.getSysParam();
        String commTypeStr = sysParam.get(SysParam.StringParam.COMM_TYPE);
        SysParam.Constant.CommType commType = SysParam.Constant.CommType.valueOf(commTypeStr);
        switch (commType) {
            case LAN:
                commManager.setLanParam(loadLanParam(sysParam));
                break;
            case MOBILE:
                // mobile参数设置
                commManager.setMobileParam(loadMobileParam(sysParam));
                break;
            case DEMO:
                onShowMsg(Utils.getString(R.string.wait_demo_mode), 5);
                return TransResult.SUCC;
            case WIFI:
                break;
            default:
                return TransResult.ERR_CONNECT;

        }

        return TransResult.SUCC;
    }

    private MobileParam loadMobileParam(SysParam sysParam) {
        MobileParam param = new MobileParam();
        param.setApn(sysParam.get(SysParam.StringParam.MOBILE_APN));
        param.setPassword(sysParam.get(SysParam.StringParam.MOBILE_PWD));
        param.setUsername(sysParam.get(SysParam.StringParam.MOBILE_USER));
        return param;
    }

    private LanParam loadLanParam(SysParam sysParam) {
        LanParam lanParam = new LanParam();
        lanParam.setDhcp(sysParam.get(SysParam.BooleanParam.LAN_DHCP));
        lanParam.setDns1(sysParam.get(SysParam.StringParam.LAN_DNS1));
        lanParam.setDns2(sysParam.get(SysParam.StringParam.LAN_DNS2));
        lanParam.setGateway(sysParam.get(SysParam.StringParam.LAN_GATEWAY));
        lanParam.setLocalIp(sysParam.get(SysParam.StringParam.LAN_LOCAL_IP));
        lanParam.setSubnetMask(sysParam.get(SysParam.StringParam.LAN_NETMASK));
        return lanParam;
    }

}
