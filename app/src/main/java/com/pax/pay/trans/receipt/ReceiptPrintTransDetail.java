/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import com.pax.dal.exceptions.PrinterDevException;
import com.pax.data.entity.TransData;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

import java.util.ArrayList;
import java.util.List;

/**
 * print detail
 *
 * <AUTHOR>
 */
public class ReceiptPrintTransDetail extends AReceiptPrint {

    public int print(String title, List<TransData> list, PrintListener listener) {
        this.listener = listener;
        int count = 0;

        if (listener != null)
            listener.onShowMessage(null, Utils.getString(R.string.wait_print));
        // print detail main information
        ReceiptGeneratorTransDetail receiptGeneratorTransDetail = new ReceiptGeneratorTransDetail();
        try {
            int ret = printBitmap(receiptGeneratorTransDetail.generateMainInfo(title));
            if (ret != 0) {
                if (listener != null) {
                    listener.onEnd();
                }
                return ret;
            }
            List<TransData> details = new ArrayList<>();
            for (TransData data : list) {
                details.add(data);
                count++;
                // 开启分页，防止数量过大时ScollView无法缓存导致无法成功转换bitmap，导致打印异常的问题
                if (count == list.size() || count % 20 == 0) {
                    receiptGeneratorTransDetail = new ReceiptGeneratorTransDetail(details);
                    ret = printBitmap(receiptGeneratorTransDetail.generateBitmap());
                    if (ret != 0) {
                        if (listener != null) {
                            listener.onEnd();
                        }
                        return ret;
                    }
                    details.clear();
                }

            }
            ret = printBitmap(receiptGeneratorTransDetail.genTotal());

            printStr("\n\n");
            if (listener != null) {
                listener.onEnd();
            }

            return ret;
        } catch (PrinterDevException e) {
            if (listener != null) {
                listener.onEnd();
                listener.onConfirm(null, String.format("%s %s", e.getErrModule(), e.getErrMsg()));
            }
        }
        return -1;
    }

    public View generateDetailView(Context context, String title, List<TransData> list) {
        // 创建一个 ScrollView 作为容器，使得内容可以滚动
        ScrollView scrollView = new ScrollView(context);
        // 禁用滚动条
        scrollView.setScrollbarFadingEnabled(false); // 禁用滚动条消失的效果
        scrollView.setVerticalScrollBarEnabled(false); // 隐藏垂直滚动条

        // 创建一个 LinearLayout 作为子容器
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);  // 设置垂直排列

        int count = 0;
        ReceiptGeneratorTransDetail receiptGeneratorTransDetail = new ReceiptGeneratorTransDetail();
        List<TransData> details = new ArrayList<>();

        // 添加主信息 Bitmap 到 View
        Bitmap mainInfoBitmap = receiptGeneratorTransDetail.generateMainInfo(title);
        if (mainInfoBitmap != null) {
            container.addView(Utils.createImageView(context, mainInfoBitmap));
        }

        // 遍历打印明细的 Bitmap
        for (TransData data : list) {
            details.add(data);
            count++;

            // 分页处理，每 20 条记录生成一个 Bitmap
            if (count == list.size() || count % 20 == 0) {
                receiptGeneratorTransDetail = new ReceiptGeneratorTransDetail(details);
                Bitmap detailBitmap = receiptGeneratorTransDetail.generateBitmap();
                if (detailBitmap != null) {
                    container.addView(Utils.createImageView(context, detailBitmap));
                }
                details.clear();
            }
        }

        // 添加总计 Bitmap 到 View
        Bitmap totalBitmap = receiptGeneratorTransDetail.genTotal();
        if (totalBitmap != null) {
            container.addView(Utils.createImageView(context, totalBitmap));
        }

        // 将 LinearLayout 添加到 ScrollView 中
        scrollView.addView(container);

        // 返回包含所有 Bitmap 的可滚动视图
        return scrollView;
    }
}
