/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.action;

import static com.pax.pay.constant.Constants.ISSUER_RUPAY;

import android.app.Activity;
import android.content.Context;
import android.os.ConditionVariable;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransData.EnterMode;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.device.DeviceImplNeptune;
import com.pax.eemv.EmvImpl;
import com.pax.eemv.IEmv;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.eventbus.EmvCallbackEvent;
import com.pax.jemv.device.DeviceManager;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvListenerImpl;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.activity.SelectCurrencyActivity;
import com.pax.pay.trans.action.activity.TransPreviewActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.trans.transmit.Transmit;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class ActionEmvProcess extends AAction {
    private Context context;
    private IEmv emv;
    private TransData transData;
    private TransProcessListener transProcessListener;
    private EmvListenerImpl emvListener;
    private EmvCallbackEvent.Status status;

    public ActionEmvProcess(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, IEmv emv, TransData transData) {
        this.context = context;
        this.emv = emv;
        this.transData = transData;
        transProcessListener = new TransProcessListenerImpl(context);
        emvListener = new EmvListenerImpl(context, emv, transData, transProcessListener);
    }

    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onCardNumConfirmEvent(EmvCallbackEvent event) {
        status = (EmvCallbackEvent.Status) event.getStatus();
        switch (status) {
            case OFFLINE_PIN_ENTER_READY:
                emvListener.offlinePinEnterReady();
                break;
            case CARD_NUM_CONFIRM_SUCCESS:
                emvListener.cardNumConfigSucc((String[]) event.getData());
                break;
            case TIMEOUT:
                emvListener.onTimeOut();
                break;
            case CARD_NUM_CONFIRM_ERROR:
            default:
                emvListener.cardNumConfigErr();
                break;
        }
    }

    @Override
    protected void process() {
        DeviceManager.getInstance().setIDevice(DeviceImplNeptune.getInstance());
        FinancialApplication.getApp().runInBackground(new ProcessRunnable());
    }

    private class ProcessRunnable implements Runnable {
        private final EmvTransProcess emvTransProcess;

        ProcessRunnable() {
            if (transData.getEnterMode() == EnterMode.INSERT) {
                transProcessListener.onShowProgress(context.getString(R.string.wait_process), 0);
            }
            emvTransProcess = new EmvTransProcess(emv);
            emvTransProcess.init();
        }

        @Override
        public void run() {
            try {
                FinancialApplication.getApp().register(ActionEmvProcess.this);
                ConditionVariable cv = new ConditionVariable();
                transProcessListener.onShowProgress(context.getString(R.string.wait_process), 0, cv);
                // 等待弹窗显示出来再继续，防止弹窗一直显示不会取消的异常
                cv.close();
                cv.block();
                CTransResult result = emvTransProcess.transProcess(transData, emvListener);
                transProcessListener.onHideProgress();

                if (result.getTransResult() != ETransResult.ABORT_TERMINATED) {
                    //AET-260
                    if (!((transData.getTransType() == ETransType.SALE_ADVICE || transData.getTransType() == ETransType.PREAUTH_ADVICE)
                            && (result.getTransResult() == ETransResult.ERR_CONNECT))) {
                        updateReversalStatus();
                    }
                    //交易完成后判断交易是否需要冲正，需要则进入冲正流程
                    if (transData.isOnlineTrans()) {
                        try {
                            int ret = new Transmit().sendReversal(transProcessListener);
                            if (ret == TransResult.SUCC && transData.getReversalStatus() == TransData.ReversalStatus.PENDING) {
                                transData.setReversalStatus(TransData.ReversalStatus.REVERSAL_COM);
                            }
                        } catch (PedDevException e) {
                            LogUtils.e(TAG, "", e);
                            transProcessListener.onShowErrMessage(e.getMessage(), Constants.FAILED_DIALOG_SHOW_TIME, false);
                        }
                    }

                    transProcessListener.onHideMessage();
                    setResult(new ActionResult(TransResult.SUCC, result));
                }
                //reset the isTimeout state
                EmvImpl.isTimeOut = false;
            } catch (EmvException e) {
                LogUtils.e(TAG, "", e);
                handleException(e);
            } finally {
                byte[] value95 = emv.getTlv(0x95);
                byte[] value9B = emv.getTlv(0x9B);

                if (value95 != null) {
                    LogUtils.e("TLV", "95:" + ConvertHelper.getConvert().bcdToStr(value95));
                }
                if (value9B != null) {
                    LogUtils.e("TLV", "9b:" + ConvertHelper.getConvert().bcdToStr(value9B));
                }

                // no memory leak
                emv.setListener(null);
                FinancialApplication.getApp().unregister(ActionEmvProcess.this);
            }
        }

        private void handleException(EmvException e) {
            EmvImpl.isTimeOut = false;
            ETransType transType = transData.getTransType();
            boolean needShowReversal = ActivityStack.getInstance().top() instanceof SelectCurrencyActivity;
            ActivityStack.getInstance().popTo((Activity) context);
            if (Component.isDemo() && e.getErrCode() == EEmvExceptions.EMV_ERR_UNKNOWN.getErrCodeFromBasement()) {
                transProcessListener.onHideProgress();
                updateReversalStatus();
                // end the EMV process, and continue a mag process
                setResult(new ActionResult(TransResult.SUCC, ETransResult.ARQC));
                return;
            }

            if (status == EmvCallbackEvent.Status.TIMEOUT && transData != null && transType != null) {
                transProcessListener.onShowErrMessage(transType.getTransName(), context.getString(R.string.err_timeout), Constants.FAILED_DIALOG_SHOW_TIME, false);
            } else if (e.getErrCode() != EEmvExceptions.EMV_ERR_UNKNOWN.getErrCodeFromBasement() && transData != null) {
                if (e.getErrCode() == EEmvExceptions.EMV_ERR_FALL_BACK.getErrCodeFromBasement()) {
                    byte searchCardMode = Component.getCardReadMode(transType);
                    if ((searchCardMode & 0x01) != 0x01 || (!(transType.equals(ETransType.SALE) || transType.equals(ETransType.SALE_CASH) || transType.equals(ETransType.CASH_ONLY) || transType.equals(ETransType.PREAUTH)))) { // Check if swipe is supported
                        transProcessListener.onShowErrMessage(context.getString(R.string.prompt_not_support_fall_back), Constants.FAILED_DIALOG_SHOW_TIME, false);
                        transProcessListener.onHideProgress();
                        setResult(new ActionResult(TransResult.ERR_ABORTED, null));
                        return;
                    }
                    Device.beepErr();
                    transProcessListener.onShowNormalMessage(context.getString(R.string.prompt_fall_back), Constants.SUCCESS_DIALOG_SHOW_TIME, true);
                    transProcessListener.onHideProgress();
                    setResult(new ActionResult(TransResult.NEED_FALL_BACK, null));
                    return;
                } else {
                    transProcessListener.onHideProgress();
                    //对交易存在的异常情况进行冲正处理
                    if (transData.isOnlineTrans() && (transData.getOfflineSendState() == null)) {
                            try {
                                if (e.getErrCode() == EEmvExceptions.EMV_ERR_REVELSAL.getErrCodeFromBasement() && ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName())) {
                                    if (ETransType.QSPARC_BALANCE_UPDATE.equals(transData.getTransType())) {
                                        //  RUPAY.QCD.081测试认证case要求
                                        transData.setDupReason("E2");
                                    } else {
                                        transData.setDupReason("22");
                                    }
                                }
                                // 如果是DCC选汇界面发起的reversal，且是icc报错，则在reversal前提示一下该错误
                                if (needShowReversal && EEmvExceptions.EMV_ERR_ICC_CMD.getErrMsg().equals(e.getErrMsg())) {
                                    transProcessListener.onShowErrMessage(e.getMessage(), Constants.FAILED_DIALOG_SHOW_TIME, false);
                                }
                                GreendaoHelper.getTransDataHelper().update(transData);//在交易冲正前更新数据库数据，保证冲正流程中获取到的数据是最新值
                                new Transmit().sendReversal(transProcessListener);
                            } catch (PedDevException ex) {
                                transProcessListener.onShowErrMessage(ex.getMessage(), Constants.FAILED_DIALOG_SHOW_TIME, false);
                            }
                    }
                    if (transData.getResponseCode() != null && !"00".equals(transData.getResponseCode().getCode())) {
                        transProcessListener.onShowErrMessage(transData.getResponseCode().getMessage() + " (" + transData.getResponseCode().getCode() + ")", Constants.FAILED_DIALOG_SHOW_TIME, false);
                    } else {
                        //如果是在选汇界面超时所发起的reversal，需要与其他reversal保持一直，显示已执行reversal
                        if (needShowReversal && ((EEmvExceptions.EMV_ERR_TIMEOUT.getErrMsg().equals(e.getErrMsg())) ||
                                EEmvExceptions.EMV_ERR_ICC_CMD.getErrMsg().equals(e.getErrMsg()))) {
                            transProcessListener.onShowErrMessage(Utils.getString(R.string.error_reversal_executed), Constants.FAILED_DIALOG_SHOW_TIME, false);
                        } else {
                            transProcessListener.onShowErrMessage(e.getErrMsg(), Constants.FAILED_DIALOG_SHOW_TIME, false);
                        }
                    }
                }
            }
            transProcessListener.onHideProgress();

            if (!(ActivityStack.getInstance().top() instanceof TransPreviewActivity)) {
                setResult(new ActionResult(TransResult.ERR_ABORTED, null));
            }
        }

        private void updateReversalStatus() {
            transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            transData.setDupReason("");
            GreendaoHelper.getTransDataHelper().update(transData);
        }
    }
}

