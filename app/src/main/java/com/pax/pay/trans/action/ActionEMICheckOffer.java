package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.data.entity.Acquirer;
import com.pax.data.emi.BankEMIOfferRequestData;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.emi.BankEMIOfferActivity;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

public class ActionEMICheckOffer extends AAction {
    private Context context;
    private String amount;

    public ActionEMICheckOffer(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context, String amount) {
        this.context = context;
        this.amount = amount;
    }

    @Override
    protected void process() {
        Intent intent = new Intent(context, BankEMIOfferActivity.class);
        BankEMIOfferRequestData bankEMIOfferRequestData = new BankEMIOfferRequestData();
        String name = SysParam.getInstance().getString(R.string.ACQ_NAME);
        Acquirer acquirer = GreendaoHelper.getAcquirerHelper().findAcquirer(name);
        bankEMIOfferRequestData.setTid(acquirer.getTerminalId());
        bankEMIOfferRequestData.setMid(acquirer.getMerchantId());
        bankEMIOfferRequestData.setDeviceModel("PAX");
        bankEMIOfferRequestData.setAmount(Utils.insertDecimalPoint(amount));
        bankEMIOfferRequestData.setMode("OFFER");
        bankEMIOfferRequestData.setToken(SysParam.getInstance().getString(Utils.getString(R.string.token)));
        bankEMIOfferRequestData.setApiRefNumber(SysParam.getInstance().getString(Utils.getString(R.string.api_ref_number)));
        intent.putExtra(EUIParamKeys.BANK_EMI_OFFER_DATA.toString(), bankEMIOfferRequestData);
        context.startActivity(intent);
    }
}
