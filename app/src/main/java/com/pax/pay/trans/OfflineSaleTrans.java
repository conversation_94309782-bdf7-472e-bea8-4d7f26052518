/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         lixc                    Create
 * ===========================================================================================
 */
package com.pax.pay.trans;

import android.content.Context;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.action.ActionAdjustTip;
import com.pax.pay.trans.action.ActionClssPreProc;
import com.pax.pay.trans.action.ActionClssProcess;
import com.pax.pay.trans.action.ActionEmvProcess;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionSearchCard;
import com.pax.pay.trans.action.ActionSelectAccount;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

import java.lang.ref.WeakReference;

public class OfflineSaleTrans extends BaseTrans {

    private byte searchCardMode = -1; // search card mode
    private float percent;
    private byte currentMode;
    private PrintTask printTask;

    /**
     * @param context :context
     */
    public OfflineSaleTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.OFFLINE_TRANS_SEND, transListener);
        if (searchCardMode == -1) { // 待机银行卡消费入口
            searchCardMode = Component.getCardReadMode(ETransType.OFFLINE_TRANS_SEND);
            this.transType = ETransType.OFFLINE_TRANS_SEND;
        }
    }

    @Override
    public void bindStateOnAction() {
        // enter trans amount action(This action is mainly used to handle bank card consumption and flash close paid deals)
        ActionEnterAmount amountAction = new ActionEnterAmount(action -> ((ActionEnterAmount) action).setParam(getCurrentContext(),
                getString(R.string.trans_offline), false));
        bind(State.ENTER_AMOUNT.toString(), amountAction, true);

        // search card action
        ActionSearchCard searchCardAction = new ActionSearchCard(action -> ((ActionSearchCard) action).setParam(getCurrentContext(), getString(R.string.trans_offline), searchCardMode, transData.getAmount(),
                null, ""));
        bind(State.CHECK_CARD.toString(), searchCardAction, true);

        //adjust tip action
        ActionAdjustTip adjustTipAction = new ActionAdjustTip(action -> {
            String amount = String.valueOf(Utils.parseLongSafe(transData.getAmount(), 0) -
                    Utils.parseLongSafe(transData.getTipAmount(), 0));
            ((ActionAdjustTip) action).setParam(getCurrentContext(), getString(R.string.trans_offline), amount, percent);
        });
        bind(State.ADJUST_TIP.toString(), adjustTipAction, true);

        // emv process action
        ActionEmvProcess emvProcessAction = new ActionEmvProcess(action -> ((ActionEmvProcess) action).setParam(getCurrentContext(), emv, transData));
        bind(State.EMV_PROC.toString(), emvProcessAction);

        //clss process action
        ActionClssProcess clssProcessAction = new ActionClssProcess(action -> ((ActionClssProcess) action).setParam(getCurrentContext(), clss, transData));
        bind(State.CLSS_PROC.toString(), clssProcessAction);

        //clss preprocess action
        ActionClssPreProc clssPreProcAction = new ActionClssPreProc(action -> ((ActionClssPreProc) action).setParam(clss, transData));
        bind(State.CLSS_PREPROC.toString(), clssPreProcAction);

        //select account
        ActionSelectAccount selectAccount = new ActionSelectAccount(action ->
                ((ActionSelectAccount) action).setParam(context, getString(R.string.trans_offline)));
        bind(State.SELECT_ACCOUNT.toString(), selectAccount);

        //Enter digits Number action
        ActionInputTransData inputFourDigits = new ActionInputTransData(action -> {
            String pan = transData.getPan();
            Context context = getCurrentContext();
            ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_offline))
                    .setInputLine(getString(R.string.prompt_four_digits), ActionInputTransData.EInputType.NUM, 4, 4,
                            pan.substring(pan.length() - 4), getString(R.string.trans_invalid_digits));
        });
        bind(State.INPUT_DIGITS.toString(), inputFourDigits, true);

        // signature action
        ActionSignature signatureAction = new ActionSignature(action -> ((ActionSignature) action).setParam(getCurrentContext(), transData.isDcc() ? transData.getInternationalCurrencyAmount() : transData.getAmount(),
                transData.isDcc() ? transData.getInternationalCurrency().getCode() : transData.getLocalCurrency().getCode()));
        bind(State.SIGNATURE.toString(), signatureAction);

        //e charge slip
        EChargeSlipTask chargeSlipTask = new EChargeSlipTask(getCurrentContext(), getString(R.string.trans_offline), transData, EChargeSlipTask.genTransEndListener(OfflineSaleTrans.this, State.E_CHARGE_SLIP.toString()));
        bind(State.E_CHARGE_SLIP.toString(), chargeSlipTask);

        //print preview action
        printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(OfflineSaleTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        // perform the first action
        gotoState(State.ENTER_AMOUNT.toString());
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        int ret = result.getRet();
        State state = State.valueOf(currentState);
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType, true);
            if (f55Dup.length > 0) {
                TransData dupTransData = GreendaoHelper.getTransDataHelper().findFirstDupRecord();
                if (dupTransData != null) {
                    dupTransData.setDupIccData(ConvertHelper.getConvert().bcdToStr(f55Dup));
                    GreendaoHelper.getTransDataHelper().update(dupTransData);
                }
            }
            if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }

        if (state == State.CLSS_PREPROC && ret != TransResult.SUCC) {
            searchCardMode &= 0x03;
        }

        switch (state) {
            case ENTER_AMOUNT:
                // save trans amount
                transData.setAmount(result.getData().toString());
                goTipBranch();
                break;
            case CHECK_CARD: // subsequent processing of check card
                onCheckCard(result);
                break;
            case ADJUST_TIP:
                onAdjustTip(result);
                break;
            case SELECT_ACCOUNT:
                onSelectAccount(result);
                break;
            case INPUT_DIGITS:
                toSignature();
                break;
            case EMV_PROC: // emv后续处理
                //get trans result
                CTransResult transResult = (CTransResult) result.getData();
                // EMV完整流程 脱机批准或联机批准都进入签名流程
                afterEMVProcess(transResult.getTransResult());
                break;
            case CLSS_PREPROC:
                gotoState(State.CHECK_CARD.toString());
                break;
            case CLSS_PROC:
                CTransResult clssResult = (CTransResult) result.getData();
                afterClssProcess(clssResult);
                break;
            case SIGNATURE:
                // save signature data
                byte[] signData = (byte[]) result.getData();
                byte[] signPath = (byte[]) result.getData1();

                if (signData != null && signData.length > 0 &&
                        signPath != null && signPath.length > 0) {
                    transData.setSignData(signData);
                    transData.setSignPath(signPath);
                    // update trans data，save signature
                    GreendaoHelper.getTransDataHelper().update(transData);
                }

                //check offline trans
                toChargeSlipOrPrint();
                break;
            case E_CHARGE_SLIP:
                String chooseResult = (String) result.getData();
                if (Constants.E_CHARGE_SLIP.equals(chooseResult)) {
                    transEnd(new ActionResult(TransResult.SUCC, null));
                } else if (Constants.NO_CHARGE_SLIP.equals(chooseResult)) {
                    printTask.setParam(true);
                    gotoState(CashOnlyTrans.State.PRINT.toString());
                } else {
                    gotoState(CashOnlyTrans.State.PRINT.toString());
                }
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }

    private void onCheckCard(ActionResult result) {
        ActionSearchCard.CardInformation cardInfo = (ActionSearchCard.CardInformation) result.getData();
        saveCardInfo(cardInfo, transData);
        transData.setTransType(ETransType.OFFLINE_TRANS_SEND);
        // enter card number manually
        currentMode = cardInfo.getSearchMode();
        if (ActionSearchCard.SearchMode.isWave(currentMode)) {
            needRemoveCard = true;
            // AET-15
            gotoState(State.CLSS_PROC.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.SWIPE || currentMode == ActionSearchCard.SearchMode.KEYIN) {
            gotoState(State.SELECT_ACCOUNT.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.INSERT) {
            needRemoveCard = true;
            // EMV process
            gotoState(State.EMV_PROC.toString());
        }
    }

    private void onSelectAccount(ActionResult result) {
        if (result.getRet() == TransResult.ERR_TIMEOUT) {
            transEnd(result);
            return;
        }
        String accountType = "00";//default type
        if (result.getData() != null) {
            accountType = result.getData().toString();
        }
        transData.setAccountType(accountType);
        gotoState(State.INPUT_DIGITS.toString());
    }


    private void onAdjustTip(ActionResult result) {
        //get total amount
        String totalAmountStr = String.valueOf(CurrencyConverter.parse(result.getData().toString()));
        transData.setAmount(totalAmountStr);
        //get tip amount
        Long tip = CurrencyConverter.parse(result.getData1().toString());
        if (tip > 0) {
            transData.setTipAmount(String.valueOf(tip));
            transData.setHasTip(true);
        }
        gotoState(State.CLSS_PREPROC.toString());
    }

    private void toChargeSlipOrPrint() {
        if (!Utils.isA50() || SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            gotoState(State.E_CHARGE_SLIP.toString());
        } else {
            gotoState(State.PRINT.toString());
        }
    }

    private void goTipBranch() {
        boolean enableTip = SysParam.getInstance().getBoolean(R.string.EDC_SUPPORT_TIP);
        percent = SysParam.getInstance().getInt(R.string.EDC_TIP_PERCENTAGE);
        //adjust tip
        if (enableTip && percent != 0) {
            gotoState(State.ADJUST_TIP.toString());
        } else {
            gotoState(State.CLSS_PREPROC.toString());
        }
    }

    private void afterEMVProcess(ETransResult transResult) {
        EmvTransProcess.emvTransResultProcess(transResult, emv, transData);
        if (transResult == ETransResult.SIMPLE_FLOW_END) {//simple
            toSignature();
        } else { // emv interrupt
            Device.beepErr();
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        }
    }

    private void afterClssProcess(CTransResult transResult) {
        if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_AGAIN) {
            DialogUtils.showErrMessage(getCurrentContext(), transType.getTransName(), getString(R.string.prompt_please_retry), null, Constants.FAILED_DIALOG_SHOW_TIME);
            //AET-175
            gotoState(State.CLSS_PREPROC.toString());
            return;
        }
        ClssTransProcess.clssTransResultProcess(transResult, clss, transData);
        // 设置交易结果
        transData.setEmvResult(transResult.getTransResult());
        if (transResult.getTransResult() != ETransResult.SIMPLE_FLOW_END) {
            Device.beepErr();
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            return;
        }
        toSignature();
    }

    private void toSignature() {
        transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
        transData.setAuthCode(transData.getDateTime().substring(8));//生成6位 approveCode
        GreendaoHelper.getTransDataHelper().insert(transData);
        Component.incInvoiceNo();
        Component.incTransNo();
        gotoState(State.SIGNATURE.toString());
    }


    enum State {
        ENTER_AMOUNT,
        CHECK_CARD,
        ADJUST_TIP,
        EMV_PROC,
        CLSS_PREPROC,
        CLSS_PROC,
        SELECT_ACCOUNT,
        INPUT_DIGITS,
        SIGNATURE,
        PRINT,
        E_CHARGE_SLIP
    }
}
