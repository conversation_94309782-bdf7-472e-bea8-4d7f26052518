/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans;

import android.content.Context;
import android.text.TextUtils;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.entity.DUKPTResult;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ECvmResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.action.ActionClssPreProc;
import com.pax.pay.trans.action.ActionClssProcess;
import com.pax.pay.trans.action.ActionEmvProcess;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionEnterPin;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionSearchCard;
import com.pax.pay.trans.action.ActionSearchCard.CardInformation;
import com.pax.pay.trans.action.ActionSelectAccount;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class RefundTrans extends BaseTrans {
    private byte searchCardMode = -1; // search card mode
    private String initAmount;
    private boolean needFallBack = false;
    private byte currentMode;
    private PrintTask printTask;

    public RefundTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.REFUND, transListener);
        initAmount = null;
    }

    public RefundTrans(Context context, String amount, TransEndListener transListener) {
        super(context, ETransType.REFUND, transListener);
        initAmount = amount;
    }

    @Override
    protected void bindStateOnAction() {

        transData.setAmount(initAmount); // cannot set in the Constructor cuz the Component.transInit() is called in execute
        searchCardMode = Component.getCardReadMode(ETransType.REFUND);
        ActionInputPassword inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(getCurrentContext(), 6,
                        getString(R.string.prompt_refund_pwd), null);
            }
        });
        bind(State.INPUT_PWD.toString(), inputPasswordAction, true);

        // enter amount action
        ActionEnterAmount amountAction = new ActionEnterAmount(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEnterAmount) action).setParam(getCurrentContext(),
                        getString(R.string.trans_refund), false);
            }
        });
        bind(State.ENTER_AMOUNT.toString(), amountAction, true);

        // search card
        ActionSearchCard searchCardAction = new ActionSearchCard(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSearchCard) action).setParam(getCurrentContext(), getString(R.string.trans_refund),
                        searchCardMode, transData.getAmount(),
                        null, "", needFallBack);
            }
        });
        bind(State.CHECK_CARD.toString(), searchCardAction, true);

        // input password action
        ActionEnterPin enterPinAction = new ActionEnterPin(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionEnterPin) action).setParam(getCurrentContext(),
                        getString(R.string.trans_refund), transData.getPan(), Constants.ISSUER_UNIONPAY.equalsIgnoreCase(transData.getIssuer().getName()),
                        getString(R.string.prompt_pin),
                        getString(R.string.prompt_no_pin),
                        "-" + transData.getAmount(),
                        null,
                        ActionEnterPin.EEnterPinType.ONLINE_PIN);
            }
        });
        bind(State.ENTER_PIN.toString(), enterPinAction, true);
        //select account
        ActionSelectAccount selectAccount = new ActionSelectAccount(action ->
                ((ActionSelectAccount) action).setParam(context, getString(R.string.trans_refund)));
        bind(State.SELECT_ACCOUNT.toString(), selectAccount);


        //Enter digits Number action
        ActionInputTransData inputFourDigits = new ActionInputTransData(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                String pan = transData.getPan();
                Context context = getCurrentContext();
                ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_refund))
                        .setInputLine(getString(R.string.prompt_four_digits), ActionInputTransData.EInputType.NUM, 4, 4,
                                pan.substring(pan.length() - 4), getString(R.string.trans_invalid_digits));
            }
        });
        bind(State.INPUT_DIGITS.toString(), inputFourDigits, true);

        //Enter RRN Action
        ActionInputTransData inputRRN = new ActionInputTransData(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                Context context = getCurrentContext();
                ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_refund))
                        .setInputLine(getString(R.string.prompt_rrn), ActionInputTransData.EInputType.NUM, 12, 0,  false);
            }
        });
        bind(State.INPUT_RRN.toString(), inputRRN);

        //clss process action
        ActionClssProcess clssProcessAction = new ActionClssProcess(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionClssProcess) action).setParam(getCurrentContext(), clss, transData);
            }
        });
        bind(State.CLSS_PROC.toString(), clssProcessAction);

        //clss preprocess action
        ActionClssPreProc clssPreProcAction = new ActionClssPreProc(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionClssPreProc) action).setParam(clss, transData);
            }
        });
        bind(State.CLSS_PREPROC.toString(), clssPreProcAction);

        // emv deal action
        ActionEmvProcess emvProcessAction = new ActionEmvProcess(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEmvProcess) action).setParam(getCurrentContext(), emv, transData);
            }
        });
        bind(State.EMV_PROC.toString(), emvProcessAction);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0);
            }
        });

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);
        // signature action
        ActionSignature signatureAction = new ActionSignature(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSignature) action).setParam(getCurrentContext(), transData.getAmount() ,null ,true);
            }
        });
        bind(State.SIGNATURE.toString(), signatureAction);
        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionOfflineSend) action).setParam(getCurrentContext());
            }
        });
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);


        //e charge slip
        EChargeSlipTask chargeSlipTask = new EChargeSlipTask(getCurrentContext(), getString(R.string.trans_refund), transData,
                EChargeSlipTask.genTransEndListener(RefundTrans.this, State.E_CHARGE_SLIP.toString()));
        bind(State.E_CHARGE_SLIP.toString(), chargeSlipTask);

        //print preview action
        printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(RefundTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        // whether need input management password for void and refund
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_REFUND_PASSWORD)) {
            gotoState(State.INPUT_PWD.toString());
        } else {
            gotoState(State.ENTER_AMOUNT.toString());
        }
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);
        int ret = result.getRet();
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType, true);
            if (f55Dup.length > 0) {
                TransData dupTransData = GreendaoHelper.getTransDataHelper().findFirstDupRecord();
                if (dupTransData != null) {
                    dupTransData.setDupIccData(ConvertHelper.getConvert().bcdToStr(f55Dup));
                    GreendaoHelper.getTransDataHelper().update(dupTransData);
                }
            }
            if (ret == TransResult.NEED_FALL_BACK) {
                needFallBack = true;
                searchCardMode &= 0x01;
                gotoState(State.CHECK_CARD.toString());
                return;
            } else if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }

        if (state == State.CLSS_PREPROC && ret != TransResult.SUCC) {
            searchCardMode &= 0x03;
        }

        switch (state) {
            case INPUT_PWD:
                onInputPwd(result);
                break;
            case ENTER_AMOUNT:
                onEnterAmount(result);
                break;
            case CHECK_CARD: // check card
                onCheckCard(result);
                break;
            case CLSS_PREPROC:
                gotoState(State.CHECK_CARD.toString());
                break;
            case CLSS_PROC:
                CTransResult clssResult = (CTransResult) result.getData();
                afterClssProcess(clssResult);
                break;
            case INPUT_DIGITS:
                gotoState(State.INPUT_RRN.toString());
                break;
            case SELECT_ACCOUNT:
                onSelectAccount(result);
                break;
            case INPUT_RRN:
                onEnterRRN(result);
                break;
            case ENTER_PIN: // enter pin
                onEnterPin(result);
                break;
            case EMV_PROC: // emv
                //get trans result
                CTransResult transResult = (CTransResult) result.getData();
                // EMV完整流程 脱机批准或联机批准都进入签名流程
                onEmvProc(transResult.getTransResult());
                break;
            case MAG_ONLINE: // after online
                toSignOrPrint();
                break;
            case SIGNATURE:
                onSignature(result);
                break;
            case OFFLINE_SEND:
                toChargeSlipOrPrint();
                break;
            case E_CHARGE_SLIP:
                String chooseResult = (String) result.getData();
                if (Constants.E_CHARGE_SLIP.equals(chooseResult)) {
                    transEnd(new ActionResult(TransResult.SUCC, null));
                } else if (Constants.NO_CHARGE_SLIP.equals(chooseResult)) {
                    printTask.setParam(true);
                    gotoState(State.PRINT.toString());
                } else {
                    gotoState(State.PRINT.toString());
                }
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }

    private void onInputPwd(ActionResult result) {
        String data = EncUtils.sha1((String) result.getData());
        if (!data.equals(SysParam.getInstance().getString(R.string.SEC_REFUND_PWD))) {
            transEnd(new ActionResult(TransResult.ERR_PASSWORD, null));
            return;
        }
        if (TextUtils.isEmpty(transData.getAmount())) {
            gotoState(State.ENTER_AMOUNT.toString());
            return;
        }
        gotoState(State.CLSS_PREPROC.toString());
    }

    private void onEnterAmount(ActionResult result) {
        transData.setAmount(result.getData().toString());
        gotoState(State.CLSS_PREPROC.toString());
    }

    private void onCheckCard(ActionResult result) {
        CardInformation cardInfo = (CardInformation) result.getData();
        saveCardInfo(cardInfo, transData);
        transData.setTransType(ETransType.REFUND);
        if (needFallBack) {
            transData.setEnterMode(TransData.EnterMode.FALLBACK);
        }
        // enter card number manually
        currentMode = cardInfo.getSearchMode();
        if (ActionSearchCard.SearchMode.isWave(currentMode)) {
            needRemoveCard = true;
            // AET-15
            gotoState(State.CLSS_PROC.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.SWIPE) {
            gotoState(State.SELECT_ACCOUNT.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.INSERT) {
            needRemoveCard = true;
            // EMV process
            gotoState(State.EMV_PROC.toString());
        }
    }

    private void onSelectAccount(ActionResult result) {
        if (result.getRet() == TransResult.ERR_TIMEOUT) {
            transEnd(result);
            return;
        }
        String accountType = "00";//default type
        if (result.getData() != null) {
            accountType = result.getData().toString();
        }
        transData.setAccountType(accountType);
        gotoState(State.INPUT_DIGITS.toString());
    }

    private void onEnterRRN(ActionResult result) {
        //如果输入RRN点击返回则进行取消
        if (result.getRet() == TransResult.ERR_USER_CANCEL || result.getRet() == TransResult.ERR_TIMEOUT) {
            transEnd(new ActionResult(result.getRet(), null));
            return;
        }
        if (result.getData() != null) {
            String rrn = (String) result.getData();
            transData.setRefNo(rrn);
        }

        if (transData.isPinFree()) {
            // online
            transData.setHasPin(false);
            gotoState(State.MAG_ONLINE.toString());
        } else {
            // enter pin
            gotoState(State.ENTER_PIN.toString());
        }
    }

    private void onEnterPin(ActionResult result) {
        DUKPTResult dukptResult = (DUKPTResult) result.getData();
        if (dukptResult.getResult() != null && dukptResult.getResult().length != 0) {
            transData.setPin(ConvertHelper.getConvert().bcdToStr(dukptResult.getResult()));
            transData.setHasPin(true);
        }
        if (dukptResult.getKsn() != null && dukptResult.getKsn().length != 0) {
            transData.setPinKsn(ConvertHelper.getConvert().bcdToStr(dukptResult.getKsn()));
        }

        // online
        gotoState(State.MAG_ONLINE.toString());
    }

    private void afterClssProcess(CTransResult transResult) {
        if (transResult.getTransResult() == ETransResult.CLSS_OC_SEE_PHONE) {
            searchCardMode = 0x04;
            gotoState(State.CLSS_PREPROC.toString());
            return;
        }

        if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_AGAIN) {
            DialogUtils.showErrMessage(getCurrentContext(), transType.getTransName(), getString(R.string.prompt_please_retry), null, Constants.FAILED_DIALOG_SHOW_TIME);
            //AET-175
            gotoState(State.CLSS_PREPROC.toString());
            return;
        }

        // 设置交易结果
        transData.setEmvResult(transResult.getTransResult());
        if (transResult.getTransResult() == ETransResult.ABORT_TERMINATED ||
                transResult.getTransResult() == ETransResult.CLSS_OC_DECLINED) { // emv interrupt
            Device.beepErr();
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            return;
        }

        if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_ANOTHER_INTERFACE) {
            //todo: use contact
            //此处应当结束整个交易，重新发起交易时，客户自然会使用接触式
            ToastUtils.showMessage("Please use contact");
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            return;
        }

        ClssTransProcess.clssTransResultProcess(transResult, clss, transData);

        if (transResult.getCvmResult() == ECvmResult.SIG) {
            //do signature after online
            transData.setSignFree(false);
            transData.setPinFree(true);
        } else if (transResult.getCvmResult() == ECvmResult.ONLINE_PIN_SIG) {
            transData.setSignFree(false);
            transData.setPinFree(false);
        } else if (transResult.getCvmResult() == ECvmResult.ONLINE_PIN) {
            transData.setSignFree(true);
            transData.setPinFree(false);
        } else {
            transData.setSignFree(true);
            transData.setPinFree(true);
        }
        GreendaoHelper.getTransDataHelper().update(transData);

        if (transResult.getTransResult() == ETransResult.CLSS_OC_APPROVED || transResult.getTransResult() == ETransResult.ONLINE_APPROVED) {
            transData.setOnlineTrans(transResult.getTransResult() == ETransResult.ONLINE_APPROVED);
            Component.incInvoiceNo();  //非接增加小票号
            if (!transData.isSignFree()) {
                gotoState(State.SIGNATURE.toString());
            } else {
                checkOfflineTrans();
            }
        } else {
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        }
        // ETransResult.CLSS_OC_ONLINE_REQUEST, online is handled in the module
    }

    private void onEmvProc(ETransResult transResult) {
        // EMV完整流程 脱机批准或联机批准都进入签名流程
        EmvTransProcess.emvTransResultProcess(transResult, emv, transData);
        if (transResult == ETransResult.ONLINE_APPROVED || transResult == ETransResult.OFFLINE_APPROVED) {// 联机批准/脱机批准处理
            // electronic signature
            Component.incInvoiceNo();  //交易成功后小票号+1
            gotoState(State.SIGNATURE.toString());
        } else if (transResult == ETransResult.ARQC || transResult == ETransResult.SIMPLE_FLOW_END) { // request online/simplify process
            // enter pin
            gotoState(State.ENTER_PIN.toString());
        } else if (transResult == ETransResult.ONLINE_DENIED) { // online denied
            // transaction end
            transEnd(new ActionResult(TransResult.ERR_HOST_REJECT, null));
        } else if (transResult == ETransResult.ONLINE_CARD_DENIED) {// platform approve card denied
            transEnd(new ActionResult(TransResult.ERR_CARD_DENIED, null));
        } else if (transResult == ETransResult.ABORT_TERMINATED) { // emv terminated
            // 交易结束
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        } else if (transResult == ETransResult.OFFLINE_DENIED) {
            // 走到这里EMV有问题, 退货不支持脱机， 怎么改看着办吧
            // FIXME Kim CDOL
            Device.beepErr();
            transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
        }
    }

    private void checkOfflineTrans() {
        //get offline trans data list
        List<TransData.OfflineStatus> filter = new ArrayList<>();
        filter.add(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        List<TransData> offlineTransList = GreendaoHelper.getTransDataHelper().findOfflineTransData(filter);
        if (!offlineTransList.isEmpty() && !offlineTransList.get(0).getId().equals(transData.getId())) { //AET-92
            //offline send
            gotoState(State.OFFLINE_SEND.toString());
            return;
        }
        // if terminal not support electronic signature, user do not make signature or signature time out, print preview
        toChargeSlipOrPrint();
    }

    private void toChargeSlipOrPrint() {
        if (!Utils.isA50() || SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            gotoState(State.E_CHARGE_SLIP.toString());
        } else {
            gotoState(State.PRINT.toString());
        }
    }

    // need electronic signature or send
    private void toSignOrPrint() {
        Component.incInvoiceNo();  //交易成功后小票号+1
        if (transData.getSignFree()) {// signature free
            // print preview
            transData.setSignFree(true);
            checkOfflineTrans();
        } else {
            transData.setSignFree(false);
            gotoState(State.SIGNATURE.toString());
        }
        GreendaoHelper.getTransDataHelper().update(transData);
    }

    private void onSignature(ActionResult result) {
        // save signature
        byte[] signData = (byte[]) result.getData();
        byte[] signPath = (byte[]) result.getData1();

        if (result.getRet() == TransResult.ERR_TIMEOUT && signData == null) {
            //Refund 签名界面超时也要打印签名栏
            transData.setSignFree(false);
        }

        if (signData != null && signData.length > 0 && signPath != null) {
            transData.setSignData(signData);
            transData.setSignPath(signPath);
            transData.setSignFree(false);
        }

        // update trans data，save signature
        GreendaoHelper.getTransDataHelper().update(transData);
        checkOfflineTrans();
    }

    private enum State {
        INPUT_PWD,
        ENTER_AMOUNT,
        CHECK_CARD,
        SELECT_ACCOUNT,
        INPUT_DIGITS,
        INPUT_RRN,
        ENTER_PIN,
        MAG_ONLINE,
        CLSS_PREPROC,
        CLSS_PROC,
        EMV_PROC,
        SIGNATURE,
        OFFLINE_SEND,
        PRINT,
        E_CHARGE_SLIP
    }
}
