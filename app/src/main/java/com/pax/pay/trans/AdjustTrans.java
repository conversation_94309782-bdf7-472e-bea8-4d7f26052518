/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         caowb                   Create
 * ===========================================================================================
 */
package com.pax.pay.trans;

import android.content.Context;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

/*
 * according to the EDC Prolin, Tip of Sale,
 * Offline sale can be adjusted times before settlement.
 */

public class AdjustTrans extends BaseTrans {

    // the adjustment is just a state for a transaction,
    // so it cannot use the logic of base transaction which uses transData from the BaseTrans,
    // and in the db, each record has its id,
    // which means we cannot call transData.save() cuz it will create an excess record,
    // and we cannot call the transData.updateTrans() either, cuz the record with new id is not existed.
    // So we have to use the origTransData instead of transData.
    private TransData origTransData;
    List<ETransType> eTransTypeList = new ArrayList<>(Arrays.asList(ETransType.SALE));
    List<TransData.ETransStatus> statusList = new ArrayList<>(Arrays.asList(TransData.ETransStatus.NORMAL));

    public AdjustTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.ADJUST, transListener);
    }

    @Override
    protected void bindStateOnAction() {
        //input manager password
        ActionInputPassword inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(getCurrentContext(), 6, getString(R.string.prompt_adjust_pwd), null);
            }
        });
        bind(State.INPUT_PWD.toString(), inputPasswordAction, true);

        //input original trance no
        ActionInputTransData enterTransNoAction = new ActionInputTransData(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                Context context = getCurrentContext();
                ((ActionInputTransData) action).setParam(new WeakReference<>(context), getString(R.string.trans_adjust)).setInputLine(getString(R.string.prompt_input_transno), ActionInputTransData.EInputType.NUM, 6, 1);
            }
        });
        bind(State.ENTER_TRANSNO.toString(), enterTransNoAction, true);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(action -> ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0));

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);

        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(action ->
                ((ActionOfflineSend) action).setParam(getCurrentContext()));
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);

        ActionSignature signatureAction = new ActionSignature(action ->
                ((ActionSignature) action).setParam(getCurrentContext(), transData.isDcc() ? transData.getInternationalCurrencyAmount() : transData.getAmount(),
                        transData.isDcc() ? transData.getInternationalCurrency().getCode() : transData.getLocalCurrency().getCode()));
        bind(State.SIGNATURE.toString(), signatureAction);

        // input new tips
        ActionInputTransData newTipsAction = new ActionInputTransData(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                String title = getString(R.string.trans_adjust);
                String transAmount = origTransData.getAmount();
                String transTips = origTransData.getTipAmount();
                float adjustPercent = SysParam.getInstance().getInt(R.string.EDC_TIP_PERCENTAGE);

                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                map.put(getString(R.string.prompt_total_amount), transAmount);
                map.put(getString(R.string.prompt_ori_tips), transTips);
                map.put(getString(R.string.prompt_adjust_percent), Float.toString(adjustPercent));

                ((ActionInputTransData) action).setParam(getCurrentContext(), title, map).setInputLine(getString(R.string.prompt_new_tips), ActionInputTransData.EInputType.AMOUNT, Constants.AMOUNT_DIGIT);
            }
        });
        bind(State.ENTER_AMOUNT.toString(), newTipsAction, true);

        PrintTask printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(AdjustTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        //process first action
        gotoState(State.ENTER_TRANSNO.toString());

    }

    private void copyOrigTransData() {
        transData.setOrigAmount(origTransData.getAmount());
        transData.setOrigBatchNo(origTransData.getBatchNo());
        transData.setOrigAuthCode(origTransData.getAuthCode());
        transData.setOrigRefNo(origTransData.getRefNo());
        transData.setOrigRefNo(origTransData.getRefNo());
        transData.setRefNo(origTransData.getRefNo());
        transData.setAuthCode(origTransData.getAuthCode());
        transData.setOrigTransNo(origTransData.getTraceNo());
        transData.setPan(origTransData.getPan());
        transData.setExpDate(origTransData.getExpDate());
        transData.setAcquirer(origTransData.getAcquirer());
        transData.setInvoiceNo(origTransData.getInvoiceNo());
        transData.setIssuer(origTransData.getIssuer());
        transData.setOrigTransType(origTransData.getTransType());
        transData.setOrigInvoiceNo(origTransData.getInvoiceNo());
        transData.setIssuerScriptResults(origTransData.getIssuerScriptResults());
        transData.setSendIccData(origTransData.getSendIccData());
        transData.setEmvAppName(origTransData.getEmvAppName());
        transData.setEmvAppLabel(origTransData.getEmvAppLabel());
        transData.setTc(origTransData.getTc());
        transData.setAid(origTransData.getAid());
        transData.setTvr(origTransData.getTvr());
        transData.setTsi(origTransData.getTsi());
        transData.setEmvResult(origTransData.getEmvResult());
        transData.setFullOnline(origTransData.isFullOnline());
        transData.setEnterMode(origTransData.getEnterMode());
        transData.setTrack2(origTransData.getTrack2());
        transData.setDcc(origTransData.isDcc());
        transData.setInternationalCurrency(origTransData.getInternationalCurrency());
        transData.setMakeUp(origTransData.getMakeUp());
        transData.setCommission(origTransData.getCommission());
        transData.setRate(origTransData.getRate());
    }


    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);
        switch (state) {
            case INPUT_PWD:
                onInputPwd(result);
                break;
            case ENTER_TRANSNO:
                onEnterInvoiceNo(result);
                break;
            case ENTER_AMOUNT:
                onEnterAmount(result);
                break;
            case MAG_ONLINE: // subsequent processing of online
                //更新原交易的DCC信息
                if (transData.getInternationalCurrency() != null && transData.getInternationalCurrencyAmount() != null
                        && transData.getMakeUp() != null && transData.getRate() != null) {
                    origTransData.setInternationalCurrency(transData.getInternationalCurrency());
                    origTransData.setInternationalCurrencyAmount(transData.getInternationalCurrencyAmount());
                    origTransData.setMakeUp(transData.getMakeUp());
                    origTransData.setRate(transData.getRate());
                    transData.setDcc(true);
                } else {
                    origTransData.setDcc(false);
                }
                gotoState(State.SIGNATURE.toString());
                break;
            case SIGNATURE:
                byte[] signData = (byte[]) result.getData();
                byte[] signPath = (byte[]) result.getData1();

                if (signData != null && signData.length > 0 &&
                        signPath != null && signPath.length > 0) {
                    // update trans data，save signature
                    transData.setSignData(signData);
                    transData.setSignPath(signPath);
                    //调整之后原Sale允许打印签名
                    origTransData.setSignFree(false);
                    origTransData.setSignData(signData);
                    origTransData.setSignPath(signPath);
                }
                // 更新原Sale交易的时间
                origTransData.setTransState(TransData.ETransStatus.ADJUSTED);
                origTransData.setDateTime(transData.getDateTime());
                GreendaoHelper.getTransDataHelper().delete(transData);
                GreendaoHelper.getTransDataHelper().update(origTransData);
                //check offline trans
                checkOfflineTrans();
                break;
            case OFFLINE_SEND:
                gotoState(State.PRINT.toString());
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                break;
        }
    }

    private void onInputPwd(ActionResult result) {
        String data = EncUtils.sha1((String) result.getData());
        if (!data.equals(SysParam.getInstance().getString(R.string.SEC_ADJUST_PWD))) {
            transEnd(new ActionResult(TransResult.ERR_PASSWORD, null));
            return;
        }

        gotoState(State.ENTER_TRANSNO.toString());
    }

    private void onEnterInvoiceNo(ActionResult result) {
        if (!SysParam.getInstance().getBoolean(R.string.EDC_SUPPORT_TIP)) {
            // end trans
            transEnd(new ActionResult(TransResult.ERR_ADJUST_UNSUPPORTED,null));
            return;
        }
        String content = (String) result.getData();
        long invoiceNo;
        if (content == null) {
            transEnd(new ActionResult(TransResult.ERR_NO_TRANS, null));
            return;
        } else {
            invoiceNo = Utils.parseLongSafe(content, -1);
            origTransData = GreendaoHelper.getTransDataHelper().findTransDataByInvoiceNo(invoiceNo);
            if (origTransData == null) {
                // no original transaction
                transEnd(new ActionResult(TransResult.ERR_NO_ORIG_TRANS, null));
                return;
            }
            ETransType trType = origTransData.getTransType();
            String transTipAmount = origTransData.getTipAmount();
            // 只允许tip金额为0的Adjust交易
            if (!trType.isAdjustAllowed() || (transTipAmount != null && !"0".equals(transTipAmount))) {
                transEnd(new ActionResult(TransResult.ERR_ADJUST_UNSUPPORTED, null));
                return;
            }
            //  has voided/adjust transaction can not adjust
            TransData.ETransStatus trStatus = origTransData.getTransState();
            if (trStatus.equals(TransData.ETransStatus.VOIDED)) {
                transEnd(new ActionResult(TransResult.ERR_HAS_VOIDED, null));
                return;
            }
        }
        copyOrigTransData();
        gotoState(State.ENTER_AMOUNT.toString());
    }

    private void onEnterAmount(ActionResult result) {
        long newTotalAmount = (long) result.getData();
        long newTipAmount = (long) result.getData1();

        //base amount and tip
        origTransData.setAmount(newTotalAmount + "");
        //set tip
        origTransData.setTipAmount(newTipAmount + "");
        // update original transaction record
        transData.setAmount(origTransData.getAmount());
        transData.setTipAmount(origTransData.getTipAmount());
        gotoState(State.MAG_ONLINE.toString());
    }

    private void checkOfflineTrans() {
        //get offline trans data list
        List<TransData.OfflineStatus> filter = new ArrayList<>();
        filter.add(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        List<TransData> offlineTransList = GreendaoHelper.getTransDataHelper().findOfflineTransData(filter);
        //AET-150
        if ((transData.getTransType().equals(ETransType.ADJUST) && transData.isOnlineTrans() &&
                !offlineTransList.isEmpty() &&
                !offlineTransList.get(0).getId().equals(transData.getId()))) { //AET-92
            if (transData.getEnterMode() == TransData.EnterMode.CLSS && transData.getEmvResult() != ETransResult.ONLINE_APPROVED) {
                gotoState(State.PRINT.toString());
                return;
            }
            //offline send
            gotoState(State.OFFLINE_SEND.toString());
        } else {
            // if terminal does not support signature ,card holder does not sign or time out，print preview directly.
            gotoState(State.PRINT.toString());
        }
    }


    enum State {
        INPUT_PWD,
        ENTER_TRANSNO,
        ENTER_AMOUNT,
        ENQUIRY,
        SELECT_CURRENCY,
        MAG_ONLINE,
        SIGNATURE,
        OFFLINE_SEND,
        PRINT
    }

}
