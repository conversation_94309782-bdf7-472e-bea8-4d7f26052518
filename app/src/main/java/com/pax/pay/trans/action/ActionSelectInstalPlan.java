package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import com.pax.abl.core.AAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.activity.SelectInstalPlanActivity;

/**
 * Created by yangrr on 2017/12/28.
 */

public class ActionSelectInstalPlan extends AAction {
    private Context mContext;
    private String mTitle;

    public ActionSelectInstalPlan(ActionStartListener listener) {
        super(listener);
    }

    /**
     *
     */
    public void setParam(Context context, String title) {
        mContext = context;
        mTitle = title;
    }

    @Override
    protected void process() {
        Intent intent = new Intent(mContext, SelectInstalPlanActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString(EUIParamKeys.NAV_TITLE.toString(), mTitle);
        bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
        intent.putExtras(bundle);
        mContext.startActivity(intent);
    }
}
