/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.receipt;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.View;

import com.pax.commonlib.utils.FontCache;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.impl.GL;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.io.InputStream;

/**
 * receipt generator
 *
 * <AUTHOR>
 */
public class ReceiptGeneratorSQR implements IReceiptGenerator {

    private int receiptNo = 0;
    private SQRData transData;
    private boolean isRePrint = false;
    private int receiptMax = 0;
    private boolean isPrintPreview = false;
    private Bitmap logo;
    private Bitmap signature;

    /**
     * @param transData        ：transData
     * @param currentReceiptNo : currentReceiptNo
     * @param receiptMax       ：generate which one, start from 0
     * @param isReprint        ：is reprint?
     */
    public ReceiptGeneratorSQR(SQRData transData, int currentReceiptNo, int receiptMax, boolean isReprint) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
    }

    public ReceiptGeneratorSQR(SQRData transData, int currentReceiptNo, int receiptMax, boolean isReprint, boolean isPrintPreview) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
        this.isPrintPreview = isPrintPreview;
    }

    @Override
    public Bitmap generateBitmap() {
        View view = pageToView();
        view.setDrawingCacheEnabled(true); // 启用绘图缓存
        view.buildDrawingCache(); // 确保绘图缓存是最新的
        Bitmap bitmap = Bitmap.createBitmap(view.getDrawingCache()); // 通过复制绘图缓存来创建Bitmap，避免对原缓存的直接引用

        // 清理工作
        view.destroyDrawingCache(); // 清理绘图缓存
        view.setDrawingCacheEnabled(false); // 禁用绘图缓存

        return bitmap; // 返回创建的Bitmap
    }

    @NotNull
    public View pageToView() {
        // 生成第几张凭单不合法时， 默认0
        if (receiptNo > receiptMax) {
            receiptNo = 0;
        }

//        IPage page = Device.generatePage();//此方法获得的page是static，导致其他对象无法释放。
        IPage page = GL.getGL().getImgProcessing().createPage();
        page.adjustLineSpace(-9);
        page.setTypeFace(FontCache.get(Constants.PAID_FONT_NAME, FinancialApplication.getApp()));

        // transaction type
        ETransType transType = ETransType.QR;
        Acquirer acquirer = FinancialApplication.getAcqManager().getCurAcq();

        String temp;
        logo = getImageFromAssetsFile("pax_logo_boid_slogan.png");

        // title
        page.addLine()
                .addUnit(page.createUnit()
                        .setBitmap(logo)
                        .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //merchant information
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_NAME))
                        .setTextStyle(Typeface.BOLD)
                        .setGravity(Gravity.CENTER_HORIZONTAL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(SysParam.getInstance().getString(R.string.EDC_MERCHANT_ADDRESS))
                        .setTextStyle(Typeface.BOLD)
                        .setGravity(Gravity.CENTER_HORIZONTAL));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        if (isRePrint) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("----" + Utils.getString(R.string.receipt_print_again) + "----")
                            .setFontSize(FONT_BIG)
                            .setGravity(Gravity.CENTER));
        }

        // date/time
        String date = TimeConverter.convert(transData.getTranDate(), Constants.TIME_PATTERN_MQTT_QR,
                Constants.DATE_PATTERN);
        String time = TimeConverter.convert(transData.getTranDate(), Constants.TIME_PATTERN_MQTT_QR,
                Constants.TIME_PATTERN);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_transaction_date) + date)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_transaction_time) + time)
                        .setGravity(Gravity.END)
                        .setFontSize(FONT_SMALL));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));
        // merchant ID and TID
        String merchantId = acquirer.getMerchantId();
        String terminalId = acquirer.getTerminalId();

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_merchant_id) + merchantId)
                        .setWeight(3.0f)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_terminal_id) + terminalId)
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f)
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //Batch No and Invoice No
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_batch_no) + transData.getBatch())
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_invoice_no) + transData.getInvoiceNr())
                        .setGravity(Gravity.END)
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        //transType
        String type = transType.getTransName();

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(type)
                        .setGravity(Gravity.CENTER_HORIZONTAL)
                        .setFontSize(FONT_BIG));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        String temp1 = " MANUAL";
        page.addLine().
                addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_type) + "QR")
                        .setFontSize(FONT_SMALL)
                        .setWeight(3.0f))
                .addUnit(page.createUnit()
                        .setText(temp1)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        // auth code / RRN
        temp = transData.getRrn();
        if (temp == null) {
            temp = "";
        }

        temp1 = transData.getAuthCode();
        if (temp1 == null) {
            temp1 = "";
        }

        page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_ref_no) + temp)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_auth_code) + temp1)
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_LINE));

        temp = transData.getStan();
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.tran_id) + temp)
                .setFontSize(FONT_SMALL)
                .setGravity(Gravity.START));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        // TOTAL
        int resAmountId = R.string.receipt_amount_total;
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(resAmountId))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(8.0f))
                .addUnit(page.createUnit()
                        .setText("₹" + transData.getAmount())
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(5.0f));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        //verify prompt
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_verify))
                .setFontSize(18)//temporary change
                .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.greet_messsage))
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.CENTER));

        if (receiptMax == 3) {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_acquire))
                                .setGravity(Gravity.CENTER));
            } else if (receiptNo == 1) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_merchant))
                                .setGravity(Gravity.CENTER));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_user))
                                .setGravity(Gravity.CENTER));
            }
        } else {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_merchant))
                                .setGravity(Gravity.CENTER));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_stub_user))
                                .setGravity(Gravity.CENTER));
            }
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getTerminalAndAppVersion())
                        .setGravity(Gravity.CENTER)
                        .setFontSize(FONT_SMALL));

        if (Component.isDemo()) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        if (!isPrintPreview) {
            page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        }

        IImgProcessing imgProcessing = GL.getGL().getImgProcessing();
        return imgProcessing.pageToView(page, 384);
    }

    private Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }

        return image;
    }

    public void clear() {
        if (logo != null && !logo.isRecycled()) {
            logo.recycle();
            logo = null;
        }
        if (signature != null && !signature.isRecycled()) {
            signature.recycle();
            signature = null;
        }
    }
}
