/*
 *
 *  * ============================================================================
 *  * PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  * This software is supplied under the terms of a license agreement or nondisclosure
 *  * agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  * disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) $YEAR-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * Description:
 *  * Revision History:
 *  * Date	             Author	                Action
 *  * 20190103   	     ligq           	Create/Add/Modify/Delete
 *  * ============================================================================
 *
 */
package com.pax.pay.trans.btprinter;

import android.graphics.Bitmap;
import android.os.Looper;

import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransTotal;
import com.pax.gl.commhelper.IBtScanner;
import com.pax.gl.commhelper.impl.PaxGLComm;
import com.pax.gl.extprinter.exception.PrintException;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.action.PrintType;
import com.pax.pay.trans.btprinter.adapters.BtDeviceAdapter;
import com.pax.pay.trans.receipt.ReceiptGeneratorAidParam;
import com.pax.pay.trans.receipt.ReceiptGeneratorCapkParam;
import com.pax.pay.trans.receipt.ReceiptGeneratorFailedTransDetail;
import com.pax.pay.trans.receipt.ReceiptGeneratorTotal;
import com.pax.pay.trans.receipt.ReceiptGeneratorTrans;
import com.pax.pay.trans.receipt.ReceiptGeneratorTransDetail;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;

public abstract class BaseBtPrinter implements IBtPrinter {
    @Override
    public Observable<BtDeviceAdapter.ItemBtDevice> scan() {
        return Observable.create(new ObservableOnSubscribe<BtDeviceAdapter.ItemBtDevice>() {
            @Override
            public void subscribe(final ObservableEmitter<BtDeviceAdapter.ItemBtDevice> emitter) throws Exception {
                Looper.prepare();
                IBtScanner btScan = PaxGLComm.getInstance(FinancialApplication.getApp()).getBtScanner();
                btScan.start(new IBtScanner.IBtScannerListener() {
                    @Override
                    public void onDiscovered(IBtScanner.IBtDevice iBtDevice) {
                        LogUtils.d(TAG, "onDiscovered:" + iBtDevice.getName());
                        if (iBtDevice.getName().toUpperCase().contains(BP60A)) {
                            emitter.onNext(new BtDeviceAdapter.ItemBtDevice(iBtDevice.getName(), iBtDevice.getIdentifier()));
                        }
                    }

                    @Override
                    public void onFinished() {
                        LogUtils.d(TAG, "onFinished");
                        emitter.onComplete();
                    }
                }, 5);
                Looper.loop();
            }
        });
    }

    protected <T> void printOtherType(String printType, T t, Printer<T> print) {
        List<Bitmap> bitmaps = null;
        if (PrintType.TYPE_AID.equals(printType)) {
            bitmaps = new ReceiptGeneratorAidParam().generateBitmaps();
        } else if (PrintType.TYPE_CAPK.equals(printType)) {
            bitmaps = new ReceiptGeneratorCapkParam().generateBitmaps();
        }
        if (bitmaps == null) {
            return;
        }
        for (Bitmap item : bitmaps) {
            print.print(item, t);
        }
        print.onEnd(t);
    }

    protected <T> void printTotal(TransTotal transTotal, String printType, String title, String resultMsg, T t, Printer<T> print) {
        switch (printType) {
            case PrintType.TYPE_SUMMARY:
            case PrintType.TYPE_SETTLE:
            case PrintType.TYPE_LAST_BATCH:
                ReceiptGeneratorTotal receiptGeneratorTotal = new ReceiptGeneratorTotal(title, resultMsg, transTotal,false);
                print.print(receiptGeneratorTotal.generateBitmap(), t);
                break;
            default:
                break;
        }
    }

    protected <T> void printList(List<TransData> array, String printType, String title, T t, Printer<T> print) throws PrintException {
        switch (printType) {
            case PrintType.TYPE_DETAILS:
                int count = 0;
                ReceiptGeneratorTransDetail receiptGeneratorTransDetail = new ReceiptGeneratorTransDetail();
                Bitmap titleBitmap = receiptGeneratorTransDetail.generateMainInfo(title);
                print.print(titleBitmap, t);
                ArrayList<TransData> details = new ArrayList<>();
                for (int i = 0; i < array.size(); i++) {
                    Object o = array.get(0);
                    if (o instanceof TransData) {
                        details.add((TransData) o);
                        count++;
                        if (count == array.size() || count % 20 == 0) {
                            receiptGeneratorTransDetail = new ReceiptGeneratorTransDetail(details);
                            print.print(receiptGeneratorTransDetail.generateBitmap(), t);
                        }
                    } else {
                        print.onEnd(t);
                        throw new PrintException(-1);
                    }
                }
                print.onEnd(t);
                break;
            case PrintType.TYPE_DETAILS_FAILED:
                ArrayList<TransData> detailList = new ArrayList<>();
                for (int i = 0; i < array.size(); i++) {
                    Object o = array.get(0);
                    if (o instanceof TransData) {
                        detailList.add((TransData) o);
                    } else {
                        print.onEnd(t);
                        throw new PrintException(-1);
                    }
                }
                ReceiptGeneratorFailedTransDetail receiptGeneratorFailedTransDetail = new ReceiptGeneratorFailedTransDetail(title, detailList);
                print.print(receiptGeneratorFailedTransDetail.generateBitmap(), t);
                print.onEnd(t);
                break;
            default:
                throw new PrintException(-1);
        }
    }

    protected <T> void printTransReceipt(TransData data, String printType, T t, Printer<T> print) {
        if (!SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_PRINT)) {
            return;
        }
        int receiptNum = getVoucherNum();

        for (int i = 0; i < receiptNum; i++) {
            ReceiptGeneratorTrans receiptGeneratorTrans = new ReceiptGeneratorTrans(data, i, receiptNum, Objects.equals(printType, PrintType.TYPE_REPRINT));
            print.print(receiptGeneratorTrans.generateBitmap(), t);
        }
    }

    private int getVoucherNum() {
        int receiptNum = SysParam.getInstance().getInt(R.string.EDC_RECEIPT_NUM);
        // receipt copy number is 1-3
        if (receiptNum < 1 || receiptNum > 3) {
            receiptNum = 2;
        }
        return receiptNum;
    }

    public interface Printer<T> {
        void print(Bitmap bitmap, T t);

        void onEnd(T t);
    }


}
