package com.pax.pay.trans.qsparctrans;

import android.content.Context;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.eemv.entity.CTransResult;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

/**
 * @Author: Yangsh
 * @Date: 2020/6/8 15:31
 */
public class QsparcBalanceUpdateTrans extends BaseQsparcTrans {

    public QsparcBalanceUpdateTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.QSPARC_BALANCE_UPDATE, transListener);
        searchCardMode = Component.getCardReadMode(transType);
        transNameResId = R.string.trans_qsparc_balance_update;
    }

    @Override
    protected void bindStateOnAction() {
        bindClssPre();
        bindCheckCard();
        bindClssTrans();
        bindEmvProcess();
        bindSignature();
        bindPrintReceipt();
        bindOfflienSend();

        gotoState(State.CLSS_PREPROC.toString());
    }

    @Override
    protected void bindCheckCard() {
        transData.setAmount("0");
        super.bindCheckCard();
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {

        int ret = result.getRet();
        State state = State.valueOf(currentState);
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType, true);
            if (f55Dup.length > 0) {
                TransData dupTransData = GreendaoHelper.getTransDataHelper().findFirstDupRecord();
                if (dupTransData != null) {
                    dupTransData.setDupIccData(ConvertHelper.getConvert().bcdToStr(f55Dup));
                    GreendaoHelper.getTransDataHelper().update(dupTransData);
                }
            }

            if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }

        if (state == State.CLSS_PREPROC && ret != TransResult.SUCC) {
            searchCardMode &= 0x03;
        }

        switch (state) {
            case CHECK_CARD: // 检测卡的后续处理
                onCheckCard(result);
                break;
            case CLSS_PREPROC:
                gotoState(State.CHECK_CARD.toString());
                break;
            case CLSS_PROC:
                CTransResult clssResult = (CTransResult) result.getData();
                afterClssProcess(clssResult);
                break;
            case EMV_PROC: // emv后续处理
                onEmvProc(result);
                break;
            case SIGNATURE:
                onSignature(result);
                break;
            case OFFLINE_SEND:
                gotoState(State.PRINT.toString());
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }

    }
}
