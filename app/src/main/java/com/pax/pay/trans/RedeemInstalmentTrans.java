package com.pax.pay.trans;

import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.BaseRequest;
import com.pax.edc.opensdk.SaleRequest;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ECvmResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.action.ActionCashDolSelect;
import com.pax.pay.trans.action.ActionClssPreProc;
import com.pax.pay.trans.action.ActionClssProcess;
import com.pax.pay.trans.action.ActionEmvProcess;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionSearchCard;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.Utils;
import com.pax.view.dialog.DialogUtils;

/**
 * Created by zhouhong on 2017/12/20.
 */

public class RedeemInstalmentTrans extends BaseTrans {
    protected byte searchCardMode = -1; // search card mode
    protected String transtTitle;
    protected String amount;

    protected boolean isFreePin;
    protected boolean needFallBack = false;

    protected byte currentMode;

    /**
     * Constructor
     * @param context Context
     * @param transListener TransEndListener
     */
    public RedeemInstalmentTrans(Context context, TransEndListener transListener) {
        super(context, ETransType.REDEEM_INST, transListener);
        setParam(getString(R.string.trans_redeem_instalment), searchCardMode, true);
    }

    public RedeemInstalmentTrans(Context context, BaseRequest baseRequest,TransEndListener transListener) {
        super(context, ETransType.REDEEM_INST, transListener);
        setParam(getString(R.string.trans_redeem_instalment), searchCardMode, true);
        thirdPartyBaseRequest = baseRequest;
        isThirdMode =true;
    }

    /**
     * set param
     * @param transtTitle String
     * @param mode byte
     * @param isFreePin boolean
     */
    protected void setParam(String transtTitle, byte mode, boolean isFreePin) {
        this.transtTitle = transtTitle;
        this.searchCardMode = mode;
        this.isFreePin = isFreePin;
    }

    enum State {
        ENTER_AMOUNT,
        CHECK_CARD,
        SELECT_CASH_DOL,
        SCAN_CODE,
        MAG_ONLINE,
        EMV_PROC,
        CLSS_PREPROC,
        CLSS_PROC,
        SIGNATURE,
        USER_AGREEMENT,
        OFFLINE_SEND,
        PRINT,
    }

    /**
     * bind state on action
     */
    @Override
    protected void bindStateOnAction() {
        if (isThirdMode) {
            transData.setEcrMsgType((byte) thirdPartyBaseRequest.getTransType());
            int transTypeDetail = thirdPartyBaseRequest.getTransTypeDetail();
            transData.setTransTypeDetail(transTypeDetail);
        }
        if (searchCardMode == -1) { // 待机银行卡消费入口
            searchCardMode = Component.getCardReadMode(ETransType.REDEEM_INST);
        }

        // enter trans amount action(This action is mainly used to handle bank card consumption and flash close paid deals)
        ActionEnterAmount amountAction = new ActionEnterAmount(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEnterAmount) action).setParam(getCurrentContext(),
                        transtTitle, false);
            }
        });
        bind(State.ENTER_AMOUNT.toString(), amountAction, true);

        // search card action
        ActionSearchCard searchCardAction = new ActionSearchCard(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionSearchCard) action).setParam(getCurrentContext(), transtTitle, searchCardMode,
                        String.valueOf(transData.getAmount()), null, "", transData);
            }
        });
        bind(State.CHECK_CARD.toString(), searchCardAction, true);

        // Select the Cash Dollar action
        ActionCashDolSelect cashDolSelect = new ActionCashDolSelect(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionCashDolSelect) action).setParam(getCurrentContext(), transData);
            }
        });

        bind(State.SELECT_CASH_DOL.toString(), cashDolSelect, true);


        // emv process action
        ActionEmvProcess emvProcessAction = new ActionEmvProcess(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionEmvProcess) action).setParam(getCurrentContext(), emv, transData);
            }
        });
        bind(State.EMV_PROC.toString(), emvProcessAction);


        //clss process action
        ActionClssProcess clssProcessAction = new ActionClssProcess(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionClssProcess) action).setParam(getCurrentContext(), clss, transData);
            }
        });
        bind(State.CLSS_PROC.toString(), clssProcessAction);

        //clss preprocess action
        ActionClssPreProc clssPreProcAction = new ActionClssPreProc(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionClssPreProc) action).setParam(clss, transData);
            }
        });
        bind(State.CLSS_PREPROC.toString(), clssPreProcAction);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), transData);
            }
        });
        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);


        // signature action
        ActionSignature signatureAction = new ActionSignature(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSignature) action).setParam(getCurrentContext(),
                        String.valueOf(transData.getAmount()));
            }
        });
        bind(State.SIGNATURE.toString(), signatureAction);

        //print preview action
        PrintTask printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        //如果ECR only=true, 则不允许从应用主界面发起交易,只能接受ECR交易请求
        if (!isEcrMode && Component.isEcrOnlyEnable()) {
            transEnd(new ActionResult(TransResult.ECR_MODE, null));
            return;
        }

        //如果是三方应用进入且金额大于0，则不用显示小费页面，直接切换到检卡界面
        if(isThirdMode && thirdPartyBaseRequest instanceof SaleRequest){
            transData.setAmount(((SaleRequest)thirdPartyBaseRequest).getAmount());
            if(transData.getAmount() > 0) {
                gotoState(State.CHECK_CARD.toString());
                return;
            }
        }

        // perform the first action
        if (TextUtils.isEmpty(amount)) {
            gotoState(State.ENTER_AMOUNT.toString());
        } else {
            gotoState(State.CLSS_PREPROC.toString());
        }
    }

    /**
     * on action result
     * @param currentState ：current State
     * @param result       ：current action result
     */
    @Override
    public void onActionResult(String currentState, ActionResult result) {
        int ret = result.getRet();
        State state = State.valueOf(currentState);
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType, transData.isDcc(), true, FinancialApplication.getAcqManager().getECardType(transData.getPan()));
            if (f55Dup.length > 0) {
//                TransData dupTransData = FinancialApplication.getTransDataDbHelper().findFirstDupRecord();
                TransData dupTransData = FinancialApplication.getTransDataDbHelper()
                        .findFistDupRecordByAcquirer(transData.getAcquirer());
                if (dupTransData != null) {
                    dupTransData.setDupIccData(Utils.bcd2Str(f55Dup));
                    FinancialApplication.getTransDataDbHelper().updateTransData(dupTransData);
                }
            }
            if (ret == TransResult.NEED_FALL_BACK) {
                needFallBack = true;
                searchCardMode &= 0x01;
                gotoState(State.CHECK_CARD.toString());
                return;
            } else if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }

        if (state == State.CLSS_PREPROC && ret != TransResult.SUCC) {
            searchCardMode &= 0x03;
        }

        switch (state) {
            case ENTER_AMOUNT:
                // save trans amount
                transData.setAmount((long) result.getData());
                gotoState(State.CHECK_CARD.toString());
                break;
            case CHECK_CARD: // subsequent processing of check card
                onCheckCard(result);
                break;
            case SELECT_CASH_DOL:
                gotoState(State.MAG_ONLINE.toString());
                break;
            case MAG_ONLINE: // subsequent processing of online
                if (transData.getLysStatus() != null &&
                        !TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
                    DialogUtils.showWarnMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            displayLoyaltyStatus(transData.getLysStatus()),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    // determine whether need electronic signature or print
                                    toSignOrPrint();
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    // determine whether need electronic signature or print
                    toSignOrPrint();
                }
                break;
            case EMV_PROC: // emv后续处理
                //get trans result
                final CTransResult transResult = (CTransResult) result.getData();
                if (transData.getLysStatus() != null &&
                        !TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
                    DialogUtils.showWarnMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            displayLoyaltyStatus(transData.getLysStatus()),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    // EMV完整流程 脱机批准或联机批准都进入签名流程
                                    afterEMVProcess(transResult.getTransResult());
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    afterEMVProcess(transResult.getTransResult());
                }
                break;
            case CLSS_PREPROC:
                gotoState(State.CLSS_PROC.toString());
                break;
            case CLSS_PROC:
                final CTransResult clssResult = (CTransResult) result.getData();
                if (transData.getLysStatus() != null &&
                        !TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
                    DialogUtils.showWarnMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            displayLoyaltyStatus(transData.getLysStatus()),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    afterClssProcess(clssResult);
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    afterClssProcess(clssResult);
                }
                break;
            case SIGNATURE:
                // save signature data
                byte[] signData = (byte[]) result.getData();
                byte[] signPath = (byte[]) result.getData1();

                if (signData != null && signData.length > 0 &&
                        signPath != null && signPath.length > 0) {
                    transData.setSignData(signData);
                    transData.setSignPath(signPath);
                    // update trans data，save signature
                    FinancialApplication.getTransDataDbHelper().updateTransData(transData);
                }

                gotoState(State.PRINT.toString());
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transType.getTransName(), result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }

    /**
     * on check card
     * @param result ActionResult
     */
    private void onCheckCard(ActionResult result) {
        ActionSearchCard.CardInformation cardInfo = (ActionSearchCard.CardInformation) result.getData();
        saveCardInfo(cardInfo, transData);

        // enter card number manually
        currentMode = cardInfo.getSearchMode();
        if (currentMode == ActionSearchCard.SearchMode.SWIPE || currentMode == ActionSearchCard
                .SearchMode.KEYIN) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(transData.getIssuer
                    (), transData.getTransType());
            if (!acquirer.getName().equals("HASE_OLS")) {
                transEnd(new ActionResult(TransResult.ERR_CARD_UNSUPPORTED, null));
            }
            transData.setAcquirer(acquirer);
            if (!Utils.checkAcquirerSettleStatus(acquirer.getName())) {
                transEnd(new ActionResult(TransResult.SETTLEMENT_PENDING, null));
                return;
            }
        }

        if (needFallBack) {
            transData.setEnterMode(TransData.EnterMode.FALLBACK);
        }

        if (currentMode == ActionSearchCard.SearchMode.SWIPE || currentMode == ActionSearchCard.SearchMode.KEYIN) {
            gotoState(State.SELECT_CASH_DOL.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.INSERT) {
            needRemoveCard = true;
            // EMV process
            gotoState(State.EMV_PROC.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.WAVE) {
            needRemoveCard = true;
            gotoState(State.CLSS_PREPROC.toString());
        } else if (currentMode == ActionSearchCard.SearchMode.QR) {
            needRemoveCard = false;
            gotoState(State.SCAN_CODE.toString());
        }
    }

    // need electronic signature or send
    private void toSignOrPrint() {
        if (Component.isSignatureFree(transData)) {// signature free
            // print preview
            transData.setSignFree(true);
            gotoState(State.PRINT.toString());
        } else {
            transData.setSignFree(false);
            gotoState(State.PRINT.toString());//恒生要求移除Signature流程，但是数据库中的signFree需保留置位
        }
        FinancialApplication.getTransDataDbHelper().updateTransData(transData);
    }

    /**
     * after emv process
     * @param transResult ETransResult
     */
    private void afterEMVProcess(ETransResult transResult) {
        EmvTransProcess.emvTransResultProcess(transResult, emv, transData);
        if (transResult == ETransResult.ONLINE_APPROVED) {// 联机批准
            toSignOrPrint();
        } else if (transResult == ETransResult.OFFLINE_APPROVED) {//脱机批准处理
            //恒生不支持脱机批准, 因为已经有了Offline Sale
            transEnd(new ActionResult(TransResult.ERR_NOT_SUPPORT_TRANS, null));
        } else {
            transactionFailProcess(transResult);
        }
    }

    /**
     * after clss process
     * @param transResult CTransResult
     */
    private void afterClssProcess(CTransResult transResult) {
        // 设置交易结果
        transData.setEmvResult(transResult.getTransResult());
        //Tap card fails will fall to swipe / insert card mode
        if (transResult.getTransResult() == ETransResult.APPLICATION_REJECTION) {
            searchCardMode &= 0x03;
            gotoState(State.CHECK_CARD.toString());
            return;
        }
        ClssTransProcess.clssTransResultProcess(transResult, clss, transData);
        if (transResult.getCvmResult() == ECvmResult.SIG || transResult.getCvmResult() == ECvmResult.ONLINE_PIN_SIG /*|| !Component.isSignatureFree(transData)*/) { // AET-283  //Jerry mask
            transData.setSignFree(false);
        } else {
            transData.setSignFree(true);
        }

        if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_AGAIN) {
            DialogUtils.showErrMessage(getCurrentContext(), transType.getTransName(), getString(R.string.prompt_please_retry), null, Constants.FAILED_DIALOG_SHOW_TIME);
            //AET-175
            gotoState(State.CLSS_PREPROC.toString());
        } else if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_ANOTHER_INTERFACE) {
            //此处应当结束整个交易，重新发起交易时，客户自然会使用接触式
            transEnd(new ActionResult(TransResult.PLEASE_USE_CONTACT, null));
        } else if (transResult.getTransResult() == ETransResult.CLSS_OC_APPROVED || transResult.getTransResult() == ETransResult.ONLINE_APPROVED) {
            transData.setOnlineTrans(transResult.getTransResult() == ETransResult.ONLINE_APPROVED);
            if (!transData.isSignFree()) {
                gotoState(State.SIGNATURE.toString());
            } else {
                gotoState(State.PRINT.toString());
            }
        } else {
            transactionFailProcess(transResult.getTransResult());
        }
    }
}
