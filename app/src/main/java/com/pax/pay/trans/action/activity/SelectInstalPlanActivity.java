package com.pax.pay.trans.action.activity;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.trans.model.InstalmentData;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by yangrr on 2017/12/29.
 */

public class SelectInstalPlanActivity extends BaseActivityWithTickForAction {

    private RecyclerView mRecyclerView;
    private InstalPlanAdapter mAdapter;
    private List<Acquirer> mAcqs;
    //打印失败后选择取消，itemView setEnable失效，使用此标志位避免二次点击
    private boolean isFirstClick = true;

    /**
     * get layout id
     *
     * @return int
     */
    @Override
    protected int getLayoutId() {
        return R.layout.activity_selectplan_layout;
    }

    /**
     * init views
     */
    @Override
    protected void initViews() {
        mRecyclerView = (RecyclerView) findViewById(R.id.select_plan_list);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new InstalPlanAdapter(mAcqs);
        mRecyclerView.setAdapter(mAdapter);
    }

    /**
     * set listeners
     */
    @Override
    protected void setListeners() {

    }

    /**
     * load param
     */
    @Override
    protected void loadParam() {
        mAcqs = new ArrayList<>();
        List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();

        for (Acquirer acquirer : listAcquirers) {
            if (acquirer.getName().contains("HASE_INS")) {
                mAcqs.add(acquirer);
            }
        }
    }

    /**
     * get title string
     * @return String
     */
    @Override
    protected String getTitleString() {
        return getString(R.string.instal_select_plan);
    }

    /**
     * on key back down
     * @return boolean
     */
    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

    /**
     * install plan holder
     */
    private class InstalPlanHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        private TextView mPlanName;
        private Acquirer mCurInstalAcq;

        /**
         * Constructor
         * @param itemView View
         */
        InstalPlanHolder(View itemView) {
            super(itemView);
            itemView.setOnClickListener(this);
            mPlanName = (TextView) itemView.findViewById(R.id.item_selectPlan_title);
        }

        /**
         * Constructor
         *
         * @param index int
         * @param acquirer acquirer
         */
        void bindPlan(int index, Acquirer acquirer) {
            mCurInstalAcq = acquirer;
            InstalmentData instalmentData = new InstalmentData(acquirer.getInstalPlan(),
                    "INST_" + acquirer.getName().split("_")[2]);
            String tmpStr =
                    String.format("%d.%s - %02dmths", index + 1, instalmentData.getInstalName(),
                            instalmentData.getInstalMonth());
            if (!TextUtils.isEmpty(acquirer.getAcqDescription())) {
                tmpStr = tmpStr + "(" + acquirer.getAcqDescription() + ")";
            }
            mPlanName.setText(tmpStr);
        }

        /**
         * 点击事件
         * @param v View
         */
        @Override
        public void onClick(View v) {
            if (isFirstClick) {
                isFirstClick = false;
                itemView.setEnabled(false);
                finish(new ActionResult(TransResult.SUCC, mCurInstalAcq));
            }
        }
    }

    /**
     * Instal plan adapter
     */
    private class InstalPlanAdapter extends RecyclerView.Adapter<InstalPlanHolder> {
        List<Acquirer> instalmentAcqs;

        InstalPlanAdapter(List<Acquirer> acquirers) {
            instalmentAcqs = acquirers;
        }

        /**
         * Instal plan holder
         *
         * @param parent ViewGroup
         * @param viewType int
         * @return InstalPlanHolder
         */
        @Override
        public InstalPlanHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            LayoutInflater layoutInflater = LayoutInflater.from(SelectInstalPlanActivity.this);
            View view = layoutInflater.inflate(R.layout.item_selectplan_layout, parent, false);
            return new InstalPlanHolder(view);
        }

        /**
         * on bind view holder
         * @param holder InstalPlanHolder
         * @param position int
         */
        @Override
        public void onBindViewHolder(InstalPlanHolder holder, int position) {
            Acquirer instalAcq = instalmentAcqs.get(position);
            holder.bindPlan(position, instalAcq);
        }

        /**
         * get item count
         * @return int
         */
        @Override
        public int getItemCount() {
            return instalmentAcqs.size();
        }
    }
}
