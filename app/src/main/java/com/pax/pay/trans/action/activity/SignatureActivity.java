/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * 20200320              Joshua.Huang            Modify
 * ===========================================================================================
 */
package com.pax.pay.trans.action.activity;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.glwrapper.impl.GL;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.view.ElectronicSignatureView;

import java.io.ByteArrayOutputStream;
import java.util.List;

import static com.pax.commonlib.utils.convert.IConvert.EEndian.LITTLE_ENDIAN;

public class SignatureActivity extends BaseActivityWithTickForAction {

    private ElectronicSignatureView mSignatureView;

    private RelativeLayout writeUserName = null;

    private Button clearBtn;
    private Button confirmBtn;

    private String amount;

    private boolean processing = false;

    private boolean isSymbolNegative;

    private String currencyCode;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_signature;
    }

    @Override
    protected void loadParam() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            amount = bundle.getString(EUIParamKeys.TRANS_AMOUNT.toString());
            currencyCode = bundle.getString(EUIParamKeys.CURRENCY_CODE.toString());
            isSymbolNegative = getIntent().getBooleanExtra(EUIParamKeys.IS_SYMBOL_NEGATIVE.toString(), false);
        }
    }

    @Override
    protected String getTitleString() {
        return getString(R.string.trans_signature);
    }

    @Override
    protected void initViews() {

        enableBackAction(false);

        TextView amountText = findViewById(R.id.trans_amount_tv);
        long amountLong = Utils.parseLongSafe(amount, 0);
        CurrencyCode byCode = CurrencyCode.getByCode(currencyCode);
        amount = byCode == null ? CurrencyConverter.convert(isSymbolNegative ? -amountLong : amountLong) : CurrencyConverter.convert(amountLong, byCode, isSymbolNegative);
        amountText.setText(amount);

        writeUserName = findViewById(R.id.writeUserNameSpace);
        mSignatureView = new ElectronicSignatureView(SignatureActivity.this);


        mSignatureView.setSampleRate(5);
        mSignatureView.setBitmap(new Rect(0, 0, 474, 158), 10, Color.WHITE);
        writeUserName.addView(mSignatureView);

        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT);
        lp.addRule(RelativeLayout.CENTER_IN_PARENT);

        clearBtn = findViewById(R.id.clear_btn);
        confirmBtn = findViewById(R.id.confirm_btn);
        confirmBtn.requestFocus();
    }

    @Override
    protected void setListeners() {
        clearBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
    }

    @Override
    protected boolean onKeyBackDown() {
        ToastUtils.showMessage(R.string.err_not_allowed);
        return true;
    }

    @Override
    protected boolean onKeyDel() {
        onClearClicked();
        return true;
    }

    @Override
    public void onClickProtected(View v) {

        switch (v.getId()) {
            case R.id.clear_btn:
                onClearClicked();
                break;
            case R.id.confirm_btn:
                confirmBtn.setClickable(false);
                onOKClicked();
                break;
            default:
                break;
        }
    }

    public void onClearClicked() {
        if (isProcessing()) {
            return;
        }
        setProcessFlag();
        mSignatureView.clear();
        clearProcessFlag();
    }

    public void onOKClicked() {
        if (isProcessing()) {
            return;
        }
        setProcessFlag();
        Bitmap bitmap = mSignatureView.save(true, 0);
        // 保存签名图片
        byte[] data = GL.getGL().getImgProcessing().bitmapToJbig(bitmap, Constants.rgb2MonoAlgo);

        if (data.length > 999) {
            ToastUtils.showMessage(R.string.signature_redo);
            setProcessFlag();
            mSignatureView.clear();
            clearProcessFlag();
            return;
        }
        clearProcessFlag();

        byte[] signPath = genSignPos();
        finish(new ActionResult(TransResult.SUCC, data, signPath));
    }

    @Override
    protected void onTimerFinish() {
        if (!mSignatureView.getTouched()) {
            super.onTimerFinish();
            return;
        }

        Bitmap bitmap = mSignatureView.save(true, 0);
        // 保存签名图片
        byte[] data = GL.getGL().getImgProcessing().bitmapToJbig(bitmap, Constants.rgb2MonoAlgo);

        if (data.length > 999) {
            super.onTimerFinish();
            return;
        }
        clearProcessFlag();

        byte[] signPath = genSignPos();
        finish(new ActionResult(TransResult.SUCC, data, signPath));
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        return item.getItemId() == android.R.id.home || super.onOptionsItemSelected(item);
    }

    private byte[] genSignPos() {
        List<float[]> signPos = mSignatureView.getPathPos();
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        for (float[] i : signPos) {
            if (i[0] < 0 && i[1] < 0) {
                swapStream.write(ConvertHelper.getConvert().intToByteArray(0xFFFFFFFF, LITTLE_ENDIAN), 0, 4);
            } else {
                byte[] bytes = ConvertHelper.getConvert().shortToByteArray((short) i[0], LITTLE_ENDIAN);
                swapStream.write(bytes, 0, 2);
                bytes = ConvertHelper.getConvert().shortToByteArray((short) i[1], LITTLE_ENDIAN);
                swapStream.write(bytes, 0, 2);
            }
        }
        return swapStream.toByteArray();
    }

    protected void setProcessFlag() {
        processing = true;
    }

    protected void clearProcessFlag() {
        processing = false;
    }

    protected boolean isProcessing() {
        return processing;
    }
}