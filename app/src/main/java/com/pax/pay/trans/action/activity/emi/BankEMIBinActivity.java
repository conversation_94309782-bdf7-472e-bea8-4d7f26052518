package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.BankEMIBinRequestData;
import com.pax.data.emi.BankEMIBinResponseData;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;

public class BankEMIBinActivity extends BaseEMIActivity<BankEMIBinRequestData, BankEMIBinResponseData> {
    private BankEMIBinRequestData bankEMIBinRequestData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestData = bankEMIBinRequestData;
        try {
            handleRequest();
        } catch (JsonProcessingException e) {
            handleError(getGeneralErrorCode(), null);
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected void loadParam() {
        bankEMIBinRequestData = (BankEMIBinRequestData) getIntent().getSerializableExtra(EUIParamKeys.BANK_EMI_BIN_DATA.toString());
        isFinishActivity = getIntent().getBooleanExtra("IS_FINISH_ACTIVITY", false);
    }

    @Override
    protected String getTitleString() {
        return "Bank EMI Bin";
    }

    @Override
    protected String getRequestType() {
        return "BINREQUEST";
    }

    @Override
    protected int getRequestCode() {
        return 112;
    }

    @Override
    protected int getGeneralErrorCode() {
        return TransResult.ERR_CHECK_EMI_BIN;
    }

    @Override
    protected Class<BankEMIBinResponseData> getResponseClass() {
        return BankEMIBinResponseData.class;
    }
}