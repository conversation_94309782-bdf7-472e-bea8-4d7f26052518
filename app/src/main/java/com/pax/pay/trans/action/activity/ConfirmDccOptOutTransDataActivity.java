/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-1-13
 * Module Author: qixw
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.EnterAmountTextWatcher;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.view.keyboard.CustomKeyboardEditText;

import java.math.BigDecimal;

public class ConfirmDccOptOutTransDataActivity extends BaseActivityWithTickForAction {

    private TextView mOriTips;
    private TextView mTotalAmount;

    private Button confirmBtn;

    private String navTitle;
    private ActionInputTransData.EInputType inputType;

    private long totalAmountLong = 0L;
    private long tipAmountLong = 0L;
    private long baseAmountLong = 0L;
    private float adjustPercent = 0.0f;
    private long oriTipAmountLong = 0L;
    private long maxValue;
    private String currencySymbol;
    private boolean isDccOptOut;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setEditText();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_confirm_dcc_opt_out_amount;
    }

    /**
     * load parameter
     */
    @Override
    protected void loadParam() {
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        inputType = (ActionInputTransData.EInputType) getIntent().getSerializableExtra(EUIParamKeys.INPUT_TYPE.toString());

        String oriTransAmount = getIntent().getStringExtra(EUIParamKeys.TRANS_AMOUNT.toString());
        String oriTips = getIntent().getStringExtra(EUIParamKeys.ORI_TIPS.toString());
        totalAmountLong = Utils.parseLongSafe(oriTransAmount, 0);
        tipAmountLong = Utils.parseLongSafe(oriTips, 0);
        oriTipAmountLong = tipAmountLong;
        baseAmountLong = totalAmountLong - tipAmountLong;
        adjustPercent = getIntent().getFloatExtra(EUIParamKeys.TIP_PERCENT.toString(), 0.0f);
        currencySymbol = getIntent().getStringExtra(EUIParamKeys.TRANS_CURRENCY_SYMBOL.toString());
        isDccOptOut = getIntent().getBooleanExtra(EUIParamKeys.IS_DCC_OPT_OUT.toString(), false);
    }

    /**
     * get title string
     * @return String
     */
    @Override
    protected String getTitleString() {
        return navTitle;
    }

    /**
     * init views
     */
    @Override
    protected void initViews() {
        TextView mBaseAmount = (TextView) findViewById(R.id.value_base_amount);
        mOriTips = (TextView) findViewById(R.id.value_ori_tips);
        mTotalAmount = (TextView) findViewById(R.id.value_total_amount);

        mBaseAmount.setText(CurrencyConverter.convert(baseAmountLong, CurrencyCode.getBySymbol(currencySymbol)));
        mOriTips.setText(CurrencyConverter.convert(tipAmountLong, CurrencyCode.getBySymbol(currencySymbol)));
        mTotalAmount.setText(CurrencyConverter.convert(totalAmountLong, CurrencyCode.getBySymbol(currencySymbol) ));

        confirmBtn = (Button) findViewById(R.id.info_confirm);
    }

    /**
     * set edit text
     */
    private void setEditText() {

        confirmBtnChange(); //AET-19

        final EnterAmountTextWatcher amountWatcher = new EnterAmountTextWatcher(0, 0, CurrencyCode.getBySymbol(currencySymbol));

        amountWatcher.setOnTipListener(new EnterAmountTextWatcher.OnTipListener() {
            @Override
            public void onUpdateTipListener(long baseAmount, long newTotalAmount) {
                tipAmountLong = newTotalAmount - baseAmountLong;
                totalAmountLong = newTotalAmount;
                confirmBtnChange();
            }

            @Override
            public boolean onVerifyTipListener(long baseAmount, long newTotalAmount) {
                //AET-205
                return baseAmountLong * (1 + adjustPercent / 100) >= newTotalAmount && newTotalAmount <= amountWatcher.getMaxValue();
            }
        });
        maxValue = amountWatcher.getMaxValue();
    }

    /**
     * confirm button change
     */
    private void confirmBtnChange() {
        long oriTotleAmount = baseAmountLong + oriTipAmountLong;
        boolean enable = totalAmountLong != oriTotleAmount;
        if (isDccOptOut) {
            enable = true;
        }
        confirmBtn.setEnabled(enable);
    }

    /**
     * set listeners
     */
    @Override
    protected void setListeners() {
        confirmBtn.setOnClickListener(this);
    }

    /**
     * 点击事件
     * @param v View
     */
    @Override
    public void onClickProtected(View v) {

        if (v.getId() == R.id.info_confirm) {
            String content = process();
            if (content == null || content.isEmpty()) {
                ToastUtils.showMessage(R.string.invalid_amount);
                return;
            }
            if (isDccOptOut){
                BigDecimal hundredBd = new BigDecimal(100);
                BigDecimal percentBd = BigDecimal.valueOf(adjustPercent);
                BigDecimal baseAmountBd = new BigDecimal(baseAmountLong);
                BigDecimal maxTipsBd = baseAmountBd.multiply(percentBd).divide(hundredBd, 2);

                if (baseAmountLong > maxValue) {
                    ToastUtils.showMessage(R.string.please_input_again);
                    return;
                }
            }
            finish(new ActionResult(TransResult.SUCC, totalAmountLong, tipAmountLong));
        }

    }

    /**
     * 输入数值检查
     */
    private String process() {
        return String.valueOf(baseAmountLong);
    }

    /**
     * on key back down
     * @return boolean
     */
    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }
}
