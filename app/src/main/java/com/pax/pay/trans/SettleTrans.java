/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans;

import android.content.Context;
import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionSelectAcquirer;
import com.pax.pay.trans.action.ActionSettle;
import com.pax.pay.trans.model.ETransType;
import com.pax.settings.SysParam;
import java.util.ArrayList;

public class SettleTrans extends BaseTrans {

    protected ArrayList<String> selectAcqs;
    protected boolean isHaseCupSettleSuccess = false;
    protected boolean masterCardSettleSuccess = false;

    /**
     * Constructor
     * @param context Context
     * @param listener TransEndListener
     */
    public SettleTrans(Context context, TransEndListener listener) {
        super(context, ETransType.EMV_SETTLE, listener);
    }

    /**
     * bind state on action
     */
    @Override
    protected void bindStateOnAction() {

        ActionInputPassword inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(getCurrentContext(), 6,
                        getString(R.string.prompt_settle_pwd), null);
            }
        });
        bind(State.INPUT_PWD.toString(), inputPasswordAction, true);

        ActionSelectAcquirer actionSelectAcquirer = new ActionSelectAcquirer(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSelectAcquirer) action).setParam(getCurrentContext(),
                        getString(R.string.settle_select_acquirer));
            }
        });
        bind(State.SELECT_ACQ.toString(), actionSelectAcquirer, true);

        ActionSettle settleAction = new ActionSettle(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSettle) action).setParam(getCurrentContext(),
                        getString(R.string.trans_settle), selectAcqs);
            }

        });

        bind(State.SETTLE.toString(), settleAction);

        //结算是否需要输入密码
        if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SETTLEMENT_PWD)) {
            gotoState(State.INPUT_PWD.toString());
        } else {
            gotoState(State.SELECT_ACQ.toString());
        }
    }

    enum State {
        INPUT_PWD,
        SELECT_ACQ,
        SETTLE
    }

    /**
     * on action result
     * @param currentState ：current State
     * @param result       ：current action result
     */
    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);
        switch (state) {
            case INPUT_PWD:
                String data = EncUtils.pwdSha((String) result.getData());
                if (!data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_SETTLE_PWD)) && !data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_SYS_PWD))) {
                    if (selectAcqs != null)
                        selectAcqs.clear();
                    transEnd(new ActionResult(TransResult.ERR_PASSWORD, null));
                    return;
                }
                gotoState(State.SELECT_ACQ.toString());
                break;
            case SELECT_ACQ:
                //noinspection unchecked
                selectAcqs = (ArrayList<String>) result.getData();
                gotoState(State.SETTLE.toString());
                break;
            case SETTLE:
                if (result.getRet() == TransResult.ERR_USER_CANCEL) {
                    gotoState(State.SELECT_ACQ.toString());
                } else {
                    ////HASE_CUP acquirer结算成功,则需要自动执行 UPI signon
                    if (selectAcqs != null && selectAcqs.contains("HASE_CUP") && result.getRet() == TransResult.SUCC) {
                        isHaseCupSettleSuccess = true;
                    }
                    //如果HASE_EMV acquirer结算成功,则需要自动执行 Mastercard signon
                    if (selectAcqs != null && selectAcqs.contains("HASE_EMV") && result.getRet() == TransResult.SUCC) {
                        masterCardSettleSuccess = true;
                    }
                    result.setData(isHaseCupSettleSuccess);
                    result.setData1(masterCardSettleSuccess);
                    if (selectAcqs != null) {
                        selectAcqs.clear();
                    }
                    transEnd(result);
                }
                break;
        }
    }

}
