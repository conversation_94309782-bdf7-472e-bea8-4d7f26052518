package com.pax.pay.trans.receipt;

import android.text.TextUtils;
import android.view.Gravity;
import com.pax.edc.R;
import com.pax.eemv.enums.ETransResult;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

public class LoyaltyVoidReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     */
    public LoyaltyVoidReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isPrintPreview isPrintPreview
     */
    public LoyaltyVoidReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addReferenceNO();
        addAppCode();
        addNewline(1);
        addEcrRef();
        addNewline(1);
        addEMVInfo();
        addSystemTrace();
        addNewline(1);
        addAmount();
        addNewline(1);
        addEnd();
    }

    @Override
    public void addInvoiceNo() {
        temp = Component.getPaddedNumber(transData.getOriginvoiceNo(), 6);

        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INVOICE NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END));
    }

    @Override
    public void addTransType() {
        String transName;
        switch (transData.getOrigTransType()) {
            case SALE:
            case SALES_WITH_CASH_DOLLAR:
                transName = "(VOID SALE) (取消 銷售)";
                break;
            case REFUND:
                transName = "(VOID REFUND) (取消 退貨)";
                break;
            case INSTALMENT:
                transName = "(VOID INSTAL) (取消 分期)";
                break;
            case PURE_REDEEM:
                transName = "(VOID REDEEM) (取消 換領)";
                break;
            case REDEEM_INST:
                transName = "VOID REDEEM-FOR-INSTAL";
                break;
            case MULTIPLE_UP:
                transName = "VOID REDEEM-MULTI-UP";
                break;
            case INST_MULTI_UP:
                transName = "VOID INST-MULTI-UP";
                break;
            default:
                transName = "";
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(transName)
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

    @Override
    public void addEMVInfo() {
        // for insert or wave, return
        if (!(transData.getEnterMode() == TransData.EnterMode.INSERT || transData.getEnterMode() == TransData.EnterMode.CLSS)) {
            return;
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("APP:")
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getEmvAppLabel())
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("AID:")
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getAid())
                        .setGravity(Gravity.END)
                        .setWeight(7.0f));
        if (!transData.getAcquirer().getName().equals("HASE_CUP")) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TC CODE:")
                            .setWeight(2.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getTc())
                            .setGravity(Gravity.END)
                            .setWeight(5.0f));
        } else {
            if (transData.getEmvResult() == ETransResult.OFFLINE_APPROVED) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TC CODE:")
                                .setWeight(2.0f))
                        .addUnit(page.createUnit()
                                .setText(transData.getTc())
                                .setGravity(Gravity.END)
                                .setWeight(5.0f));
            }

            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TVR:"))
                    .addUnit(page.createUnit()
                            .setText(transData.getTvr())
                            .setGravity(Gravity.END));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("TSI:  " + transData.getTsi()))
                    .addUnit(page.createUnit()
                            .setText("ATC:  " + transData.getAtc())
                            .setGravity(Gravity.END));
        }
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
    }

    @Override
    public void addAmount() {
        addBaseAmount();
        addMer1CashDollar();
        addMer2CashDollar();
        addHaseCashDollar();
        addBonus();
        addDashLine();
        addNetTotal();
        addDoubleLine();
        if (!TransData.LYSStatus.LYS_OK.equals(transData.getVoidLysStatus())) {
            addNewline(1);
            addRedeemUnavailableMsg(transData.getLysStatus());
            addNewline(1);
            return;
        }
        addAvailableRedeemBalance();
        //按照hase需求移除EXP BAL字段的同时，移除空格。
        //addNewline(1);
        addExpiredRedeemBalance();
    }

    @Override
    protected void addBaseAmount() {
        long baseAmount = transData.getAmount();
        if (transData.getOrigTransType() != ETransType.OFFLINE_TRANS_SEND && transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT) {
            baseAmount = baseAmount - transData.getTipAmount();
        }
        baseAmount = -baseAmount;
        String str = CurrencyConverter.convert(baseAmount, transData.getCurrency());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_amount_base))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(8.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(10.0f));
    }

    @Override
    protected void addMer1CashDollar() {
        if (TextUtils.isEmpty(transData.getMer1ProgramId())) {
            return;
        }
        long mer1Redeem = -transData.getMer1CashDolRedeemed();
        String str = CurrencyConverter.convert(mer1Redeem, transData.getCurrency());
        String mer1Name = FinancialApplication.getSysParam().get(SysParam.StringParam
                .MERCHANT1_DOL_NAME);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(mer1Name)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    @Override
    protected void addMer2CashDollar() {
        if (TextUtils.isEmpty(transData.getMer2ProgramId())) {
            return;
        }
        long mer2Redeem = -transData.getMer2CashDolRedeemed();
        String str = CurrencyConverter.convert(mer2Redeem, transData.getCurrency());
        String mer2Name = FinancialApplication.getSysParam().get(SysParam.StringParam
                .MERCHANT2_DOL_NAME);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(mer2Name)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    @Override
    protected void addHaseCashDollar() {
        if (TextUtils.isEmpty(transData.getCashDolProgramId())) {
            return;
        }
        long haseCash = -transData.getCashDolRedeemed();
        String str = CurrencyConverter.convert(haseCash, transData.getCurrency());
        String haseCashDolNam = FinancialApplication.getSysParam().get(SysParam.StringParam
                .HASE_CASH_DOL_NAME);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(haseCashDolNam)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    @Override
    protected void addBonus() {
        long bonus = -transData.getCashDolBonusRedeemed();
        if (bonus == 0) {
            return;
        }
        String str = CurrencyConverter.convert(bonus, transData.getCurrency());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_bonus))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));

    }

    @Override
    protected void addNetTotal() {
        long netAmount = -transData.getNetAmount();
        String netTotal = CurrencyConverter.convert(netAmount, transData.getCurrency());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_net_total))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(netTotal)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }
}

