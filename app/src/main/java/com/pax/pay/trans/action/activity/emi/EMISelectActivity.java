package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;
import android.view.KeyEvent;
import android.widget.Button;

import com.pax.abl.core.ActionResult;
import com.pax.device.Device;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.trans.TransResult;
import com.pax.sbipay.R;

public class EMISelectActivity extends BaseActivityWithTickForAction {
    private Button swipeDipTap;
    private Button checkEmiOffer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Device.enableStatusBar(false);
        Device.enableHomeRecentKey(false);
        tickTimer.stop();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_emiselect;
    }

    @Override
    protected void initViews() {
        enableActionBar(false);
        swipeDipTap = findViewById(R.id.buttonSwipeDipTap);
        checkEmiOffer = findViewById(R.id.buttonCheckEmiOffer);
    }

    @Override
    protected void setListeners() {
        checkEmiOffer.setOnClickListener(v -> finish(new ActionResult(TransResult.SUCC, true)));

        swipeDipTap.setOnClickListener(v -> finish(new ActionResult(TransResult.SUCC, false)));
    }

    @Override
    protected void loadParam() {
        //do nothing
    }

    @Override
    protected String getTitleString() {
        return "Select EMI";
    }

    @Override
    public void onBackPressed() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        }
        return super.onKeyDown(keyCode, event);
    }
}