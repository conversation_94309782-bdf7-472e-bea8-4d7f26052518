package com.pax.pay.trans.action;

import android.content.Context;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.dal.IScanner;
import com.pax.dal.IScanner.IScanListener;
import com.pax.device.Device;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.utils.TickTimer;

public class ActionScanCode extends AAction {

    private String qrCode = null;
    private IScanner scanner = null;

    public ActionScanCode(ActionStartListener listener) {
        super(listener);
    }

    @Override
    protected void process() {
        scanner = Device.getScanner();
        scanner.close(); // 系统扫码崩溃之后，再调用掉不起来

        scanner.setTimeOut(TickTimer.DEFAULT_TIMEOUT * 1000);
        scanner.open();
        scanner.start(new IScanListener() {
            @Override
            public void onCancel() {
                // DO NOT call setResult here since it will be can in onFinish
            }

            @Override
            public void onFinish() {
                if (scanner != null) {
                    scanner.close();
                }
                if (qrCode != null && qrCode.length() > 0) {
                    setResult(new ActionResult(TransResult.SUCC, qrCode));
                } else { //FIXME press key back on A920C while scanning should return to onCancel firstly
                    setResult(new ActionResult(TransResult.ERR_USER_CANCEL, null));
                }
            }

            @Override
            public void onRead(String content) {
                qrCode = content;
            }
        });
    }

}
