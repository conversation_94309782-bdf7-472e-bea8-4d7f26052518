package com.pax.pay.trans.receipt;

import android.graphics.Bitmap;
import android.view.Gravity;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransTotal;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import java.util.ArrayList;
import java.util.List;

public class ReceiptGeneratorSettleReport implements IReceiptGenerator {
    private ArrayList<TransTotal> transTotals;
    private List<String> successSettlement = new ArrayList<>();
    private List<String> failSettlement = new ArrayList<>();

    /**
     * constructor
     *
     * @param transTotals transTotals
     */
    ReceiptGeneratorSettleReport(ArrayList<TransTotal> transTotals) {
        this.transTotals = transTotals;
    }

    @Override
    public Bitmap generateBitmap() {
        IPage page = Device.generatePage();

        if (transTotals.size() == 1) {
            page.addLine()
                    .addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
            page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
            page.addLine().addUnit(page.createUnit().setText("ACQUIRER GRAND TOTAL").setGravity(Gravity.CENTER));
            TransTotal transTotal = transTotals.get(0);
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            page.addLine().addUnit(page.createUnit().setText(transTotal.getAcquirer().getName()));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            List<Acquirer> acqs = (ArrayList<Acquirer>) FinancialApplication.getAcqManager().findAllAcquirers();
            for (int i = 0; i < acqs.size() - 1; i++) {
                String acqName = acqs.get(i).getName();
                long saleNum = 0;
                long saleAmt = 0;
                if (transTotal.getAcquirer().getName().equals(acqs.get(i).getName())) {
                    acqName = transTotal.getAcquirer().getName();
                    saleNum = transTotal.getSaleNum();
                    saleAmt = transTotal.getSaleAmt();
                }

                // sale
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(setReportAcqName(acqName))
                                .setFontSize(FONT_NORMAL)
                                .setWeight(2.5f))
                        .addUnit(page.createUnit()
                                .setText(Long.toString(saleNum))
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.CENTER)
                                .setWeight(1.5f))
                        .addUnit(page.createUnit()
                                .setText(CurrencyConverter.convert(saleAmt))
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
            }

            page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_amount_total))
                            .setFontSize(FONT_NORMAL)
                            .setWeight(2.5f))
                    .addUnit(page.createUnit()
                            .setText(Long.toString(transTotal.getSaleNum()))
                            .setFontSize(FONT_NORMAL)
                            .setGravity(Gravity.CENTER)
                            .setWeight(1.5f))
                    .addUnit(page.createUnit()
                            .setText(CurrencyConverter.convert(transTotal.getSaleAmt()))
                            .setFontSize(FONT_NORMAL)
                            .setGravity(Gravity.END)
                            .setWeight(3.0f));
        } else {
            for (int i = 0; i < transTotals.size(); i++) {
                String acuirerName = transTotals.get(i).getAcquirer().getName();
                if (Utils.checkAcquirerSettleStatus(acuirerName)) {
                    successSettlement.add(acuirerName);
                } else {
                    failSettlement.add(acuirerName);
                }
            }

            if (failSettlement.size() > 0) {
                page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                page.addLine().addUnit(page.createUnit().setText("*     SETTLEMENT FAIL     *").setGravity(Gravity.CENTER));
                for (int i = 0; i < failSettlement.size(); i++) {
                    page.addLine().addUnit(page.createUnit().setText(failSettlement.get(i)));
                }
                page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                if (successSettlement.size() > 0) {
                    page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                    page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                    page.addLine().addUnit(page.createUnit().setText("*     ACQUIRER GRAND TOTAL     *").setGravity(Gravity.CENTER));
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                    for (int i = 0; i < successSettlement.size(); i++) {
                        page.addLine().addUnit(page.createUnit().setText(successSettlement.get(i)));
                    }
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                }
            } else {
                page.addLine().addUnit(page.createUnit().setText("GRAND TOTAL"));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            }

            if (successSettlement.size() > 0) {
                long successAcqNum = 0;
                long successAcqAmount = 0;
                for (int i = 0; i < successSettlement.size(); i++) {
                    TransTotal transTotal = new TransTotal();
                    for (int j = 0; j < transTotals.size(); j++) {
                        if (successSettlement.get(i).equals(transTotals.get(j).getAcquirer().getName())) {
                            transTotal = transTotals.get(j);
                            successAcqNum = successAcqNum + transTotal.getSaleNum();
                            successAcqAmount = successAcqAmount + transTotal.getSaleAmt();
                        }
                    }

                    // sale
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(setReportAcqName(transTotal.getAcquirer().getName()))
                                    .setFontSize(FONT_NORMAL)
                                    .setWeight(2.5f))
                            .addUnit(page.createUnit()
                                    .setText(Long.toString(transTotal.getSaleNum()))
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.CENTER)
                                    .setWeight(1.5f))
                            .addUnit(page.createUnit()
                                    .setText(CurrencyConverter.convert(transTotal.getSaleAmt()))
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.END)
                                    .setWeight(3.0f));
                }
                page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')));
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_amount_total))
                                .setFontSize(FONT_NORMAL)
                                .setWeight(2.5f))
                        .addUnit(page.createUnit()
                                .setText(Long.toString(successAcqNum))
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.CENTER)
                                .setWeight(1.5f))
                        .addUnit(page.createUnit()
                                .setText(CurrencyConverter.convert(successAcqAmount))
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
                if (failSettlement.size() == 0) {
                    page.addLine().addUnit(page.createUnit().setText("\n\n"));
                    page.addLine().addUnit(page.createUnit().setText("*            ALL SETTLEMENT OK          *").setGravity(Gravity.CENTER));
                    page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 39, '*')));
                }
            }
        }

        if (Component.isDemo()) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.demo_mode))
                            .setGravity(Gravity.CENTER));
        }
        page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        IImgProcessing imgProcessing = FinancialApplication.getGl().getImgProcessing();
        return imgProcessing.pageToBitmap(page, 384);
    }

    private String setReportAcqName(String acqName) {
        String reportAcqName = null;
        Acquirer acquirer;
        switch (acqName) {
            case "HASE":
            case "HASE_OLS":
                reportAcqName = "LYS GROSS";
                break;
            case "HASE_EMV":
                reportAcqName = "SALES";
                break;
            case "HASE_CUP":
                reportAcqName = "UNION PAY";
                break;
            case "AMEX":
                reportAcqName = "AMEX";
                break;
            case "HASE_INS_P1":
            case "HASE_INS_P2":
            case "HASE_INS_P3":
            case "HASE_INS_P4":
            case "HASE_INS_P5":
            case "HASE_INS_P6":
            case "HASE_INS_P7":
            case "HASE_INS_P8":
            case "HASE_INS_P9":
            case "HASE_INS_P10":
                acquirer = FinancialApplication.getAcqManager().findAcquirer(acqName);
                if (acquirer.getAcqDescription() != null) {
                    reportAcqName = "HASE "
                            + acquirer.getInstalPlan()
                            + "m-"
                            + acquirer.getAcqDescription();
                } else {
                    reportAcqName = "HASE " + acquirer.getInstalPlan() + "m";
                }
                break;
            case "HASE_DCC":
                reportAcqName = "HASE DCC ";
                break;
            case "HASE_MACAO":
                reportAcqName = "HASE MACAO ";
                break;
            case "HASE_YUU":
                reportAcqName = "HASE YUU ";
                break;
            default:
                break;
        }
        return reportAcqName;
    }

    @Override
    public String generateString() {
        return null;
    }
}
