package com.pax.pay.trans.action.activity.emi;

import android.os.Bundle;
import android.view.KeyEvent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.emi.EMIStatusCheckRequest;
import com.pax.data.emi.EMIStatusCheckResponse;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;

public class EMIStatusCheckActivity extends BaseEMIActivity<EMIStatusCheckRequest, EMIStatusCheckResponse> {
    private EMIStatusCheckRequest emiStatusCheckRequest;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestData = emiStatusCheckRequest;
        try {
            handleRequest();
        } catch (JsonProcessingException e) {
            handleError(getGeneralErrorCode(), null);
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    protected void loadParam() {
        emiStatusCheckRequest = (EMIStatusCheckRequest) getIntent().getSerializableExtra(EUIParamKeys.EMI_STATUS_CHECK_DATA.toString());
    }

    @Override
    protected String getTitleString() {
        return "EMI Status Check";
    }

    @Override
    public void onBackPressed() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected String getRequestType() {
        return "STATUS_CHECK_AND_CHARGESLIP";
    }

    @Override
    protected int getRequestCode() {
        return 119;
    }

    @Override
    protected int getGeneralErrorCode() {
        return TransResult.ERR_EMI_STATUS_CHECK;
    }

    @Override
    protected Class<EMIStatusCheckResponse> getResponseClass() {
        return EMIStatusCheckResponse.class;
    }

}