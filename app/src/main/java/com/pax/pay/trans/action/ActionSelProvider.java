package com.pax.pay.trans.action;

import android.content.Context;
import android.content.Intent;

import com.pax.abl.core.AAction;
import com.pax.settings.ProviderSelectActivity;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 * @Date 2024/2/2
 */
public class ActionSelProvider extends AAction {
    private WeakReference<Context> weakContext;
    public ActionSelProvider(ActionStartListener listener) {
        super(listener);
    }

    public void setParam(Context context) {
        this.weakContext = new WeakReference(context);
    }

    @Override
    protected void process() {
        Intent intent = new Intent(weakContext.get(), ProviderSelectActivity.class);
        weakContext.get().startActivity(intent);
    }
}
