/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/3/24                      Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.trans.model.TransData;

import static com.pax.pay.utils.Utils.subBytes;

public class PackYUUReversal extends PackIso8583 {
    public PackYUUReversal(PackListener listener) {
        super(listener);
    }

    /**
     * get YUUReversal RequiredFields
     *
     * @return int[]
     */
    @Override
    protected int[] getRequiredFields() {
        return new int[] { 2, 3, 4, 11, 14, 22, 24, 25, 41, 42, 62, 63 };
    }

    /**
     * unpackF63
     *
     * @param transData transData
     * @param buffer buffer
     * @return int[]
     */
    @Override
    protected int UnpackF63(@NonNull TransData transData, byte[] buffer) {
        int len = buffer.length;

        if (len != 61) {
            return TransResult.ERR_UNPACK;
        }

        String ciam = new String(subBytes(buffer, 20, 40));
        String programRef = new String(subBytes(buffer, 41, 61));
        transData.setEcrYUUCiam(ciam);
        transData.setEcrYUUProgramRef(programRef);

        return TransResult.SUCC;
    }
}
