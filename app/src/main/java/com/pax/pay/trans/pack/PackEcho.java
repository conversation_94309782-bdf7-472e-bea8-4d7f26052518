/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;
import com.pax.abl.core.ipacker.PackListener;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.trans.model.TransData;

public class PackEcho extends PackIso8583 {

    private static final String TAG = "PackEcho";

    public PackEcho(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[] { 3, 4, 11, 22, 24, 25, 41, 42, 62 };
    }

    /**
     * setBitData22
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData22(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("22", "0001");
    }

    /**
     * setBitData4
     *
     * @param transData transData
     * @throws Iso8583Exception Iso8583Exception
     */
    @Override
    protected void setBitData4(@NonNull TransData transData) throws Iso8583Exception {
        setBitData("4", "000000000000");
    }

    //    @Override
    //    @NonNull
    //    public byte[] pack(@NonNull TransData transData) {
    //
//        try {
//            setMandatoryData(transData);
//            setBitData60(transData);
//
////            return pack(false);
//        } catch (Exception e) {
//            Log.e(TAG, "", e);
//        }
//        return "".getBytes();
//    }


}
