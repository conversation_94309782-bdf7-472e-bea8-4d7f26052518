/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.trans.pack;

import androidx.annotation.NonNull;

import com.pax.abl.core.ipacker.PackListener;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.data.entity.TransData;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.pay.app.FinancialApplication;

import java.util.Map;

public class PackEcho extends PackIso8583 {

    public PackEcho(PackListener listener) {
        super(listener);
    }

    @Override
    protected int[] getRequiredFields() {
        return new int[]{3, 11, 41, 42, 63};
    }

    @Override
    protected void setBitData63(@NonNull TransData transData) throws Iso8583Exception {
        Map<ETermInfoKey, String> map = FinancialApplication.getDal().getSys().getTermInfo();
        //sn号（长度最大20字符） + "|" + 版本号（长度最大20字符）
        //数据长度不对或者SN在后台未注册，后台会返回96的错误码
        String privateUse = map.get(ETermInfoKey.SN) + "|" + FinancialApplication.getVersion().split("_")[0];
        setBitData("63", privateUse);
    }
}
