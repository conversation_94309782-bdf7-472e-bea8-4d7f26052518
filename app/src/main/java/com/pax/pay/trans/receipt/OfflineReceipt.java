package com.pax.pay.trans.receipt;

import android.graphics.Bitmap;
import android.view.Gravity;
import com.pax.edc.R;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

/**
 * Created by xub on 2018/3/28.
 */

public class OfflineReceipt extends AReceiptGenerator {

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     */
    public OfflineReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint) {
        super(transData, currentReceiptNo, receiptMax, isReprint);
    }

    /**
     * constructor
     *
     * @param transData transData
     * @param currentReceiptNo currentReceiptNo
     * @param receiptMax receiptMax
     * @param isPrintPreview isPrintPreview
     */
    public OfflineReceipt(TransData transData, int currentReceiptNo, int receiptMax,
            boolean isReprint, boolean isPrintPreview) {
        super(transData, currentReceiptNo, receiptMax, isReprint, isPrintPreview);
    }

    @Override
    public void addContent(IPage page) {
        addLogo();
        addMerchantName();
        addAddress();
        addTid();
        addMid();
        addNewline(1);
        addTransType();
        addNewline(1);
        addDataTime();
        addCardType();
        addCardNO();
        addCardHolder();
        addExpDate();
        addBatchNo();
        addInvoiceNo();
        addDccOfflineReferenceNO();
        addAppCode();
        addEMVInfo();
        addSystemTrace();
        addNewline(1);
        addEcrRef();
        addNewline(1);
        addAmount();
        addDoubleLine();
        addNewline(1);
        addAcquirerRefNo();
        addEnd();
    }

    @Override
    public void addTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("OFFLINE 離線交易")
                        .setFontSize(FONT_BIG)
                        .setTextStyle(BOLD));
    }

    // for dcc transaction may need rrn print on the receipt
    public void addDccOfflineReferenceNO() {
        if (transData.getAcquirer().getName().equals("HASE_DCC")) {
            temp = transData.getRefNo();
            if (temp == null) {
                temp = "";
            }
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("RRN:")
                            .setFontSize(FONT_NORMAL))
                    .addUnit(page.createUnit()
                            .setText(temp)
                            .setGravity(Gravity.END));
        }
    }

    @Override
    public void addReferenceNO() {
        String panpart1 = transData.getPan().substring(0, 6);
        String panpart2 = transData.getPan().substring(6, 12);
        if (panpart1 == null) {
            return;
        }

        // add bank reference number on the receipt
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("Bank Ref Num:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(panpart1 + PanShuffle(panpart2, 6))
                        .setGravity(Gravity.END));
    }

    @Override
    public void addEnd() {
        if (receiptNo == 0) {
            if (!transData.getAcquirer().getName().equals("AMEX")) {
                addReferenceNO();
                addNewline(1);
            }
        }
        if (transData.getInstalment() != 0) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_prompt))
                            .setFontSize(FONT_NORMAL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }

        boolean isSignFree = transData.isSignFree();

        if (isSignFree) {// only sign free
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_amount_prompt_end_sign)));
            page.addLine().addUnit(page.createUnit()
                    .setText("無須簽名"));
        }

        if (!isSignFree && isPrintSign) { // is not sign free and need sign
            if (!transData.isDcc() || transData.isFullOutSource()) {
                page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_verify_Chinese))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.LEFT));
                page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_verify))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.LEFT));
            } else {
                if (transData.getAcquirer().getName().equals("HASE_DCC")) {
                    if (!(Utils.getString(R.string.issuer_visa).equalsIgnoreCase(transData.getIssuer().getName()))) {
                        page.addLine().addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_dcc_mc_claim))
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.LEFT));
                        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                    }
                }
            }
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_sign_Chinese))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            Bitmap bitmap = loadSignature(transData);
            if (bitmap != null) {
                page.addLine().addUnit(page.createUnit()
                        .setBitmap(loadSignature(transData))
                        .setGravity(Gravity.CENTER));
            } else {
                page.addLine().addUnit(page.createUnit().setText(" "));
            }

        }

        if (isSignFree) { // don't need sign
            addNewline(1);
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify_Chinese))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            addNewline(1);
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_sign_line))
                            .setGravity(Gravity.CENTER));
        } else if (receiptNo == 0) {
            addNewline(2);
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("X")
                            .setGravity(Gravity.LEFT));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_sign_line))
                            .setGravity(Gravity.CENTER));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        }

        if (receiptMax == 3) { // for receipt number require is 3
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 銀行存根 " + Utils.getString(R.string.receipt_stub_acquire) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else if (receiptNo == 1) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 商戶存根 " + Utils.getString(R.string.receipt_stub_merchant) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 客戶存根 " + Utils.getString(R.string.receipt_stub_user) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        } else { // for receipt number require is 1,2
            if (receiptNo == 0) { // for receipt number require is 1
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 商戶存根 " + Utils.getString(R.string.receipt_stub_merchant) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else { // for receipt number require is 2
                if (transData.isDcc() && !transData.isFullOutSource() && !(Utils.getString(R.string.issuer_visa).equalsIgnoreCase(transData.getIssuer().getName()))){
                    page.addLine().addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_dcc_mc_claim))
                            .setFontSize(FONT_SMALL)
                            .setGravity(Gravity.LEFT));
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                }
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 客戶存根 " + Utils.getString(R.string.receipt_stub_user) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        }
        if (!transData.isDcc() || transData.isFullOutSource()) { // for trans not dcc/ macao
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("Cardholder acknowledges receipt of goods and services and agrees to pay the total shown here in.")
                            .setFontSize(FONT_SMALL));
        }

        if (Component.isDemo()) { // demo mode only
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        if (!isPrintPreview) {
            page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        }
    }

    private String PanShuffle(String panBuf, int panLen) {
        int i;
        char panDigit;
        StringBuilder temp = new StringBuilder(panBuf);
        for (i = 0; i < panLen; i++) {
            panDigit = panBuf.charAt(i);
            temp.replace(i, i + 1, Pan(panDigit));
        }
        panBuf = temp.toString();
        return panBuf.substring(2, 6) + panBuf.substring(0, 2);
    }

    private String Pan(char panDigit) {
        String mapDigit = "";

        switch (panDigit) {
            case '0': {
                mapDigit = "3";
            }
            break;

            case '1': {
                mapDigit = "5";
            }
            break;

            case '2': {
                mapDigit = "7";
            }
            break;

            case '3': {
                mapDigit = "9";
            }
            break;

            case '4': {
                mapDigit = "1";
            }
            break;

            case '5': {
                mapDigit = "2";
            }
            break;

            case '6': {
                mapDigit = "8";
            }
            break;

            case '7': {
                mapDigit = "4";
            }
            break;

            case '8': {
                mapDigit = "6";
            }
            break;

            case '9': {
                mapDigit = "0";
            }
            break;
        }

        return mapDigit;
    }

}
