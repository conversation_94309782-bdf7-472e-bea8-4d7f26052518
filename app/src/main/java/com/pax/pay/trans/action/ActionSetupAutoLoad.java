/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-12-23
 * Module Author: lixc
 * Description: setup auto load action
 *
 * ============================================================================
 */
package com.pax.pay.trans.action;

import android.content.Context;
import android.content.DialogInterface;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;

public class ActionSetupAutoLoad extends AAction {

    private Context context;

    /**
     * derived classes must call super(listener) to set
     *
     * @param listener {@link ActionStartListener}
     */
    public ActionSetupAutoLoad(ActionStartListener listener) {
        super(listener);
    }

    /**
     * Sets param.
     *
     * @param context the context
     */
    public void setParam(Context context) {
        this.context = context;
    }

    @Override
    protected void process() {
        final String[] autoLoad = new String[]{context.getString(R.string.autoload_manual), context.getString(R.string.autoload_auto)};
        String mode = FinancialApplication.getSysParam().get(SysParam.StringParam.SETUP_AUTO_LOAD);
        int choiceIndex = 0;
        if (mode == null || mode.isEmpty()) {
            FinancialApplication.getSysParam().set(SysParam.StringParam.SETUP_AUTO_LOAD, autoLoad[0]);
        } else if (mode.equals(context.getString(R.string.autoload_auto))) {
            choiceIndex = 1;
        }
        //show dialog to set up manual or auto
        DialogUtils.showSingleChoiceSelectDialog(context, context.getString(R.string.setup_autoload), autoLoad, choiceIndex,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        //save
                        FinancialApplication.getSysParam().set(SysParam.StringParam.SETUP_AUTO_LOAD, autoLoad[which]);
                        dialog.dismiss();
                    }
                }, new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        setResult(new ActionResult(TransResult.SUCC, null));
                    }
                });
    }
}
