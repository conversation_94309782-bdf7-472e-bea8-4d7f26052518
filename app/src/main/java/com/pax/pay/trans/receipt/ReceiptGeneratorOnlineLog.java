/*
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen); CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen); CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C); 2019-? PAX Computer Technology(Shenzhen); CO., LTD All rights reserved.
 *  * Description: // Detail description about the voidction of this module,
 *  *             // interfaces with the other modules, and dependencies.
 *  * Revision History:
 *  * Date                  Author	                 Action
 *  * 20200713  	         xieYb                   Modify
 *  * ===========================================================================================
 *
 */
package com.pax.pay.trans.receipt;

import android.graphics.Bitmap;

import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.glwrapper.impl.GL;
import com.pax.glwrapper.page.IPage;
import com.pax.pay.utils.Utils;

import java.util.HashMap;

/**
 * This class works for generate Transaction Total information
 */
public class ReceiptGeneratorOnlineLog {
    private static final int FONT_BIG = 30;
    private static final int FONT_SMALL = 19;
    //保存M和3域内容，用于区分BQR的63域
    private String mField = "";
    private String thirdField = "";

    public Bitmap call(HashMap<String, byte[]> map, Boolean isSend) {
        IPage page = GL.getGL().getImgProcessing().createPage();
        if (isSend) {
            page.addLine().addUnit(page.createUnit().setText("Send data:").setFontSize(FONT_SMALL));
        } else {
            page.addLine().addUnit(page.createUnit().setText("Recv data:").setFontSize(FONT_SMALL));
        }

        generateBitmapSingleLine("H", map.get("h"), page);
        generateBitmapSingleLine("M", map.get("m"), page);

        for (int i = 0; i <= 64; ++i) {
            String tag = Integer.toString(i);
            generateBitmapSingleLine(tag, map.get(tag), page);

        }
        generateBitmapSingleLine("123", map.get("123"), page);
        page.addLine().addUnit(page.createUnit().setText("\n\n\n").setFontSize(FONT_SMALL));

        IImgProcessing imgProcessing = GL.getGL().getImgProcessing();
        return imgProcessing.pageToBitmap(page, 384);
    }

    private void generateBitmapSingleLine(String tag, byte[] value, IPage page) {
        if (value == null || value.length == 0) {
            return;
        }
        String writeValue;
        String writeTag = tag;
        // 筛选BHARQR的63域，特殊处理
        if("63".equals(tag) && (("0110".equals(mField) && "330001".equals(thirdField))
         || ("0620".equals(mField)))){
            mField = "";
            thirdField = "";
            writeValue = new String(value);
        } else if ("48".equals(writeTag) || "52".equals(writeTag) || "53".equals(writeTag) || "55".equals(writeTag) || "56".equals(writeTag) || "62".equals(writeTag) || "63".equals(writeTag) || "123".equals(writeTag)) {
            writeValue = ConvertHelper.getConvert().bcdToStr(value);
            if ("53".equals(writeTag)) {
                writeValue = Utils.maskString(writeValue);
            }
        } else if ("22".equals(writeTag)) {
            writeValue = new String(value).substring(1, 4);
        } else {
            writeValue = new String(value);
        }
        if("M".equals(tag)){
            mField = writeValue;
        }else if("3".equals(tag)){
            thirdField = writeValue;
        }

        page.addLine().addUnit(page.createUnit().setText("[" + writeTag + "]:" + writeValue).setFontSize(FONT_SMALL));
    }
}
