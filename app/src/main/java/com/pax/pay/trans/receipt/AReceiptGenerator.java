package com.pax.pay.trans.receipt;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import androidx.annotation.StringRes;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;

import com.pax.abl.utils.PanUtils;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.eemv.enums.ETransResult;
import com.pax.glwrapper.imgprocessing.IImgProcessing;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

import java.io.IOException;
import java.io.InputStream;
import java.util.EnumSet;
import java.util.Map;
import java.util.Set;

import static com.pax.glwrapper.page.IPage.ILine.IUnit.BOLD;

/**
 * Created by liliang on 2017/11/15.
 */

/**
 * Abstract class to generate receipts, other receipts need to extends this class and implements
 * {@link AReceiptGenerator#addContent(com.pax.glwrapper.page.IPage)}
 */
public abstract class AReceiptGenerator implements IReceiptGenerator {

    protected int receiptNo = 0;
    protected TransData transData;
    protected boolean isRePrint = false;
    protected int receiptMax = 0;
    protected boolean isPrintPreview = false;
    boolean isPrintSign = false;
    String temp;


    com.pax.glwrapper.page.IPage page = Device.generatePage();
    SysParam sysParam = FinancialApplication.getSysParam();


    public AReceiptGenerator(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
    }

    public AReceiptGenerator(TransData transData, int currentReceiptNo, int receiptMax, boolean isReprint, boolean isPrintPreview) {
        this.transData = transData;
        this.receiptNo = currentReceiptNo;
        this.isRePrint = isReprint;
        this.receiptMax = receiptMax;
        this.isPrintPreview = isPrintPreview;
    }

    @Override
    public Bitmap generateBitmap() {
        if (receiptNo > receiptMax) {
            receiptNo = 0;
        }
        // the first copy print signature, if three copies, the second copy should print signature too
        if (receiptNo == 0 || ((receiptMax == 3) && receiptNo == 1)) {
            isPrintSign = true;
        }
        //mPage.adjustLineSpace(1);
        addContent(page);
        IImgProcessing imgProcessing = FinancialApplication.getGl().getImgProcessing();
        return imgProcessing.pageToBitmap(page, 384);
    }


    /**
     * Add your receipts content in your implementation of this method.
     *
     * @param page
     */
    public abstract void addContent(com.pax.glwrapper.page.IPage page);

    /**
     * Add logo on the receipt
     */
    public void addLogo() {
        if (Component.isDemo()) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        //Reprint
        if (isRePrint) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("----" + Utils.getString(R.string.receipt_print_again) + "----")
                            .setFontSize(FONT_BIG)
                            .setGravity(Gravity.CENTER)
                            .setTextStyle(BOLD));
        }

        addNewline(1);

        //Add logo
        page.addLine()
                .addUnit(page.createUnit()
                        .setBitmap(Utils.getImageFile("logo.bmp"))
                        .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit().setText(" "));
    }

    /**
     * Add merchant name on the receipt.
     */
    public void addMerchantName() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(sysParam.get(SysParam.StringParam.EDC_MERCHANT_NAME_EN))
                        .setGravity(Gravity.LEFT)
                        .setFontSize(FONT_BIG));
    }

    /**
     * Add merchant address on the receipt.
     */
    public void addAddress() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(sysParam.get(SysParam.StringParam.EDC_MERCHANT_ADDRESS))
                        .setGravity(Gravity.LEFT));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
    }

    /**
     * Add mid on the receipt.
     */
    public void addMid() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_merchant_code) + ":")
                        .setWeight(2.0f))
                .addUnit(page.createUnit()
                        .setText(transData.getAcquirer().getMerchantId())
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
    }

    /**
     * Add merchant id on the receipt.
     */
    public void addMerchantId() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_long_merchant_id))
                        .setWeight(2.0f)
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(transData.getAcquirer().getMerchantId())
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
    }

    /**
     * Add tid on the receipt.
     */
    public void addTid() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_terminal_code_space) + ":"))
                .addUnit(page.createUnit()
                        .setText(transData.getAcquirer().getTerminalId())
                        .setGravity(Gravity.END));
    }

    /**
     * Add terminal id on the receipt.
     */
    public void addTerminalId() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_long_terminal_id)).setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(transData.getAcquirer().getTerminalId())
                        .setGravity(Gravity.END));
    }


    /**
     * Add card type date on the receipt.
     */
    public void addCardType() {
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.card_type) + transData.getIssuer().getName())
                .setGravity(Gravity.START));
    }


    /**
     * Add card NO.on the receipt.
     */
    public void addCardNO() {
        Set<ETransType> transTypes = EnumSet.of(
                ETransType.PREAUTH, ETransType.AUTHORIZATION
        );

        if ((receiptNo == 0 && transTypes.contains(transData.getTransType())) ||
                transData.getTransType() == ETransType.INC_AUTHORIZATION || transData.getTransType() == ETransType.OFFLINE_TRANS_SEND) {
            if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_MASK_CARD_NO)) {
                temp = transData.getPan();
            } else {
                temp = PanUtils.maskCardNo(transData.getPan(), transData.getIssuer().getPanMaskPattern());
            }
        } else {
            if (!transData.isOnlineTrans()) {
                temp = transData.getPan();
            } else {
                temp = PanUtils.maskCardNo(transData.getPan(),
                        transData.getIssuer().getPanMaskPattern());
            }
        }

        if (transData.getMcQrcodeLast4Pan() != null) {
            temp = temp.substring(0, temp.length() - 4) + transData.getMcQrcodeLast4Pan();
        }

        //对于dual brand卡的银联sale交易，fallback应做swipe处理
        if ("HASE_CUP".equals(transData.getAcquirer().getName()) || transData.getIssuer()
                .getName()
                .equals("HASE_CUP")) {
            if (transData.getEnterMode().equals(TransData.EnterMode.FALLBACK)) {
                //銀聯後台已不再接受fallback交易, 所以若採用95,會導致後台拒絕該交易.所以,非银联卡的Fallback交易, 需將交易辨別為SWIPE交易處理
                if (transData.getIssuer().getName().equals("UnionPay") || transData.getIssuer()
                        .getName()
                        .equals("HASE_CUP")) {
                    temp += " " + transData.getEnterMode().toString();
                } else {
                    temp += " " + TransData.EnterMode.SWIPE.toString();
                }
            } else {
                temp += " " + transData.getEnterMode().toString();
            }
        } else {
            temp += " " + transData.getEnterMode().toString();
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText("CARD#")
                        .setWeight(2.0f)
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END)
                        .setWeight(8.0f));
    }

    /**
     * Add expire date on the receipt.
     */
    public void addExpDate() {
        //expiry date
        String expDate = transData.getExpDate();
        if (expDate != null && !expDate.isEmpty()) {
            if (transData.getIssuer().isRequireMaskExpiry()) {
                expDate = "**/**";
            } else {
                expDate = expDate.substring(2) + "/" + expDate.substring(0, 2);// 将yyMM转换成MMyy
            }
        } else {
            expDate = "";
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("EXP DATE:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(expDate)
                        .setGravity(Gravity.END));
    }

    /**
     * Add card holder on the receipt.
     */
    public void addCardHolder() {
        //edc增加参数控制是否打印cardholder name
        if (transData.getCardHolder() == null || !FinancialApplication.getSysParam().
                get(SysParam.BooleanParam.PRINT_CARDHOLDER_NAME)) {
            return;
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(transData.getCardHolder())
                        .setGravity(Gravity.START)
                        .setFontSize(FONT_NORMAL));
    }

    /**
     * Add transaction type on the receipt.
     */
    public void addTransType() {
        temp = transData.getTransState().equals(TransData.ETransStatus.NORMAL) ? "" : " (" + transData.getTransState().toString() + ")";
        if (transData.getOrigTransType() == ETransType.REFUND) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(transData.getTransType().getTransName() + " " + transData.getOrigTransType().getTransName() + temp)
                            .setFontSize(FONT_BIG)
                            .setTextStyle(BOLD));
        } else {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(transData.getTransType().getTransName() + temp)
                            .setFontSize(FONT_BIG)
                            .setTextStyle(BOLD));
        }
    }


    /**
     * Add batch number on the receipt.
     */
    public void addBatchNo() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("BATCH NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6))
                        .setGravity(Gravity.END));
    }

    /**
     * Add invoice no on the receipt.
     */
    public void addInvoiceNo() {
        temp = Component.getPaddedNumber(transData.getInvoiceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INVOICE NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END));
    }

    /**
     * Add system trace
     */
    public void addSystemTrace() {
        temp = Component.getPaddedNumber(transData.getTraceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("SYSTEM TRACE:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setGravity(Gravity.END));
    }

    /**
     * Add date and time on the receipt
     */
    public void addDataTime() {
        String formattedDate = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN_DISPLAY2);
        page.addLine().addUnit(page.createUnit().setText(formattedDate).setFontSize(FONT_NORMAL));
    }

    /**
     * Add reference NO. on the receipt.
     */
    public void addReferenceNO() {
        temp = transData.getRefNo();
        if (transData.getTransType() == ETransType.INC_AUTHORIZATION && transData.getOrigRefNo() != null) {
            temp = transData.getOrigRefNo();
        }
        if (temp == null) {
            temp = "";
        }
        if (transData.getAcquirer().getName().equals("HASE_CUP")) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("HASE RRN:")
                            .setFontSize(FONT_NORMAL))
                    .addUnit(page.createUnit()
                            .setText(temp)
                            .setGravity(Gravity.END));
        } else {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("RRN:")
                            .setFontSize(FONT_NORMAL))
                    .addUnit(page.createUnit()
                            .setText(temp)
                            .setGravity(Gravity.END));
        }
    }

    /**
     * Add app code on the receipt.
     */
    public void addAppCode() {
        String authCode = transData.getAuthCode();
        if (transData.getTransType() == ETransType.INC_AUTHORIZATION && transData.getOrigAuthCode() != null) {
            authCode = transData.getOrigAuthCode();
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("APP CODE:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(authCode)
                        .setGravity(Gravity.END));
    }

    void addEcrRef() {
        if (transData.isEcrMode()) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("ECR REF: ")
                            .setWeight(4.0f)
                            .setFontSize(FONT_NORMAL))
                    .addUnit(page.createUnit()
                            .setText(transData.getEcrRefferenceNo())
                            .setWeight(7.0f)
                            .setGravity(Gravity.END));
        }
    }

    /**
     * Add cup rrn on the receipt.
     */
    protected void addCupRRN() {
        if ("HASE_CUP".equals(transData.getAcquirer().getName()) || "HASE_CUP".equals(
                transData.getIssuer().getName())) {
            String cupTrace = "";

            String cupRRN = transData.getCupRRN();
            if (cupRRN == null) {
                cupRRN = "";
            }
            if (cupRRN.length() == 12) {
                cupTrace = cupRRN.substring(6, 12);
            }
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("CUP RRN: ")
                            .setFontSize(FONT_NORMAL))
                    .addUnit(page.createUnit()
                            .setText(cupRRN)
                            .setGravity(Gravity.END));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("UPI TRACE: ")
                            .setFontSize(FONT_NORMAL))
                    .addUnit(page.createUnit()
                            .setText(cupTrace)
                            .setGravity(Gravity.END));
        }
    }

    /**
     * Add print instalment on the receipt.
     */
    public void addPrintInstalment() {
        if (transData.getInstalment() != 0) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_month) + ":" + Component.getPaddedNumber(transData.getInstalment(), 2))
                            .setFontSize(FONT_NORMAL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }


        if (transData.getTransType() == ETransType.VOID) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_orig_trans_no)
                                    + Component.getPaddedNumber(transData.getOrigTransNo(), 6)));
        }
    }

    /**
     * Add EMV information on the receipt.
     */
    public void addEMVInfo() {
        if (transData.getEnterMode() == TransData.EnterMode.INSERT || transData.getEnterMode() == TransData.EnterMode.CLSS || transData.getEnterMode() == TransData.EnterMode.QR) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("APP:")
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getEmvAppLabel())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("AID:")
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getAid())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            if (!transData.getAcquirer().getName().equals("HASE_CUP")) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TC CODE:")
                                .setWeight(2.0f))
                        .addUnit(page.createUnit()
                                .setText(transData.getTc())
                                .setGravity(Gravity.END)
                                .setWeight(5.0f));
            } else {
                if (transData.getEmvResult() == ETransResult.OFFLINE_APPROVED) {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText("TC CODE:")
                                    .setWeight(2.0f))
                            .addUnit(page.createUnit()
                                    .setText(transData.getTc())
                                    .setGravity(Gravity.END)
                                    .setWeight(5.0f));
                }

                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TVR:"))
                        .addUnit(page.createUnit()
                                .setText(transData.getTvr())
                                .setGravity(Gravity.END));
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TSI:  " + transData.getTsi()))
                        .addUnit(page.createUnit()
                                .setText("ATC:  " + transData.getAtc())
                                .setGravity(Gravity.END));
            }
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        }
    }

    /**
     * Add NO. of instalment on the receipt.
     */

    public void addInstalmentNo() {
        if (transData.getTransType() == ETransType.VOID
                && transData.getOrigTransType() != ETransType.INSTALMENT) {
            return;
        }
        addNewline(1);
        String instalmentNo = "0";
        if (transData.getInstalment() < 10) {
            instalmentNo = "0" + transData.getInstalment();
        } else {
            instalmentNo = String.valueOf(transData.getInstalment());
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_instalment_month) + instalmentNo)
                        .setFontSize(FONT_NORMAL));
        addNewline(1);
    }

    /**
     * Add amount information on the receipt.
     */
    public void addAmount() {
        // check if transaction is dcc, macao or other
        // dcc & macao receipt will have differnet layout
        if (transData.isDcc() && !transData.isFullOutSource()) { // dcc/ macao transaction
            String tipAmopunt = String.valueOf(transData.getTipAmount());
            ETransType transType = transData.getTransType();
            boolean hasTip = !(tipAmopunt == null || tipAmopunt.isEmpty() || tipAmopunt.equals("0"));
            boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP);
            String localCurrencySymbol = transData.getLocalCurrency().getSymbol();
            //rate
            String homeCurrencySymbol = transData.isFullOutSource() ? transData.getReceiptHomeCurrency().getSymbol() : transData.getHomeCurrency().getSymbol();
            if (transData.getTransType() != ETransType.AUTH_REVERSAL) {
                String rate;
                if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_INVERSE_RATE) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC)) {
                    rate = "1 " + homeCurrencySymbol + " = " + Utils.inverseRate(transData.getRate(), 8) + " " + localCurrencySymbol;
                } else {
                    rate = "1 " + localCurrencySymbol + " = " + Utils.initRate(transData.getRate()) + " " + homeCurrencySymbol;
                }
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_rate))
                                .setFontSize(FONT_SMALL)
                                .setWeight(8.0f))
                        .addUnit(page.createUnit()
                                .setText(rate)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(20.0f));
                // for dcc visa will have addition charge
                if (Utils.getString(R.string.issuer_visa).equalsIgnoreCase(transData.getIssuer().getName())) {
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                    page.addLine().addUnit(page.createUnit()
                            .setText("(" + Utils.initMarkupRate(transData.getDccMarkupRate()) + " Markup Included)")
                            .setFontSize(FONT_SMALL));
                }

                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

                // total
                long amount = transData.getAmount();
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("SELECT[X]TRANSACTION CURRENCY")
                                .setFontSize(FONT_SMALL));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

                long homeAmount = transData.isFullOutSource() ? Utils.parseLongSafe(transData.getReceiptHomeCurrencyAmount(), 0) : Utils.parseLongSafe(transData.getHomeCurrencyAmount(), 0);
                //VOID REFUND, REFUND(VOIDED) & Dcc Full reversal print "-"
                if (transData.getTransType().isSymbolNegative() && transData.getOrigTransType() != ETransType.REFUND) {
                    if (transData.getOfflineSendState() == TransData.OfflineStatus.OFFLINE_NOT_SENT) {
                        if (!(transData.getOrigTransType() == ETransType.OFFLINE_TRANS_SEND)) {
                            amount = transData.getNotSendAmount();
                            homeAmount = Utils.parseLongSafe(transData.getNotSendHomeCurrencyAmount(), 0);
                        }
                        amount = -amount;
                        homeAmount = -homeAmount;
                    } else {
                        amount = -amount;
                        homeAmount = -homeAmount;
                    }
                }
                if (transData.getOrigTransType() == ETransType.REFUND || transData.isFullReversal()) {
                    amount = -amount;
                    homeAmount = -homeAmount;
                }
                temp = CurrencyConverter.convert(amount, transData.getLocalCurrency()); // local currency amount
                String temp2 = transData.isFullOutSource() ? CurrencyConverter.convert(homeAmount, transData.getReceiptHomeCurrency()) : CurrencyConverter.dccConvert(homeAmount, transData.getHomeCurrency(), hasTip); // home currency amount

                String amountLab = " AMOUNT[X]";
                //for sale, auth one step flow amount will be []
                if (((transType == ETransType.INC_AUTHORIZATION || transType == ETransType.AUTHORIZATION) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_DCC_AUTH_NEW_FLOW))
                        || ((transType == ETransType.SALE || transType == ETransType.OFFLINE_TRANS_SEND) && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_DCC_SALE_NEW_FLOW))) {
                    amountLab = " AMOUNT[  ]";
                }
                //for incremental auth, pre-auth amount will be []
                if (transType == ETransType.INC_AUTHORIZATION || transType == ETransType.PREAUTH || transData.isFullReversal()) {
                    amountLab = " AMOUNT[  ]";
                }

                // [X] will be present for selecting currency
                String localCurrency = transData.isFullOutSource() ? "[X]" + transData.getLocalCurrency().getSymbol() + " AMOUNT" : "[  ]" + transData.getLocalCurrency().getSymbol() + " AMOUNT";
                String homeCurrency = transData.isFullOutSource() ? transData.getReceiptHomeCurrency().getSymbol() + " AMOUNT[  ]" : transData.getHomeCurrency().getSymbol() + amountLab;
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(localCurrency)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.START))
                        .addUnit(page.createUnit()
                                .setText(homeCurrency)
                                .setFontSize(FONT_SMALL)
                                .setGravity(Gravity.END));

                // receipt print out tip's detail
                if (hasTip && transType.isAdjustAllowed() && transData.getTransState() != TransData.ETransStatus.VOIDED) { // trans with tips
                    String localTip = CurrencyConverter.convert(transData.getTipAmount(), transData.getLocalCurrency());
                    String homeTip = transData.isFullOutSource() ? CurrencyConverter.convert(Utils.parseLongSafe(transData.getReceiptHomeCurrencyTipAmount(), 0), transData.getReceiptHomeCurrency()) : CurrencyConverter.dccConvert(Utils.parseLongSafe(transData.getHomeCurrencyTipAmount(), 0), transData.getHomeCurrency(), true);
                    String localBase = CurrencyConverter.convert(transData.getAmount() - transData.getTipAmount(), transData.getLocalCurrency());
                    String homeBase = transData.isFullOutSource() ? CurrencyConverter.convert(Utils.parseLongSafe(transData.getReceiptHomeCurrencyAmount(), 0) - Utils.parseLongSafe(transData.getReceiptHomeCurrencyTipAmount(), 0), transData
                            .getReceiptHomeCurrency()) : CurrencyConverter.dccConvert(Utils.parseLongSafe(Component.localAmountToHomeAmount(transData.getHomeCurrency(), String.valueOf(transData.getAmount() - transData.getTipAmount()), transData.getRate()), 0), transData
                            .getHomeCurrency(), true);

                    //base
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(localBase)
                                    .setWeight(2.5f)
                                    .setFontSize(FONT_SMALL)
                                    .setGravity(Gravity.START))
                            .addUnit(page.createUnit()
                                    .setText("")
                                    .setFontSize(FONT_SMALL)
                                    .setWeight(1f))
                            .addUnit(page.createUnit()
                                    .setText(homeBase)
                                    .setFontSize(FONT_SMALL)
                                    .setWeight(2.5f)
                                    .setGravity(Gravity.END));

                    //tip
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(localTip)
                                    .setWeight(2f)
                                    .setFontSize(FONT_SMALL)
                                    .setGravity(Gravity.START))
                            .addUnit(page.createUnit()
                                    .setFontSize(FONT_SMALL)
                                    .setText(Utils.getString(R.string.receipt_amount_tip))
                                    .setWeight(1f))
                            .addUnit(page.createUnit()
                                    .setText(homeTip)
                                    .setFontSize(FONT_SMALL)
                                    .setWeight(2f)
                                    .setGravity(Gravity.END));

                    //total
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(temp)
                                    .setFontSize(FONT_SMALL)
                                    .setWeight(2f)
                                    .setGravity(Gravity.START))
                            .addUnit(page.createUnit()
                                    .setFontSize(FONT_SMALL)
                                    .setText(Utils.getString(R.string.receipt_amount_total))
                                    .setWeight(1f))
                            .addUnit(page.createUnit()
                                    .setText(temp2)
                                    .setFontSize(FONT_SMALL)
                                    .setWeight(2f)
                                    .setGravity(Gravity.END));

                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                } else { // trans without tips
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(temp)
                                    .setFontSize(FONT_SMALL)
                                    .setGravity(Gravity.START))
                            .addUnit(page.createUnit()
                                    .setText(temp2)
                                    .setFontSize(FONT_SMALL)
                                    .setGravity(Gravity.END));
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                }

                //when support tip, print tip line
                //only for sale and offline
                if (enableTip && transType.isAdjustAllowed() && !hasTip && transData.getTransState() != TransData.ETransStatus.VOIDED) {
                    page.addLine().addUnit(page.createUnit().setText("TIPS IN TXN CURRENCY _________________").setFontSize(FONT_SMALL));
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                    page.addLine().addUnit(page.createUnit().setText("TOTAL IN TXN CURRENCY _______________").setFontSize(FONT_SMALL));
                    page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
                }
            } else {
                long amount = transData.isFullReversal() ? 0 : transData.getAmount();
                temp = CurrencyConverter.convert(amount, transData.getLocalCurrency()); // local currency amount
                page.addLine().addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_used_amount))
                                .setFontSize(FONT_SMALL)
                                .setWeight(12.0f))
                        .addUnit(page.createUnit()
                                .setText(String.valueOf(temp))
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(20.0f));
            }

            if (Utils.getString(R.string.issuer_visa).equalsIgnoreCase(transData.getIssuer().getName())) {
                page.addLine().addUnit(page.createUnit().setText("------------------------------------------").setFontSize(FONT_SMALL));
                if (transData.getLocalCurrency().getCode().equals("446")) {
                    page.addLine().addUnit(page.createUnit().setText(Utils.getString(R.string.receipt_dcc_visa_claim_MOP)).setFontSize(FONT_SMALL).setGravity(Gravity.CENTER));
                } else {
                    page.addLine().addUnit(page.createUnit().setText(Utils.getString(R.string.receipt_dcc_visa_claim_HKD)).setFontSize(FONT_SMALL).setGravity(Gravity.CENTER));
                }
            }
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        } else { // local trans (not dcc/ macao trans)
            boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP)
                    && transData.getIsPreSaleTips();
            boolean isAmex = Component.isAmex(transData.getAcquirer().getName());
            enableTip = enableTip && !isAmex;
            //base & tip
            if (transData.getTransType().isAdjustAllowed()) {
                String AmtTotalName = Utils.getString(R.string.receipt_amount_total);
                long base = transData.getAmount() - transData.getTipAmount();
                long TotalAmount = base;

                temp = CurrencyConverter.convert(base, transData.getCurrency());
                if (enableTip) {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(Utils.getString(R.string.receipt_amount_base))
                                    .setFontSize(FONT_NORMAL)
                                    .setWeight(8.0f))
                            .addUnit(page.createUnit()
                                    .setText(temp)
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.END)
                                    .setWeight(10.0f));
                }

                long tips = transData.getTipAmount();
                temp = CurrencyConverter.convert(tips, transData.getCurrency());
                if (enableTip) {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(Utils.getString(R.string.receipt_amount_tip))
                                    .setFontSize(FONT_NORMAL)
                                    .setWeight(2.0f))
                            .addUnit(page.createUnit().setText(temp)
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.END)
                                    .setWeight(3.0f));
                    page.addLine().addUnit(page.createUnit().setText(Component.getPaddedString(" ", 35, '-')).setFontSize(FONT_NORMAL));
                }
                TotalAmount += tips;
                temp = CurrencyConverter.convert(TotalAmount, transData.getCurrency());
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(AmtTotalName)
                                .setWeight(2.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(3.0f));
            } else {
                long BaseAmount = transData.getAmount();
                String BaseName;
                if (transData.getTransType().isSymbolNegative() && transData.getOrigTransType() != ETransType.REFUND) {
                    BaseAmount = -BaseAmount;
                    //fix add Cash$ about Redeem VOID.
                    BaseName = Utils.getString(R.string.receipt_amount_total);
                } else {
                    BaseName = Utils.getString(R.string.receipt_amount_total);
                }

                temp = CurrencyConverter.convert(BaseAmount, transData.getCurrency());
                //Base/total amount
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(BaseName)
                                .setFontSize(FONT_NORMAL)
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(temp)
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(9.0f));
            }
        }
    }


    /**
     * The end of your receipt.
     */
    public void addEnd() {
        if (transData.getInstalment() != 0) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_prompt_chinese_part1))
                            .setFontSize(FONT_NORMAL));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_prompt_chinese_part2))
                            .setFontSize(FONT_NORMAL));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_prompt))
                            .setFontSize(FONT_NORMAL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }

        boolean isSignFree = transData.isSignFree();
        boolean isDccOptOut = transData.getTransType().equals(ETransType.DCC_OPT_OUT);

        if (isSignFree) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_amount_prompt_end_sign)));
            page.addLine().addUnit(page.createUnit()
                    .setText("無須簽名"));
        }

        if (!isSignFree && (!transData.isDcc() || transData.isFullOutSource())) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify_Chinese))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        } else {
            if (transData.getAcquirer().getName().equals("HASE_DCC")) { //for dcc trans & non visa card
                addNewline(1);
                page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_verify_Chinese))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.LEFT));
                page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_dcc_mc_claim))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.LEFT));
                page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            }
        }
        if (!isSignFree && isPrintSign) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_sign_Chinese))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            Bitmap bitmap = loadSignature(transData);
            if (bitmap != null) {
                page.addLine().addUnit(page.createUnit()
                        .setBitmap(loadSignature(transData))
                        .setGravity(Gravity.CENTER));
            } else {
                page.addLine().addUnit(page.createUnit().setText(" "));
            }

        }

        if (isSignFree) {
            addNewline(1);
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify_Chinese))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_verify))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.LEFT));
            addNewline(1);
        } else if (receiptNo == 0) {
            addNewline(2);
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("X")
                            .setGravity(Gravity.LEFT));
        }
        //以恒生信用卡進行 LYS ONUS 交易 (交易送往 HASE_OLS), 於 SALE, VOID SALE, REFUND, VOID REFUND 交易的 CUSTOMER COPY 小票, 加上以下的 Disclaimer
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_LYS_DISCLAIMER)
                && transData.getAcquirer().getName().equals("HASE_OLS")) {
            if (transData.getTransType() == ETransType.SALE
                    || transData.getTransType() == ETransType.REFUND
                    || transData.getTransType() == ETransType.SALES_WITH_CASH_DOLLAR
                    || transData.getOrigTransType() == ETransType.SALE
                    || transData.getOrigTransType() == ETransType.REFUND
                    || transData.getOrigTransType() == ETransType.SALES_WITH_CASH_DOLLAR) {
                if (receiptNo == receiptMax - 1) {
                    addDisclaimer();
                }
            }
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_sign_line))
                        .setGravity(Gravity.CENTER));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));

        if (receiptMax == 3) {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 銀行存根 "
                                        + Utils.getString(R.string.receipt_stub_acquire)
                                        + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else if (receiptNo == 1) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 商戶存根 " + Utils.getString(R.string.receipt_stub_merchant) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 客戶存根 " + Utils.getString(R.string.receipt_stub_user) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        } else {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 商戶存根 " + Utils.getString(R.string.receipt_stub_merchant) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("* 客戶存根 " + Utils.getString(R.string.receipt_stub_user) + " *")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        }
        if (!transData.isDcc() || transData.isFullOutSource()) {
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("Cardholder acknowledges receipt of goods and services and agrees to pay the total shown here in.")
                            .setFontSize(FONT_SMALL));
        }

        if (Component.isDemo()) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        if (!isPrintPreview) {
            page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        }
    }


    /**
     * Add CUP card NO. and expire date on the receipt.
     */
    public void addCUPCardNOExpDate() {
        if (transData.getTransType() == ETransType.PREAUTH || transData.getTransType() == ETransType.AUTHORIZATION) {
            temp = transData.getPan();
        } else {
            if (!transData.isOnlineTrans()) {
                temp = transData.getPan();
            } else {
                temp = PanUtils.maskCardNo(transData.getPan(), transData.getIssuer().getPanMaskPattern());
            }
        }

        temp += " /" + transData.getEnterMode().toString();

        String expDate = transData.getExpDate();
        if (expDate != null && !expDate.isEmpty()) {
            if (transData.getIssuer().isRequireMaskExpiry()) {
                expDate = "**/**";
            } else {
                expDate = expDate.substring(2) + "/" + expDate.substring(0, 2);// 将yyMM转换成MMyy
            }
        } else {
            expDate = "";
        }

        //card NO/expiry date
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_no) + "/" + Utils.getString(R.string.receipt_card_date))
                        .setFontSize(FONT_SMALL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setWeight(4.6f))
                .addUnit(page.createUnit()
                        .setText(expDate)
                        .setGravity(Gravity.END)
                        .setWeight(1.0f));
    }


    /**
     * Add CUP batch number and trans NO. on the receipt.
     */
    public void addCUPBatchTransNo() {
        temp = Component.getPaddedNumber(transData.getTraceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_batch_num_colon))
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_trans_no))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Component.getPaddedNumber(transData.getAcquirer().getCurrBatchNo(), 6))
                        .setFontSize(FONT_BIG))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_BIG)
                        .setGravity(Gravity.END));

        temp = Component.getPaddedNumber(transData.getInvoiceNo(), 6);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText("INVOICE NO:")
                        .setFontSize(FONT_NORMAL))
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_BIG)
                        .setGravity(Gravity.END));
    }


    /**
     * Add CUP reference NO. and App code on the receipt.
     */
    public void addCUPReferenceNOAppCode() {
        temp = transData.getRefNo();
        if (temp == null) {
            temp = "";
        }
        String authCode = transData.getAuthCode();

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_ref_no))
                        .setFontSize(FONT_SMALL))
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_app_code))
                        .setFontSize(FONT_SMALL)
                        .setGravity(Gravity.END));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(temp)
                        .setFontSize(FONT_BIG)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(authCode)
                        .setFontSize(FONT_BIG)
                        .setGravity(Gravity.END)
                        .setWeight(3.0f));
        page.addLine().addUnit(page.createUnit().setText(" "));
    }

    /**
     * Add CUP date and time code on the receipt.
     */
    public void addCUPDateTime() {
        String formattedDate = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN_DISPLAY);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_date))
                        .setFontSize(FONT_SMALL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(formattedDate)
                        .setFontSize(FONT_BIG));
    }

    /**
     * Add CUP date and time code on the receipt.
     */
    public void addCUPTransType() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_trans_type))
                        .setFontSize(FONT_SMALL));
        temp = transData.getTransState().equals(TransData.ETransStatus.NORMAL) ? "" : " (" + transData.getTransState().toString() + ")";
        if (transData.getOrigTransType() == ETransType.REFUND) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(transData.getTransType().getTransName() + " " + transData.getOrigTransType().getTransName() + temp)
                            .setFontSize(FONT_BIG));
        } else {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(transData.getTransType().getTransName() + temp)
                            .setFontSize(FONT_BIG));
        }
    }

    /**
     * The CUP end of your receipt.
     */
    public void addCUPEnd() {
        if (transData.getInstalment() != 0) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_instalment_prompt))
                            .setFontSize(FONT_NORMAL));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }

        long pinFreeAmtLong = sysParam.get(SysParam.NumberParam.QUICK_PASS_TRANS_PIN_FREE_AMOUNT);
        boolean isPinFree = transData.isPinFree();
        boolean isSignFree = transData.isSignFree();

        if (isPinFree && isSignFree) {// sign free and pin free
            if (transData.getEnterMode() == TransData.EnterMode.QR) {
                page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_sign_Chinese))
                        .setGravity(Gravity.START));
            } else {
                page.addLine().addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_amount_prompt_end)));
            }
        } else if (isSignFree) {// only sign free
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_amount_prompt_end_sign)));

        } else if (isPinFree && !transData.isCDCVM() && !transData.isDcc()) {// pin free
            page.addLine().addUnit(page.createUnit()
                    .setText(genFreePrompt(R.string.receipt_amount_prompt_start,
                            pinFreeAmtLong, R.string.receipt_amount_prompt_end_pin)));
        }

        if (!isSignFree && isPrintSign) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.receipt_sign_Chinese))
                    .setFontSize(FONT_SMALL)
                    .setGravity(Gravity.START));
            Bitmap bitmap = loadSignature(transData);
            if (bitmap != null) {
                page.addLine().addUnit(page.createUnit()
                        .setBitmap(loadSignature(transData))
                        .setGravity(Gravity.CENTER));
            } else {
                page.addLine().addUnit(page.createUnit().setText(" "));
            }

        }

        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));

        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_CUP_verify))
                        .setFontSize(FONT_SMALL));
        if (receiptMax == 3) {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("****  " + Utils.getString(R.string.receipt_stub_acquire) + "   ****")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else if (receiptNo == 1) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("****  " + Utils.getString(R.string.receipt_stub_merchant) + "   ****")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("****  " + Utils.getString(R.string.receipt_stub_user) + "   ****")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        } else {
            if (receiptNo == 0) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("****  " + Utils.getString(R.string.receipt_stub_merchant) + "   ****")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            } else {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("****  " + Utils.getString(R.string.receipt_stub_user) + "   ****")
                                .setGravity(Gravity.CENTER)
                                .setFontSize(FONT_NORMAL));
            }
        }

        if (Component.isDemo()) {
            page.addLine().addUnit(page.createUnit()
                    .setText(Utils.getString(R.string.demo_mode))
                    .setGravity(Gravity.CENTER));
        }

        if (!isPrintPreview) {
            page.addLine().addUnit(page.createUnit().setText("\n\n\n\n"));
        }
    }


    /**
     * Add CUP EMV information on the receipt.
     */
    public void addCUPEMVInfo() {
        if (transData.getEnterMode() == TransData.EnterMode.INSERT || transData.getEnterMode() == TransData.EnterMode.CLSS) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("APP:" + transData.getEmvAppLabel())
                            .setGravity(Gravity.START));
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("AID:" + transData.getAid())
                            .setGravity(Gravity.START));
            if (!transData.getAcquirer().getName().equals("HASE_CUP")) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TC:" + transData.getTc())
                                .setGravity(Gravity.START));
            } else {
                if (transData.getEmvResult() == ETransResult.OFFLINE_APPROVED) {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText("TC:" + transData.getTc())
                                    .setGravity(Gravity.START));
                } else {
                    String arqc = transData.getArqc();
                    if (arqc == null) {
                        arqc = "";
                    }
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText("ARQC:" + arqc)
                                    .setGravity(Gravity.START));
                }

                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TVR:" + transData.getTvr())
                                .setGravity(Gravity.START));
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("TSI:  " + transData.getTsi())
                                .setGravity(Gravity.START));
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText("ATC:  " + transData.getAtc())
                                .setGravity(Gravity.START));
            }
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
        }
    }


    /**
     * Add CUP card type date on the receipt.
     */
    public void addCUPCardType() {
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_card_type) + ":")
                .setGravity(Gravity.START)
                .setFontSize(FONT_NORMAL));
        page.addLine().addUnit(page.createUnit()
                .setText("中國銀聯 CUP")
                .setFontSize(FONT_BIG)
                .setGravity(Gravity.START));
    }

    /**
     * Add CUP card holder on the receipt.
     */
    public void addCUPCardHolder() {
        if (transData.getCardHolder() == null) {
            return;
        }
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_card_holder))
                        .setGravity(Gravity.START)
                        .setFontSize(FONT_NORMAL));
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(transData.getCardHolder())
                        .setGravity(Gravity.START)
                        .setFontSize(FONT_BIG));
    }

    /**
     * Add newline in your implementation of this method.
     *
     * @param n
     */
    public void addNewline(int n) {
        for (int i = 0; i < n; i++) {
            page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
            if (i == 10) break;
        }
    }

    /**
     * Add newline in your implementation of this method.
     */
    public void addDashLine() {
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_sign_line))
                .setGravity(Gravity.CENTER));
    }

    /**
     * Add double line in your implementation of this method.
     */
    public void addDoubleLine() {
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_double_line))
                .setGravity(Gravity.CENTER));
    }

    /**
     * Add CUP double line in your implementation of this method.
     */
    public void addCUPDoubleLine() {
        page.addLine().addUnit(page.createUnit()
                .setText(Utils.getString(R.string.receipt_CUP_double_line))
                .setGravity(Gravity.END));
        page.addLine().addUnit(page.createUnit().setText(" ").setFontSize(FONT_SMALL));
    }


    protected void addBaseAmount() {
        long baseAmount = transData.getAmount() - transData.getTipAmount();
        String str = CurrencyConverter.convert(baseAmount, transData.getCurrency());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_amount_base))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(8.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(10.0f));
    }

    protected void addMer1CashDollar() {
        if (TextUtils.isEmpty(transData.getMer1ProgramId())) {
            return;
        }
        long mer1Redeem = transData.getMer1CashDolRedeemed();
        String str = CurrencyConverter.convert(mer1Redeem, transData.getCurrency());
        String mer1Name = FinancialApplication.getSysParam().get(SysParam.StringParam
                .MERCHANT1_DOL_NAME);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(mer1Name)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    protected void addMer2CashDollar() {
        if (TextUtils.isEmpty(transData.getMer2ProgramId())) {
            return;
        }
        long mer2Redeem = transData.getMer2CashDolRedeemed();
        String str = CurrencyConverter.convert(mer2Redeem, transData.getCurrency());
        String mer2Name = FinancialApplication.getSysParam().get(SysParam.StringParam
                .MERCHANT2_DOL_NAME);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(mer2Name)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    protected void addHaseCashDollar() {
        if (TextUtils.isEmpty(transData.getCashDolProgramId())) {
            return;
        }
        long haseCash = transData.getCashDolRedeemed();
        String str = CurrencyConverter.convert(haseCash, transData.getCurrency());
        String haseCashDolNam = FinancialApplication.getSysParam().get(SysParam.StringParam
                .HASE_CASH_DOL_NAME);
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(haseCashDolNam)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    protected void addBonus() {
        long bonus = transData.getCashDolBonusRedeemed();
        if (bonus == 0) {
            return;
        }
        String str = CurrencyConverter.convert(bonus, transData.getCurrency());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_bonus))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    protected void addNetAmount() {
        String str = CurrencyConverter.convert(transData.getNetAmount(), transData.getCurrency());
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_net_amount))
                        .setFontSize(FONT_NORMAL)
                        .setWeight(4.0f))
                .addUnit(page.createUnit()
                        .setText(str)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(6.0f));
    }

    protected void addNetTotal() {
        long netAmount = transData.getNetAmount();
        long tips = transData.getTipAmount();
        boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP);
        String netTotal = CurrencyConverter.convert(netAmount + tips, transData.getCurrency());
        if (enableTip) {
            if (transData.getIsPreSaleTips() || isRePrint && transData.getTransState() == TransData.ETransStatus.ADJUSTED) {
                page.addLine()
                        .addUnit(page.createUnit()
                                .setText(Utils.getString(R.string.receipt_net_total))
                                .setFontSize(FONT_NORMAL)
                                .setWeight(4.0f))
                        .addUnit(page.createUnit()
                                .setText(netTotal)//HongKong requirement，Tips should be written on receipt by customer
                                .setFontSize(FONT_NORMAL)
                                .setGravity(Gravity.END)
                                .setWeight(6.0f));
            } else {
                if (!transData.getIsPreSaleTips()
                        && transData.getAcquirer()
                        .getName()
                        .equalsIgnoreCase("HASE_OLS")
                        && transData.getEnterMode() == TransData.EnterMode.CLSS) {
                    //恒生卡拍卡交易不支持小费
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(Utils.getString(R.string.receipt_net_total))
                                    .setFontSize(FONT_NORMAL)
                                    .setWeight(4.0f))
                            .addUnit(page.createUnit()
                                    .setText(netTotal)
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.END)
                                    .setWeight(6.0f));
                } else {
                    page.addLine()
                            .addUnit(page.createUnit()
                                    .setText(Utils.getString(R.string.receipt_net_total))
                                    .setFontSize(FONT_NORMAL)
                                    .setWeight(4.0f))
                            .addUnit(page.createUnit()
                                    .setText(
                                            "")//HongKong requirement，Tips should be written on receipt by customer
                                    .setFontSize(FONT_NORMAL)
                                    .setGravity(Gravity.END)
                                    .setWeight(6.0f));
                }
            }
        } else {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText(Utils.getString(R.string.receipt_net_total))
                            .setFontSize(FONT_NORMAL)
                            .setWeight(4.0f))
                    .addUnit(page.createUnit()
                            .setText(netTotal)//HongKong requirement，Tips should be written on receipt by customer
                            .setFontSize(FONT_NORMAL)
                            .setGravity(Gravity.END)
                            .setWeight(6.0f));
        }
    }

    protected void addAvailableRedeemBalance() {
        if (TextUtils.isEmpty(transData.getMer1ProgramId()) &&
                TextUtils.isEmpty(transData.getMer2ProgramId()) &&
                TextUtils.isEmpty(transData.getCashDolProgramId())) {
            return;
        }

        if (isInvalidExpDate(transData.getMer1CashDolExpdate()) &&
                isInvalidExpDate(transData.getMer2CashDolExpdate()) &&
                isInvalidExpDate(transData.getCashDolExpdate())) {
            return;
        }

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(" ")
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.LEFT)
                        .setWeight(5.0f))
                .addUnit(page.createUnit()
                        .setText("EXP")
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.LEFT)
                        .setWeight(3.0f))
                .addUnit(page.createUnit()
                        .setText("BAL")       //getCashRedeem()
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(4.0f));

        addRedeemBalanceItem(transData.getMer1ProgramId(), transData.getMer1CashDolExpdate(), transData
                .getMer1CashDolBalance(), transData.getMer1CashDolBalanceSign());

        addRedeemBalanceItem(transData.getMer2ProgramId(), transData.getMer2CashDolExpdate(), transData
                .getMer2CashDolBalance(), transData.getMer2CashDolBalanceSign());

        addRedeemBalanceItem(transData.getCashDolProgramId(), transData.getCashDolExpdate(), transData
                .getCashDolBalance(), transData.getCashDolBalanceSign());
    }

    protected void addExpiredRedeemBalance() {
        if (TextUtils.isEmpty(transData.getMer1ProgramId()) &&
                TextUtils.isEmpty(transData.getMer2ProgramId()) &&
                TextUtils.isEmpty(transData.getCashDolProgramId())) {
            return;
        }

        if (isInvalidExpDate(transData.getMer1ExpiredCashDolExpDate()) &&
                isInvalidExpDate(transData.getMer2ExpiredCashDolExpDate()) &&
                isInvalidExpDate(transData.getCashDolExpBalanceExpDate())) {
            return;
        }

        if (isInvalidLoyaltyProgramId(transData.getMer1ProgramId()) &&
                isInvalidLoyaltyProgramId(transData.getMer2ProgramId())) {

        }
        //按恒生需求, 將小票的 "EXPIRED" 移除
        /*page.addLine()
                .addUnit(page.createUnit()
                        .setText("EXPIRED")
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.LEFT)
                        .setWeight(5.0f))
                .addUnit(page.createUnit()
                        .setText("EXP")
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.LEFT)
                        .setWeight(3.0f))
                .addUnit(page.createUnit()
                        .setText("BAL")       //getCashRedeem()
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(4.0f));*/

        addRedeemBalanceItem(transData.getMer1ProgramId(), transData.getMer1ExpiredCashDolExpDate(),
                transData.getMer1ExpiredCashDolBalance(), "0");

        addRedeemBalanceItem(transData.getMer2ProgramId(), transData.getMer2ExpiredCashDolExpDate(), transData
                .getMer2ExpiredCashDolBalance(), "0");

        addRedeemBalanceItem(transData.getCashDolProgramId(), transData.getCashDolExpBalanceExpDate(),
                transData.getCashDolExpBalance(), "0");
    }

    protected void addRedeemBalanceItem(String programId, String expDate,
                                        long balance, String
                                                balanceSign) {
        if (TextUtils.isEmpty(programId)) {
            return;
        }

        if ("00000000".equals(expDate)) {
            return;
        }

        String redeemName = "";
        if (programId.equals(transData.getCashDolProgramId())) {
            redeemName = FinancialApplication.getSysParam().get(SysParam.StringParam
                    .HASE_CASH_DOL_NAME);
            ;
        } else if (programId.equals(transData.getMer1ProgramId())) {
            redeemName = FinancialApplication.getSysParam().get(SysParam.StringParam
                    .MERCHANT1_DOL_NAME);
        } else if (programId.equals(transData.getMer2ProgramId())) {
            redeemName = FinancialApplication.getSysParam().get(SysParam.StringParam
                    .MERCHANT2_DOL_NAME);
        }
        if (TextUtils.isEmpty(redeemName)) {
            redeemName = " ";
        }
        String formatedDate = "";
        if (!TextUtils.isEmpty(expDate)) {
            formatedDate = expDate.substring(4, 6) + "/" + expDate.substring(2, 4);
        }

        long redeemBalance = balance;
        if ("1".equals(balanceSign)) {
            redeemBalance = -redeemBalance;
        }
        String balanceStr = CurrencyConverter.convert(redeemBalance, transData.getCurrency())
                .replace(Utils.getCurrencySymbol(), "");

        page.addLine()
                .addUnit(page.createUnit()
                        .setText(redeemName)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(5.0f))
                .addUnit(page.createUnit()
                        .setText(formatedDate)
                        .setFontSize(FONT_NORMAL)
                        .setWeight(3.0f))
                .addUnit(page.createUnit()
                        .setText(balanceStr)
                        .setFontSize(FONT_NORMAL)
                        .setGravity(Gravity.END)
                        .setWeight(4.0f));
    }

    protected void addRedeemUnavailableMsg(TransData.LYSStatus lyssStatus) {
        String cashText1;
        String cashText2;
        switch (lyssStatus) {
            case LYS_NOK:
                cashText1 = "* + FUN$ FUNCTION IS       *";
                cashText2 = "* TEMPORARY UNAVAILABLE   *";
                break;
            case LYS_NO_HASE_CARD:
                cashText1 = "* + FUN$ NOT AVAILABLE     *";
                cashText2 = "* NOT HASE CARD           *";
                break;
            case LYS_REDEEM_NOT_ALLOW:
                cashText1 = "* + FUN$ NOT AVAILABLE     *";
                cashText2 = "* REDEMPTION NOT ALLOWED  *";
                break;
            default:
                cashText1 = "* + FUN$ FUNCTION IS       *";
                cashText2 = "* TEMPORARY UNAVAILABLE   *";
                break;
        }
        page.addLine().addUnit(page.createUnit().setText(cashText1).setFontSize(FONT_NORMAL));
        page.addLine().addUnit(page.createUnit().setText(cashText2).setFontSize(FONT_NORMAL));
    }

    public Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }

        return image;

    }

    protected String genFreePrompt(@StringRes int amountPrompt, long amount,
                                   @StringRes int resultPrompt) {
        return Utils.getString(amountPrompt)
                + CurrencyConverter.convert(amount, transData.getCurrency())
                + ", "
                + Utils.getString(resultPrompt);
    }

    private String getTerminalAndAppVersion() {
        Map<ETermInfoKey, String> map = FinancialApplication.getDal().getSys().getTermInfo();
        return map.get(ETermInfoKey.MODEL) + " " + FinancialApplication.getVersion();
    }

    protected String getRedeemName(TransData transData, SysParam sysParam) {
        String RedeemAmountName = FinancialApplication.getSysParam().get(SysParam.StringParam
                .HASE_CASH_DOL_NAME);
        if (transData.getLysStatus() == TransData.LYSStatus.LYS_OK) {
            String redemptionFlag = transData.getRedemptionFlag();
            if (redemptionFlag == null) {
                return RedeemAmountName;
            }

            if (redemptionFlag.equals("010")) {      //M1$
                RedeemAmountName = sysParam.get(SysParam.StringParam.MERCHANT1_DOL_NAME);
            } else if (redemptionFlag.equals("001")) {     //M2$
                RedeemAmountName = sysParam.get(SysParam.StringParam.MERCHANT2_DOL_NAME);
            } //HASE CASH$
        }
        return RedeemAmountName;
    }

    private String getSignaturePrompts(TransData transData) {
        String tmp = "";
        if (transData.getInstalment() != 0) {
            tmp = Utils.getString(R.string.receipt_signature_instalment_prompt);
        } else {
            tmp = Utils.getString(R.string.receipt_signature_prompt);
        }
        return tmp;
    }

    protected Bitmap loadSignature(TransData transData) {
        byte[] signData = transData.getSignData();
        if (signData == null) {
            return null;
        }
        return FinancialApplication.getGl().getImgProcessing().jbigToBitmap(signData);
    }

    public String generateString() {
        return "Card No:" + transData.getPan() + "\nTrans Type:" + transData.getTransType().toString()
                + "\nAmount:" + CurrencyConverter.convert(transData.getAmount(), transData.getCurrency())
                + "\nTip:" + CurrencyConverter.convert(transData.getTipAmount(), transData.getCurrency())
                + "\nTransData:" + transData.getDateTime();
    }

    private boolean isInvalidLoyaltyProgramId(String programId) {
        return "   ".equals(programId) && "000".equals(programId);
    }

    private boolean isInvalidExpDate(String date) {
        return "00000000".equals(date) || TextUtils.isEmpty(date);
    }

    public void addAcquirerRefNo() {
        if (transData.getAcquirer().isEnableRefNo() && !TextUtils.isEmpty(
                transData.getAcqReferenceNo())) {
            page.addLine()
                    .addUnit(page.createUnit()
                            .setText("REF NO:")
                            .setFontSize(FONT_NORMAL)
                            .setWeight(2.0f))
                    .addUnit(page.createUnit()
                            .setText(transData.getAcqReferenceNo())
                            .setGravity(Gravity.END)
                            .setWeight(7.0f));
            page.addLine().addUnit(page.createUnit().setText(" "));
        }
    }

    protected void addDisclaimer() {
        page.addLine()
                .addUnit(page.createUnit()
                        .setText(Utils.getString(R.string.receipt_lys_disclaimer))
                        .setFontSize(FONT_SMALL)
                        .setWeight(2.0f));
    }
}
