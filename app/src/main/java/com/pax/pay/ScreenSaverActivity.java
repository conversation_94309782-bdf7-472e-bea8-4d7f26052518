/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/9/2                       Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.pay;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;

import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.Utils;

import java.io.IOException;
import java.io.InputStream;

/**
 * The type Screen saver activity.
 */
public class ScreenSaverActivity extends BaseActivity {
    private ImageView screenSaver;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_screen_saver_layout;
    }

    @Override
    protected void initViews() {
        // 设置全屏显示
        enableFullScreen();

        screenSaver = (ImageView) findViewById(R.id.iv_screen_saver);

        // 设置图片缩放类型以适应全屏显示
        screenSaver.setScaleType(ImageView.ScaleType.CENTER_CROP);

        // 设置点击监听器，点击任何地方退出屏保
        screenSaver.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                exitScreenSaver();
            }
        });

        Bitmap image = null;
        InputStream is;
        // 获取当前的屏保文件名（应该是BMP文件）
        String screenSaverFileName = Component.getScreenSaverFileName();
        if (screenSaverFileName != null && Utils.isFileExist(screenSaverFileName)) {
            try {
                is = FinancialApplication.getApp().openFileInput(screenSaverFileName);
                image = BitmapFactory.decodeStream(is);
                is.close();
            } catch (IOException e) {
                Log.e(TAG, "", e);
            }
            screenSaver.setImageBitmap(image);
        }
    }

    @Override
    protected void setListeners() {
        enableActionBar(false);
    }

    @Override
    protected void loadParam() {
    }

    /**
     * 启用全屏显示，隐藏状态栏和导航栏
     */
    private void enableFullScreen() {
        // 隐藏状态栏和导航栏
        FinancialApplication.getDal().getSys().showStatusBar(false);
        FinancialApplication.getDal().getSys().showNavigationBar(false);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        exitScreenSaver();
        return true;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        exitScreenSaver();
        return true;
    }

    /**
     * 退出屏保，延迟恢复状态栏和导航栏显示以避免退出过程中的空白区域
     */
    private void exitScreenSaver() {
        finish();
    }
}
