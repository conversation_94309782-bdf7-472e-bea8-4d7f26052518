/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.app;

import android.app.Application;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.PowerManager;
import androidx.annotation.NonNull;
import android.util.Log;
import com.pax.amex.PackAmexManager;
import com.pax.appstore.DownloadManager;
import com.pax.appstore.IDownloadListener;
import com.pax.dal.IDAL;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.device.GeneralParam;
import com.pax.ecrsdk.server.ServerManager;
import com.pax.edc.BuildConfig;
import com.pax.edc.opensdk.ITransAPI;
import com.pax.edc.opensdk.TransAPIFactory;
import com.pax.eventbus.Event;
import com.pax.glwrapper.IGL;
import com.pax.glwrapper.convert.IConvert;
import com.pax.glwrapper.impl.GL;
import com.pax.glwrapper.packer.IPacker;
import com.pax.pay.ScreenSaverVideoActivity;
import com.pax.pay.base.document.AcqDocument;
import com.pax.pay.base.document.AidDocument;
import com.pax.pay.base.document.CapkDocument;
import com.pax.pay.base.document.CardRangeDocument;
import com.pax.pay.base.document.EcrDocument;
import com.pax.pay.base.document.EdcDocument;
import com.pax.pay.base.document.IntendedOfflineDocument;
import com.pax.pay.base.document.IssuerDocument;
import com.pax.pay.base.document.LoyaltyDocument;
import com.pax.pay.base.document.YUUCardRangeDocument;
import com.pax.pay.constant.Constants;
import com.pax.pay.db.CardBinDb;
import com.pax.pay.db.EmvDb;
import com.pax.pay.db.TornLogDb;
import com.pax.pay.db.TransDataDb;
import com.pax.pay.db.TransTotalDb;
import com.pax.pay.ecr.impl.DeviceListenerImp;
import com.pax.pay.ecr.impl.ServerListenerImp;
import com.pax.pay.ecr.server.CupQRForwardingServer;
import com.pax.pay.ecr.server.EPSForwardingServer;
import com.pax.pay.ecr.server.ICupQRForwarding;
import com.pax.pay.ecr.server.IEPSForwarding;
import com.pax.pay.ecr.server.IMicroPayForwarding;
import com.pax.pay.ecr.server.IPartyKingForwarding;
import com.pax.pay.ecr.server.MicroPayForwardingServer;
import com.pax.pay.ecr.server.PartyKingForwardingServer;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.model.AcqManager;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ResponseCode;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sdk.Sdk;
import com.pax.settings.SysParam;
import com.pax.settings.base.BaseManager;
import com.pax.view.dialog.DialogUtils;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import org.greenrobot.eventbus.EventBus;

/**
 * customized application
 */
public class FinancialApplication extends Application {
    public static final String TAG = "FinancialApplication";
    private static FinancialApplication mApp;
    private static SysParam sysParam;
    private static Controller controller;
    private static ResponseCode rspCode;
    private static GeneralParam generalParam;
    private static AcqManager acqManager;
    private static CardBinDb cardBinDb;
    private static EmvDb emvDbHelper;
    private static TransDataDb transDataDbHelper;
    private static TransTotalDb transTotalDbHelper;
    private static TornLogDb tornLogDbHelper;

    // Neptune interface
    private static IDAL dal;
    private static IGL gl;
    private static IConvert convert;
    private static IPacker packer;

    //opensdk
    private static ITransAPI iTransAPI;
    //转发ECR请求到CUPQR应用
    private static ICupQRForwarding cupQrServer;
    private static IEPSForwarding epsForwardServer;

    //partyking相关的Server，用于处理partyking的请求
    private static IPartyKingForwarding partyKingServer;
    private static com.pax.edc.upiopensdk.ITransAPI iUpiTransAPI;
    private static com.pax.edc.eps.opensdk.ITransAPI iEpsTransAPI;

    //转发ECR请求到MicroPayA应用
    private static IMicroPayForwarding microQrServer;
    private static com.pax.micropayopensdk.factory.ITransAPI iMicroTransAPI;

    //App Store
    private static DownloadManager downloadManager;

    // app version
    private static String version;

    //ECR Server
    private static ServerManager serverManager;

    private Handler handler;
    private ExecutorService backgroundExecutor;

    private static BaseManager baseManager;

    private static ETransType currentETransType;

    private static final SysParam.UpdateListener updateListener = new SysParam.UpdateListener() {

        @Override
        public void onErr(String prompt) {
            DialogUtils.showUpdateDialog(getApp(), prompt);
        }
    };

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        FinancialApplication.mApp = this;
        version = updateVersion();
        CrashHandler.getInstance();
        init();
        initData();

        handler = new Handler();
        backgroundExecutor = Executors.newFixedThreadPool(10, new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable runnable) {
                Thread thread = new Thread(runnable, "Background executor service");
                thread.setPriority(Thread.MIN_PRIORITY);
                thread.setDaemon(true);
                return thread;
            }
        });
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        changeLanguage(newConfig);
    }

    /**
     * 初始化
     */
    public void init() {
        // get Neptune instance
        PackAmexManager.getInstance().init(getApp());//Init Amex Packer
        dal = Sdk.getInstance().getDal(getApp());
        gl = GL.getInstance(getApp());
        convert = getGl().getConvert();
        packer = getGl().getPacker();

        cardBinDb = CardBinDb.getInstance();
        emvDbHelper = EmvDb.getInstance();
        transDataDbHelper = TransDataDb.getInstance();
        transTotalDbHelper = TransTotalDb.getInstance();
        tornLogDbHelper = TornLogDb.getInstance();

        // init
        sysParam = SysParam.getInstance();
        SysParam.setUpdateListener(updateListener);
        controller = new Controller();
        generalParam = new GeneralParam();
        acqManager = AcqManager.getInstance();
        baseManager = new BaseManager(getApp());
        PowerManager powerManagerHelper = (PowerManager) this.getSystemService(Context.POWER_SERVICE);
        serverManager = new ServerManager(new ServerListenerImp(), new DeviceListenerImp(getApplicationContext(), powerManagerHelper));

        ToastUtils.init(this);
        downloadManager = DownloadManager.getInstance();
        downloadManager.setAppKey(BuildConfig.APP_KEY);
        downloadManager.setAppSecret(BuildConfig.APP_SECRET);
        downloadManager.setSn(dal.getSys().getTermInfo().get(ETermInfoKey.SN));
        //downloadManager.setFilePath(Environment.getExternalStorageDirectory().getPath() + File.separator + "ParamEDC/");
        downloadManager.setFilePath(getFilesDir().getAbsolutePath() + "/");
        downloadManager.setListener(() -> {
            //去掉无意义的代码
            //if (!BaseTrans.isTransRunning() && getTransDataDbHelper().countOf() == 0) {
            //    Utils.restart();
            //}
        });
        downloadManager
                .addDocument(new AcqDocument(Constants.ACQ_PATH))
                .addDocument(new IssuerDocument(Constants.ISSUER_PATH))
                .addDocument(new CardRangeDocument(Constants.CARD_RANGE_PATH))
                .addDocument(new LoyaltyDocument(Constants.LOYALTY_PATH))
                .addDocument(new CapkDocument(Constants.CAPK_PATH))
                .addDocument(new AidDocument(Constants.AID_PATH))
                .addDocument(new EdcDocument(Constants.EDC_PATH))
                .addDocument(new YUUCardRangeDocument(Constants.YUU_CARD_RANGE_PATH))
                .addDocument(new EcrDocument(Constants.ECR_PATH))
                .addDocument(new IntendedOfflineDocument(Constants.INTENDED_OFFLINE_PATH));
        downloadManager.init(FinancialApplication.getApp(), new IDownloadListener() {
            @Override
            public boolean isReadyUpdate() {
                return !BaseTrans.isTransRunning() && getTransDataDbHelper().countOf() == 0;
            }
        });
    }

    /**
     * 初始化数据
     */
    public static void initData() {
        new Thread(new Runnable() {

            @Override
            public void run() {
                // init response code
                rspCode = ResponseCode.getInstance();
                getRspCode().init();

                //调用该静态方法的地方很多, 在初始化时加入内存, 防止第一次调用时发生在UI操作中而产生顿卡, 影响用户体验
                CurrencyConverter.convert(0L);
            }
        }).start();
    }

    /**
     * get app version
     */
    private String updateVersion() {
        try {
            PackageManager manager = getPackageManager();
            PackageInfo info = manager.getPackageInfo(getPackageName(), 0);
            return info.versionName;
        } catch (Exception e) {
            Log.w(TAG, e);
            return null;
        }
    }

    public static ITransAPI getiTransAPI() {
        if (iTransAPI == null) {
            iTransAPI = TransAPIFactory.createTransAPI();
        }
        return iTransAPI;
    }

    public static com.pax.edc.upiopensdk.ITransAPI getiUpiTransAPI() {
        if (iUpiTransAPI == null) {
            iUpiTransAPI = com.pax.edc.upiopensdk.TransAPIFactory.createTransAPI();
        }
        return iUpiTransAPI;
    }

    public static com.pax.edc.eps.opensdk.ITransAPI getiEpsTransAPI() {
        if (iEpsTransAPI == null) {
            iEpsTransAPI = com.pax.edc.eps.opensdk.TransAPIFactory.createTransAPI();
        }
        return iEpsTransAPI;
    }

    public static com.pax.micropayopensdk.factory.ITransAPI getiMicroTransAPI() {
        if (iMicroTransAPI == null) {
            iMicroTransAPI = com.pax.micropayopensdk.factory.TransAPIFactory.createTransAPI();
        }
        return iMicroTransAPI;
    }

    public static ICupQRForwarding getCupQRForwardServer() {
        if (cupQrServer == null) {
            cupQrServer = new CupQRForwardingServer();
        }
        return cupQrServer;
    }

    public static IEPSForwarding getEpsForwardServer() {
        if (epsForwardServer == null) {
            epsForwardServer = new EPSForwardingServer();
        }
        return epsForwardServer;
    }

    public static IMicroPayForwarding getMicroQRForwardServer() {
        if (microQrServer == null) {
            microQrServer = new MicroPayForwardingServer();
        }
        return microQrServer;
    }

    //提供获取PartykingForwardServer的方法，来进行partyking交易
    public static IPartyKingForwarding getPartyKingForwardServer() {
        //如果为空则新创建
        if (partyKingServer == null) {
            partyKingServer = new PartyKingForwardingServer();
        }
        //返回partykingServer
        return partyKingServer;
    }


    // getter
    public static FinancialApplication getApp() {
        return mApp;
    }

    public static SysParam getSysParam() {
        return sysParam;
    }

    public static Controller getController() {
        return controller;
    }

    public static ResponseCode getRspCode() {
        return rspCode;
    }

    public static GeneralParam getGeneralParam() {
        return generalParam;
    }

    public static AcqManager getAcqManager() {
        return acqManager;
    }

    public static CardBinDb getCardBinDb() {
        return cardBinDb;
    }

    public static EmvDb getEmvDbHelper() {
        return emvDbHelper;
    }

    public static TransDataDb getTransDataDbHelper() {
        return transDataDbHelper;
    }

    public static TransTotalDb getTransTotalDbHelper() {
        return transTotalDbHelper;
    }

    public static TornLogDb getTornLogDbHelper() {
        return tornLogDbHelper;
    }

    public static IDAL getDal() {
        return dal;
    }

    public static IGL getGl() {
        return gl;
    }

    public static IConvert getConvert() {
        return convert;
    }

    public static IPacker getPacker() {
        return packer;
    }

    public static DownloadManager getDownloadManager() {
        return downloadManager;
    }

    public static String getVersion() {
        return version;
    }

    // merge handles from all activities
    public void runInBackground(final Runnable runnable) {
        backgroundExecutor.submit(runnable);
    }

    public void runOnUiThread(final Runnable runnable) {
        handler.post(runnable);
    }

    public void runOnUiThreadDelay(final Runnable runnable, long delayMillis) {
        handler.postDelayed(runnable, delayMillis);
    }

    // eventbus helper
    public void register(Object obj) {
        EventBus.getDefault().register(obj);
    }

    public void unregister(Object obj) {
        EventBus.getDefault().unregister(obj);
    }

    public void doEvent(Event event) {
        EventBus.getDefault().post(event);
    }

    public static BaseManager getBaseManager() {
        return baseManager;
    }

    public static ServerManager getServerManager() {
        return serverManager;
    }

    public static ETransType getCurrentETransType() {
        return currentETransType;
    }

    public static void setCurrentETransType(ETransType currentETransType) {
        FinancialApplication.currentETransType = currentETransType;
    }

    /**
     * 修改本地语言
     *
     * @param newConfig newConfig
     */
    private void changeLanguage(Configuration newConfig) {
        // 如果新配置是横屏或者当前Activity是屏保界面，则代表是屏保相关的不用进行重启主界面的操作，防止在其他界面进入屏保而结束回到主界面
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_SCREEN_SAVER) &&
                (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE ||
                        ActivityStack.getInstance().top() instanceof ScreenSaverVideoActivity)) {
            Utils.changeAppLanguageInApp(FinancialApplication.getApp());
        } else if (FinancialApplication.getSysParam() != null) {
            Utils.changeAppLanguageInApp(FinancialApplication.getApp());
            Utils.restart();
        }
    }
}
