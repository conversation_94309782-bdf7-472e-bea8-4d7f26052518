/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220813  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.strategy;

import com.pax.abl.core.ATransaction;
import com.pax.abl.core.ActionResult;
import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.message.request.Request;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.ecr.Constants;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;
import java.util.ArrayList;
import java.util.List;

import static com.pax.pay.utils.Utils.getString;

public class TransRetrieval implements ITrans {
    @Override
    public void transExecute(Request reqMsg, ATransaction.TransEndListener endListener) {

        FinancialApplication.getApp().runInBackground(() -> {
            TransData transData = null;
            //retrieve last YUU registration
            if (reqMsg.getMsgType() == EcrConstants.LOYALTY_RETRIEVAL_MSG_TYPE) {
                //YUU registration只保存了最近一笔交易
                List<ETransType> list = new ArrayList<>();
                list.add(ETransType.YUU_REGISTRATION);
                List<TransData.ETransStatus> filter = new ArrayList<>();
                List<TransData> transDataList =
                        FinancialApplication.getTransDataDbHelper().findTransData(list, filter);
                if (transDataList.size() > 0) {
                    transData = transDataList.get(0);
                }
            } else {
                //Credit && CUP Retrieval
                String invoiceNo = reqMsg.getTraceNumber();
                if (Constants.RETRIEVE_LAST_TRANS_INVOICE_NO.equals(invoiceNo)) {
                    transData = FinancialApplication.getTransDataDbHelper().findLastTransData(true);
                } else {
                    transData =
                            FinancialApplication.getTransDataDbHelper().findTransDataByInvoiceNo(
                                    Utils.parseLongSafe(invoiceNo, 0));
                }
            }

            int isTransExist =
                    transData != null ? TransResult.ECR_RETRIEVAL_SUCC
                            : TransResult.ERR_NO_ORIG_TRANS;
            // EDC retrieve 只能 retrieve EDC 交易, CUP retrieve 只能retrieve CUP 交易
            if (transData == null || transData.getAcquirer().getName() == null) {
                isTransExist = TransResult.ERR_NO_ORIG_TRANS;
            } else if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_CUP_ECR) &&
                    transData.getAcquirer().getName().equals("HASE_CUP") &&
                    reqMsg.getMsgType() == EcrConstants.CREDIT_RETRIEVAL_MSG_TYPE) {
                // 仅当CUP_ECR开关关闭时才走原逻辑：不允许普通的cup卡查询
                isTransExist = TransResult.ERR_NO_ORIG_TRANS;
                transData = null;
            } else if (!transData.getAcquirer().getName().equals("HASE_CUP") &&
                    reqMsg.getMsgType() == EcrConstants.CUP_RETRIEVAL_MSG_TYPE) {
                isTransExist = TransResult.ERR_NO_ORIG_TRANS;
                transData = null;
            }
            //display query result
            displayRetrievalResult(isTransExist);

            ActionResult result = new ActionResult(isTransExist, transData);
            endListener.onEnd(result);
        });
    }

    private void displayRetrievalResult(int retrievalResult) {
        if (retrievalResult == TransResult.ECR_RETRIEVAL_SUCC) {
            DialogUtils.showSuccMessage(ActivityStack.getInstance().top(),
                    getString(R.string.ecr_transaction_retrieval), null,
                    com.pax.pay.constant.Constants.SUCCESS_DIALOG_SHOW_TIME);
        } else {
            DialogUtils.showErrMessage(ActivityStack.getInstance().top(),
                    getString(R.string.ecr_transaction_retrieval),
                    TransResultUtils.getMessage(retrievalResult), null,
                    com.pax.pay.constant.Constants.FAILED_DIALOG_SHOW_TIME);
        }
    }
}
