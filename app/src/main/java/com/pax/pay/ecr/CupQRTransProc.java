/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220827  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr;

import static com.pax.pay.utils.Utils.getString;

import android.content.Intent;
import com.pax.abl.core.ATransaction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.LogUtils;
import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.message.request.Request;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.edc.upiopensdk.BaseResponse;
import com.pax.edc.upiopensdk.TransResponse;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.ecr.responses.ParseRespMsg;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.TransResultUtils;
import com.pax.view.dialog.DialogUtils;

public class CupQRTransProc {
    private Request ecrRequest;

    private CupQRTransProc() {
    }

    private static class SingletonHolder {
        private static final CupQRTransProc instance = new CupQRTransProc();
    }

    public static CupQRTransProc getInstance() {
        return SingletonHolder.instance;
    }

    public void doCupQRTrans(Request request, ATransaction.TransEndListener endListener) {
        this.ecrRequest = request;
        FinancialApplication.getCupQRForwardServer().init(ActivityStack.getInstance().top());
        TransContext.getInstance().setTransListener(endListener);
        byte transType = request.getMsgType();
        switch (transType) {
            case EcrConstants.CUP_SALE_MSG_TYPE:
                FinancialApplication.getCupQRForwardServer()
                        .doCupQRSale(request.getAmountInCents(), "0");
                break;
            case EcrConstants.CUP_VOID_MSG_TYPE:
                FinancialApplication.getCupQRForwardServer()
                        .doCupQRVoid(request.getAmountInCents(), request.getTraceNumber());
                break;
            case EcrConstants.CUP_REFUND_MSG_TYPE:
                FinancialApplication.getCupQRForwardServer()
                        .doCupQRRefund(request.getAmountInCents(), request.getTraceNumber());
                break;
            case EcrConstants.CUP_QR_RETRIEVAL_MSG_TYPE:
                FinancialApplication.getCupQRForwardServer()
                        .doCupQRRetrieval(request.getTraceNumber());
                break;
            default:
                break;
        }
    }

    /**
     * 处理CUPQR应用交易返回结果
     */
    public void handleCupQRResult(int requestCode, int resultCode, Intent data) {
        BaseResponse baseResponse =
                FinancialApplication.getCupQRForwardServer()
                        .onResult(requestCode, resultCode, data);

        if (baseResponse == null) {
            ActionResult actionResult = new ActionResult(TransResult.ERR_CUP_QR_FAILED, null);
            TransContext.getInstance().getTransListener().onEnd(actionResult);
            return;
        }

        TransResponse qrResData = (TransResponse) baseResponse;
        LogUtils.d("response", qrResData.toString());

        //生成ECR response并回复ecr
        TransData transData = ParseRespMsg.generateUPIRspData(qrResData, ecrRequest);
        if (ecrRequest.getMsgType() == EcrConstants.CUP_QR_RETRIEVAL_MSG_TYPE && resultCode == -1) {
            if (transData.getAmount() != 0) {
                DialogUtils.showSuccMessage(ActivityStack.getInstance().top(),
                        getString(R.string.ecr_transaction_retrieval), null,
                        com.pax.pay.constant.Constants.SUCCESS_DIALOG_SHOW_TIME);
            } else {
                DialogUtils.showErrMessage(ActivityStack.getInstance().top(),
                        getString(R.string.ecr_transaction_retrieval),
                        TransResultUtils.getMessage(TransResult.ERR_NO_ORIG_TRANS), null,
                        com.pax.pay.constant.Constants.FAILED_DIALOG_SHOW_TIME);
                transData = null;
            }
        }
        ActionResult result = new ActionResult(qrResData.getRspCode(), transData);
        TransContext.getInstance().getTransListener().onEnd(result);
    }
}
