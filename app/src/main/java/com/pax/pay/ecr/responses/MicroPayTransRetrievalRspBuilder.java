package com.pax.pay.ecr.responses;

import com.pax.abl.core.ActionResult;
import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.message.request.Request;
import com.pax.ecrsdk.message.response.Response;

/*
micro pay transaction retrieval 构建返回给ECR的数据
 */
public class MicroPayTransRetrievalRspBuilder extends BaseResponseBuilder {
    @Override
    public Response build(Request reqMsg, ActionResult result) {
        //处理Micro Pay Retrieval对应的response
        if (reqMsg.getMsgType() == EcrConstants.MICRO_PAY_TRANSACTION_RETRIEVAL_MSG_TYPE) {
            com.pax.micropayopensdk.message.TransResponse transResponse = (com.pax.micropayopensdk.message.TransResponse) result.getData();
            if (transResponse == null) {
                //当Retrieval Terminal busy，此时不会去第三方查询最新的交易数据，直接按照sale报文返回数据给ECR
                return new MicroPaySaleRspBuilder().build(reqMsg, result);
            } else if (transResponse.getMessageType().equals("T")) {
                //Retrieval查询的最新交易为refund交易
                return new MicroPayRefundRspBuilder().build(reqMsg, result);
            } else {
                //Retrieval查询的最新交易为sale交易
                return new MicroPaySaleRspBuilder().build(reqMsg, result);
            }
        }
        return null;
    }

    @Override
    public void buildRspData(Response rspMsg, Request reqMsg, ActionResult result) {
        //do nothing
    }
}
