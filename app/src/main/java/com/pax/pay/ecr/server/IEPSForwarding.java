package com.pax.pay.ecr.server;

import com.pax.ecrsdk.message.request.Request;

public interface IEPSForwarding extends ITransForwarding<com.pax.edc.eps.opensdk.BaseResponse> {
    /**
     * do EPS sale trans
     *
     * @param ecrRequest ecr请求
     */
    boolean doEpsSale(Request ecrRequest);

    /**
     * do EPS cup sale trans
     *
     * @param ecrRequest ecr请求
     */
    boolean doEpsCupSale(Request ecrRequest);

    /**
     * do EPS cup void trans
     *
     * @param ecrRequest ecr请求
     */
    boolean doEpsCupVoid(Request ecrRequest);
}
