package com.pax.pay.ecr.server;

import android.content.Context;
import android.content.Intent;
import com.pax.ecrsdk.message.request.Request;
import com.pax.edc.eps.opensdk.BaseRequest;
import com.pax.edc.eps.opensdk.BaseResponse;
import com.pax.edc.eps.opensdk.Constants;
import com.pax.edc.eps.opensdk.ITransAPI;
import com.pax.edc.eps.opensdk.SaleMsg;
import com.pax.edc.eps.opensdk.TransAPIFactory;
import com.pax.edc.eps.opensdk.VoidMsg;

public class EPSForwardingServer implements IEPSForwarding {
    private ITransAPI iTransAPI;
    private Context context;

    @Override
    public void init(Context context) {
        this.context = context;
        iTransAPI = TransAPIFactory.createTransAPI();
    }

    @Override
    public boolean doEpsSale(Request ecrRequest) {
        SaleMsg.Request saleRequest = new SaleMsg.Request();
        saleRequest.setEpsRequestInfo(new String(ecrRequest.getReqRawMsg()));
        //saleRequest.setPackageName("EPS");
        saleRequest.setCategory(Constants.CATEGORY_SALE);
        return doTrans(saleRequest);
    }

    @Override
    public boolean doEpsCupSale(Request ecrRequest) {
        SaleMsg.Request saleRequest = new SaleMsg.Request();
        //saleRequest.setAmount(Long.parseLong(transAmount));
        //saleRequest.setTipAmount(Long.parseLong(tipAmount));
        saleRequest.setEpsRequestInfo(new String(ecrRequest.getReqRawMsg()));
        //saleRequest.setPackageName("EPS_CUP");
        saleRequest.setCategory(Constants.CATEGORY_CUPSALE);
        return doTrans(saleRequest);
    }

    @Override
    public boolean doEpsCupVoid(Request ecrRequest) {
        VoidMsg.Request voidRequest = new VoidMsg.Request();
        voidRequest.setEpsRequestInfo(new String(ecrRequest.getReqRawMsg()));
        //voidRequest.setPackageName("EPS_CUP");
        voidRequest.setCategory(Constants.CATEGORY_CUPVOID);
        //voidRequest.setVoucherNo(Integer.parseInt(voucherNo));
        return doTrans(voidRequest);
    }

    @Override
    public BaseResponse onResult(int requestCode, int resultCode, Intent data) {
        return iTransAPI.onResult(requestCode, resultCode, data);
    }

    private boolean doTrans(BaseRequest baseRequest) {
        return iTransAPI != null && iTransAPI.doTrans(context, baseRequest);
    }
}
