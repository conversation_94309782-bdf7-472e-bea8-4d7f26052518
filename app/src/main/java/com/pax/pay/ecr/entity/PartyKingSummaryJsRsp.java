package com.pax.pay.ecr.entity;

import com.google.gson.annotations.SerializedName;

/**
 * Author Fanjiaming
 * Date 2024/1/31
 */
//PartyKing的Summary的json对应的实体类
public class PartyKingSummaryJsRsp extends BaseResponse {
    //partyKingExtras，用于接收Summary的所有字段，全部封装在Extras中
    @SerializedName("extras")
    private PartyKingExtras partyKingExtras;

    //获取Extras方法
    public PartyKingExtras getPartyKingExtras() {
        return partyKingExtras;
    }

    //设置Extras方法
    public void setPartyKingExtras(PartyKingExtras partyKingExtras) {
        this.partyKingExtras = partyKingExtras;
    }

    @Override
    public String toString() {
        return "PartyKingSummaryJsRsp{" +
                ", partyKingExtras=" + partyKingExtras +
                '}';
    }

}
