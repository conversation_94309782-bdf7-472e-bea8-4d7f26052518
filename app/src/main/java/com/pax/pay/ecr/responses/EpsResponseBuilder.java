/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220815  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.responses;

import com.pax.abl.core.ActionResult;
import com.pax.ecrsdk.message.request.Request;
import com.pax.ecrsdk.message.response.EpsResponse;
import com.pax.ecrsdk.message.response.Response;
import com.pax.pay.trans.component.Component;

import java.util.Objects;

class EpsResponseBuilder extends BaseResponseBuilder {
    @Override
    public Response build(Request reqMsg, ActionResult result) {
        EpsResponse epsResponse = new EpsResponse();
        buildRspData(epsResponse, reqMsg, result);
        return epsResponse;
    }

    @Override
    public void buildRspData(Response rspMsg, Request reqMsg, ActionResult result) {
        EpsResponse response = (EpsResponse) rspMsg;
        super.buildRspData(response, reqMsg, result);
        response.setAmountInCents(
                Component.getPaddedString(
                        String.valueOf(Objects.isNull(transData) ? 0 : transData.getAmount()), 12,
                        '0'));
        response.setOtherAmountInCents(Component.getPaddedString("0", 12, '0'));

        response.setPrimaryAccountNumber(Component.getPaddedString(" ", 19, ' '));

        response.setStoreId(Component.getPaddedString(" ", 3, ' '));

        response.setIsn(Component.getPaddedString(" ", 6, ' '));
        response.setValueDate(Component.getPaddedString(" ", 4, ' '));
        response.setDebitAccountNumber(Component.getPaddedString(" ", 28, ' '));
        response.setBankAdditionalResponse(Component.getPaddedString(" ", 20, ' '));
        response.setBrandMnemonic(Component.getPaddedString(" ", 3, ' '));
        response.setChargingCurrency(Component.getPaddedString(" ", 6, ' '));
        response.setPurchaseAmtInCents(Component.getPaddedString("0", 12, '0'));
        response.setCashBackAmtInCents(Component.getPaddedString("0", 12, '0'));
        response.setAccountIndicator(Component.getPaddedString(" ", 3, ' '));
        response.setEpsReferenceNum(Component.getPaddedString(" ", 6, ' '));
        response.setFiller(Component.getPaddedString(" ", 19, ' '));
    }
}
