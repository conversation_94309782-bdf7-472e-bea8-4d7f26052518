/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220714  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.impl;

import android.app.ActivityManager;
import android.content.Context;
import android.text.TextUtils;

import com.pax.abl.core.ATransaction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.LogUtils;
import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.message.request.Request;
import com.pax.ecrsdk.server.IServerListener;
import com.pax.ecrsdk.server.TransResponseListener;
import com.pax.ecrsdk.utils.EcrUtils;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.ScreenSaverVideoActivity;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.ecr.Constants;
import com.pax.pay.ecr.responses.ParseRespMsg;
import com.pax.pay.ecr.strategy.ITrans;
import com.pax.pay.ecr.strategy.TransCupQR;
import com.pax.pay.ecr.strategy.TransFactory;
import com.pax.pay.ecr.strategy.TransMicroPay;
import com.pax.pay.menu.TransMenuActivity;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.utils.ToastUtils;
import com.pax.settings.SysParam;

import java.lang.ref.WeakReference;
import java.util.List;

public class ServerListenerImp implements IServerListener {
    private Request reqMsg;
    TransResponseListener transResponseListener = null;
    private byte[] respMsg;
    private boolean isTransRunning = false;//判断micro pay是否正在执行ecr指令
    private boolean microPayIsRunning = false;//判断 micro pay是否是单机运行

    @Override
    public void doServer(Request request, TransResponseListener transResponseListener) {
        if (request == null) {
            return;
        }

        this.transResponseListener = transResponseListener;
        reqMsg = request;

        respMsg = null;

        isTransRunning = FinancialApplication.getSysParam().get(SysParam.BooleanParam.MICRO_PAY_IS_DO_ECR_TRANS);
        microPayIsRunning = FinancialApplication.getSysParam().get(SysParam.BooleanParam.MICRO_PAY_IS_STAND_ALONE);
        WeakReference<Context> contextWeakReference = new WeakReference<>(ActivityStack.getInstance().top());
        boolean isForeground = isAppInForeground(contextWeakReference.get());
        if (microPayIsRunning || isTransRunning || BaseTrans.isTransRunning() || !isForeground ||
                (!(ActivityStack.getInstance().top() instanceof TransMenuActivity) &&
                        !(ActivityStack.getInstance().top() instanceof ScreenSaverVideoActivity))) {
            LogUtils.e("ServerListenerImp", "=====trans is runing,recv another new trans====");
            FinancialApplication.getApp().runOnUiThread(() ->
                    ToastUtils.showMessage("trans is running, start ecr error"));

            //当前有交易正在执行 或者 应用当前在其他设置界面时，收到ECR请求后,返回ECR_TERMINAL_BUSY
            EcrUtils.runInBackground(() -> {
                transFinish(new ActionResult(TransResult.ECR_TERMINAL_BUSY, null));
            });
            return;
        }

        FinancialApplication.getApp().runOnUiThread(this::doTrans);

    }

    private void doTrans() {
        byte type = reqMsg.getMsgType();
        ITrans trans = TransFactory.getTrans(type);
        if (trans == null) {
            transFinish(new ActionResult(TransResult.APPLICATION_REJECTION, null));
            return;
        }

        String cardType = reqMsg.getCardType();
        if (type == EcrConstants.CUP_QR_RETRIEVAL_MSG_TYPE || (!TextUtils.isEmpty(cardType) && cardType.equals(Constants.CUP_QR_CARD_TYPE))) {
            //cardType=CQ, forward request to CUPQR app
            trans = new TransCupQR();
        }

        if (trans instanceof TransMicroPay) {
            FinancialApplication.getSysParam().set(SysParam.BooleanParam.MICRO_PAY_IS_DO_ECR_TRANS, true);
        }

        trans.transExecute(reqMsg, endListener);
    }

    private ATransaction.TransEndListener endListener = new ATransaction.TransEndListener() {

        @Override
        public void onEnd(ActionResult result) {
            transFinish(result);
        }
    };

    private void transFinish(ActionResult result) {
        LogUtils.e("ServerListenerImp", "=====transFinish====");
        respMsg = ParseRespMsg.generateEcrResponse(reqMsg, result);
        transResponseListener.onTransResultUpdate(respMsg);
    }

    public boolean isAppInForeground(Context context) {
        //获取AM来进行判断进程是否处于前台
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();
        if (activityManager == null) {
            return false;
        }
        // 获取正在运行的任务列表
        List<ActivityManager.RunningTaskInfo> tasks = activityManager.getRunningTasks(1);
        if (tasks.isEmpty()) {
            return false;
        }
        // 获取顶部任务的包名,通过比对栈顶任务和当前进程的包名来判断是否处于前台
        String topPackageName = tasks.get(0).topActivity.getPackageName();
        return packageName.equals(topPackageName);
    }
}
