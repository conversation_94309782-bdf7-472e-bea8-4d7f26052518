/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220813  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.strategy;

import com.pax.abl.core.ATransaction;
import com.pax.ecrsdk.message.request.Request;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.trans.RefundTrans;

public class TransRefund implements ITrans {
    @Override
    public void transExecute(Request reqMsg, ATransaction.TransEndListener endListener) {
        new RefundTrans(ActivityStack.getInstance().top(), reqMsg,
                endListener).execute();
    }
}
