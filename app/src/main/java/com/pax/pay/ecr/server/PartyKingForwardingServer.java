package com.pax.pay.ecr.server;

import static com.pax.pay.menu.TransMenuActivity.REQUEST_CODE_PARTY_KING;

import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.IBinder;
import android.util.Log;

import com.google.gson.Gson;
import com.pax.partyking.dr3.I3rdResultCallback;
import com.pax.pay.ecr.PartyKingTransProc;
import com.pax.pay.ecr.entity.BaseResponse;
import com.pax.pay.ecr.entity.PartyKingCommJsRsp;
import com.pax.pay.ecr.entity.PartyKingSummaryJsRsp;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Author Fanjiaming
 * Date 2024/1/29
 */
public class PartyKingForwardingServer implements IPartyKingForwarding {
    private static final String TAG = "PartyKingForwardingServer";
    private Context context;
    //partyking应用的包名
    private static final String PARTYKING_PACKAGE_NAME = "com.pax.hase.partyking";
    // partyking各个交易的基本路径
    private static final String BASE_DATA = "android-app://com.pax.partyking/3rd/";
    // gift的请求路径
    private static final String GIFT_DATA = BASE_DATA + "gift_redeem";
    // cash的请求路径
    private static final String CASH_DATA = BASE_DATA + "cash_redeem";
    // void的请求路径
    private static final String VOID_DATA = BASE_DATA + "void";
    // query的请求路径
    private static final String QUERY_DATA = BASE_DATA + "query";
    // summary的请求路径
    private static final String SUMMARY_DATA = BASE_DATA + "summary";
    // retrieval的请求路径
    private static final String RETRIEVAL_DATA = "content://com.pax.partyking/3rd/detail";

    //初始化传入context对象
    @Override
    public void init(Context context) {
        this.context = context;
    }
    //用于储存请求的url中需要携带的参数列表
    private final Map<String, String> requestMap = new HashMap<>();

    private String ecrReferenceNo = "";
    //partyking的gift交易
    @Override
    public boolean doPartyKingGift(String outTradeNo,String ecrReferenceNo) {
        // 执行交易前清空map集合，重新赋值
        requestMap.clear();
        //传入gift需要的outtradeno
        requestMap.put("out_trade_no", outTradeNo);
        this.ecrReferenceNo = ecrReferenceNo;
        //传入交易请求url进行交易
        doTrans(GIFT_DATA);
        return true;
    }
    //partyking的cash交易
    @Override
    public boolean doPartyKingCash(String amount, String outTradeNo,String ecrReferenceNo) {
        // 执行交易前清空map集合，重新赋值
        requestMap.clear();
        //传入cash需要的amount
        requestMap.put("totalAmount", amountFormat(Long.parseLong(amount)));
        this.ecrReferenceNo = ecrReferenceNo;
        //传入交易请求url进行交易
        doTrans(CASH_DATA);
        return true;
    }
    //partyking的void交易
    @Override
    public boolean doPartyKingVoid(String transactionId, String outTradeNo,String ecrReferenceNo) {
        // 执行交易前清空map集合，重新赋值
        requestMap.clear();
        //传入交易需要的transactionId和outtradeNo
        requestMap.put("transactionId", transactionId);
        requestMap.put("outTradeNo", outTradeNo);
        this.ecrReferenceNo = ecrReferenceNo;
        //传入交易请求url进行交易
        doTrans(VOID_DATA);
        return true;
    }
    //partyking的query交易
    @Override
    public boolean doPartyKingQuery(String outTradeNo,String ecrReferenceNo) {
        // 执行交易前清空map集合，重新赋值
        requestMap.clear();
        //传入query需要的outtradeNo
        requestMap.put("outTradeNo", outTradeNo);
        this.ecrReferenceNo = ecrReferenceNo;
        //传入交易请求url进行交易
        doTrans(QUERY_DATA);
        return true;
    }

    @Override
    public boolean doPartyKingRetrieval(String outTradeNo,String ecrReferenceNo) {
        this.ecrReferenceNo = ecrReferenceNo;
        // 利用Content Provider来请求数据
        try (Cursor cursor = context.getContentResolver().query(Uri.parse(RETRIEVAL_DATA), null, "outTradeNo=?", new String[]{outTradeNo}, null)) {
            if (cursor != null && cursor.moveToNext()) {
                // 将查询到的数据放进intent中给到PartyKingTransProc统一处理
                String message = cursor.getString(0);
                Intent intent = new Intent();
                //将数据存储到intent的extra中
                intent.putExtra("data", message);
                //通过PartyKingTransProc进行结果的统一处理
                PartyKingTransProc.getInstance().handlePartyKingResult(REQUEST_CODE_PARTY_KING, 0, intent);
            }
        } catch (SecurityException e) {
            e.printStackTrace();
            //通过PartyKingTransProc进行结果的统一处理
            PartyKingTransProc.getInstance().handlePartyKingResult(REQUEST_CODE_PARTY_KING, -2, new Intent());
            return false;
        }
        return true;
    }

    @Override
    public boolean doPartyKingSummary(String startDate, String endDate,String ecrReferenceNo) {
        // 执行交易前清空map集合，重新赋值
        requestMap.clear();
        //传入summary需要的起始时间
        requestMap.put("startDate", startDate);
        //传入summary需要的结束时间
        requestMap.put("endDate", endDate);
        this.ecrReferenceNo = ecrReferenceNo;
        //传入交易请求url进行交易
        doTrans(SUMMARY_DATA);
        return true;
    }

    public BaseResponse onResult(int requestCode, int resultCode, Intent data) {
        //从intent中获取data名字的Extra，即为json字符串集合
        String responseJsonData = data.getStringExtra("data");
        //准备将json字符串转换成相应实体类
        Gson gson = new Gson();
        boolean commSale = true;
        // 根据返回的service来判断是否是哪一种交易类型， 进而决定使用哪一个json模板接收转换
        try {
            JSONObject jsonObject = new JSONObject(responseJsonData);
            //根据service键值获取service结果，通过service来判断是否是summary交易
            String service = jsonObject.getString("service");
            if ("pay.partyking.voucher.summary".equals(service)) commSale = false;
        } catch (JSONException e) {
            Log.e(TAG, "", e);
        }
        //如果是普通交易，则通过PartyKingCommJsRsp实体类接收
        if (commSale) {
            return gson.fromJson(responseJsonData, PartyKingCommJsRsp.class);
        } else {
            //如果是Summary交易，PartyKingSummaryJsRsp实体类进行接收
            return gson.fromJson(responseJsonData, PartyKingSummaryJsRsp.class);
        }

    }

    private void doTrans(String transType) {
        //统一实例化intent对象，并传入action，category，package，以及data等来请求intent
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setPackage(PARTYKING_PACKAGE_NAME);
        Uri.Builder uriBuilder = Uri.parse(transType).buildUpon();
        // 将特定的请求参数加入请求的Url中
        for (Map.Entry<String, String> entry : requestMap.entrySet()) {
            uriBuilder.appendQueryParameter(entry.getKey(), entry.getValue());
        }
        intent.setData(uriBuilder.build());
        Bundle dataBundle = new Bundle();
        // 定义aidl，供partyking在交易结束的时候调用来进行结果的处理
        I3rdResultCallback i3rdResultCallback = new I3rdResultCallback.Stub() {
            @Override
            public void onResult(int result, String message) {
                Intent intent = new Intent();
                //将返回的json数据统一放进intent中统一处理
                intent.putExtra("data", message);
                PartyKingTransProc.getInstance().handlePartyKingResult(REQUEST_CODE_PARTY_KING, result, intent);
            }
        };
        //传入aidl给intent，供partyking使用
        dataBundle.putBinder("3rdResultCallback", (IBinder) i3rdResultCallback);
        dataBundle.putString("ECR Reference", ecrReferenceNo);
        intent.putExtras(dataBundle);
        //通过context上下文启动activity
        context.startActivity(intent);
    }

    // 将金额从数字格式化成特定的两位小数的港币格式
    private String amountFormat(long amount) {
        NumberFormat format = NumberFormat.getCurrencyInstance(new Locale("en", "HK"));

        // 设置小数点位数
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);

        // 格式化金额并添加港币符号
        return format.format(amount / 100.0);
    }
}
