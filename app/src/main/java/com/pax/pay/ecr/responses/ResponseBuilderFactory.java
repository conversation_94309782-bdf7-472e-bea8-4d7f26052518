/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220809  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.responses;

import com.pax.commonlib.LogUtils;
import com.pax.ecrsdk.EcrConstants;
import com.pax.pay.ecr.strategy.TransRetrieval;

import java.util.HashMap;
import java.util.Map;

public class ResponseBuilderFactory {
    public static final String TAG = "ResponseBuilderFactory";

    private static final Map<Byte, BaseResponseBuilder> cachedBuilder = new HashMap<>();

    static {
        cachedBuilder.put(EcrConstants.CREDIT_SALE_MSG_TYPE, new CreditResponseBuilder());
        cachedBuilder.put(EcrConstants.CREDIT_OFFLINE_MSG_TYPE, new CreditResponseBuilder());
        cachedBuilder.put(EcrConstants.CREDIT_REFUND_MSG_TYPE, new CreditResponseBuilder());
        cachedBuilder.put(EcrConstants.CREDIT_VOID_MSG_TYPE, new CreditResponseBuilder());
        cachedBuilder.put(EcrConstants.CREDIT_ADJUST_MSG_TYPE, new CreditResponseBuilder());
        cachedBuilder.put(EcrConstants.CREDIT_INSTALMENT_MSG_TYPE, new CreditResponseBuilder());
        cachedBuilder.put(EcrConstants.CREDIT_RETRIEVAL_MSG_TYPE, new CreditResponseBuilder());


        cachedBuilder.put(EcrConstants.CUP_SALE_MSG_TYPE, new CupResponseBuilder());
        cachedBuilder.put(EcrConstants.CUP_VOID_MSG_TYPE, new CupResponseBuilder());
        cachedBuilder.put(EcrConstants.CUP_ADJUST_MSG_TYPE, new CupResponseBuilder());
        cachedBuilder.put(EcrConstants.CUP_REFUND_MSG_TYPE, new CupResponseBuilder());
        cachedBuilder.put(EcrConstants.CUP_RETRIEVAL_MSG_TYPE, new CupResponseBuilder());
        cachedBuilder.put(EcrConstants.CUP_QR_RETRIEVAL_MSG_TYPE, new CupResponseBuilder());
        cachedBuilder.put(EcrConstants.CARD_PROMOTION_SALE, new CupResponseBuilder());

        //EPS应用回复的是已经组好的报文(0x02开头0x03结尾报文),不必再根据EpsResponse去组装
        //cachedBuilder.put(EcrConstants.EPS_SALE_MSG_TYPE, new EpsResponseBuilder());
        //cachedBuilder.put(EcrConstants.EPS_RETRIEVAL_TYPE, new EpsResponseBuilder());

        cachedBuilder.put(EcrConstants.LOYALTY_CREATION_MSG_TYPE, new YuuResponseBuilder());
        cachedBuilder.put(EcrConstants.LOYALTY_ENQUIRY_MSG_TYPE, new YuuResponseBuilder());
        cachedBuilder.put(EcrConstants.LOYALTY_RETRIEVAL_MSG_TYPE, new YuuResponseBuilder());

        //MicroPay 返回给ECR报文入口
        cachedBuilder.put(EcrConstants.MICRO_PAY_SALE_MSG_TYPE, new MicroPaySaleRspBuilder());
        cachedBuilder.put(EcrConstants.MICRO_PAY_SALE_QUERY_MSG_TYPE, new MicroPaySaleRspBuilder());
        cachedBuilder.put(EcrConstants.MICRO_PAY_REFUND_MSG_TYPE, new MicroPayRefundRspBuilder());
        cachedBuilder.put(EcrConstants.MICRO_PAY_REFUND_QUERY_MSG_TYPE, new MicroPayRefundRspBuilder());
        cachedBuilder.put(EcrConstants.MICRO_PAY_TRANSACTION_SUMMARY_MSG_TYPE, new MicroPayTransSummaryRspBuilder());
        cachedBuilder.put(EcrConstants.MICRO_PAY_TRANSACTION_RETRIEVAL_MSG_TYPE, new MicroPayTransRetrievalRspBuilder());

        //PartyKing 返回给ECR报文入口
        cachedBuilder.put(EcrConstants.PARTYKING_GIFT_COUPON_REDEEM, new PartyKingCommRspBuilder());
        cachedBuilder.put(EcrConstants.PARTYKING_CASH_COUPON_REDEEM, new PartyKingCommRspBuilder());
        cachedBuilder.put(EcrConstants.PARTYKING_VOID_COUPON, new PartyKingCommRspBuilder());
        cachedBuilder.put(EcrConstants.PARTYKING_COUPON_QUERY, new PartyKingCommRspBuilder());
        cachedBuilder.put(EcrConstants.PARTYKING_TRANSACTION_RETRIEVAL, new PartyKingCommRspBuilder());
        cachedBuilder.put(EcrConstants.PARTYKING_TRANSACTION_SUMMARY, new PartyKingSummaryRspBuilder());

    }

    public static BaseResponseBuilder createBuilder(byte msgType) {
        try {
            BaseResponseBuilder builder = cachedBuilder.get(msgType);
            return builder;
        } catch (ClassCastException | NullPointerException e) {
            LogUtils.e(TAG, e);
        }
        return null;
    }
}
