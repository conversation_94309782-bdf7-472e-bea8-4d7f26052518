package com.pax.pay.ecr.server;


import com.pax.pay.ecr.entity.BaseResponse;

/**
 * Author Fanjiaming
 * Date 2024/1/29
 */
//做partyking相关交易的接口
public interface IPartyKingForwarding extends ITransForwarding<BaseResponse> {
    //partyking 的gift交易
    boolean doPartyKingGift(String outTradeNumber,String EcrReferenceNo);
    //partyking 的cash交易
    boolean doPartyKingCash( String amount,String outTradeNumber,String EcrReferenceNo);
    //partyking 的void交易
    boolean doPartyKingVoid(String transactionId,String outTradeNo,String EcrReferenceNo);
    //partyking 的query交易
    boolean doPartyKingQuery(String outTradeNo,String EcrReferenceNo);
    //partyking 的retrieval交易
    boolean doPartyKingRetrieval(String outTradeNumber,String EcrReferenceNo);
    //partyking 的summary交易
    boolean doPartyKingSummary(String startDate,String endDate,String EcrReferenceNo);
}
