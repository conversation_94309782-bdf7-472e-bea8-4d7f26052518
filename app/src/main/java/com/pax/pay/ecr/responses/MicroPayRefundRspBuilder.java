package com.pax.pay.ecr.responses;

import com.pax.abl.core.ActionResult;
import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.message.request.Request;
import com.pax.ecrsdk.message.response.MicroPayRefundRsp;
import com.pax.ecrsdk.message.response.Response;
import com.pax.edc.opensdk.TransResult;
import com.pax.glwrapper.convert.IConvert;
import com.pax.micropayopensdk.message.TransResponse;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;

/*
micro pay refund、refund query、 refund retrieval构建返回给ECR的数据
 */
public class MicroPayRefundRspBuilder extends BaseResponseBuilder {
    Request requestMsg;

    @Override
    public Response build(Request reqMsg, ActionResult result) {
        requestMsg = reqMsg;
        MicroPayRefundRsp microPayRefundRsp = new MicroPayRefundRsp(reqMsg);
        buildRspData(microPayRefundRsp, reqMsg, result);
        return microPayRefundRsp;
    }

    @Override
    public void buildRspData(Response rspMsg, Request reqMsg, ActionResult result) {
        MicroPayRefundRsp response = (MicroPayRefundRsp) rspMsg;
        TransResponse transResponse = (TransResponse) result.getData();

        //设置ECR返回报文
        if (result.getRet() == TransResult.APPLICATION_REJECTION) {
            //当没有Micro pay应用或者Micro Pay Enable没有打开的时候
            response.setMessageType(requestMsg.getMsgType());
            response.setEcrReferenceNo(requestMsg.getEcrReferenceNo());
            response.setResultCode(EcrConstants.TERMINAL_CANCEL_RESULT_CODE);

        } else if (result.getRet()==TransResult.ECR_TERMINAL_BUSY) {
            //处理refund、refund query Terminal Busy的情况
            response.setMessageType(requestMsg.getMsgType());
            response.setEcrReferenceNo(requestMsg.getEcrReferenceNo());
            response.setResultCode(EcrConstants.TERMINAL_BUSY_RESULT_CODE);

        } else if (transResponse != null && requestMsg.getMsgType() == EcrConstants.MICRO_PAY_TRANSACTION_RETRIEVAL_MSG_TYPE) {
            ////处理ECR Retrieve 指令返回的最近交易是refund交易的情况
            response.setMessageType(EcrConstants.MICRO_PAY_REFUND_MSG_TYPE);
            if (transResponse.getEcrReferenceNo() == null) {
                response.setEcrReferenceNo(Component.getPaddedString(" ", 16, ' '));
            } else {
                response.setEcrReferenceNo(transResponse.getEcrReferenceNo());
            }
            response.setResultCode((byte) transResponse.getResultCode());

        } else {
            //处理ECR refund、refund query流程
            response.setMessageType(requestMsg.getMsgType());
            //对于跨店refund query，Ecr RefNo填空，对于本地存在的数据refund query，上送Ecr RefNo
            if (transResponse == null || transResponse.getEcrReferenceNo() == null) {
                response.setEcrReferenceNo(Component.getPaddedString(" ", 16, ' '));
            } else {
                response.setEcrReferenceNo(transResponse.getEcrReferenceNo());
            }
            if (transResponse == null || transResponse.getResultCode() < 0) {
                //当MicroPay异常结束的时候，或者调用旧版本micro pay软件的时候
                response.setResultCode(EcrConstants.MICRO_PAY_TRADE_FAIL);
            } else {
                response.setResultCode((byte) transResponse.getResultCode());
            }
        }


        if (transResponse == null || transResponse.getErrCode() == null) {
            response.setErrCode(Component.getPaddedString(" ", 32, ' '));
        } else {
            response.setErrCode(FinancialApplication.getConvert().stringPadding(transResponse.getErrCode(), ' ', 32,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
            //micro pay的error msg转成字节的时候总长度可能超过32,超过时返回general failure
            if (response.getErrCode().getBytes().length != 32) {
                response.setErrCode(FinancialApplication.getConvert().stringPadding(EcrConstants.MICRO_PAY_GENERAL_FAILURE, ' ', 32,
                        IConvert.EPaddingPosition.PADDING_RIGHT));
            }
        }

        if (transResponse == null || transResponse.getTerminalId() == null) {
            response.setTerminalId(Component.getPaddedString(" ", 8, ' '));
        } else {
            response.setTerminalId(transResponse.getTerminalId());
        }

        if (transResponse == null || transResponse.getMerchantId() == null) {
            response.setMerchantId(Component.getPaddedString(" ", 32, ' '));
        } else {
            response.setMerchantId(FinancialApplication.getConvert().stringPadding(transResponse.getMerchantId(), ' ', 32,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }

        if (transResponse == null || transResponse.getTradeType() == EcrConstants.MICRO_PAY_TRADE_TYPE_NULL) {
            response.setTradeType((byte) 0x00);

        } else {
            response.setTradeType((byte) transResponse.getTradeType());
        }

        if (transResponse == null || transResponse.getTransactionId() == null) {
            response.setTransactionId(Component.getPaddedString(" ", 32, ' '));
        } else {
            response.setTransactionId(FinancialApplication.getConvert().stringPadding(transResponse.getTransactionId(), ' ', 32,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }

        if (transResponse == null || transResponse.getOutTradeNumber() == null) {
            response.setOutTradeNumber(Component.getPaddedString(" ", 30, ' '));
        } else {
            response.setOutTradeNumber(FinancialApplication.getConvert().stringPadding(transResponse.getOutTradeNumber(), ' ', 30,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }
        if (transResponse == null || transResponse.getOutRefundNumber() == null) {
            response.setOutRefundNumber(Component.getPaddedString(" ", 30, ' '));
        } else {
            response.setOutRefundNumber(FinancialApplication.getConvert().stringPadding(transResponse.getOutRefundNumber(), ' ', 30,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }

        if (transResponse == null || transResponse.getRefundId() == null) {
            response.setRefundId(Component.getPaddedString(" ", 32, ' '));
        } else {
            response.setRefundId(FinancialApplication.getConvert().stringPadding(transResponse.getRefundId(), ' ', 32,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }

        if (transResponse == null || transResponse.getRefundAmount() == null) {
            response.setRefundAmount(Component.getPaddedString(" ", 12, ' '));
        } else if (reqMsg.getMsgType() == EcrConstants.MICRO_PAY_TRANSACTION_RETRIEVAL_MSG_TYPE) {
            response.setRefundAmount(FinancialApplication.getConvert().stringPadding(transResponse.getRefundAmount(), '0', 12,
                    IConvert.EPaddingPosition.PADDING_LEFT));
        } else {
            response.setRefundAmount(FinancialApplication.getConvert().stringPadding(transResponse.getRefundAmount(), '0', 12,
                    IConvert.EPaddingPosition.PADDING_LEFT));
        }

        if (transResponse == null || transResponse.getRefundTime() == null) {
            response.setRefundTime(Component.getPaddedString(" ", 14, ' '));
        } else {
            response.setRefundTime(transResponse.getRefundTime());
        }

        if (transResponse == null || transResponse.getRefundStatus() == null) {
            response.setRefundStatus(Component.getPaddedString(" ", 16, ' '));
        } else {
            response.setRefundStatus(FinancialApplication.getConvert().stringPadding(transResponse.getRefundStatus(), ' ', 16,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }

        if (transResponse == null || transResponse.getRefundChannel() == null) {
            response.setRefundChannel(Component.getPaddedString(" ", 16, ' '));
        } else {
            response.setRefundChannel(FinancialApplication.getConvert().stringPadding(transResponse.getRefundChannel(), ' ', 16,
                    IConvert.EPaddingPosition.PADDING_RIGHT));
        }

        if (transResponse == null || transResponse.getRefundCount() <= 0) {
            response.setRefundCount((byte) 0x00);
        } else {
            response.setRefundCount((byte) transResponse.getRefundCount());
        }

        //实际交易没有产生的数据置为空
        response.setCouponRefundFee(Component.getPaddedString(" ", 12, ' '));
        response.setCashFee(Component.getPaddedString(" ", 12, ' '));
        response.setReserve(Component.getPaddedString(" ", 32, ' '));


    }

}
