/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220819  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import com.pax.device.Device;
import com.pax.ecrsdk.server.IDeviceListener;

public class DeviceListenerImp implements IDeviceListener {
    public static final String TAG = "DeviceListenerImp";
    private Context mContext;
    private PowerManager mPowerManagerHelper;
    private PowerManager.WakeLock mWakeLock;

    @SuppressLint("InvalidWakeLockTag")
    public DeviceListenerImp(Context context, PowerManager powerManager) {
        mContext = context;
        mPowerManagerHelper = powerManager;
        mWakeLock = mPowerManagerHelper.newWakeLock(PowerManager.ACQUIRE_CAUSES_WAKEUP
                | PowerManager.FULL_WAKE_LOCK, TAG);
    }

    @Override
    public void beepOk() {
        Device.beepPrompt();
    }

    @Override
    public void beepErr() {
        Device.beepErr();
    }

    @Override
    public void wakeScreen() {
        if (mPowerManagerHelper != null) {
            if (mWakeLock.isHeld()) {
                mWakeLock.release();
            }
            mWakeLock.acquire(getScreenOffTime());
        }
    }

    private int getScreenOffTime() {
        int screenOffTime = 0;
        try {
            screenOffTime = Settings.System.getInt(mContext.getContentResolver(),
                    Settings.System.SCREEN_OFF_TIMEOUT);
            Log.d("DeviceListenerImp", "screenOffTime:" + screenOffTime);
        } catch (Exception localException) {
            Log.e("DeviceListenerImp", localException.getMessage());
        }
        return screenOffTime;
    }

    @Override
    public void saveLog(String message, String title) {

    }
}
