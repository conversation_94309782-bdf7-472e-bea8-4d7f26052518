package com.pax.pay.record;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Spinner;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.pay.BaseActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.mqtt.CurrentBatchResponse;
import com.pax.pay.mqtt.CurrentBatchSummary;
import com.pax.pay.mqtt.MQTTCommand;
import com.pax.pay.mqtt.L5TransactionResponse;
import com.pax.pay.mqtt.MQTTForegroundService;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.mqtt.Transaction;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionDispTransDetail;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionInputTransData.EInputType;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.NewSpinnerAdapter;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class SQRQueryActivity extends BaseActivity {

    private Acquirer acquirer = null;
    private NewSpinnerAdapter<Acquirer> adapter;
    private PagerAdapter pagerAdapter;

    private String navTitle;
    private final OnDataLoadedListener onDataLoadedListener = this::initializeViewPagerData;
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private ActionInputTransData inputTransDataAction;
    private MQTTForegroundService mqttService;
    private boolean isBound = false;
    private CustomAlertDialog loadingDialog;
    private static final ObjectMapper MAPPER = new ObjectMapper();


    private final ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            MQTTForegroundService.LocalBinder binder = (MQTTForegroundService.LocalBinder) service;
            mqttService = binder.getService();
            isBound = true;
        }

        @Override
        public void onServiceDisconnected(ComponentName arg0) {
            isBound = false;
        }
    };
    private final BroadcastReceiver networkReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            ConnectivityManager cm = (ConnectivityManager)
                    FinancialApplication.getApp().getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo info = cm.getActiveNetworkInfo();
            boolean connected = info != null && info.isConnected();
            if (!connected && loadingDialog != null && loadingDialog.isShowing()) {
                loadingDialog.dismiss();
                if (mqttService != null) {
                    mqttService.setMessageCallback(null);
                }
                showPushError(Utils.getString(R.string.err_connect));
            }
        }
    };

    @Override
    protected void onStart() {
        super.onStart();
        Intent intent = new Intent(this, MQTTForegroundService.class);
        bindService(intent, connection, Context.BIND_AUTO_CREATE);
        // 注册网络变化监听
        registerReceiver(networkReceiver, new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION));
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (isBound) {
            unbindService(connection);
            isBound = false;
        }
        // 注销网络变化监听
        unregisterReceiver(networkReceiver);
    }

    @Override
    protected void loadParam() {
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        Handler handler = new Handler(Looper.getMainLooper());
        adapter = new NewSpinnerAdapter<>(this);
        executorService.execute(() -> {
            // 后台线程执行耗时操作
            List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();

            // 切回主线程更新UI
            handler.post(() -> {
                if (acquirer == null) {
                    for (Acquirer acq : listAcquirers) {
                        if (acq.getName().equals(FinancialApplication.getAcqManager().getCurAcq().getName())) {
                            acquirer = acq;
                            break;
                        }
                    }
                }
                // 更新适配器的数据
                adapter.setListInfo(listAcquirers);
                adapter.setOnTextUpdateListener((list, position) -> ((Acquirer) list.get(position)).getName());

                // 只加载 DetailFragment
                pagerAdapter = new MyAdapter(getSupportFragmentManager());

                onDataLoadedListener.onDataLoaded();
            });
        });
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_sqr_query_layout;
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        //nothing to do
    }

    private void initializeViewPagerData() {
        ViewPager pager = findViewById(R.id.pager);
        Spinner spinner = findViewById(R.id.trans_history_acq_list);
        if (adapter.getCount() > 1) {
            spinner.setVisibility(View.VISIBLE);
            spinner.setAdapter(adapter);
            spinner.setSelection(adapter.getListInfo().indexOf(acquirer));
            spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int pos, long id) {
                    Acquirer newAcquirer = adapter.getListInfo().get(pos);
                    if (!newAcquirer.getId().equals(acquirer.getId())) {
                        acquirer = newAcquirer;
                        pagerAdapter.notifyDataSetChanged();
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // 另一个接口回调
                }
            });
        }
        pager.setAdapter(pagerAdapter);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Device.enablePhysicalPowerButton(true);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 关闭 ExecutorService 以释放资源
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        if (inputTransDataAction != null) {
            inputTransDataAction.setEndListener(null);
            inputTransDataAction = null;
        }
        if (mqttService != null) {
            // 移除当前Activity注册的回调
            mqttService.setMessageCallback(null);
            mqttService = null;
        }

        // 清理ViewPager和Adapter，防止内存泄漏
        ViewPager pager = findViewById(R.id.pager);
        if (pager != null) {
            pager.setAdapter(null);
        }
        pagerAdapter = null;
        adapter = null;

        // 清理TransContext中的引用，防止内存泄漏
        TransContext.clearInstance();
    }

    @Override
    protected void setListeners() {
        // do nothing
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.sqr_query_action, menu);

        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public void onClickProtected(View v) {
        // do nothing
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (quickClickProtection.isStarted()) { // AET-123
            return true;
        }
        quickClickProtection.start();
        event.startTracking();
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onOptionsItemSelectedSub(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            setResult(100);
            finish();
            return true;
        } else if (itemId == R.id.history_menu_search) {
            queryTransRecordByRefNo();
            return true;
        } else if (itemId == R.id.history_menu_print_last_5_trans) {
            executeWithProgressDialog("L5", this::processAndDisplayMQTTMessage, getString(R.string.querying_data_for_last_5_transactions));
            return true;
        } else if (itemId == R.id.history_menu_print_current_batch) {
            executeWithProgressDialog(
                    "CBR",
                    msg -> processAndDisplayMQTTBatch(
                            msg,
                            getString(R.string.sqr_current_batch)
                    ),
                    getString(R.string.querying_data_for_current_batch_report)
            );
            return true;
        } else if (itemId == R.id.history_menu_print_last_batch) {
            executeWithProgressDialog(
                    "LBR",
                    msg -> processAndDisplayMQTTBatch(
                            msg,
                            getString(R.string.sqr_last_batch)
                    ),
                    getString(R.string.querying_data_for_last_batch_record)
            );
            return true;
        } else {
            return super.onOptionsItemSelectedSub(item);
        }
    }

    private void executeWithProgressDialog(String commandType, Consumer<String> messageCallback, String title) {
        //禁用电源按键,防止在执行查询指令的时候终端息屏
        Device.enablePhysicalPowerButton(false);
        // 首先检查网络连接状态
        if (!MQTTUtils.isConnectIsNormal()) {
            // 网络断开，显示连接错误提示
            showPushError(Utils.getString(R.string.err_connect));
            return;
        }

        //CBR/LBR/L5指令超时时间为30s
        loadingDialog = DialogUtils.showProcessingMessage(this, title, 30);

        mqttService.setMessageCallback(new MQTTForegroundService.MQTTMessageCallback() {
            @Override
            public void onMessageReceived(String message) {
                runOnUiThread(() -> {
                    closeDialog();
                    // 清除回调，防止下次重复触发
                    mqttService.setMessageCallback(null);
                });
                messageCallback.accept(message);
            }

            @Override
            public void onMessageFailed(String error) {
                runOnUiThread(() -> {
                    closeDialog();
                    // 清除回调
                    mqttService.setMessageCallback(null);
                    // 检查是否是网络连接错误
                    if (error.equals("Disconnected") || !MQTTUtils.isConnectIsNormal()) {
                        showPushError(Utils.getString(R.string.err_connect));
                    } else {
                        showPushError(Utils.getString(R.string.querying_error));
                    }
                });
            }
        });

        sendMQTTCommandToServer(commandType);
    }

    private void sendMQTTCommandToServer(String type) {
        // 首先检查网络连接状态
        if (!MQTTUtils.isConnectIsNormal()) {
            // 网络断开，关闭倒计时对话框并显示连接错误提示
            closeDialog();
            showPushError(Utils.getString(R.string.err_connect));
            return;
        }

        if (isBound && mqttService != null) {
            // 构造指令对象
            MQTTCommand command = new MQTTCommand();
            String name = SysParam.getInstance().getString(R.string.ACQ_NAME);
            Acquirer currAcquirer = GreendaoHelper.getAcquirerHelper().findAcquirer(name);
            command.setType(type);
            command.setTid(currAcquirer.getTerminalId());
            command.setMid(currAcquirer.getMerchantId());

            // 使用服务中的ObjectMapper序列化
            String message;
            try {
                message = MQTTForegroundService.MAPPER.writeValueAsString(command);
                String topic = "SoundBoxReport";
                mqttService.publish(message, topic, new WeakPushCallback(this));
            } catch (JsonProcessingException e) {
                LogUtils.e(TAG, "JSON序列化失败", e);
                closeDialog();
                showPushError(Utils.getString(R.string.querying_error));
            }
        } else {
            closeDialog();
            ToastUtils.showMessage("MQTT Service Unavailable");
        }
    }

    private void closeDialog() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
    }

    private void processAndDisplayMQTTMessage(String message) {
        try {
            L5TransactionResponse rsp = MAPPER.readValue(message, L5TransactionResponse.class);

            if (!MQTTUtils.validateTIDAndMID(rsp.getTid(), rsp.getMid())) {
                showToastOnUI(Utils.getString(R.string.tid_and_mid_not_match));
                return;
            }
            if (!"00".equals(rsp.getResponseCode())) {
                showToastOnUI(Utils.getString(R.string.mqtt_transaction_fail));
                return;
            }
            List<Transaction> txs = rsp.getTransactions();
            if (txs == null || txs.isEmpty()) {
                showToastOnUI(Utils.getString(R.string.mqtt_commond_response_data_error));
                return;
            }

            List<SQRData> sqrDataList = txs.stream()
                    .filter(tx -> tx.getRrn() != null && tx.getRrn().length() == 12)
                    .map(tx -> {
                        SQRData d = new SQRData();
                        d.setAmount(tx.getAmount());
                        d.setRrn(tx.getRrn());
                        d.setStan(tx.getStan());
                        d.setAuthCode(tx.getAuthCode());
                        d.setBatch(tx.getBatchNumber());
                        d.setCardNo(tx.getCardNumber());
                        d.setInvoiceNr(tx.getInvoiceNumber());
                        d.setTranDate(tx.getTransactionDate());
                        return d;
                    })
                    .collect(Collectors.toList());

            if (sqrDataList.isEmpty()) {
                LogUtils.d(TAG, "No valid RRN");
                showToastOnUI("Invalid RRN");
                return;
            }

            showLastFiveTrans(new ArrayList<>(sqrDataList));
        } catch (JsonProcessingException e) {
            LogUtils.e(TAG, "JSON解析失败", e);
        }
    }

    private void showToastOnUI(String msg) {
        runOnUiThread(() -> ToastUtils.showMessage(msg));
        Device.enablePhysicalPowerButton(true);
    }

    private void processAndDisplayMQTTBatch(String message, String title) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            CurrentBatchResponse response = mapper.readValue(message, CurrentBatchResponse.class);
            if (MQTTUtils.validateTIDAndMID(response.getTid(), response.getMid())) {
                if ("00".equals(response.getRspCode())) {
                    ArrayList<CurrentBatchSummary> currentBatchResponseList = response.getBatchSummary();
                    if (currentBatchResponseList == null || currentBatchResponseList.isEmpty()) {
                        FinancialApplication.getApp().runOnUiThread(() ->
                                ToastUtils.showMessage(R.string.mqtt_commond_response_data_error));
                        return;
                    }
                    showMQTTBatch(currentBatchResponseList, title);
                } else {
                    FinancialApplication.getApp().runOnUiThread(() ->
                            ToastUtils.showMessage(R.string.mqtt_transaction_fail));
                }
            } else {
                FinancialApplication.getApp().runOnUiThread(() ->
                        ToastUtils.showMessage(R.string.tid_and_mid_not_match));
            }

        } catch (JsonProcessingException e) {
            LogUtils.e(TAG, "JSON解析失败", e);
        }
    }

    private void queryTransRecordByRefNo() {

        inputTransDataAction = new ActionInputTransData(action -> ((ActionInputTransData) action).setParam(new WeakReference<>(SQRQueryActivity.this),
                        getString(R.string.sqr_history))
                .setInputLine(getString(R.string.prompt_input_rrn_no),
                        EInputType.NUM, 12, 12));

        inputTransDataAction.setEndListener((action, result) -> {

            if (result.getRet() != TransResult.SUCC) {
                ActivityStack.getInstance().pop();
                return;
            }

            String content = (String) result.getData();
            if (content == null || content.isEmpty()) {
                ToastUtils.showMessage(R.string.please_input_again);
                inputTransDataAction.setFinished(false);
                return;
            }
            SQRData sqrData = GreendaoHelper.getSQRDataHelper().findSQRDataByRrn(content);
            if (sqrData == null) {
                ToastUtils.showMessage(R.string.err_no_orig_trans);
                inputTransDataAction.setFinished(false);
                return;
            }

            final Map<String, String> map = MQTTUtils.prepareValuesForDispSQR(sqrData);

            ActionDispTransDetail dispTransDetailAction = new ActionDispTransDetail(action1 ->
                    ((ActionDispTransDetail) action1).setParam(SQRQueryActivity.this, getString(R.string.sqr_history), map));
            dispTransDetailAction.setEndListener((action2, result1) -> ActivityStack.getInstance().popTo(SQRQueryActivity.this));

            dispTransDetailAction.execute();
        });

        inputTransDataAction.execute();
    }

    private void showLastFiveTrans(ArrayList<SQRData> sqrDataList) {
        ActionDispTransDetail dispTransDetailAction = new ActionDispTransDetail(action1 ->
                ((ActionDispTransDetail) action1).setParam(FinancialApplication.getApp(), getString(R.string.sqr_last_5_transaction), sqrDataList));
        dispTransDetailAction.setEndListener((action2, result1) -> ActivityStack.getInstance().popTo(SQRQueryActivity.this));
        dispTransDetailAction.execute();
    }

    private void showMQTTBatch(ArrayList<CurrentBatchSummary> currentBatchList, String title) {
        ActionDispTransDetail dispTransDetailAction = new ActionDispTransDetail(action1 ->
                ((ActionDispTransDetail) action1).
                        setParam(FinancialApplication.getApp(), title, currentBatchList, true));
        dispTransDetailAction.setEndListener((action2, result1) -> ActivityStack.getInstance().popTo(SQRQueryActivity.this));
        dispTransDetailAction.execute();
    }

    private void showPushError(String errorMessage) {
        DialogUtils.showErrMessage(this,
                getString(R.string.sqr_querying_data), errorMessage,
                null, Constants.FAILED_DIALOG_SHOW_TIME);
        Device.enablePhysicalPowerButton(true);
    }

    public interface OnDataLoadedListener {
        void onDataLoaded();
    }

    // 修改后的适配器：只加载一个 DetailFragment
    private static class MyAdapter extends FragmentPagerAdapter {
        MyAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public int getCount() {
            return 1;
        }

        @NonNull
        @Override
        public Fragment getItem(int position) {
            return new SQRDetailFragment();
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            return super.instantiateItem(container, position);
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            return PagerAdapter.POSITION_NONE;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return "详情";
        }
    }

    static class WeakPushCallback implements MQTTForegroundService.PushCallback {
        private final WeakReference<SQRQueryActivity> activityRef;

        WeakPushCallback(SQRQueryActivity activity) {
            this.activityRef = new WeakReference<>(activity);
        }

        @Override
        public void onPushFailure(String error) {
            SQRQueryActivity activity = activityRef.get();
            if (activity != null && !activity.isFinishing()) {
                activity.runOnUiThread(() -> {
                    activity.closeDialog();
                    activity.showPushError(Utils.getString(R.string.querying_error));
                });
            }
        }
    }
}
