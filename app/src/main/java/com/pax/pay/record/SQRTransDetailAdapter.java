package com.pax.pay.record;

import android.content.Context;
import android.text.TextUtils;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.commonlib.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.action.ActionPreview;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.Utils;
import com.pax.pay.utils.ViewUtils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

public class SQRTransDetailAdapter extends RecyclerView.Adapter<SQRTransDetailAdapter.ViewHolder> {
    private final Context context;
    private ArrayList<SQRData> dataList;

    public SQRTransDetailAdapter(Context context, ArrayList<SQRData> dataList) {
        this.context = context;
        this.dataList = dataList;
    }

    @NonNull
    @Override
    public SQRTransDetailAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sqr_item_detail, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SQRTransDetailAdapter.ViewHolder holder, int position) {
        // 获取当前位置的数据
        SQRData transData = dataList.get(position);

        // 为了避免复用导致的布局重叠，先清空 detailLayout 里的子 View
        holder.detailLayout.removeAllViews();
        final Map<String, String> map = MQTTUtils.prepareValuesForDispSQR(transData);
        Pair<ArrayList<String>, ArrayList<String>> pair = generateSQRTransDetail(map);

        // 将多行明细添加到 detailLayout
        for (int i = 0; i < pair.second.size(); i++) {
            LinearLayout line = ViewUtils.genSingleLineLayout(
                    context,
                    pair.first.get(i),
                    pair.second.get(i)
            );
            holder.detailLayout.addView(line);
        }

        holder.rePrintBtn.setOnClickListener(v -> doReprint(transData));
    }

    private void doReprint(final SQRData transData) {
        FinancialApplication.getApp().runInBackground(() -> {
            if (Utils.isA50()) {
                ActionPreview actionPreview = new ActionPreview(action -> ((ActionPreview) action).setParam(context, transData));
                actionPreview.setEndListener((action, result) -> ActivityStack.getInstance().popTo(SQRQueryActivity.class));
                actionPreview.execute();
            } else {
                Printer.printTransAgain(ActivityStack.getInstance().top(), transData, false, null);
            }
        });
    }

    private Pair<ArrayList<String>, ArrayList<String>> generateSQRTransDetail(Map<String, String> promptValue) {
        ArrayList<String> leftColumns = new ArrayList<>();
        ArrayList<String> rightColumns = new ArrayList<>();
        Set<Map.Entry<String, String>> entries = promptValue.entrySet();
        for (Map.Entry<String, String> next : entries) {
            // 如果内容为空则不展示这条数据
            if (TextUtils.isEmpty(next.getValue())) continue;
            leftColumns.add(next.getKey());
            String value = next.getValue();
            rightColumns.add(value == null ? "" : value);
        }
        return new Pair<>(leftColumns, rightColumns);
    }

    @Override
    public int getItemCount() {
        return (dataList == null) ? 0 : dataList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        LinearLayout detailLayout;
        Button rePrintBtn;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            detailLayout = itemView.findViewById(R.id.detail_layout);
            rePrintBtn = itemView.findViewById(R.id.reprint_btn);
            if (Utils.isA50()) {
                rePrintBtn.setText(Utils.getString(R.string.trans_preview));
            }

            if (!SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_PRINT) && !Utils.isA50()) {
                rePrintBtn.setEnabled(false);
                int color = ContextCompat.getColor(FinancialApplication.getApp(), R.color.gray);
                rePrintBtn.setHintTextColor(color);
            }
        }
    }
}
