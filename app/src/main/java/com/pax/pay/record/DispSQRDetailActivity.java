/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.record;

import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.abl.core.ActionResult;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.trans.TransResult;
import com.pax.sbipay.R;

import java.util.ArrayList;

public class DispSQRDetailActivity extends BaseActivityWithTickForAction {
    private ArrayList<SQRData> dataList;  // 存放多条交易详情
    private String navTitle;
    private boolean navBack;

    @SuppressWarnings("unchecked")
    @Override
    protected void loadParam() {
        Bundle bundle = getIntent().getExtras();
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        navBack = getIntent().getBooleanExtra(EUIParamKeys.NAV_BACK.toString(), false);
        if (bundle != null) {
            dataList = (ArrayList<SQRData>) bundle.getSerializable(EUIParamKeys.SQR_TRANS_DETAILS.toString());
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_disp_sqr_detail;
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        RecyclerView rvTransDetails = findViewById(R.id.rv_trans_details);
        rvTransDetails.setLayoutManager(new LinearLayoutManager(this));
        SQRTransDetailAdapter adapter = new SQRTransDetailAdapter(this, dataList);
        rvTransDetails.setAdapter(adapter);
    }

    @Override
    protected void setListeners() {
        enableBackAction(navBack);
    }

    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

}
