/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.record;

import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ATransaction;
import com.pax.abl.core.ActionResult;
import com.pax.commonbusiness.card.PanUtils;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.edc.expandablerecyclerview.BaseViewHolder;
import com.pax.edc.expandablerecyclerview.ExpandItemAnimator;
import com.pax.edc.expandablerecyclerview.ExpandableRecyclerAdapter;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.SaleVoidTrans;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionPreview;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.utils.CurrencyCode;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class TransDetailFragment extends Fragment {

    private Context context;

    private RecyclerView mRecyclerView;
    private ExpandableRecyclerAdapter<TransData> mAdapter;
    private final List<TransData> mListItems = new ArrayList<>();

    private View noTransRecord;

    private CustomAlertDialog loadingDialog;

    private RecordAsyncTask mRecordAsyncTask;

    private boolean supportDoTrans = true;
    private String acquirerName = "";
    private long lastClickTime = 0;
    private boolean isClickAllowed = true;

    private OnBackPressedCallback mBackPressedCallback = new OnBackPressedCallback(true) {
        @Override
        public void handleOnBackPressed() {
            // nothing
        }
    };

    public TransDetailFragment() {
        // do nothing
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.activity_trans_detail_layout, container, false);
        this.context = getContext();
        noTransRecord = view.findViewById(R.id.no_trans_record);
        mRecyclerView = view.findViewById(R.id.trans_list);

        //AET-374,recyclerView notify exception
        mRecyclerView.setLayoutManager(new CustomLinearLayoutManager(this.getActivity()));
        mRecyclerView.setItemAnimator(new ExpandItemAnimator());
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        mBackPressedCallback.remove();
        if (mRecordAsyncTask != null) {
            mRecordAsyncTask.cancel(true);
            //ActivityStack.getInstance().pop(); // why need this
        }
        syncRecord();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
        if (mRecordAsyncTask != null) {
            mRecordAsyncTask.cancel(true);
        }
        mRecordAsyncTask = null;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBackPressedCallback.remove();
    }

    public String getAcquirerName() {
        return acquirerName;
    }

    public void setAcquirerName(String acquirerName) {
        this.acquirerName = acquirerName;
    }

    public boolean isSupportDoTrans() {
        return supportDoTrans;
    }

    public void setSupportDoTrans(boolean supportDoTrans) {
        this.supportDoTrans = supportDoTrans;
    }

    private void syncRecord() {
        mRecordAsyncTask = new RecordAsyncTask();
        mRecordAsyncTask.execute();
    }

    static class CustomLinearLayoutManager extends LinearLayoutManager {

        public CustomLinearLayoutManager(Context context) {
            super(context);
        }

        @Override
        public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
            try {
                super.onLayoutChildren(recycler, state);
            } catch (IndexOutOfBoundsException e) {
                LogUtils.e(e);
            }

        }
    }

    private class RecordAsyncTask extends AsyncTask<Void, Void, List<TransData>> {

        @Override
        @NonNull
        protected List<TransData> doInBackground(Void... params) {
            mListItems.clear();
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(acquirerName);
            if (acquirer != null) {
                List<TransData> list = GreendaoHelper.getTransDataHelper().findAllTransData(acquirer, true);
                mListItems.addAll(list);
                Collections.reverse(mListItems);// 把list倒序，使最新一条记录在最上
            }
            return mListItems;
        }

        @Override
        protected void onPostExecute(List<TransData> result) {
            super.onPostExecute(result);
            if (mListItems == null || mListItems.isEmpty()) {
                mRecyclerView.setVisibility(View.GONE);
                noTransRecord.setVisibility(View.VISIBLE);
                return;
            }
            mRecyclerView.setVisibility(View.VISIBLE);
            noTransRecord.setVisibility(View.GONE);
            if (mAdapter == null) {
                mAdapter = new ExpandableRecyclerAdapter<>(getActivity(), R.layout.trans_item,
                        new ExpandableRecyclerAdapter.ItemViewListener<TransData>() {
                            @Override
                            public BaseViewHolder<TransData> generate(View view) {
                                return new RecordViewHolder(view);
                            }
                        })
                        .setDataBeanList(mListItems);

            } else {
                mAdapter.setDataBeanList(mListItems);
            }
            mRecyclerView.setAdapter(mAdapter);
        }
    }

    private class RecordViewHolder extends BaseViewHolder<TransData> implements View.OnClickListener {
        private TextView transTypeTv;
        private TextView transAmountTv;
        private TextView transIssuerTv;
        private TextView transNoTv;
        private TextView transDateTv;
        private TextView historyDetailState;
        private TextView historyDetailCardNo;
        private TextView historyDetailAuthCode;
        private TextView historyDetailRefNo;
        private View historyTransActionLayout;
        private Button voidBtn;
        private Button reprintBtn;
        private Button reSendSms;
        private Button enquiryBtn;

        RecordViewHolder(View itemView) {
            super(itemView, R.id.trans_item_header, R.id.expandable);
        }

        @Override
        protected void initView() {
            transTypeTv = itemView.findViewById(R.id.trans_type_tv);
            transAmountTv = itemView.findViewById(R.id.trans_amount_tv);
            transIssuerTv = itemView.findViewById(R.id.issuer_type_tv);
            transNoTv = itemView.findViewById(R.id.trans_no_tv);
            transDateTv = itemView.findViewById(R.id.trans_date_tv);

            historyDetailState = itemView.findViewById(R.id.history_detail_state);
            historyDetailCardNo = itemView.findViewById(R.id.history_detail_card_no);
            historyDetailAuthCode = itemView.findViewById(R.id.history_detail_auth_code);
            historyDetailRefNo = itemView.findViewById(R.id.history_detail_ref_no);

            historyTransActionLayout = itemView.findViewById(R.id.history_trans_action);

            voidBtn = itemView.findViewById(R.id.history_trans_action_void);
            reprintBtn = itemView.findViewById(R.id.history_trans_action_reprint);
            if (Utils.isA50()) {
                reprintBtn.setText(getString(R.string.trans_preview));
            }
            reSendSms = itemView.findViewById(R.id.history_trans_action_resend_sms);
            enquiryBtn = itemView.findViewById(R.id.history_trans_action_enquiry);
        }

        @Override
        protected void setListener() {
            voidBtn.setOnClickListener(this);
            reprintBtn.setOnClickListener(this);
            reSendSms.setOnClickListener(this);
            enquiryBtn.setOnClickListener(this);
        }

        @Override
        public void bindView(final TransData dataBean, final BaseViewHolder viewHolder, final int pos) {
            ETransType transType = dataBean.getTransType();
            ETransType origTransType = dataBean.getOrigTransType();
            String type = transType.getTransName();
            if (ETransType.OFFLINE_TRANS_SEND == transType) {
                type = ETransType.SALE.getTransName();
            }

            if (dataBean.getTransState().equals(TransData.ETransStatus.VOIDED)) {
                type = type + "(VOID)";
            }

            if (dataBean.getTransState().equals(TransData.ETransStatus.FAILED)) {
                type = type + "(FAILED)";
            }

            if (dataBean.getTransState().equals(TransData.ETransStatus.UNKNOWN)) {
                type = type + "(UNKNOWN)";
            }

            boolean dcc = dataBean.isDcc();
            if (dcc) {
                type = "DCC " + type;
            }

            transTypeTv.setText(type);

            //AET-18
            String amount = dcc ? dataBean.getInternationalCurrencyAmount() : dataBean.getAmount();
            CurrencyCode currencyCode = dcc ? dataBean.getInternationalCurrency() : dataBean.getLocalCurrency();

            if (!dataBean.getTransType().isSymbolNegative() && !type.contains("VOID")) {
                transAmountTv.setTextColor(context.getResources().getColor(R.color.accent_amount));
                amount = CurrencyConverter.convert(Utils.parseLongSafe(amount, 0), currencyCode);
                if (transType == ETransType.QSPARC_BALANCE_UPDATE) {
                    if (!dataBean.getIsTopUp()) {
                        transAmountTv.setTextColor(context.getResources().getColor(R.color.accent));
                    }
                    amount = CurrencyConverter.convert(Utils.parseLongSafe(dataBean.getUpdateAmount(), 0), dataBean.getCurrency());
                }
            } else {
                transAmountTv.setTextColor(context.getResources().getColor(R.color.accent));
                amount = CurrencyConverter.convert(-Utils.parseLongSafe(amount, 0), currencyCode); //AET-18
                if (ETransType.REFUND == origTransType) {
                    transAmountTv.setTextColor(context.getResources().getColor(R.color.accent_amount));
                    amount = CurrencyConverter.convert(Utils.parseLongSafe(amount, 0), currencyCode); //AET-18
                }
            }
            transAmountTv.setText(amount);
            if (dataBean.getIssuer() != null) {
                transIssuerTv.setText(dataBean.getIssuer().getName());
            } else {
                transIssuerTv.setText("NA");
            }
            transNoTv.setText(Component.getPaddedNumber(dataBean.getInvoiceNo(), 6));

            String formattedDate;
            if (transType == ETransType.INSTA_EMI_SALE) {
                formattedDate = TimeConverter.convert(dataBean.getDateTime(), Constants.EMI_TIME_PATTERN_TRANS,
                        Constants.TIME_PATTERN_CUSTOM);
            } else {
                formattedDate = TimeConverter.convert(dataBean.getDateTime(), Constants.TIME_PATTERN_TRANS,
                        Constants.TIME_PATTERN_CUSTOM);
            }
            transDateTv.setText(formattedDate);

            if (viewHolder.getExpandView().getVisibility() == View.VISIBLE) {
                updateExpandableLayout(dataBean);
            }
        }

        void updateExpandableLayout(TransData transData) {
            String state = getState(transData);
            String cardNo = getCardNo(transData);
            String authCode = transData.getAuthCode();
            String refNo = transData.getRefNo();

            historyDetailState.setText(state);
            historyDetailCardNo.setText(cardNo);
            historyDetailAuthCode.setText(authCode != null ? authCode : "");
            historyDetailRefNo.setText(refNo != null ? refNo : "");

            historyTransActionLayout.setEnabled(supportDoTrans);

            if (transData.getTransState().equals(TransData.ETransStatus.NORMAL)
                    || transData.getTransState().equals(TransData.ETransStatus.ADJUSTED)) {
                voidBtn.setEnabled(transData.getTransType().isVoidAllowed() && SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_VOID));
                if (transData.getOfflineSendState() != null) {
                    voidBtn.setEnabled(false);
                }
            } else {
                voidBtn.setEnabled(false);
            }

            ETransType transType = transData.getTransType();
            // 未知的BQR需要添加查询按钮，隐藏void按钮来显示Enquiry按钮
            if (ETransType.QR == transType && TransData.ETransStatus.UNKNOWN.equals(transData.getTransState())) {
                voidBtn.setVisibility(View.GONE);
                enquiryBtn.setVisibility(View.VISIBLE);
                reprintBtn.setEnabled(false);
            } else {
                voidBtn.setVisibility(View.VISIBLE);
                enquiryBtn.setVisibility(View.GONE);
                reprintBtn.setEnabled(supportDoTrans);
            }

            boolean isEMI = ETransType.BANK_EMI_SALE.equals(transType) || ETransType.BRAND_EMI_SALE.equals(transType) || ETransType.INSTA_EMI_SALE.equals(transType);
            if (isEMI) {
                reSendSms.setEnabled(false);
            } else {
                reSendSms.setEnabled(SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP) && transType != ETransType.QR);
            }
            if (!SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_PRINT) && !Utils.isA50()) {
                reprintBtn.setEnabled(false);
            }
        }

        private String getState(TransData transData) {
            TransData.ETransStatus temp = transData.getTransState();
            String state = "";
            if (transData.isOnlineTrans()) {
                if (temp.equals(TransData.ETransStatus.NORMAL)) {
                    state = context.getString(R.string.state_normal);
                } else if (temp.equals(TransData.ETransStatus.VOIDED)) {
                    state = context.getString(R.string.state_voided);
                } else if (temp.equals(TransData.ETransStatus.ADJUSTED)) {
                    state = context.getString(R.string.state_adjusted);
                } else if (temp.equals(TransData.ETransStatus.FAILED)) {
                    state = context.getString(R.string.state_failed);
                } else if (temp.equals(TransData.ETransStatus.UNKNOWN)) {
                    state = context.getString(R.string.state_unknown);
                }

            } else if (transData.getTransType() == ETransType.QSPARC_BALANCE_ENQUIRY) {
                state = context.getString(R.string.state_normal);
            } else {
                state = getAdjustState(temp, transData.getOfflineSendState());
            }
            return state;
        }

        private String getAdjustState(TransData.ETransStatus transStatus, TransData.OfflineStatus offlineStatus) {
            String state;
            if (offlineStatus == TransData.OfflineStatus.OFFLINE_SENT) {
                state = context.getString(R.string.state_uploaded);
            } else if (offlineStatus == TransData.OfflineStatus.OFFLINE_NOT_SENT) {
                state = context.getString(R.string.state_not_sent);
            } else {
                state = context.getString(R.string.state_failed);
            }

            if (transStatus.equals(TransData.ETransStatus.ADJUSTED)) {
                state += "(" + context.getString(R.string.state_adjusted) + ")";
            }
            if (transStatus.equals(TransData.ETransStatus.VOIDED)) {
                state += "(" + context.getString(R.string.state_voided) + ")";
            }
            return state;
        }

        // 联机交易屏蔽卡号显示
        private String getCardNo(TransData transData) {
            String cardNo;
            if (transData.getPan() == null) {
                return "";
            }
            //如果Issuer和EnterMode都是空则取默认的卡号屏蔽方式
            if (transData.getIssuer() == null && transData.getEnterMode() == null) {
                return PanUtils.maskCardNo(transData.getPan());
            } else {
                cardNo = PanUtils.maskCardNo(transData.getPan(), transData.getIssuer().getPanMaskPattern());
            }
            cardNo += " /" + transData.getEnterMode().toString();
            return cardNo;
        }

        @Override
        public void onClick(View v) {
            long currentClickTime = System.currentTimeMillis();
            if (currentClickTime - lastClickTime > 500 && isClickAllowed) {
                lastClickTime = currentClickTime;
            } else {
                return;
            }
            final int position = getAdapterPosition();
            final TransData transData = mListItems.get(position);
            switch (v.getId()) {
                case R.id.history_trans_action_reprint:
                    doReprint(transData);
                    break;
                case R.id.history_trans_action_void:
                    requireActivity().getOnBackPressedDispatcher().addCallback(mBackPressedCallback);
                    doVoidAction(transData);
                    break;
                case R.id.history_trans_action_resend_sms:
                    isClickAllowed = false;
                    reSendSms(transData);
                    break;
                case R.id.history_trans_action_enquiry:
                    isClickAllowed = false;
                    TransData inquiryTransData = new TransData();
                    Component.transInit(inquiryTransData);
                    inquiryTransData.setTransType(ETransType.QR_INQUIRY);
                    inquiryTransData.setAmount(transData.getAmount());
                    doBqrEnquiry(inquiryTransData, transData);
                    break;
                default:
                    break;
            }
        }

        private void doReprint(final TransData transData) {
            FinancialApplication.getApp().runInBackground(new Runnable() {
                @Override
                public void run() {
                    if (Utils.isA50()) {
                        ActionPreview actionPreview = new ActionPreview(new AAction.ActionStartListener() {
                            @Override
                            public void onStart(AAction action) {
                                ((ActionPreview) action).setParam(context, transData);
                            }
                        });
                        actionPreview.setEndListener(new AAction.ActionEndListener() {
                            @Override
                            public void onEnd(AAction action, ActionResult result) {
                                ActivityStack.getInstance().pop();
                            }
                        });
                        actionPreview.execute();
                    } else {
                        Printer.printTransAgain(getActivity(), transData, true, null);
                    }
                }
            });
        }

        private void doBqrEnquiry(final TransData enquiryTransData, final TransData transData) {
            loadingDialog = DialogUtils.showProcessingMessage(getContext(), getString(R.string.qr_inquiry), -1);
            FinancialApplication.getApp().runInBackground(() -> {
                ActionTransOnline transOnlineAction = new ActionTransOnline(new AAction.ActionStartListener() {

                    @Override
                    public void onStart(AAction action) {
                        ((ActionTransOnline) action).setParam(context, enquiryTransData, 0);
                    }
                });
                transOnlineAction.setEndListener((act, result) -> {
                    if (loadingDialog != null && loadingDialog.isShowing()) {
                        loadingDialog.dismiss();
                    }
                    if (result.getRet() == TransResult.SUCC) {
                        FinancialApplication.getApp().runOnUiThread(() -> DialogUtils.showSuccMessage(getContext(),
                                getString(R.string.qr_transaction_successful),
                                dialog -> FinancialApplication.getApp().runInBackground(
                                        () -> Printer.printTransAgain(ActivityStack.getInstance().top(), transData, false, null)),
                                Constants.SUCCESS_DIALOG_SHOW_TIME));
                        // BQR固定卡号
                        transData.setPan("1000000000000001");
                        Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(transData.getPan());
                        transData.setIssuer(issuer);
                        transData.setTransState(TransData.ETransStatus.NORMAL);
                        transData.setProcCode(enquiryTransData.getProcCode());
                        transData.setTraceNo(enquiryTransData.getTraceNo());
                        transData.setDateTime(enquiryTransData.getDateTime());
                        transData.setRefNo(enquiryTransData.getRefNo());
                        transData.setAuthCode(enquiryTransData.getAuthCode());
                        transData.setResponseCode(enquiryTransData.getResponseCode());
                        GreendaoHelper.getTransDataHelper().delete(enquiryTransData);
                        GreendaoHelper.getTransDataHelper().update(transData);
                    } else {
                        FinancialApplication.getApp().runOnUiThread(() -> DialogUtils.showErrMessage(getContext(),
                                ETransType.QR.getTransName(), getString(R.string.qr_transaction_failed),
                                dialog -> FinancialApplication.getApp().runInBackground(
                                        () -> Printer.printTransAgain(ActivityStack.getInstance().top(), transData, false, null)),
                                Constants.FAILED_DIALOG_SHOW_TIME));
                        // 减去InvoiceNo防止invoiceNo跳跃
                        Component.decInvoiceNo();
                        GreendaoHelper.getTransDataHelper().delete(transData);
                        GreendaoHelper.getTransDataHelper().delete(enquiryTransData);
                        // 设置返回码，标识打印失败的BQR
                        transData.setResponseCode(enquiryTransData.getResponseCode());
                    }
                    isClickAllowed = true;
                    syncRecord();
                });
                transOnlineAction.execute(false);
            });
        }

        private void doVoidAction(TransData transData) {
            new SaleVoidTrans(getActivity(), transData, new ATransaction.TransEndListener() {
                @Override
                public void onEnd(ActionResult result) {
                    mBackPressedCallback.remove();
                }
            }).execute();
        }

        private void reSendSms(TransData transData) {
            EChargeSlipTask slipTask = new EChargeSlipTask(getActivity(), getString(R.string.history_detail), true, transData, new ATransaction.TransEndListener() {
                @Override
                public void onEnd(ActionResult result) {
                    ActivityStack.getInstance().popTo(getActivity());
                    isClickAllowed = true;
                }
            });
            slipTask.execute();
        }
    }
}


