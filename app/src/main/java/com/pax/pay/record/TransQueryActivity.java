/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.record;

import android.util.Log;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Spinner;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.PanUtils;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.BaseActivity;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.action.ActionDispTransDetail;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionInputTransData.EInputType;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.NewSpinnerAdapter;
import com.pax.view.PagerSlidingTabStrip;
import com.pax.view.dialog.DialogUtils;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * The type Trans query activity.
 */
public class TransQueryActivity extends BaseActivity {

    private Acquirer acquirer = null;
    private NewSpinnerAdapter<Acquirer> adapter;
    private PagerAdapter pagerAdapter;

    private String navTitle;
    private boolean supportDoTrans = true;
    private ActionInputTransData inputTransDataAction;

    @Override
    protected void loadParam() {
        String[] titles = new String[]{
                getString(R.string.history_detail), getString(R.string.history_total)
        };
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        supportDoTrans =
                getIntent().getBooleanExtra(EUIParamKeys.SUPPORT_DO_TRANS.toString(), true);

        List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();

        //不显示YUU交易记录
        /*for (Acquirer acquirer : listAcquirers) {
            if (acquirer.getName().equalsIgnoreCase("HASE_YUU")) {
                listAcquirers.remove(acquirer);
            }
        }*/

        for (int i = 0; i < listAcquirers.size(); i++) {
            Acquirer acquirer = listAcquirers.get(i);
            //不显示YUU交易记录
            if (acquirer.getName().equalsIgnoreCase("HASE_YUU")) {
                listAcquirers.remove(acquirer);
            }
        }

        acquirer = listAcquirers.get(0);

        adapter = new NewSpinnerAdapter<>(this);
        adapter.setListInfo(listAcquirers);
        adapter.setOnTextUpdateListener(new NewSpinnerAdapter.OnTextUpdateListener() {
            @Override
            public String onTextUpdate(final List<?> list, int position) {
                Acquirer acquirer = ((Acquirer) list.get(position));
                //对于instalment的acq，需要显示出期数和description
                if (acquirer.getName().contains("INS")) {
                    acquirer.setName(acquirer.getName().split("-")[0]);
                }
                acquirer.setName(Component.changeInstalmentAcqName(acquirer));
                return acquirer.getName();
            }
        });

        pagerAdapter = new MyAdapter(getSupportFragmentManager(), titles);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_trans_query_layout;
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        ViewPager pager = (ViewPager) findViewById(R.id.pager);
        PagerSlidingTabStrip tabs = (PagerSlidingTabStrip) findViewById(R.id.tabs);

        Spinner spinner = (Spinner) findViewById(R.id.trans_history_acq_list);

        if (adapter.getCount() > 1) {
            spinner.setVisibility(View.VISIBLE);
            spinner.setAdapter(adapter);

            spinner.setSelection(adapter.getListInfo().indexOf(acquirer));
            spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view,
                                           int pos, long id) {
                    Acquirer newAcquirer = adapter.getListInfo().get(pos);
                    if (newAcquirer.getId() != acquirer.getId()) {
                        acquirer = newAcquirer;
                        pagerAdapter.notifyDataSetChanged();
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // Another interface callback
                }
            });
        }
        pager.setAdapter(pagerAdapter);
        tabs.setViewPager(pager);
    }

    @Override
    protected void setListeners() {
        //do nothing
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.query_action, menu);

        if (!supportDoTrans) {
            menu.removeItem(R.id.history_menu_print_trans_last);
            menu.removeItem(R.id.history_menu_print_trans_detail);
            menu.removeItem(R.id.history_menu_print_trans_total);
            menu.removeItem(R.id.history_menu_print_last_total);
            menu.removeItem(R.id.history_menu_print_trans_all);
        }

        return super.onCreateOptionsMenu(menu);
    }

    private class MyAdapter extends FragmentPagerAdapter {
        private String[] titles;

        /**
         * Instantiates a new My adapter.
         *
         * @param fm     the fm
         * @param titles the titles
         */
        MyAdapter(FragmentManager fm, String[] titles) {
            super(fm);
            this.titles = titles;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return titles[position];
        }

        @Override
        public int getCount() {
            return titles.length;
        }

        @Override
        public Fragment getItem(int position) {
            switch (position) {
                case 0:
                    return new TransDetailFragment();
                case 1:
                    return new TransTotalFragment();
                default:
                    return null;
            }
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            if (acquirer.getName().contains("INS")) {
                acquirer.setName(acquirer.getName().split("-")[0]);
            }
            switch (position) {
                case 0:
                    TransDetailFragment f1 = (TransDetailFragment) super.instantiateItem(container, position);
                    f1.setAcquirerName(acquirer.getName());
                    f1.setSupportDoTrans(supportDoTrans);
                    return f1;
                case 1:
                    TransTotalFragment f2 = (TransTotalFragment) super.instantiateItem(container, position);
                    f2.setAcquirerName(acquirer.getName());
                    return f2;
                default:
                    return null;
            }
        }

        @Override
        public int getItemPosition(Object object) {
            return PagerAdapter.POSITION_NONE;
        }
    }

    @Override
    public void onClickProtected(View v) {
        // do nothing
    }

    @Override
    public boolean onOptionsItemSelectedSub(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                setResult(100);
                finish();
                return true;
            case R.id.history_menu_search:
                queryTransRecordByTransNo();
                return true;
            case R.id.history_menu_print_trans_last:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        int result = Printer.printLastTrans(TransQueryActivity.this);
                        if (result != TransResult.SUCC) {
                            DialogUtils.showErrMessage(TransQueryActivity.this,
                                    getString(R.string.transType_print), getString(R.string.err_no_trans),
                                    null, Constants.FAILED_DIALOG_SHOW_TIME);
                        }
                    }
                });
                return true;
            case R.id.history_menu_print_trans_detail:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        //AET-112
                        int result = Printer.printTransDetail(getString(R.string.print_history_detail),
                                TransQueryActivity.this, acquirer);
                        if (result != TransResult.SUCC) {
                            DialogUtils.showErrMessage(TransQueryActivity.this,
                                    getString(R.string.transType_print), getString(R.string.err_no_trans),
                                    null, Constants.FAILED_DIALOG_SHOW_TIME);
                        }
                    }
                });
                return true;
            case R.id.history_menu_print_trans_total:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        Printer.printTransTotal(TransQueryActivity.this, acquirer);
                    }
                });
                return true;
            case R.id.history_menu_print_last_total:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        int result = Printer.printLastBatch(TransQueryActivity.this);
                        if (result != TransResult.SUCC) {
                            DialogUtils.showErrMessage(TransQueryActivity.this,
                                    getString(R.string.transType_print), getString(R.string.err_no_trans),
                                    null, Constants.FAILED_DIALOG_SHOW_TIME);
                        }
                    }
                });
                return true;
            case R.id.history_menu_print_trans_all:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        //AET-112
                        int result = Printer.printTransAll(getString(R.string.print_trans_all),
                                TransQueryActivity.this);
                        if (result != TransResult.SUCC) {
                            DialogUtils.showErrMessage(TransQueryActivity.this,
                                    getString(R.string.transType_print), getString(R.string.err_no_trans),
                                    null, Constants.FAILED_DIALOG_SHOW_TIME);
                        }
                    }
                });
                return true;
            default:
                return super.onOptionsItemSelectedSub(item);
        }
    }

    private void queryTransRecordByTransNo() {

       inputTransDataAction = new ActionInputTransData(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputTransData) action).setParam(TransQueryActivity.this,
                        getString(R.string.trans_history)).setInputLine(getString(R.string.prompt_input_invoice_no),
                        EInputType.NUM, 6, false);
            }
        });

        inputTransDataAction.setEndListener(new AAction.ActionEndListener() {

            @Override
            public void onEnd(AAction action, ActionResult result) {

                if (result.getRet() != TransResult.SUCC) {
                    ActivityStack.getInstance().pop();
                    return;
                }

                String content = (String) result.getData();
                if (content == null || content.isEmpty()) {
                    ToastUtils.showMessage(R.string.please_input_again);
                    inputTransDataAction.setFinished(false);
                    return;
                }
                long transNo = Utils.parseLongSafe(content, -1);
                TransData transData = FinancialApplication.getTransDataDbHelper().findTransDataByInvoiceNo(transNo);

                if (transData == null) {
                    ToastUtils.showMessage(R.string.err_no_orig_trans);
                    inputTransDataAction.setFinished(false);
                    return;
                }

                final LinkedHashMap<String, String> map = prepareValuesForDisp(transData);

                ActionDispTransDetail dispTransDetailAction = new ActionDispTransDetail(
                        new AAction.ActionStartListener() {

                            @Override
                            public void onStart(AAction action) {

                                ((ActionDispTransDetail) action).setParam(TransQueryActivity.this,
                                        getString(R.string.trans_history), map);
                            }
                        }, Integer.parseInt(content));
                dispTransDetailAction.setEndListener(new AAction.ActionEndListener() {

                    @Override
                    public void onEnd(AAction action, ActionResult result) {
                        ActivityStack.getInstance().popTo(TransQueryActivity.this);
                    }
                });

                dispTransDetailAction.execute();
            }
        });

        inputTransDataAction.execute();
    }

    private LinkedHashMap<String, String> prepareValuesForDisp(TransData transData) {

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        ETransType transType = transData.getTransType();
        String amount;
        if (transType.isSymbolNegative()) {
            amount = CurrencyConverter.convert(0 - transData.getAmount(), transData.getCurrency());
        } else {
            amount = CurrencyConverter.convert(transData.getAmount(), transData.getCurrency());
        }

        String formattedDate = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN_DISPLAY);

        map.put(getString(R.string.history_detail_type), transType.getTransName());
        map.put(getString(R.string.history_detail_amount), amount);
        map.put(getString(R.string.history_detail_card_no), PanUtils.maskCardNo(transData.getPan(), transData.getIssuer().getPanMaskPattern()));
        map.put(getString(R.string.history_detail_auth_code), transData.getAuthCode());
        map.put(getString(R.string.history_detail_ref_no), transData.getRefNo());
        //map.put(getString(R.string.history_detail_trace_no), Component.getPaddedNumber(transData.getTraceNo(), 6));
        map.put(getString(R.string.history_detail_invoice_no), Component.getPaddedNumber(transData.getInvoiceNo(), 6));    //Jerry Modify 20180515 通过票据号显示和查找
        map.put(getString(R.string.dateTime), formattedDate);
        map.put(getString(R.string.history_detail_state), getState(transData));
        return map;
    }

    private String getState(TransData transData) {
        TransData.ETransStatus temp = transData.getTransState();
        String state = "";
        if (transData.isOnlineTrans()) {
            if (temp.equals(TransData.ETransStatus.NORMAL)) {
                state = this.getString(R.string.state_normal);
            } else if (temp.equals(TransData.ETransStatus.VOIDED)) {
                state = this.getString(R.string.state_voided);
            } else if (temp.equals(TransData.ETransStatus.ADJUSTED)) {
                state = getAdjustState(temp, transData.getOfflineSendState());
            }
        } else {
            state = getAdjustState(temp, transData.getOfflineSendState());
        }
        return state;
    }

    private String getAdjustState(TransData.ETransStatus transStatus, TransData.OfflineStatus offlineStatus) {
        String state;
        if (offlineStatus == TransData.OfflineStatus.OFFLINE_SENT) {
            state = this.getString(R.string.state_uploaded);
        } else {
            state = this.getString(R.string.state_not_sent);
        }

        if (transStatus.equals(TransData.ETransStatus.ADJUSTED)) {
            state += "(" + this.getString(R.string.state_adjusted) + ")";
        }
        if (transStatus.equals(TransData.ETransStatus.VOIDED)) {
            state += "(" + this.getString(R.string.state_voided) + ")";
        }
        return state;
    }

    @Override
    protected void onDestroy() {
        if (inputTransDataAction != null) {
            inputTransDataAction.setEndListener(null);
        }
        super.onDestroy();
    }
}
