/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.record;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Spinner;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ATransaction;
import com.pax.abl.core.ActionResult;
import com.pax.commonbusiness.card.PanUtils;
import com.pax.commonlib.ActivityStack;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.BaseActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionDispTransDetail;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionInputTransData.EInputType;
import com.pax.pay.trans.action.ActionPreview;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.TimeConverter;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.NewSpinnerAdapter;
import com.pax.view.PagerSlidingTabStrip;
import com.pax.view.dialog.DialogUtils;

import java.lang.ref.WeakReference;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TransQueryActivity extends BaseActivity {

    private Acquirer acquirer = null;
    private NewSpinnerAdapter<Acquirer> adapter;
    private PagerAdapter pagerAdapter;

    private String navTitle;
    private boolean supportDoTrans = true;
    private final OnDataLoadedListener onDataLoadedListener = this::initializeViewPagerData;
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private ActionInputTransData inputTransDataAction;
    private ActionDispTransDetail dispTransDetailAction;

    @Override
    protected void loadParam() {
        String[] titles = new String[]{getString(R.string.history_detail), getString(R.string.history_total)};
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        supportDoTrans = getIntent().getBooleanExtra(EUIParamKeys.SUPPORT_DO_TRANS.toString(), true);

        Handler handler = new Handler(Looper.getMainLooper());
        adapter = new NewSpinnerAdapter<>(this);
        executorService.execute(() -> {
            // 后台线程执行耗时操作
            List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();

            // 切回主线程更新UI
            handler.post(() -> {
                if (acquirer == null) {
                    for (Acquirer acq : listAcquirers) {
                        if (acq.getName().equals(FinancialApplication.getAcqManager().getCurAcq().getName())) {
                            acquirer = acq;
                            break;
                        }
                    }
                }
                // 更新适配器的数据
                adapter.setListInfo(listAcquirers);
                adapter.setOnTextUpdateListener(new NewSpinnerAdapter.OnTextUpdateListener() {
                    @Override
                    public String onTextUpdate(final List<?> list, int position) {
                        return ((Acquirer) list.get(position)).getName();
                    }
                });

                pagerAdapter = new MyAdapter(getSupportFragmentManager(), titles);

                onDataLoadedListener.onDataLoaded();
            });
        });
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_trans_query_layout;
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
    }

    private void initializeViewPagerData() {
        ViewPager pager = findViewById(R.id.pager);
        PagerSlidingTabStrip tabs = findViewById(R.id.tabs);
        Spinner spinner = findViewById(R.id.trans_history_acq_list);
        if (adapter.getCount() > 1) {
            spinner.setVisibility(View.VISIBLE);
            spinner.setAdapter(adapter);
            spinner.setSelection(adapter.getListInfo().indexOf(acquirer));
            spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int pos, long id) {
                    Acquirer newAcquirer = adapter.getListInfo().get(pos);
                    if (!newAcquirer.getId().equals(acquirer.getId())) {
                        acquirer = newAcquirer;
                        pagerAdapter.notifyDataSetChanged();
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // Another interface callback
                }
            });
        }
        pager.setAdapter(pagerAdapter);
        tabs.setViewPager(pager);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 关闭 ExecutorService 以释放资源
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        if (inputTransDataAction != null) {
            inputTransDataAction.resetStartListener();
            inputTransDataAction.setEndListener(null);
        }
        if (dispTransDetailAction != null) {
            dispTransDetailAction.resetStartListener();
            dispTransDetailAction.setEndListener(null);
        }
    }

    @Override
    protected void setListeners() {
        //do nothing
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.query_action, menu);

        if (!supportDoTrans) {
            menu.removeItem(R.id.history_menu_print_trans_last);
            menu.removeItem(R.id.history_menu_print_trans_detail);
            menu.removeItem(R.id.history_menu_print_trans_total);
            menu.removeItem(R.id.history_menu_print_last_total);
        }

        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public void onClickProtected(View v) {
        // do nothing
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (quickClickProtection.isStarted()) { //AET-123
            return true;
        }
        quickClickProtection.start();
        event.startTracking();
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onOptionsItemSelectedSub(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                setResult(100);
                finish();
                return true;
            case R.id.history_menu_search:
                queryTransRecordByTransNo(false);
                return true;
            case R.id.history_menu_print_trans_reversal:
                FinancialApplication.getApp().runInBackground(() -> {
                    int result = Printer.printReversal(TransQueryActivity.this);
                    if (result != TransResult.SUCC) {
                        showNoTransactin();
                    }
                });
                return true;
            case R.id.history_menu_print_trans_last:
                FinancialApplication.getApp().runInBackground(() -> {
                    TransData transData = GreendaoHelper.getTransDataHelper().findLastTransData();
                    if (transData != null && Utils.isA50()) {
                        Utils.showTransPreview(transData, TransQueryActivity.this, null);
                    } else {
                        int result = Printer.printLastTrans(TransQueryActivity.this);
                        if (result != TransResult.SUCC) {
                            showNoTransactin();
                        }
                    }
                });
                return true;
            case R.id.history_menu_print_trans_detail:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        int result = Printer.printTransDetail(getString(R.string.print_settle_detail),
                                TransQueryActivity.this, acquirer);
                        if (result != TransResult.SUCC) {
                            showNoTransactin();
                        }
                    }
                });
                return true;
            case R.id.history_menu_print_trans_total:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        Printer.printTransTotal(TransQueryActivity.this, acquirer, true);
                    }
                });
                return true;
            case R.id.history_menu_print_last_total:
                FinancialApplication.getApp().runInBackground(new Runnable() {
                    @Override
                    public void run() {
                        int result = Printer.printLastBatch(TransQueryActivity.this);
                        if (result != TransResult.SUCC) {
                            showNoTransactin();
                        }
                    }
                });
                return true;
            case R.id.history_menu_print_trans_preauth:
                FinancialApplication.getApp().runInBackground(() -> {
                    int result = Printer.printPreAuth(TransQueryActivity.this);
                    if (result != TransResult.SUCC) {
                        showNoTransactin();
                    }

                });
                return true;
            case R.id.history_menu_print_any_trans:
                FinancialApplication.getApp().runInBackground(() -> queryTransRecordByTransNo(true));
                return true;
            default:
                return super.onOptionsItemSelectedSub(item);
        }
    }

    private void showNoTransactin() {
        DialogUtils.showErrMessage(TransQueryActivity.this,
                getString(R.string.transType_print), getString(R.string.err_no_trans),
                null, Constants.FAILED_DIALOG_SHOW_TIME);
    }

    private void queryTransRecordByTransNo(boolean needPrint) {

         inputTransDataAction = new ActionInputTransData(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputTransData) action).setParam(new WeakReference<Context>(TransQueryActivity.this),
                        getString(R.string.trans_history)).setInputLine(getString(R.string.prompt_input_invoice_no),
                        EInputType.NUM, 6);
            }

        });

        inputTransDataAction.setEndListener(new AAction.ActionEndListener() {

            @Override
            public void onEnd(AAction action, ActionResult result) {

                if (result.getRet() != TransResult.SUCC) {
                    ActivityStack.getInstance().pop();
                    return;
                }

                String content = (String) result.getData();
                if (content == null || content.isEmpty()) {
                    ToastUtils.showMessage(R.string.please_input_again);
                    inputTransDataAction.setFinished(false);
                    return;
                }
                long invoiceNo = Utils.parseLongSafe(content, -1);
                TransData transData = GreendaoHelper.getTransDataHelper().
                        findTransDataByInvoiceNo(invoiceNo);

                if (transData == null) {
                    ToastUtils.showMessage(R.string.err_no_orig_trans);
                    inputTransDataAction.setFinished(false);
                    return;
                }

                if(!needPrint) {

                    final LinkedHashMap<String, String> map = prepareValuesForDisp(transData);

                    dispTransDetailAction = new ActionDispTransDetail(action1 -> ((ActionDispTransDetail) action1).setParam(TransQueryActivity.this, getString(R.string.trans_history), map));
                    dispTransDetailAction.setEndListener(new AAction.ActionEndListener() {

                        @Override
                        public void onEnd(AAction action, ActionResult result) {
                            ActivityStack.getInstance().popTo(TransQueryActivity.this);
                        }
                    });

                    dispTransDetailAction.execute();
                } else {
                    if (Utils.isA50()) {
                        ActionPreview actionPreview = new ActionPreview(null);
                        actionPreview.setParam(TransQueryActivity.this,transData);
                        actionPreview.setEndListener(new AAction.ActionEndListener() {
                            @Override
                            public void onEnd(AAction action, ActionResult result) {
                                // 打印结束无论如何回到历史记录界面
                                ActivityStack.getInstance().popTo(TransQueryActivity.class);
                            }
                        });
                        actionPreview.execute();

                    } else {
                        PrintTask printTask = new PrintTask(TransQueryActivity.this, transData, new ATransaction.TransEndListener() {
                            @Override
                            public void onEnd(ActionResult result) {
                                // 打印结束无论如何回到历史记录界面
                                ActivityStack.getInstance().popTo(TransQueryActivity.class);
                            }
                        });
                        printTask.setParam(false,true,false);
                        printTask.execute();
                    }
                }
            }
        });

        inputTransDataAction.execute();

    }

    private LinkedHashMap<String, String> prepareValuesForDisp(TransData transData) {

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        ETransType transType = transData.getTransType();
        String amount;
        if (transType.isSymbolNegative()) {
            amount = CurrencyConverter.convert(0 - Utils.parseLongSafe(transData.getAmount(), 0), transData.getCurrency());
        } else {
            amount = CurrencyConverter.convert(Utils.parseLongSafe(transData.getAmount(), 0), transData.getCurrency());
        }

        String formattedDate = TimeConverter.convert(transData.getDateTime(), Constants.TIME_PATTERN_TRANS,
                Constants.TIME_PATTERN_CUSTOM);
        String voidName = transData.getTransState() == TransData.ETransStatus.VOIDED ? "Voided " : "";
        map.put(getString(R.string.history_detail_type), voidName + transType.getTransName());
        if ("Voided ".equals(voidName)) {
            amount = "-" + amount;
        }
        map.put(getString(R.string.history_detail_amount), amount);
        map.put(getString(R.string.history_detail_card_no), PanUtils.maskCardNo(transData.getPan(), transData.getIssuer() == null ? "" : transData.getIssuer().getPanMaskPattern()));
        map.put(getString(R.string.history_detail_auth_code), transData.getAuthCode());
        map.put(getString(R.string.history_detail_ref_no), transData.getRefNo());
        map.put(getString(R.string.history_detail_invoice_no), Component.getPaddedNumber(transData.getInvoiceNo(), 6));
        map.put(getString(R.string.dateTime), formattedDate);
        return map;
    }

    public interface OnDataLoadedListener {
        void onDataLoaded();
    }

    private class MyAdapter extends FragmentPagerAdapter {
        private String[] titles;

        MyAdapter(FragmentManager fm, String[] titles) {
            super(fm);
            this.titles = titles;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return titles[position];
        }

        @Override
        public int getCount() {
            return titles.length;
        }

        @Override
        public Fragment getItem(int position) {
            switch (position) {
                case 0:
                    return new TransDetailFragment();
                case 1:
                    return new TransTotalFragment();
                default:
                    return null;
            }
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            switch (position) {
                case 0:
                    TransDetailFragment f1 = (TransDetailFragment) super.instantiateItem(container, position);
                    f1.setAcquirerName(acquirer.getName());
                    f1.setSupportDoTrans(supportDoTrans);
                    return f1;
                case 1:
                    TransTotalFragment f2 = (TransTotalFragment) super.instantiateItem(container, position);
                    f2.setAcquirerName(acquirer.getName());
                    return f2;
                default:
                    return null;
            }
        }

        @Override
        public int getItemPosition(Object object) {
            return PagerAdapter.POSITION_NONE;
        }
    }
}
