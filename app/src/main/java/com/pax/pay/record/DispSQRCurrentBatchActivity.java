/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.record;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.ActivityStack;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.mqtt.CurrentBatchSummary;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionPreview;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.ArrayList;

public class DispSQRCurrentBatchActivity extends BaseActivityWithTickForAction {

    private boolean navBack;
    private LinearLayout detailLayout;
    private LayoutInflater inflater;
    private ArrayList<CurrentBatchSummary> currentBatchSummaryList = new ArrayList<>();
    private Button printSqrBatch;

    private String navTitle;

    @SuppressWarnings("unchecked")
    @Override
    protected void loadParam() {
        Bundle bundle = getIntent().getExtras();
        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        navBack = getIntent().getBooleanExtra(EUIParamKeys.NAV_BACK.toString(), false);
        if (bundle != null) {
            currentBatchSummaryList = (ArrayList<CurrentBatchSummary>) bundle.getSerializable(EUIParamKeys.SQR_BATCH_SUMMARY.toString());
        }
    }

    @Override
    protected int getLayoutId() {
        if (Utils.isA50()) {
            return R.layout.sqr_batch_summary_a50;
        } else {
            return R.layout.sqr_batch_summary;
        }
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        detailLayout = findViewById(R.id.detail_layout);
        printSqrBatch = findViewById(R.id.print_sqr_batch);
        if (Utils.isA50()) {
            printSqrBatch.setText(Utils.getString(R.string.trans_preview));
        }
        inflater = LayoutInflater.from(this);
        initData();
        if (!SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_PRINT) && !Utils.isA50()) {
            printSqrBatch.setEnabled(false);
            int color = ContextCompat.getColor(FinancialApplication.getApp(), R.color.gray);
            printSqrBatch.setHintTextColor(color);
        }
    }

    @Override
    protected void setListeners() {
        enableBackAction(navBack);
        printSqrBatch.setOnClickListener(this);
    }

    @Override
    protected void onClickProtected(View v) {
        super.onClickProtected(v);
        if (v.getId() == R.id.print_sqr_batch) {
            doPrint();
        }
    }

    private void doPrint() {
        FinancialApplication.getApp().runInBackground(() -> {
            if (Utils.isA50()) {
                ActionPreview actionPreview = new ActionPreview(action -> ((ActionPreview) action).setParam(this, currentBatchSummaryList, navTitle));
                actionPreview.setEndListener((action, result) -> ActivityStack.getInstance().popTo(SQRQueryActivity.class));
                actionPreview.execute();
            } else {
                Printer.printTransAgain(ActivityStack.getInstance().top(), currentBatchSummaryList, false, navTitle);
            }
        });
    }

    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
        return true;
    }

    private void initData() {
        for (CurrentBatchSummary summary : currentBatchSummaryList) {
            // 加载单个 item 布局，不直接挂载到容器中（false）
            View itemView = inflater.inflate(R.layout.item_current_batch_summary, detailLayout, false);

            // 根据 id 获取 TextView
            TextView tvName = itemView.findViewById(R.id.tv_trans_type);
            TextView tvCount = itemView.findViewById(R.id.tv_trans_times);
            TextView tvAmount = itemView.findViewById(R.id.tv_amount);

            // 设置文本内容
            tvName.setText(summary.getName());
            tvCount.setText(summary.getCount());
            tvAmount.setText(summary.getAmount());

            // 将 itemView 添加到 detail_layout 中
            detailLayout.addView(itemView);
        }
    }
}
