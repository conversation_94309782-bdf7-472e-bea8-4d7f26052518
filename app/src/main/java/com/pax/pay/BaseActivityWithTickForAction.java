/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay;

import android.content.Context;
import android.os.Bundle;
import android.os.PowerManager;
import android.view.KeyEvent;
import android.view.MenuItem;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.utils.LogUtils;
import com.pax.device.Device;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.TransResult;
import com.pax.pay.utils.TickTimer;

public abstract class BaseActivityWithTickForAction extends BaseActivity {
    protected TickTimer tickTimer;
    //禁止点击标志位
    private boolean forbidBack = false;
    private PowerManager.WakeLock wakeLock;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        //交易过程保持屏幕常亮
        PowerManager pm = (PowerManager) FinancialApplication.getAppContext().getSystemService(Context.POWER_SERVICE);
        wakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "MyApp:TransactionWakeLock");
        wakeLock.acquire();
        //禁用电源按键
        Device.enablePhysicalPowerButton(false);
        tickTimer = new TickTimer(new TickTimer.OnTickTimerListener() {
            @Override
            public void onTick(long leftTime) {
                LogUtils.i(TAG, "onTick:" + leftTime);
            }

            @Override
            public void onFinish() {
                onTimerFinish();
            }
        });
        tickTimer.start();
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        tickTimer.stop();
        //清楚屏幕常量的标志位
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
    }

    public void finish(ActionResult result) {
        tickTimer.stop();
        AAction action = TransContext.getInstance().getCurrentAction();
        if (action != null) {
            if (action.isFinished()) {
                return;
            }
            action.setFinished(true);
            quickClickProtection.start(); // AET-93
            action.setResult(result);
        } else {
            finish();
        }
    }

    @Override
    protected boolean onKeyBackDown() {
        finish(new ActionResult(TransResult.ERR_ABORTED, null));
        return true;
    }

    @Override
    protected boolean onKeyDel() {
        finish(new ActionResult(TransResult.ERR_ABORTED, null));
        return true;
    }

    @Override
    protected boolean onOptionsItemSelectedSub(MenuItem item) {
        // 禁止点击则直接消费
        if (forbidBack) {
            return true;
        }
        if (item.getItemId() == android.R.id.home) {
            finish(new ActionResult(TransResult.ERR_USER_CANCEL, null));
            return true;
        }
        return super.onOptionsItemSelectedSub(item);
    }

    public void setForbidBack(boolean forbidBack){
        this.forbidBack = forbidBack;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return forbidBack || super.onKeyDown(keyCode, event);
    }

    protected void onTimerFinish() {
        finish(new ActionResult(TransResult.ERR_TIMEOUT, null));
    }
}
