/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         lixc                    Create
 * ===========================================================================================
 */
package com.pax.pay;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.InputFilter;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.SpannedString;
import android.text.style.AbsoluteSizeSpan;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.pax.appstore.DownloadManager;
import com.pax.appstore.InitTidException;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.view.UserGuideManager;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;
import com.shizhefei.guide.GuideHelper;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


public class InitTidActivity extends BaseActivity {

    public static final int REQ_WIZARD = 1;

    // echo test连接成功与否
    private static boolean echoTestStatus;

    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final Handler handler = new Handler(Looper.getMainLooper());
    private EditText enterTID;
    private EditText reenterTID;
    private TextView errorTextView;
    private ImageView acqStatusIcon;

    public static void onCheckPwd(Activity activity, int requestCode, boolean echoTestStatus) {
        InitTidActivity.echoTestStatus = echoTestStatus;
        Intent intent = new Intent(activity, InitTidActivity.class);
        activity.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void loadParam() {
        // do nothing
    }

    @Override
    protected int getLayoutId() {
        if (Utils.isA50()) {
            return R.layout.activity_check_pwd_layout_a50;
        } else {
            return R.layout.activity_check_pwd_layout;
        }
    }

    @Override
    protected void initViews() {
        enableActionBar(false);
        enterTID = findViewById(R.id.enterTID);
        reenterTID = findViewById(R.id.reenterTID);
        errorTextView = findViewById(R.id.errorTextView);
        acqStatusIcon = findViewById(R.id.acqStatusIcon);
        checkTerminalStatus();
        InputFilter lengthFilter = new InputFilter.LengthFilter(15); // 设置最大长度为15
        InputFilter alphanumericFilter = (source, start, end, dest, dStart, dEnd) -> {
            for (int i = start; i < end; ++i) {
                if (!Character.isLetterOrDigit(source.charAt(i))) {
                    return ""; // 非字母数字字符被过滤掉
                }
            }
            return null; // 保持原样
        };

        InputFilter[] filters = new InputFilter[]{alphanumericFilter, lengthFilter};
        enterTID.setFilters(filters);
        reenterTID.setFilters(filters);

        SpannableString enterTIDSS = new SpannableString(Utils.getString(R.string.enter_tid));
        AbsoluteSizeSpan ass = new AbsoluteSizeSpan(18, true);
        enterTIDSS.setSpan(ass, 0, enterTIDSS.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        enterTID.setHint(new SpannedString(enterTIDSS));

        SpannableString reenterTIDSS = new SpannableString(Utils.getString(R.string.reenter_tid));
        reenterTIDSS.setSpan(ass, 0, reenterTIDSS.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        reenterTID.setHint(new SpannedString(reenterTIDSS));
        enterTID.requestFocus();

        initUserGuideView();
    }

    private void checkTerminalStatus() {
        if (echoTestStatus) {
            acqStatusIcon.setImageResource(R.drawable.icon_tick);
        } else {
            acqStatusIcon.setImageResource(R.drawable.icon_cross);
        }
    }

    private void initUserGuideView() {
        if (!UserGuideManager.getInstance().isEnabled()) {
            return;
        }
        final GuideHelper guideHelper = new GuideHelper(this);
        GuideHelper.TipData tipData = new GuideHelper.TipData(R.drawable.tick_enter_tid_btn, Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL, enterTID);
        tipData.setViewBg(getResources().getDrawable(R.drawable.white_bg));
        GuideHelper.TipData tipOk = new GuideHelper.TipData(R.drawable.tip_ok_btn_en, Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL);
        if (Utils.isA50()) {
            tipOk.setLocation(0, -150);
        } else {
            tipOk.setLocation(0, -300);
        }
        tipOk.setOnClickListener(v -> guideHelper.nextPage());
        guideHelper.addPage(false, tipData, tipOk);
        guideHelper.show(false);
    }

    @Override
    protected void setListeners() {
        enterTID.setOnEditorActionListener(new PwdActionListener());
        reenterTID.setOnEditorActionListener(new PwdActionListener());
    }

    @Override
    protected boolean onKeyBackDown() {
        // exit app
        exit();
        return true;
    }

    private void exit() {
        // 确保edtTid失去焦点
        if (enterTID != null) {
            enterTID.clearFocus();
        }

        if (reenterTID != null) {
            reenterTID.clearFocus();
        }
        hideKeyBoard();
        DialogUtils.showExitAppDialog(InitTidActivity.this);
        setResult(RESULT_CANCELED);
    }

    private void hideKeyBoard() {
        // 隐藏键盘
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && enterTID != null) {
            imm.hideSoftInputFromWindow(enterTID.getWindowToken(), 0);
        }
        if (imm != null && reenterTID != null) {
            imm.hideSoftInputFromWindow(reenterTID.getWindowToken(), 0);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQ_WIZARD) {
            Intent intent = getIntent();
            setResult(RESULT_OK, intent);
            FinancialApplication.getController().set(Controller.IS_FIRST_RUN, false);
            finish();
        }
    }

    private class PwdActionListener extends EditorActionListener {
        @Override
        public void onKeyOk() {
            process();
        }

        @Override
        public void onKeyCancel() {
            exit();
        }

        /**
         * check password
         */
        private void process() {
            hideKeyBoard();
            initTidProcess();
        }

        private void initTidProcess() {
            if (!validateTIDInputs()) {
                return;
            }
            String tid = reenterTID.getText().toString().trim();
            if (tid.isEmpty()) {
                enterTID.setFocusable(true);
                enterTID.requestFocus();
                return;
            }

            if (tid.length() < 8) {
                ToastUtils.showMessage(R.string.tid_is_less_than_8_digit);
                enterTID.setFocusable(true);
                enterTID.requestFocus();
                return;
            }

            CustomAlertDialog loadingDialog = DialogUtils.showProcessingMessage(InitTidActivity.this, getString(R.string.binding_max_store), -1);

            executorService.execute(() -> {
                try {
                    DownloadManager.getInstance().initTid(tid);
                    // 关闭加载对话框
                    if (loadingDialog != null && loadingDialog.isShowing()) {
                        loadingDialog.dismiss();
                    }
                    Intent intent = new Intent(InitTidActivity.this, ConfigFirstActivity.class);
                    startActivityForResult(intent, REQ_WIZARD);
                } catch (InitTidException e) {
                    handler.post(() -> {
                        // 确保edtTid失去焦点
                        if (enterTID != null) {
                            enterTID.clearFocus();
                        }
                        if (reenterTID != null) {
                            reenterTID.clearFocus();
                        }
                        // 关闭加载对话框
                        if (loadingDialog != null && loadingDialog.isShowing()) {
                            loadingDialog.dismiss();
                        }

                        //if terminal is already activated go to application main interface
                        if (2405 == e.getCode()) {
                            ToastUtils.showMessage(R.string.terminal_has_been_activated);
                            Intent intent = new Intent(InitTidActivity.this, ConfigFirstActivity.class);
                            startActivityForResult(intent, REQ_WIZARD);
                        } else {
                            DialogUtils.showErrMessage(InitTidActivity.this, getString(R.string.init_tid_error),
                                    e.getCode() + " :" + e.getMessage(), null, Constants.FAILED_DIALOG_SHOW_TIME);
                        }
                    });
                }
            });
        }

        private boolean validateTIDInputs() {
            String tidInput = enterTID.getText().toString().toUpperCase();
            String tidReenter = reenterTID.getText().toString().toUpperCase();

            if (tidInput.isEmpty() || tidReenter.isEmpty()) {
                errorTextView.setVisibility(View.VISIBLE);
                errorTextView.setText(R.string.please_fill_in_both_fields);
                enterTID.setFocusable(true);
                enterTID.requestFocus();
                return false;
            }

            if (tidInput.equals(tidReenter)) {
                errorTextView.setVisibility(View.GONE);
            } else {
                errorTextView.setVisibility(View.VISIBLE);
                errorTextView.setText(R.string.tid_inputs_do_not_match);
                enterTID.setFocusable(true);
                enterTID.requestFocus();
                return false;
            }
            return true;
        }
    }


}
