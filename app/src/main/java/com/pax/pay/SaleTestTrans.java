/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay;

import android.content.Context;

import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.commonbusiness.card.TrackUtils;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransData.EnterMode;
import com.pax.data.local.GreendaoHelper;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionSearchCard.SearchMode;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.task.EChargeSlipTask;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.ArrayList;
import java.util.List;

public class SaleTestTrans extends BaseTrans {
    private byte searchCardMode = -1; // search card mode
    private String amount;

    private PrintTask printTask;

    /**
     * @param context :context
     * @param amount  :total amount
     * @param mode    {@link SearchMode}, 如果等于-1，
     */
    public SaleTestTrans(Context context, String amount, byte mode, TransEndListener transListener) {
        super(context, ETransType.SALE, transListener);
        setParam(amount, mode);
    }

    private void setParam(String amount, byte mode) {
        this.searchCardMode = mode;
        this.amount = amount;
        if (searchCardMode == -1) { // 待机银行卡消费入口
            searchCardMode = Component.getCardReadMode(ETransType.SALE);
            this.transType = ETransType.SALE;
        }
    }

    @Override
    public void bindStateOnAction() {
        transData.setAmount(amount);
        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), transData, 0);
            }
        });

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);

        // signature action
        ActionSignature signatureAction = new ActionSignature(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSignature) action).setParam(getCurrentContext(), transData.isDcc() ? transData.getInternationalCurrencyAmount() : transData.getAmount(), transData.isDcc() ? transData.getInternationalCurrency().getCode() : transData.getLocalCurrency().getCode());
            }
        });
        bind(State.SIGNATURE.toString(), signatureAction);

        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(new ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionOfflineSend) action).setParam(getCurrentContext());
            }
        });
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);

        //e charge slip
        EChargeSlipTask chargeSlipTask = new EChargeSlipTask(getCurrentContext(), getString(R.string.trans_sale), transData, EChargeSlipTask.genTransEndListener(SaleTestTrans.this, State.E_CHARGE_SLIP.toString()));
        bind(State.E_CHARGE_SLIP.toString(), chargeSlipTask);

        //print preview action
        printTask = new PrintTask(getCurrentContext(), transData, PrintTask.genTransEndListener(SaleTestTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        initData();
    }

    @Override
    public void onActionResult(String currentState, ActionResult result) {
        State state = State.valueOf(currentState);
        switch (state) {
            case MAG_ONLINE: // subsequent processing of online
                // determine whether need electronic signature or print
                toSignOrPrint();
                break;
            case SIGNATURE:
                // save signature data
                byte[] signData = (byte[]) result.getData();
                byte[] signPath = (byte[]) result.getData1();

                if (signData != null && signData.length > 0 && signPath != null && signPath.length > 0) {
                    transData.setSignData(signData);
                    transData.setSignPath(signPath);
                    // update trans data，save signature
                    GreendaoHelper.getTransDataHelper().update(transData);
                }

                //check offline trans
                checkOfflineTrans();
                break;
            case OFFLINE_SEND:
                toChargeSlipOrPrint();
                break;
            case E_CHARGE_SLIP:
                String chooseResult = (String) result.getData();
                if (Constants.E_CHARGE_SLIP.equals(chooseResult)) {
                    transEnd(new ActionResult(TransResult.SUCC, null));
                } else if (Constants.NO_CHARGE_SLIP.equals(chooseResult)) {
                    printTask.setParam(true);
                    gotoState(State.PRINT.toString());
                } else {
                    gotoState(State.PRINT.toString());
                }
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC || Utils.needBtPrint()) {
                    // end trans
                    transEnd(result);
                } else {
                    dispResult(transData, result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }

    private void initData() {
        String track2 = "7777670000000006=50121018638898001";
        String pan = TrackUtils.getPan(track2);
        Issuer matchedIssuer = FinancialApplication.getAcqManager().findIssuer("MASTER");
        transData.setTrack1(null);
        transData.setTrack2(track2);
        transData.setTrack3(null);
        transData.setPan(pan);
        transData.setExpDate(TrackUtils.getExpDate(track2));
        transData.setEnterMode(EnterMode.SWIPE);
        transData.setIssuer(matchedIssuer);

        String serviceCode = TrackUtils.getServiceCode(track2);
        boolean isFreePin;
        if (serviceCode != null && serviceCode.length() >= 3 && !transData.getIssuer().isRequirePIN()) {
            String third = serviceCode.substring(2, 3);
            isFreePin = !(third.equals("0") || third.equals("5") || third.equals("7"));
        } else {
            isFreePin = !transData.getIssuer().isRequirePIN();
        }
        transData.setPinFree(isFreePin);
        transData.setTransType(ETransType.SALE);
        // online process
        gotoState(State.MAG_ONLINE.toString());
    }

    private void checkOfflineTrans() {
        //get offline trans data list
        List<TransData.OfflineStatus> filter = new ArrayList<>();
        filter.add(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        List<TransData> offlineTransList = GreendaoHelper.getTransDataHelper().findOfflineTransData(filter);
        //AET-150
        if ((transData.getTransType().equals(ETransType.SALE) && transData.isOnlineTrans() && !offlineTransList.isEmpty() && !offlineTransList.get(0).getId().equals(transData.getId()))) { //AET-92
            if (transData.getEnterMode() == EnterMode.CLSS && transData.getEmvResult() != ETransResult.ONLINE_APPROVED) {
                toChargeSlipOrPrint();
                return;
            }
            //offline send
            gotoState(State.OFFLINE_SEND.toString());
        } else {
            // if terminal does not support signature ,card holder does not sign or time out，print preview directly.
            toChargeSlipOrPrint();
        }
    }

    private void toChargeSlipOrPrint() {
        if (!Utils.isA50() || SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_ECHARGESLIP)) {
            gotoState(State.E_CHARGE_SLIP.toString());
        } else {
            gotoState(State.PRINT.toString());
        }
    }

    // need electronic signature or send
    private void toSignOrPrint() {
        Component.incInvoiceNo();  //交易成功后小票号+1
        if (transData.isHasPin() || transData.getSignFree()) {// signature free
            // print preview
            transData.setSignFree(true);
            checkOfflineTrans();
        } else {
            transData.setSignFree(false);
            gotoState(State.SIGNATURE.toString());
        }
        GreendaoHelper.getTransDataHelper().update(transData);
    }

    enum State {
        MAG_ONLINE, SIGNATURE, OFFLINE_SEND, PRINT, E_CHARGE_SLIP
    }
}
