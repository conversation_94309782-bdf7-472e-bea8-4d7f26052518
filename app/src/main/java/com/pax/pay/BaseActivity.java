/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.device.Device;
import com.pax.pay.app.quickclick.QuickClickProtection;
import com.pax.pay.utils.ToastUtils;
import com.pax.sbipay.BuildConfig;
import com.pax.sbipay.R;

import java.lang.reflect.Method;

public abstract class BaseActivity extends AppCompatActivity implements View.OnClickListener {

    protected static final String TAG = "BaseActivity";
    private static final String PACKAGE_NAME = "com.vizpayemi";
    private static final String CLASS_NAME = "vizpayemi.homemodule.VizDashboardActivity";
    protected QuickClickProtection quickClickProtection = QuickClickProtection.getInstance();
    private ActionBar mActionBar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //屏蔽Home键
        Device.enableStatusBar(false);
        Device.enableHomeRecentKey(false);
        Device.disableA50Scanner();
        // 进入新的页面重新设置recover
        quickClickProtection.stop();
        if (BuildConfig.RELEASE) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);//禁用截屏
        }
        Device.enableScreenShot(false);
        loadParam();
        setContentView(getLayoutId());
        mActionBar = getSupportActionBar();
        if (mActionBar != null) {
            mActionBar.setTitle(getTitleString());
            mActionBar.setDisplayHomeAsUpEnabled(true);
            mActionBar.setDisplayShowTitleEnabled(true);
        }

        initViews();
        setListeners();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        quickClickProtection.stop();
        LogUtils.i("onDestroy", "");
    }

    /**
     * get layout ID
     *
     * @return
     */
    protected abstract int getLayoutId();

    /**
     * views initial
     */
    protected abstract void initViews();

    /**
     * set listeners
     */
    protected abstract void setListeners();

    /**
     * load parameter
     */
    protected abstract void loadParam();

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        if (menu != null && menu.getClass().getSimpleName().equals("MenuBuilder")) {
            try {
                Method m = menu.getClass().getDeclaredMethod("setOptionalIconsVisible", Boolean.TYPE);
                m.setAccessible(true);
                m.invoke(menu, true);
            } catch (Exception e) {
                LogUtils.e(getClass().getSimpleName(), "onMenuOpened...unable to set icons for overflow menu", e);
            }
        }
        return super.onPrepareOptionsMenu(menu);
    }

    @Override
    public final void onClick(View v) {
        if (quickClickProtection.isStarted()) {
            return;
        }
        quickClickProtection.start();
        onClickProtected(v);
    }

    protected void onClickProtected(View v) {
        //do nothing
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (quickClickProtection.isStarted()) { //AET-123
            return true;
        }
        quickClickProtection.start();
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return onKeyBackDown();
        } else if (keyCode == KeyEvent.KEYCODE_DEL) {
            return onKeyDel();
        }
        return super.onKeyDown(keyCode, event);
    }

    protected boolean onKeyBackDown() {
        finish();
        return true;
    }

    protected boolean onKeyDel() {
        return onKeyBackDown();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (quickClickProtection.isStarted()) { //AET-123
            return true;
        }
        quickClickProtection.start();
        return onOptionsItemSelectedSub(item);
    }

    protected boolean onOptionsItemSelectedSub(MenuItem item) {
        return super.onOptionsItemSelected(item);
    }

    protected String getTitleString() {
        return getString(R.string.app_name);
    }

    protected void enableBackAction(boolean enableBack) {
        if (mActionBar != null) {
            mActionBar.setDisplayHomeAsUpEnabled(enableBack);
        }
    }

    /**
     * 设置是否显示ActionBar
     *
     * @param showActionBar true 显示 false 隐藏
     */
    protected void enableActionBar(boolean showActionBar) {
        if (mActionBar != null) {
            if (showActionBar) {
                mActionBar.show();
            } else {
                mActionBar.hide();
            }
        }
    }

    /**
     * 启动指定的组件
     *
     * @param requestType 请求类型
     * @param requestData 请求数据
     * @param requestCode 请求代码
     */
    public void launchComponent(String requestType, String requestData, int requestCode) {
        ComponentName componentName = new ComponentName(PACKAGE_NAME, CLASS_NAME);
        try {
            if (isAppInstalled(this, PACKAGE_NAME)) {
                Intent intent = new Intent();
                intent.setComponent(componentName);
                intent.putExtra("REQUEST_TYPE", requestType);
                intent.putExtra("DATA", requestData);
                startActivityForResult(intent, requestCode);
            } else {
                ToastUtils.showMessage(R.string.please_install_the_emi_program_first);
                ActivityStack.getInstance().popTo(MainActivity.class);
                finish();
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        }
    }

    public boolean isAppInstalled(Context context, String packageName) {
        PackageManager packageManager = context.getPackageManager();
        try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true; // APK 已安装
        } catch (PackageManager.NameNotFoundException e) {
            return false; // APK 未安装
        }
    }

}
