/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.emv;

import androidx.annotation.NonNull;

import com.pax.commonlib.utils.LogUtils;
import com.pax.eemv.IEmvBase;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.TlvException;
import com.pax.glwrapper.impl.GL;
import com.pax.pay.trans.model.ETransType;

public class EmvTags {

    static final int[] SALE = {0x9F02, 0x9F03, 0x4F, 0x82, 0x9F36, 0x9F07, 0x8A, 0x9F26, 0x9F27, 0x8E,
            0x9F34, 0x9F1E, 0x9F0D, 0x9F0E, 0x9F0F, 0x9F10, 0x9F09, 0x9F33, 0x9F1A, 0x9F35,
            0x95, 0x9F53, 0x5F2A, 0x5F34, 0x9A, 0x9F41, 0x9C, 0x9F37, 0x9F36, 0x84, 0x9F6E, 0x9F7C};
    static final int[] PBOC_OFFLINE = {0x9F26, 0x9F27, 0x9F10, 0x9F37, 0x9F36, 0x95, 0x9A, 0x9C, 0x9F02,
            0x5F2A, 0x82, 0x9F1A, 0x9F03, 0x9F33, 0x9F1E, 0x84, 0x9F09, 0x9F41, 0x9F34, 0x9F35, 0x9F63, 0x8A};
    static final int[] AUTH = {0x9F26, 0x9F27, 0x9F10, 0x9F37, 0x9F36, 0x95, 0x9A, 0x9C, 0x9F02, 0x5F2A,
            0x82, 0x9F1A, 0x9F03, 0x9F33, 0x9F34, 0x9F35, 0x9F1E, 0x84, 0x9F09, 0x9F41, 0x9F63};
    /**
     * reversal
     */
    static final int[] DUP = {0x95, 0x9F10, 0x9F1E, 0xDF31};
    /**
     * 交易承兑但卡片拒绝时发起的冲正
     */
    static final int[] POS_ACCEPT_DUP = {0x95, 0x9F10, 0x9F1E, 0x9F36, 0xDF31};
    private static final String TAG = "EmvTags";

    private EmvTags() {

    }

    /**
     * generate field 55 by transaction type
     *
     * @param transType type
     * @param isDup     is reversal
     * @return data of field 55
     */
    @NonNull
    public static byte[] getF55(IEmvBase emv, ETransType transType, boolean isDup) {
        switch (transType) {
            case SALE:
            case REFUND:
            case SALE_CASH:
            case CASH_ONLY:
            case PREAUTHCOMPLETE:
            case PREAUTHCANCEL:
            case QSPARC_MONEY_ADD_CASH:
            case QSPARC_MONEY_ADD_ACCOUNT:
            case QSPARC_BALANCE_UPDATE:
            case BANK_EMI_SALE:
            case BRAND_EMI_SALE:
                if (isDup) {
                    return getValueList(emv, DUP);
                }

                return getValueList(emv, SALE);
            case PREAUTH:
                if (isDup) {
                    return getValueList(emv, DUP);
                }
                return getValueList(emv, AUTH);
            default:
                break;
        }
        return "".getBytes();
    }

    @NonNull
    public static byte[] getF55forPosAccpDup(IEmvBase emv) {
        return getValueList(emv, POS_ACCEPT_DUP);
    }

    @NonNull
    private static byte[] getValueList(IEmvBase emv, int[] tags) {
        if (tags == null || tags.length == 0) {
            return "".getBytes();
        }

        ITlv tlv = GL.getGL().getPacker().getTlv();
        ITlv.ITlvDataObjList tlvList = tlv.createTlvDataObjectList();
        for (int tag : tags) {
            byte[] value = emv.getTlv(tag);
            if (value == null || value.length == 0) {
                if (tag == 0x9f03) {
                    value = new byte[6];
                } else {
                    continue;
                }
            }
            try {
                ITlv.ITlvDataObj obj = tlv.createTlvDataObject();
                obj.setTag(tag);
                obj.setValue(value);
                tlvList.addDataObj(obj);
            } catch (Exception e) {
                LogUtils.i(TAG, "", e);
            }
        }

        try {
            return tlv.pack(tlvList);
        } catch (TlvException e) {
            LogUtils.e(TAG, "", e);
        }

        return "".getBytes();

    }
}
