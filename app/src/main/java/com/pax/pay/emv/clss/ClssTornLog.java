/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-3-6
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv.clss;

import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import java.io.Serializable;

/**
 * The type Clss torn log.
 */
@DatabaseTable(tableName = "clsstornlog")
public class ClssTornLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * The constant ID_FIELD_NAME.
     */
    public static final String ID_FIELD_NAME = "id";

    /**
     * The Id.
     */
    @DatabaseField(generatedId = true, columnName = ID_FIELD_NAME)
    protected int id;
    @DatabaseField(canBeNull = false)
    private String aucPan;
    @DatabaseField(canBeNull = false)
    private int panLen;
    @DatabaseField(canBeNull = false)
    private boolean panSeqFlg;
    @DatabaseField(dataType = DataType.BYTE, canBeNull = false)
    private byte panSeq;
    @DatabaseField(canBeNull = false)
    private String aucTornData;
    @DatabaseField(canBeNull = false)
    private int tornDataLen;

    /**
     * Instantiates a new Clss torn log.
     */
    public ClssTornLog() {
        //do nothing
    }

    /**
     * Gets auc pan.
     *
     * @return the auc pan
     */
    public String getAucPan() {
        return aucPan;
    }

    /**
     * Sets auc pan.
     *
     * @param aucPan the auc pan
     */
    public void setAucPan(String aucPan) {
        this.aucPan = aucPan;
    }

    /**
     * Gets pan len.
     *
     * @return the pan len
     */
    public int getPanLen() {
        return panLen;
    }

    /**
     * Sets pan len.
     *
     * @param panLen the pan len
     */
    public void setPanLen(int panLen) {
        this.panLen = panLen;
    }

    /**
     * Gets pan seq flg.
     *
     * @return the pan seq flg
     */
    public boolean getPanSeqFlg() {
        return panSeqFlg;
    }

    /**
     * Sets pan seq flg.
     *
     * @param panSeqFlg the pan seq flg
     */
    public void setPanSeqFlg(boolean panSeqFlg) {
        this.panSeqFlg = panSeqFlg;
    }

    /**
     * Gets pan seq.
     *
     * @return the pan seq
     */
    public byte getPanSeq() {
        return panSeq;
    }

    /**
     * Sets pan seq.
     *
     * @param panSeq the pan seq
     */
    public void setPanSeq(byte panSeq) {
        this.panSeq = panSeq;
    }

    /**
     * Gets auc torn data.
     *
     * @return the auc torn data
     */
    public String getAucTornData() {
        return aucTornData;
    }

    /**
     * Sets auc torn data.
     *
     * @param aucTornData the auc torn data
     */
    public void setAucTornData(String aucTornData) {
        this.aucTornData = aucTornData;
    }

    /**
     * Gets torn data len.
     *
     * @return the torn data len
     */
    public int getTornDataLen() {
        return tornDataLen;
    }

    /**
     * Sets torn data len.
     *
     * @param tornDataLen the torn data len
     */
    public void setTornDataLen(int tornDataLen) {
        this.tornDataLen = tornDataLen;
    }
}
