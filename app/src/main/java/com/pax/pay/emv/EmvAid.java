/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv;

import androidx.annotation.IntDef;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import com.alibaba.fastjson.annotation.JSONField;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import com.pax.eemv.entity.AidParam;
import com.pax.glwrapper.convert.IConvert;
import com.pax.glwrapper.convert.IConvert.EPaddingPosition;
import com.pax.pay.app.FinancialApplication;
import com.pax.settings.SysParam;
import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@DatabaseTable(tableName = "aid")
public class EmvAid implements Serializable {
    @IntDef({PART_MATCH, FULL_MATCH})
    @Retention(RetentionPolicy.SOURCE)
    public @interface SelType {
    }

    public static final int PART_MATCH = 0;
    public static final int FULL_MATCH = 1;

    public static final String ID_FIELD_NAME = "id";
    public static final String AID_FIELD_NAME = "aid";

    public static final String MASTER_CLSS_TAC_ONLINE = "F45084800C";
    public static final String MASTER_CLSS_TAC_DEFAULT = "F45084800C";
    public static final String MASTER_CONTACT_TAC_ONLINE = "FE50B8F800";
    public static final String MASTER_CONTACT_TAC_DEFAULT = "FE50B8F800";


    @DatabaseField(generatedId = true, columnName = ID_FIELD_NAME)
    @JSONField
    private int id;
    /**
     * name
     */
    @DatabaseField(canBeNull = false)
    private String appName;
    /**
     * aid
     */
    @DatabaseField(unique = true, canBeNull = false, columnName = AID_FIELD_NAME)
    private String aid;
    /**
     * PART_MATCH/FULL_MATCH
     */
    @DatabaseField(canBeNull = false)
    @SelType
    private int selFlag;
    /**
     * priority
     */
    @DatabaseField(canBeNull = false)
    private int priority;
    /**
     * if enable online PIN
     */
    @DatabaseField(canBeNull = false)
    private boolean onlinePin;
    /**
     * tag DF21
     */
    @DatabaseField(canBeNull = false)
    private long rdCVMLmt;
    /**
     * tag DF20
     */
    @DatabaseField(canBeNull = false)
    private long rdClssTxnLmt;
    /**
     * tag DF19
     */
    @DatabaseField(canBeNull = false)
    private long rdClssFLmt;

    /**
     * clss floor limit flag
     * 0- Deactivated
     * 1- Active and exist
     * 2- Active but not exist
     */
    @DatabaseField(canBeNull = false)
    @IntRange(from = 0, to = 2)
    private int rdClssFLmtFlg;
    /**
     * clss transaction limit flag
     * 0- Deactivated
     * 1- Active and exist
     * 2- Active but not exist
     */
    @DatabaseField(canBeNull = false)
    @IntRange(from = 0, to = 2)
    private int rdClssTxnLmtFlg;
    /**
     * clss CVM limit flag
     * 0- Deactivated
     * 1- Active and exist
     * 2- Active but not exist
     */
    @DatabaseField(canBeNull = false)
    @IntRange(from = 0, to = 2)
    private int rdCVMLmtFlg;

    /**
     * target percent
     */
    @DatabaseField(canBeNull = false)
    @IntRange(from = 0, to = 100)
    private int targetPer;
    /**
     * max target percent
     */
    @DatabaseField(canBeNull = false)
    @IntRange(from = 0, to = 100)
    private int maxTargetPer;
    /**
     * floor limit check flag
     * 0- don't check
     * 1- Check
     */
    @DatabaseField(canBeNull = false)
    @IntRange(from = 0, to = 1)
    private int floorLimitCheckFlg;
    /**
     * do random transaction selection
     */
    @DatabaseField(canBeNull = false)
    private boolean randTransSel;
    /**
     * velocity check
     */
    @DatabaseField(canBeNull = false)
    private boolean velocityCheck;
    /**
     * floor limit
     */
    @DatabaseField(canBeNull = false)
    private long floorLimit;
    /**
     * threshold
     */
    @DatabaseField(canBeNull = false)
    private long threshold;
    /**
     * TAC denial
     */
    @DatabaseField
    private String tacDenial;
    /**
     * TAC online
     */
    @DatabaseField
    private String tacOnline;
    /**
     * TAC default
     */
    @DatabaseField
    private String tacDefault;
    /**
     * acquirer id
     */
    @DatabaseField
    private String acquirerId;
    /**
     * dDOL
     */
    @DatabaseField
    private String dDOL;
    /**
     * tDOL
     */
    @DatabaseField
    private String tDOL;
    /**
     * application version
     */
    @DatabaseField
    private String version;
    /**
     * risk management data
     */
    @DatabaseField
    private String riskManageData;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getSelFlag() {
        return selFlag;
    }

    public void setSelFlag(@SelType int selFlag) {
        this.selFlag = selFlag;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean getOnlinePin() {
        return onlinePin;
    }

    public void setOnlinePin(boolean onlinePin) {
        this.onlinePin = onlinePin;
    }

    public long getRdCVMLmt() {
        return rdCVMLmt;
    }

    public void setRdCVMLmt(long rdCVMLmt) {
        this.rdCVMLmt = rdCVMLmt;
    }

    public long getRdClssTxnLmt() {
        return rdClssTxnLmt;
    }

    public void setRdClssTxnLmt(long rdClssTxnLmt) {
        this.rdClssTxnLmt = rdClssTxnLmt;
    }

    public long getRdClssFLmt() {
        return rdClssFLmt;
    }

    public void setRdClssFLmt(long rdClssFLmt) {
        this.rdClssFLmt = rdClssFLmt;
    }

    public int getRdClssFLmtFlg() {
        return rdClssFLmtFlg;
    }

    public void setRdClssFLmtFlg(@IntRange(from = 0, to = 2) int rdClssFLmtFlg) {
        this.rdClssFLmtFlg = rdClssFLmtFlg;
    }

    public int getRdClssTxnLmtFlg() {
        return rdClssTxnLmtFlg;
    }

    public void setRdClssTxnLmtFlg(@IntRange(from = 0, to = 2) int rdClssTxnLmtFlg) {
        this.rdClssTxnLmtFlg = rdClssTxnLmtFlg;
    }

    public int getRdCVMLmtFlg() {
        return rdCVMLmtFlg;
    }

    public void setRdCVMLmtFlg(@IntRange(from = 0, to = 2) int rdCVMLmtFlg) {
        this.rdCVMLmtFlg = rdCVMLmtFlg;
    }

    public int getTargetPer() {
        return targetPer;
    }

    public void setTargetPer(@IntRange(from = 0, to = 100) int targetPer) {
        this.targetPer = targetPer;
    }

    public int getMaxTargetPer() {
        return maxTargetPer;
    }

    public void setMaxTargetPer(@IntRange(from = 0, to = 100) int maxTargetPer) {
        this.maxTargetPer = maxTargetPer;
    }

    public int getFloorLimitCheckFlg() {
        return floorLimitCheckFlg;
    }

    public void setFloorLimitCheckFlg(@IntRange(from = 0, to = 1) int floorLimitCheckFlg) {
        this.floorLimitCheckFlg = floorLimitCheckFlg;
    }

    public boolean getRandTransSel() {
        return randTransSel;
    }

    public void setRandTransSel(boolean randTransSel) {
        this.randTransSel = randTransSel;
    }

    public boolean getVelocityCheck() {
        return velocityCheck;
    }

    public void setVelocityCheck(boolean velocityCheck) {
        this.velocityCheck = velocityCheck;
    }

    public long getFloorLimit() {
        return floorLimit;
    }

    public void setFloorLimit(long floorLimit) {
        this.floorLimit = floorLimit;
    }

    public long getThreshold() {
        return threshold;
    }

    public void setThreshold(long threshold) {
        this.threshold = threshold;
    }

    public String getTacDenial() {
        return tacDenial;
    }

    public void setTacDenial(String tacDenial) {
        this.tacDenial = tacDenial;
    }

    public String getTacOnline() {
        return tacOnline;
    }

    public void setTacOnline(String tacOnline) {
        this.tacOnline = tacOnline;
    }

    public String getTacDefault() {
        return tacDefault;
    }

    public void setTacDefault(String tacDefault) {
        this.tacDefault = tacDefault;
    }

    public String getAcquirerId() {
        return acquirerId;
    }

    public void setAcquirerId(String acquirerId) {
        this.acquirerId = acquirerId;
    }

    public String getDDOL() {
        return dDOL;
    }

    public void setDDOL(String dDOL) {
        this.dDOL = dDOL;
    }

    public String getTDOL() {
        return tDOL;
    }

    public void setTDOL(String tDOL) {
        this.tDOL = tDOL;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRiskManageData() {
        return riskManageData;
    }

    public void setRiskManageData(String riskManageData) {
        this.riskManageData = riskManageData;
    }

    /***************************
     * EmvAidParam to AidParam
     ***********************************/
    @NonNull
    public static List<AidParam> toAidParams(boolean isClssTxn) {
        IConvert convert = FinancialApplication.getConvert();
        List<AidParam> list = new LinkedList<>();
        List<EmvAid> aidList = FinancialApplication.getEmvDbHelper().findAllAID();
        if (aidList == null) {
            return new ArrayList<>(0);
        }
        for (EmvAid emvAidParam : aidList) {
            //In order to pass Master authorization for Contact and Contactless
            if (emvAidParam.getAid().substring(0, 10).equals("A000000004")
                    || emvAidParam.getAid().equals("A0000000101030")) {
                if (isClssTxn) {
                    emvAidParam.setTacOnline(MASTER_CLSS_TAC_ONLINE);
                    emvAidParam.setTacOnline(MASTER_CLSS_TAC_DEFAULT);
                } else {
                    emvAidParam.setTacOnline(MASTER_CONTACT_TAC_ONLINE);
                    emvAidParam.setTacOnline(MASTER_CONTACT_TAC_DEFAULT);
                }
            }
            AidParam aidParam = new AidParam();
            aidParam.setAppName(emvAidParam.getAppName());
            aidParam.setAid(convert.strToBcd(emvAidParam.getAid(), EPaddingPosition.PADDING_LEFT));
            aidParam.setSelFlag((byte) emvAidParam.getSelFlag());
            aidParam.setPriority((byte) emvAidParam.getPriority());
            aidParam.setOnlinePin(emvAidParam.getOnlinePin());
            aidParam.setRdCVMLmt(emvAidParam.getRdCVMLmt());
            aidParam.setRdClssTxnLmt(emvAidParam.getRdClssTxnLmt());
            aidParam.setRdClssFLmt(emvAidParam.getRdClssFLmt());
            aidParam.setRdClssFLmtFlag(emvAidParam.getRdClssFLmtFlg());
            aidParam.setRdClssTxnLmtFlag(emvAidParam.getRdClssTxnLmtFlg());
            aidParam.setRdCVMLmtFlag(emvAidParam.getRdCVMLmtFlg());
            aidParam.setFloorLimit(emvAidParam.getFloorLimit());
            aidParam.setFloorLimitCheckFlg(emvAidParam.getFloorLimitCheckFlg());
            aidParam.setThreshold(emvAidParam.getThreshold());
            aidParam.setTargetPer((byte) emvAidParam.getTargetPer());
            aidParam.setMaxTargetPer((byte) emvAidParam.getMaxTargetPer());
            aidParam.setRandTransSel(emvAidParam.getRandTransSel());
            aidParam.setVelocityCheck(emvAidParam.getVelocityCheck());
            aidParam.setTacDenial(convert.strToBcd(emvAidParam.getTacDenial(), EPaddingPosition.PADDING_LEFT));
            aidParam.setTacOnline(convert.strToBcd(emvAidParam.getTacOnline(), EPaddingPosition.PADDING_LEFT));
            aidParam.setTacDefault(convert.strToBcd(emvAidParam.getTacDefault(), EPaddingPosition.PADDING_LEFT));
            if (emvAidParam.getAcquirerId() != null) {
                aidParam.setAcquirerId(convert.strToBcd(emvAidParam.getAcquirerId(), EPaddingPosition.PADDING_LEFT));
            }
            if (emvAidParam.getDDOL() != null) {
                aidParam.setdDol(convert.strToBcd(emvAidParam.getDDOL(), EPaddingPosition.PADDING_LEFT));
            }
            if (emvAidParam.getTDOL() != null) {
                aidParam.settDol(convert.strToBcd(emvAidParam.getTDOL(), EPaddingPosition.PADDING_LEFT));
            }
            aidParam.setVersion(convert.strToBcd(emvAidParam.getVersion(), EPaddingPosition.PADDING_LEFT));
            if (emvAidParam.getRiskManageData() != null) {
                aidParam.setRiskManData(convert.strToBcd(emvAidParam.getRiskManageData(), EPaddingPosition.PADDING_LEFT));
            }
            list.add(aidParam);
        }
        return list;
    }

    public static void updateClssLimit() {
        long edcTXNLimitVisa = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_VISA);
        long edcFloorLimitVisa = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_FLOOR_LIMIT_VISA);
        long edcCVMLimitVisa = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_CVM_LIMIT_VISA);

        EmvAid visaCredit = FinancialApplication.getEmvDbHelper().findAID("A0000000031010");
        if (visaCredit != null) {
            visaCredit.setRdClssTxnLmt(edcTXNLimitVisa);
            visaCredit.setRdClssFLmt(edcFloorLimitVisa);
            visaCredit.setRdCVMLmt(edcCVMLimitVisa);
            FinancialApplication.getEmvDbHelper().updateAID(visaCredit);
        }

        EmvAid visaElectron = FinancialApplication.getEmvDbHelper().findAID("A0000000032010");
        if (visaElectron != null) {
            visaElectron.setRdClssTxnLmt(edcTXNLimitVisa);
            visaElectron.setRdClssFLmt(edcFloorLimitVisa);
            visaElectron.setRdCVMLmt(edcCVMLimitVisa);
            FinancialApplication.getEmvDbHelper().updateAID(visaElectron);
        }

        long edcTXNLimitMaster = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_MASTER);
        long edcFloorLimitMater =
                FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_FLOOR_LIMIT_MASTER);
        long edcCVMLimitMaster =
                FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_CVM_LIMIT_MASTER)
                        - 1L;//MasterCard 拍卡交易, 若 MasterCard CVM limit 設定為$1000, $1000 的MASTERCARD 拍卡交易, 是需要簽名的, 但現在沒有要求簽名. 到拍卡交易金額 = $1000.01, 才要求簽名. 建議可以在設置MASTERCARD CVM limit 參數做一點處理.當輸入$1000, 將CMV limit 參數存入變量時減0.01,例如 CVM limit = $1000 - $0.01. 這樣可以簡單將問題解決.

        EmvAid mChip = FinancialApplication.getEmvDbHelper().findAID("A0000000041010");
        if (mChip != null) {
            mChip.setRdClssTxnLmt(edcTXNLimitMaster);
            mChip.setRdClssFLmt(edcFloorLimitMater);
            mChip.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(mChip);
        }

        EmvAid mChip1 = FinancialApplication.getEmvDbHelper().findAID("A0000000041010D05601");
        if (mChip1 != null) {
            mChip1.setRdClssTxnLmt(edcTXNLimitMaster);
            mChip1.setRdClssFLmt(edcFloorLimitMater);
            mChip1.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(mChip1);
        }

        EmvAid mChip2 = FinancialApplication.getEmvDbHelper().findAID("A0000000041010C123456789");
        if (mChip2 != null) {
            mChip2.setRdClssTxnLmt(edcTXNLimitMaster);
            mChip2.setRdClssFLmt(edcFloorLimitMater);
            mChip2.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(mChip2);
        }

        EmvAid maestro1 = FinancialApplication.getEmvDbHelper().findAID("A0000000043060D05602");
        if (maestro1 != null) {
            maestro1.setRdClssTxnLmt(edcTXNLimitMaster);
            maestro1.setRdClssFLmt(edcFloorLimitMater);
            maestro1.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(maestro1);
        }

        EmvAid maestro2 = FinancialApplication.getEmvDbHelper().findAID("A0000000042203C123456789");
        if (maestro2 != null) {
            maestro2.setRdClssTxnLmt(edcTXNLimitMaster);
            maestro2.setRdClssFLmt(edcFloorLimitMater);
            maestro2.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(maestro2);
        }

        EmvAid maestro3 = FinancialApplication.getEmvDbHelper().findAID("A0000000043060C123456789");
        if (maestro3 != null) {
            maestro3.setRdClssTxnLmt(edcTXNLimitMaster);
            maestro3.setRdClssFLmt(edcFloorLimitMater);
            maestro3.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(maestro3);
        }

        EmvAid maestroUS = FinancialApplication.getEmvDbHelper().findAID("A0000000042203");
        if (maestroUS != null) {
            maestroUS.setRdClssTxnLmt(edcTXNLimitMaster);
            maestroUS.setRdClssFLmt(edcFloorLimitMater);
            maestroUS.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(maestroUS);
        }

        EmvAid maestroUS1 = FinancialApplication.getEmvDbHelper().findAID("A0000000042203D84002");
        if (maestroUS1 != null) {
            maestroUS1.setRdClssTxnLmt(edcTXNLimitMaster);
            maestroUS1.setRdClssFLmt(edcFloorLimitMater);
            maestroUS1.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(maestroUS1);
        }

        EmvAid mcc4 = FinancialApplication.getEmvDbHelper().findAID("A0000000046010");
        if (mcc4 != null) {
            mcc4.setRdClssTxnLmt(edcTXNLimitMaster);
            mcc4.setRdClssFLmt(edcFloorLimitMater);
            mcc4.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(mcc4);
        }

        EmvAid mcc5 = FinancialApplication.getEmvDbHelper().findAID("A0000000101030");
        if (mcc5 != null) {
            mcc5.setRdClssTxnLmt(edcTXNLimitMaster);
            mcc5.setRdClssFLmt(edcFloorLimitMater);
            mcc5.setRdCVMLmt(edcCVMLimitMaster);
            FinancialApplication.getEmvDbHelper().updateAID(mcc5);
        }

        long edcTXNLimitJcb = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_JCB);
        long edcFloorLimitJcb = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_FLOOR_LIMIT_JCB);
        long edcCVMLimitJcb = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_CVM_LIMIT_JCB);

        EmvAid jcbtest1 = FinancialApplication.getEmvDbHelper().findAID("A0000000651010");
        if (jcbtest1 != null) {
            jcbtest1.setRdClssTxnLmt(edcTXNLimitJcb);
            jcbtest1.setRdClssFLmt(edcFloorLimitJcb);
            jcbtest1.setRdCVMLmt(edcCVMLimitJcb);
            FinancialApplication.getEmvDbHelper().updateAID(jcbtest1);
        }

        EmvAid jcbtest2 = FinancialApplication.getEmvDbHelper().findAID("F1234567890123");
        if (jcbtest2 != null) {
            jcbtest2.setRdClssTxnLmt(edcTXNLimitJcb);
            jcbtest2.setRdClssFLmt(edcFloorLimitJcb);
            jcbtest2.setRdCVMLmt(edcCVMLimitJcb);
            FinancialApplication.getEmvDbHelper().updateAID(jcbtest2);
        }

        long edcTXNLimitCUP = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_CUP);
        long edcFloorLimitCUP = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_FLOOR_LIMIT_CUP);
        long edcCVMLimitCUP = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_CVM_LIMIT_CUP);

        EmvAid pboc1 = FinancialApplication.getEmvDbHelper().findAID("A000000333010101");
        if (pboc1 != null) {
            pboc1.setRdClssTxnLmt(edcTXNLimitCUP);
            pboc1.setRdClssFLmt(edcFloorLimitCUP);
            pboc1.setRdCVMLmt(edcCVMLimitCUP);
            FinancialApplication.getEmvDbHelper().updateAID(pboc1);
        }

        EmvAid pboc2 = FinancialApplication.getEmvDbHelper().findAID("A000000333010102");
        if (pboc2 != null) {
            pboc2.setRdClssTxnLmt(edcTXNLimitCUP);
            pboc2.setRdClssFLmt(edcFloorLimitCUP);
            pboc2.setRdCVMLmt(edcCVMLimitCUP);
            FinancialApplication.getEmvDbHelper().updateAID(pboc2);
        }

        EmvAid pboc3 = FinancialApplication.getEmvDbHelper().findAID("A000000333010103");
        if (pboc3 != null) {
            pboc3.setRdClssTxnLmt(edcTXNLimitCUP);
            pboc3.setRdClssFLmt(edcFloorLimitCUP);
            pboc3.setRdCVMLmt(edcCVMLimitCUP);
            FinancialApplication.getEmvDbHelper().updateAID(pboc3);
        }

        EmvAid pboc6 = FinancialApplication.getEmvDbHelper().findAID("A000000333010106");
        if (pboc6 != null) {
            pboc6.setRdClssTxnLmt(edcTXNLimitCUP);
            pboc6.setRdClssFLmt(edcFloorLimitCUP);
            pboc6.setRdCVMLmt(edcCVMLimitCUP);
            FinancialApplication.getEmvDbHelper().updateAID(pboc6);
        }

        EmvAid pboc8 = FinancialApplication.getEmvDbHelper().findAID("A000000333010108");
        if (pboc8 != null) {
            pboc8.setRdClssTxnLmt(edcTXNLimitCUP);
            pboc8.setRdClssFLmt(edcFloorLimitCUP);
            pboc8.setRdCVMLmt(edcCVMLimitCUP);
            FinancialApplication.getEmvDbHelper().updateAID(pboc8);
        }

        long edcTXNLimitAMEX = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_AMEX);
        long edcFloorLimitAMEX = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_FLOOR_LIMIT_AMEX);
        long edcCVMLimitAMEX = FinancialApplication.getSysParam().get(SysParam.NumberParam.EDC_CVM_LIMIT_AMEX);
        EmvAid amex1 = FinancialApplication.getEmvDbHelper().findAID("A00000002501");
        if (amex1 != null) {
            amex1.setRdClssTxnLmt(edcTXNLimitAMEX);
            amex1.setRdClssFLmt(edcFloorLimitAMEX);
            amex1.setRdCVMLmt(edcCVMLimitAMEX);
            FinancialApplication.getEmvDbHelper().updateAID(amex1);
        }
    }

    @Override
    public String toString() {
        return appName;
    }

    public static void load(List<EmvAid> aids) {
        //Load default aids
        FinancialApplication.getEmvDbHelper().insertAllAID(aids);

        EmvAid visaCredit = FinancialApplication.getEmvDbHelper().findAID("A0000000031010");
        if (visaCredit != null) {
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_CVM_LIMIT_VISA, (int) visaCredit.getRdCVMLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_FLOOR_LIMIT_VISA, (int) visaCredit.getRdClssFLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_VISA, (int) visaCredit.getRdClssTxnLmt());
        }

        EmvAid mChip = FinancialApplication.getEmvDbHelper().findAID("A0000000041010");
        if (mChip != null) {
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_CVM_LIMIT_MASTER, (int) mChip.getRdCVMLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_FLOOR_LIMIT_MASTER, (int) mChip.getRdClssFLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_MASTER, (int) mChip.getRdClssTxnLmt());
        }

        EmvAid jcbtest1 = FinancialApplication.getEmvDbHelper().findAID("A0000000651010");
        if (jcbtest1 != null) {
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_CVM_LIMIT_JCB, (int) jcbtest1.getRdCVMLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_FLOOR_LIMIT_JCB, (int) jcbtest1.getRdClssFLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_JCB, (int) jcbtest1.getRdClssTxnLmt());
        }

        EmvAid pboc1 = FinancialApplication.getEmvDbHelper().findAID("A000000333010101");
        if (pboc1 != null) {
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_CVM_LIMIT_CUP, (int) pboc1.getRdCVMLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_FLOOR_LIMIT_CUP, (int) pboc1.getRdClssFLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_CUP, (int) pboc1.getRdClssTxnLmt());
        }

        EmvAid amex1 = FinancialApplication.getEmvDbHelper().findAID("A00000002501");
        if (amex1 != null) {
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_CVM_LIMIT_AMEX, (int) amex1.getRdCVMLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_FLOOR_LIMIT_AMEX, (int) amex1.getRdClssFLmt());
            FinancialApplication.getSysParam().set(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_AMEX, (int) amex1.getRdClssTxnLmt());
        }
    }
}
