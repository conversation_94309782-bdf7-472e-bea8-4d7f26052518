/*
 * ============================================================================
 * COPYRIGHT
 *               Pax CORPORATION PROPRIETARY INFORMATION
 *    This software is supplied under the terms of a license agreement or
 *    nondisclosure agreement with Pax Corporation and may not be copied
 *    or disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 *  Module Date: 2017-8-7 4:54
 *  Module Author: liliang
 *  Description:
 *  ============================================================================
 */

package com.pax.pay.emv;

import android.util.Base64;
import android.util.Log;
import com.pax.abl.utils.TrackUtils;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.utils.Tools;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.TlvException;
import com.pax.glwrapper.convert.IConvert;
import com.pax.jemv.device.DeviceManager;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class EmvQr {
    public static final String TAG = EmvQr.class.getSimpleName();
    private static final String MASTER_MCHIP = "A0000000041010";
    private static final String MASTER_MAESTRO = "A0000000043060";
    private static final String AMEX_AID = "A000000025011001";

    private static final String QR_DEFAULT_AMOUNT = "000000000000";
    private static final String QR_DEFAULT_TRANS_DATE = "140101";
    private static final String QR_DEFAULT_COUNTRY_CODE = "0000";
    private static final String QR_DEFAULT_CRYPTOGRAM_INFO_DATA = "80";
    private static final String QR_DEFAULT_TVR = "8000000000";
    private static final String QR_DEFAULT_AIP = "0000";
    private static final String QR_DEFAULT_APP_VERSION = "0010";
    private static final String QR_DEFAULT_CVM_RESULT = "010002";
    private static boolean isAmexTagIntermittently;

    private EmvQr() {
    }

    public static int decodeEmvQr(TransData transData, String qrCode) {
        //The beginning of qr code must be "hQVDUFY"
        if (qrCode == null || qrCode.length() < 14 || (!"hQVDUFY".equals(qrCode.substring(0, 7)) && !"hQVDUFY"
                .equals(qrCode.substring(7, 14)))) {
            return TransResult.ERR_CARD_INVALID;
        }
        if ("hQVDUFY".equals(qrCode.substring(7, 14))) {
            qrCode = qrCode.substring(7);
        }
        try {
            byte[] bytes = Base64.decode(qrCode, Base64.DEFAULT);
            if (bytes == null) {
                return TransResult.ERR_INVALID_EMV_QR;
            }

            Log.d(TAG, Tools.bcd2Str(bytes));

            return decodeEmvQrBcd(transData, bytes);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
        return TransResult.ERR_INVALID_EMV_QR;
    }

    private static int decodeEmvQrBcd(TransData transData, byte[] qrCode) {
        if (qrCode == null || qrCode.length == 0) {
            return TransResult.ERR_INVALID_EMV_QR;
        }

        try {
            ITlv tlv = FinancialApplication.getPacker().getTlv();
            ITlv.ITlvDataObjList objList = tlv.unpack(qrCode);

            if (objList == null) {
                return TransResult.ERR_INVALID_EMV_QR;
            }

            byte[] bytes = objList.getValueByTag(0x85);
            if (isBytesEmpty(bytes)) {
                return TransResult.ERR_INVALID_EMV_QR;
            }

            //AMEX request there's no tag between 0x61 and 0x85.
            isAmexTagIntermittently = qrCode[bytes.length + 2] != 0x61;

            ITlv.ITlvDataObjList objList61;
            //support muliti Application template.
            while (true) {
                bytes = objList.getValueByTag(0x61);
                if (bytes == null) {
                    return TransResult.ERR_INVALID_EMV_QR;
                }

                objList61 = unpackAllTlv(bytes);
                if (objList61 == null) {
                    return TransResult.ERR_INVALID_EMV_QR;
                }

                byte[] bytes4F = objList61.getValueByTag(0x4F);
                if (bytes4F == null || bytes4F.length == 0) {
                    return TransResult.ERR_INVALID_EMV_QR;
                } else {
                    if (!isMasterMchip(Utils.bcd2Str(bytes4F)) && !isAMEX(Utils.bcd2Str(bytes4F))) {
                        objList.removeByTag(0x61);
                    } else {
                        break;
                    }
                }
            }

            return check61TagData(transData, objList61);
        } catch (Exception e) {
            Log.e(TAG, "Failed to decode EMV QR Code.", e);
        }
        return TransResult.ERR_INVALID_EMV_QR;
    }

    private static int check61TagData(TransData transData, ITlv.ITlvDataObjList objList) {
        //AID
        byte[] bytes = objList.getValueByTag(0x4F);
        if (isBytesEmpty(bytes)) {
            return TransResult.ERR_INVALID_EMV_QR;
        }
        transData.setAid(FinancialApplication.getConvert().bcdToStr(bytes));

        if (!isMasterMchip(transData.getAid()) && !isAMEX(transData.getAid())) {
            return TransResult.ERR_INVALID_EMV_QR;
        }

        if (isAMEX(transData.getAid())) {
            if (isAmexTagIntermittently) {
                return TransResult.ERR_INVALID_EMV_QR;
            }
            boolean isHasDuplicateTag = false;
            List<ITlv.ITlvDataObj> objs = objList.getDataObjectList();
            List<ITlv.ITlvDataObj> objstmp = new ArrayList<>();
            for (ITlv.ITlvDataObj object : objs) {
                for (ITlv.ITlvDataObj object2 : objstmp) {
                    if (object.getIntTag() != null && object2.getIntTag() != null && object2.getIntTag()
                            .equals(object.getIntTag())) {
                        isHasDuplicateTag = true;
                        break;
                    }
                }
                if (isHasDuplicateTag) {
                    return TransResult.ERR_INVALID_EMV_QR;
                }
                objstmp.add(object);
            }
        }

        //AppLabel
        bytes = objList.getValueByTag(TagsTable.APP_LABEL); //0x50
        if (bytes != null && bytes.length != 0) {
            try {
                transData.setEmvAppLabel(new String(bytes, "GBK"));
            } catch (UnsupportedEncodingException e) {
                Log.e(TAG, e.getMessage());
            }
        } else {
            transData.setEmvAppLabel(getAppLabel(transData.getAid()));
        }

        if (isAMEX(transData.getAid())) {
            //app expiration date
            bytes = objList.getValueByTag(0x9F08);
            if (bytes != null && bytes.length >= 2) {
                if (!Utils.bcd2Str(bytes).equals(QR_DEFAULT_APP_VERSION)) {
                    return TransResult.ERR_INVALID_EMV_QR;
                }
            }
        }

        saveCardInfo(transData, objList);

        //track2
        int ret = saveTrack2(transData, objList);
        if (ret != 0) {
            return ret;
        }
        //simple flow for refund
        if (transData.getTransType() == ETransType.REFUND) {
            return 0;
        }

        ret = checkAndSaveTvr(transData, objList);
        if (ret != 0) {
            return ret;
        }
        return createIccData(transData, objList);
    }

    private static ITlv.ITlvDataObjList unpackAllTlv(byte[] byte61) {
        if (byte61 == null || byte61.length == 0) {
            return null;
        }

        ITlv tlv = FinancialApplication.getPacker().getTlv();
        ITlv.ITlvDataObjList objList = null;
        try {
            objList = tlv.unpack(byte61);
        } catch (TlvException e) {
            e.printStackTrace();
        }
        byte[] tag63 = null;
        if (objList != null) {
            tag63 = objList.getValueByTag(0x63);
        }
        if (tag63 != null && tag63.length != 0) {
            byte[] byteAll = new byte[byte61.length + tag63.length];
            System.arraycopy(byte61, 0, byteAll, 0, byte61.length);
            System.arraycopy(tag63, 0, byteAll, byte61.length, tag63.length);
            try {
                objList = tlv.unpack(byteAll);
                return objList;
            } catch (TlvException e) {
                e.printStackTrace();
            }
        }
        return objList;
    }

    //save track2
    private static int saveTrack2(TransData transData, ITlv.ITlvDataObjList objList) {
        byte[] bytes = objList.getValueByTag(TagsTable.TRACK2);
        String pan;
        if (bytes == null || bytes.length == 0) {
            if (transData.getPan() == null
                    || transData.getPan().isEmpty()
                    || transData.getExpDate() == null
                    || transData.getExpDate().isEmpty()) {
                return TransResult.ERR_INVALID_EMV_QR;
            } else {
                if (transData.getPan().endsWith("F")) {
                    transData.setPan(transData.getPan().substring(0, transData.getPan().length() - 1));
                }
                pan = transData.getPan();
            }
        } else {
            String track2Data = Tools.bcd2Str(bytes);
            if (track2Data.endsWith("F")) {
                track2Data = track2Data.substring(0, track2Data.length() - 1);
            }
            transData.setTrack2(track2Data);
            //expiry date
            if (transData.getExpDate() == null
                    || transData.getExpDate().isEmpty()
                    || transData.getPan() == null
                    || transData.getPan().isEmpty()) {
                transData.setExpDate(TrackUtils.getExpDate(track2Data));
            }
            //pan
            if (transData.getPan() == null || transData.getPan().isEmpty()) {
                pan = TrackUtils.getPan(track2Data);
                if (pan == null || pan.isEmpty()) {
                    return TransResult.ERR_INVALID_EMV_QR;
                }
                transData.setPan(pan);
            } else {
                pan = transData.getPan();
            }
        }
        //check pan
        long temp;
        if (pan.substring(0, 1).equals("5")) {//MASTERCARD
            temp = Long.parseLong(pan.substring(0, 2));
            if (temp < 51 || temp > 55) {
                return TransResult.ERR_INVALID_EMV_QR;
            }
        } else if (pan.substring(0, 1).equals("2")) {//MASTERCARD
            temp = Long.parseLong(pan.substring(0, 6));
            if (temp < 222100 || temp > 272099) {
                return TransResult.ERR_INVALID_EMV_QR;
            }
        } else if (pan.substring(0, 1).equals("3")) {//AMEX
            temp = Long.parseLong(pan.substring(0, 2));
            if (temp != 34 && temp != 37) {
                return TransResult.ERR_INVALID_EMV_QR;
            }
        } else {
            return TransResult.ERR_INVALID_EMV_QR;
        }

        return 0;
    }

    private static int createIccData(TransData transData, ITlv.ITlvDataObjList objList) {
        ITlv tlv = FinancialApplication.getPacker().getTlv();
        ITlv.ITlvDataObjList list = tlv.createTlvDataObjectList();
        List<QrTag> qrTagList;
        if (isMasterMchip(transData.getAid())) {
            qrTagList = QrTag.genMCQr55Tags();
            for (QrTag i : qrTagList) {
                ITlv.ITlvDataObj tag = tlv.createTlvDataObject();
                tag.setTag(i.getTag());
                byte[] temp;
                if (i.getTag() == 0x84) {
                    tag.setValue(Tools.str2Bcd(transData.getAid()));
                } else if (0x9B == i.getTag()) {
                    tag.setValue(new byte[2]);
                } else if (0x9F09 == i.getTag()) {
                    tag.setValue(new byte[2]);
                } else if (0x9F1E == i.getTag()) {
                    byte[] sn = new byte[10];
                    byte[] snData = new byte[8];
                    DeviceManager.getInstance().readSN(sn);
                    System.arraycopy(sn, 0, snData, 0, Math.min(snData.length, sn.length));
                    tag.setValue(snData);
                } else if (0x9F35 == i.getTag()) {
                    tag.setValue(Utils.str2Bcd("22"));
                } else if (0x9F41 == i.getTag()) {
                    tag.setValue(new byte[3]);
                } else if (0x9F53 == i.getTag()) {
                    tag.setValue(Utils.str2Bcd("52"));
                } else {
                    temp = objList.getValueByTag(i.getTag());
                    if (temp == null || temp.length == 0) {
                        if (i.getOption() == QrTag.DE55_MUST_SET) {
                            return TransResult.ERR_INVALID_EMV_QR;
                        }
                        temp = getMasterCardDefaultValue(i.getTag());
                        if (temp == null || temp.length == 0) {
                            continue;
                        }
                    }
                    if (i.getTag() == 0x95) {
                        temp[3] = (byte) (temp[3] | 0x01);
                    }
                    tag.setValue(temp);
                }
                list.addDataObj(tag);
            }

            byte[] f55Data;
            try {
                f55Data = tlv.pack(list);
            } catch (TlvException e) {
                Log.e(TAG, "", e);
                return TransResult.ERR_INVALID_EMV_QR;
            }

            //icc data length can not be over 255
            if (f55Data.length > 255) {
                return TransResult.ERR_INVALID_EMV_QR;
            }
            transData.setSendIccData(FinancialApplication.getConvert().bcdToStr(f55Data));
        } else if (isAMEX(transData.getAid())) {
            qrTagList = QrTag.genAMEXQr55Tags();
            Map<Integer, String> values = new LinkedHashMap<>();

            for (QrTag i : qrTagList) {
                byte[] value = objList.getValueByTag(i.getTag());
                if (value == null || value.length == 0) {
                    if (i.getOption() == QrTag.DE55_MUST_SET) {
                        return TransResult.ERR_INVALID_EMV_QR;
                    }
                    value = getAmexDefaultValue(i.getTag());
                    if (value == null || value.length == 0) {
                        continue;
                    }
                }
                values.put(i.getTag(), Tools.bcd2Str(value));
            }

            StringBuilder sb = new StringBuilder();
            for (Map.Entry<Integer, String> entry : values.entrySet()) {
                if (entry.getKey() == 0x9F10 || entry.getKey() == 0x9F06) {
                    int vl = entry.getValue().length() / 2;
                    //                    String len = Integer.toString(vl).toUpperCase();
                    //                    if (len.length() == 1) {
                    //                        len = "0" + len;
                    //                    }
                    String len = String.format("%02x", vl);
                    sb.append(len);
                    sb.append(entry.getValue());
                } else {
                    sb.append(entry.getValue());
                }
            }

            //Issuer Action Code
            sb.append("000000000080000000008000000000");

            String header = "C1C7D5E2";
            String version = "0001";
            String f55Data = header + version + sb.toString();
            if (f55Data.length() > 256) {
                return TransResult.ERR_PACKET;
            }
            transData.setSendIccData(f55Data);
        }

        return 0;
    }

    //when tag is 0x9F02, or 0x9F03, or 0x9A, or 0x5F2A, or 0x9F1A, or 0x9C, or 0x95
    //set the default value if not get the value from qr code
    private static byte[] getMasterCardDefaultValue(int tag) {
        byte[] temp = new byte[] {};
        if (tag == TagsTable.AMOUNT || tag == TagsTable.AMOUNT_OTHER) {  //0x9F02 0x9F03
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_AMOUNT, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.TRANS_DATE) { //0x9A
            temp = FinancialApplication.getConvert()
                    .strToBcd("000000", IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.CURRENCY_CODE || tag == TagsTable.COUNTRY_CODE) { //0x5F2A  0x9F1A
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_COUNTRY_CODE, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.TRANS_TYPE) {  //0x9C
            //transaction type
            temp = new byte[] { 0x00 };
        } else if (tag == TagsTable.TVR) {  //0x95
            //terminal verification results
            temp = FinancialApplication.getConvert()
                    .strToBcd("0000000100", IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == 0x9F34) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_CVM_RESULT, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == 0x9F33) {
            temp = FinancialApplication.getConvert()
                    .strToBcd("E0B8C8", IConvert.EPaddingPosition.PADDING_RIGHT);
        }

        return temp;
    }

    //when tag is 0x9F02, or 0x9F03, or 0x9A, or 0x5F2A, or 0x9F1A, or 0x9C, or 0x95
    //set the default value if not get the value from qr code
    private static byte[] getAmexDefaultValue(int tag) {
        byte[] temp = new byte[] {};
        if (tag == TagsTable.AMOUNT || tag == TagsTable.AMOUNT_OTHER) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_AMOUNT, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.TRANS_DATE) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_TRANS_DATE, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.CURRENCY_CODE || tag == TagsTable.COUNTRY_CODE) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_COUNTRY_CODE, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.TRANS_TYPE) {
            //transaction type
            temp = new byte[] { 0x00 };
        } else if (tag == TagsTable.CRYPTOGRAM_INFO_DATA) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_CRYPTOGRAM_INFO_DATA, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.AIP) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_AIP, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.TVR) {
            //terminal verification results
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_TVR, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.AID) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(AMEX_AID, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.APP_VER) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_APP_VERSION, IConvert.EPaddingPosition.PADDING_RIGHT);
        } else if (tag == TagsTable.CVM_RESULT) {
            temp = FinancialApplication.getConvert()
                    .strToBcd(QR_DEFAULT_CVM_RESULT, IConvert.EPaddingPosition.PADDING_RIGHT);
        }

        return temp;
    }

    //check master
    private static boolean isMasterMchip(String aid) {
        return aid != null && (aid.toUpperCase().contains(MASTER_MCHIP) || aid.toUpperCase()
                .contains(MASTER_MAESTRO));
    }

    private static boolean isBytesEmpty(byte[] bytes) {
        return bytes == null || bytes.length == 0;
    }

    private static void saveCardInfo(TransData transData, ITlv.ITlvDataObjList objList) {
        //PAN
        byte[] bytes = objList.getValueByTag(0x5A);
        if (bytes != null && bytes.length != 0) {
            transData.setPan(FinancialApplication.getConvert().bcdToStr(bytes));
        }
        //app expiration date
        bytes = objList.getValueByTag(0x5F24);
        if (bytes != null && bytes.length >= 2) {
            transData.setExpDate(Tools.bcd2Str(bytes).substring(0, 4));
        }

        //Last 4 digital of PAN
        bytes = objList.getValueByTag(0x9F25);
        if (bytes != null && bytes.length != 0) {
            transData.setMcQrcodeLast4Pan(FinancialApplication.getConvert().bcdToStr(bytes));
        }
    }

    //check and save icc data
    private static int checkAndSaveTvr(TransData transData, ITlv.ITlvDataObjList objList) {
        // TVR
        byte[] bytes = objList.getValueByTag(0x95);
        if (bytes != null && bytes.length != 0) {
            String tvr = Tools.bcd2Str(bytes);
            transData.setTvr(tvr.substring(0, 7) + "1" + tvr.substring(8));
        } else {
            transData.setTvr("0000000100");
        }

        // ATC
        bytes = objList.getValueByTag(0x9F36);
        if (bytes == null || bytes.length == 0) {
            return TransResult.ERR_INVALID_EMV_QR;
        }
        transData.setTvr(Tools.bcd2Str(bytes));

        //AppCrypto
        bytes = objList.getValueByTag(0x9F26);
        if (bytes == null || bytes.length == 0) {
            return TransResult.ERR_INVALID_EMV_QR;
        }
        transData.setArqc(Tools.bcd2Str(bytes));
        transData.setTc(Tools.bcd2Str(bytes));

        bytes = objList.getValueByTag(0x5F34);
        if (bytes == null || bytes.length == 0) {
            return TransResult.ERR_INVALID_EMV_QR;
        }
        transData.setCardSerialNo(Tools.bcd2Str(bytes));

        return 0;
    }

    //get App Label
    private static String getAppLabel(String aid) {
        if (aid.toUpperCase().contains(MASTER_MCHIP)) {
            return "MASTERCARD";
        } else if (aid.toUpperCase().contains(MASTER_MAESTRO)) {
            return "MAESTRO";
        } else if (aid.toUpperCase().contains(AMEX_AID)) {
            return "AMERICAN EXPRESS";
        }
        return null;
    }

    //check AMEX
    private static boolean isAMEX(String aid) {
        return aid != null && (aid.toUpperCase().contains(AMEX_AID));
    }
}
