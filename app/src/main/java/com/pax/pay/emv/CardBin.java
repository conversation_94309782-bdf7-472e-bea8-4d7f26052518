/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;

@DatabaseTable(tableName = "card_bin")
public class CardBin {
    public static final String ID_FIELD_NAME = "id";
    public static final String BIN_FIELD_NAME = "card_bin";
    public static final String BIN_LOW_FIELD_NAME = "card_bin_low";
    public static final String BIN_HIGH_FIELD_NAME = "card_bin_high";

    @DatabaseField(generatedId = true, columnName = ID_FIELD_NAME)
    protected int id;
    // 卡号
    @DatabaseField(unique = true, columnName = BIN_FIELD_NAME)
    protected String bin;
    @DatabaseField(unique = true, columnName = BIN_LOW_FIELD_NAME)
    protected String binLow;
    @DatabaseField(unique = true, columnName = BIN_HIGH_FIELD_NAME)
    protected String binHigh;
    @DatabaseField
    protected int cardNoLen;
    @DatabaseField
    protected String dateTime;
    @DatabaseField
    protected String currencyCode;
    @DatabaseField
    protected String totalBlock;

    public CardBin() {
    }

    public CardBin(String bin, int cardNoLen) {
        this.bin = bin;
        this.cardNoLen = cardNoLen;
    }

    /**
     * Instantiates a new Card bin.
     *
     * @param binLow       the bin low
     * @param binHigh      the bin high
     * @param cardNoLen    the card no len
     * @param dateTime     the date time
     * @param currencyCode the currency code
     * @param totalBlock   the total block
     */
    public CardBin(String bin, String binLow, String binHigh, int cardNoLen, String dateTime, String currencyCode, String totalBlock) {
        this.bin = bin;
        this.binLow = binLow;
        this.binHigh = binHigh;
        this.cardNoLen = cardNoLen;
        this.dateTime = dateTime;
        this.currencyCode = currencyCode;
        this.totalBlock = totalBlock;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getBin() {
        return bin;
    }

    public void setBin(String bin) {
        this.bin = bin;
    }

    public String getBinLow() {
        return binLow;
    }

    public void setBinLow(String binLow) {
        this.binLow = binLow;
    }

    public String getBinHigh() {
        return binHigh;
    }

    public void setBinHigh(String binHigh) {
        this.binHigh = binHigh;
    }

    public int getCardNoLen() {
        return cardNoLen;
    }

    public void setCardNoLen(int cardNoLen) {
        this.cardNoLen = cardNoLen;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getTotalBlock() {
        return totalBlock;
    }

    public void setTotalBlock(String totalBlock) {
        this.totalBlock = totalBlock;
    }

}
