/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-1-3
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv;

import java.util.ArrayList;
import java.util.List;

class QrTag {

    private int tag;
    private byte option;

    public static final byte DE55_MUST_SET = 0x10;// 必须存在
    public static final byte DE55_OPT_SET = 0x20;// 可选择存在

    private QrTag(int tag, byte option) {
        this.tag = tag;
        this.option = option;
    }

    /**
     * Gen qr 55 tags list.
     *
     * @return the list
     */
    static List<QrTag> genMCQr55Tags() {
        List<QrTag> qrTagList = new ArrayList<>();
        qrTagList.add(new QrTag(0x57, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x5F2A, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x5F34, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x82, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x84, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x95, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9A, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9B, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9C, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F02, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F03, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F08, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F09, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F10, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F1A, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F1E, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F26, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F27, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F33, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F34, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F35, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F36, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F37, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F41, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F53, DE55_OPT_SET));
        return qrTagList;
    }

    /**
     *
     */
    static List<QrTag> genAMEXQr55Tags() {
        List<QrTag> qrTagList = new ArrayList<>();
        qrTagList.add(new QrTag(0x9F26, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F10, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F37, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F36, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x95, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9A, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9C, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F02, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x5F2A, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F1A, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x82, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F03, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x5F34, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F27, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F06, DE55_OPT_SET));
        qrTagList.add(new QrTag(0x9F08, DE55_MUST_SET));
        qrTagList.add(new QrTag(0x9F34, DE55_OPT_SET));

        //        qrTagList.add(new QrTag(0x9F09, DE55_OPT_SET));
//        qrTagList.add(new QrTag(0x9F34, DE55_OPT_SET));
//        qrTagList.add(new QrTag(0x84, DE55_OPT_SET));
//        qrTagList.add(new QrTag(0x9F1E, DE55_OPT_SET));
//        qrTagList.add(new QrTag(0x9F33, DE55_OPT_SET));
//        qrTagList.add(new QrTag(0x9F35, DE55_OPT_SET));
//        qrTagList.add(new QrTag(0x9F53, DE55_OPT_SET));

        return qrTagList;
    }

    public int getTag() {
        return tag;
    }

    public byte getOption() {
        return option;
    }
}
