/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.emv;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.ConditionVariable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.commonbusiness.card.TrackUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.eemv.EmvImpl;
import com.pax.eemv.IEmv;
import com.pax.eemv.IEmvListener;
import com.pax.eemv.entity.Amounts;
import com.pax.eemv.entity.CandList;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.enums.EOnlineResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.eemv.utils.Tools;
import com.pax.eventbus.SearchCardEvent;
import com.pax.jemv.clcommon.ByteArray;
import com.pax.jemv.clcommon.RetCode;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionSelectCurrency;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;

import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.pax.jemv.clcommon.RetCode.EMV_OK;
import static com.pax.jemv.clcommon.RetCode.ICC_CMD_ERR;
import static com.pax.pay.constant.Constants.ISSUER_RUPAY;

public class EmvListenerImpl extends EmvBaseListenerImpl implements IEmvListener {

    private static final String TAG = EmvListenerImpl.class.getSimpleName();
    private IEmv emv;
    private EOnlineResult dccResult;

    public EmvListenerImpl(Context context, IEmv emv, TransData transData, TransProcessListener listener) {
        super(context, emv, transData, listener);
        this.emv = emv;
    }

    @Override
    public final int onCardHolderPwd(final boolean isOnlinePin, final int offlinePinLeftTimes, byte[] pinData) {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }
        cv = new ConditionVariable();
        intResult = 0;

        if (pinData != null && pinData[0] != 0) {
            if (pinData[0] == 1) {
                LogUtils.e(TAG, "enter pin timeout");
                return RetCode.EMV_TIME_OUT;
            } else {
                return pinData[0];
            }
        }
        enterPin(isOnlinePin, offlinePinLeftTimes);
        LogUtils.logTemp("1 onCardHolderPwd cv block: "+cv);
        cv.block();// for the Offline pin case, block it for make sure the PIN activity is ready, otherwise, may get the black screen.
        return intResult;
    }

    @Override
    public final boolean onChkExceptionFile() {
        byte[] track2 = emv.getTlv(TagsTable.TRACK2);
        String strTrack2 = TrackUtils.getTrack2FromTag57(track2);
        // 卡号
        String pan = TrackUtils.getPan(strTrack2);
        boolean ret = GreendaoHelper.getCardBinBlackHelper().isBlack(pan);
        if (ret) {
            transProcessListener.onShowErrMessage(context.getString(R.string.emv_card_in_black_list), Constants.FAILED_DIALOG_SHOW_TIME, false);
            return true;
        }
        return false;
    }

    //RUPAY认证要求，Offline Pin输入错误时，需要提示错误信息
    @Override
    public void onShowOfflinPinError(byte[] pinData) {

        //pinData首字节为1时表示输PIN超时异常，此时不提示错误信息
        if (transProcessListener != null && !(pinData != null && pinData[0] == 1)) {
            transProcessListener.onShowErrMessage("INCORRECT PIN", Constants.FAILED_DIALOG_SHOW_TIME, false);
        }
    }

    //RUPAY认证要求，reversal时需上送脚本数据tag 9F5B
    @Override
    public void onSaveIssuerScriptResults(ByteArray issuerScriptResults, int retCode) {
        if (ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName()) && issuerScriptResults.length > 0) {
            transData.setIssuerScriptResults(Tools.bcd2Str(issuerScriptResults.data).substring(0, issuerScriptResults.length * 2));
        }
        if (retCode == ICC_CMD_ERR) {
            transData.setDupReason("E2"); //第二次GAC失败时，reversal的39域上送E2
        } else if (retCode != EMV_OK) {
            transData.setDupReason("E1"); //无后台返回时，reversal的39域上送E1
        }
    }

    //处理NOCVM的case
    @Override
    public void onSetSignatureFlag(int signFlag) {
        if (0 == signFlag) {
            transData.setSignFree(true);
        } else {
            transData.setSignFree(false);
        }
    }

    @Override
    public boolean onCheckCardStatusUpdate(byte[] value91) {
        if (ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName())) {
            return checkCardStatusUpdate(value91);
        }
        return false;
    }

    @Override
    public void onUpdateRupayAppLabelAndAppName(byte[] valueDF8108) {
        if (ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName())) {
            updateRupayAppLabelAndAppName(valueDF8108);
        }
    }

    @Override
    public void onSetOfflinePinResult() {
        //处理offline 输入超过次数限制且PIN错误时，不需要打印Pin Verified OK
        //Byte3 bit 8: cardholder verification was not successful
        //Byte3 bit 6: PIN try limit exceeded
        byte[] tvr = emv.getTlv(TagsTable.TVR);
        if ((tvr[2] & 0xA0) == 160) {
            transData.setHasPin(false);
        }
    }

    @Override
    public final int onConfirmCardNo(final String cardno) {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }

        Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(cardno);
        if (issuer != null && FinancialApplication.getAcqManager().isIssuerSupported(issuer)) {
            transData.setIssuer(issuer);
        } else {
            intResult = EEmvExceptions.EMV_ERR_DATA.getErrCodeFromBasement();
            return intResult;
        }
        if (transData.getTransType().equals(ETransType.PREAUTH) && !issuer.isAllowPreAuth()) {
            intResult = EEmvExceptions.EMV_ERR_DENIAL.getErrCodeFromBasement();
            return intResult;
        }

        //检查是否支持Cash类交易，Amex卡片不支持
        if (!Component.isSupportCash(transData)) {
            intResult = EEmvExceptions.EMV_ERR_DATA.getErrCodeFromBasement();
            return intResult;
        }

        if (Component.isQsparcTrans(transData) && !ISSUER_RUPAY.equalsIgnoreCase(transData.getIssuer().getName())) {
            intResult = EEmvExceptions.EMV_ERR_UNSUPPORTED.getErrCodeFromBasement();
            return intResult;
        }

        cv = new ConditionVariable();

        byte[] holderNameBCD = emv.getTlv(0x5F20);
        if (holderNameBCD == null) {
            holderNameBCD = " ".getBytes();
        }
        String holderName = " ";
        try {
            holderName = new String(holderNameBCD, "GB2312"); //兼容cardholder name为中文的情况
            transData.setCardHolderName(holderName);
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException : ", e);
        }
        byte[] expDateBCD = emv.getTlv(0x5F24);
        String expDate = ConvertHelper.getConvert().bcdToStr(expDateBCD);
        float percent = 0; //AET_247
        FinancialApplication.getApp().doEvent(new SearchCardEvent(SearchCardEvent.Status.ICC_UPDATE_CARD_INFO, new CardInfo(cardno, holderName, expDate, percent)));

        if (!Component.isDemo() &&
                (!Issuer.validPan(transData.getIssuer(), cardno) ||
                        !Issuer.validCardExpiry(expDate))) {
            intResult = EEmvExceptions.EMV_ERR_DATA.getErrCodeFromBasement();
            return intResult;
        }

        //RUPAY认证时，卡片过期需做界面提示，并继续执行交易
        if (!Issuer.validCardExpiryWithoutCondition(expDate) && transProcessListener != null) {
            if (transProcessListener != null) {
                transProcessListener.onShowErrMessage("CARD EXPIRED", Constants.FAILED_DIALOG_SHOW_TIME, false);
            }
        }


        FinancialApplication.getApp().doEvent(new SearchCardEvent(SearchCardEvent.Status.ICC_CONFIRM_CARD_NUM));

        cv.block();

        return intResult;
    }

    @Override
    public int inputInfo() {
        EmvTransProcess.saveCardInfoAndCardSeq(emv, transData);
        if (transData.getTransType() != ETransType.REFUND) {//目前只有refund需要输入RRN
            return TransResult.SUCC;
        }
        cv = new ConditionVariable();
        intResult = inputRefundRRN();//refundRRN
        cv.block();
        return intResult;
    }

    @Override
    public void performBinValidation(String pan, String expDate) throws EmvException {
        if (ETransType.BANK_EMI_SALE != transData.getTransType() &&
                ETransType.BRAND_EMI_SALE != transData.getTransType()) {
            return;
        }
        cv = new ConditionVariable();
        doBinValidation(pan, expDate);
        cv.block();
    }

    @Override
    public void performCheckCardBin(String pan, String expDate) throws EmvException {
        if (ETransType.BANK_EMI_SALE != transData.getTransType() &&
                ETransType.BRAND_EMI_SALE != transData.getTransType()) {
            return;
        }
        cv = new ConditionVariable();
        doCheckCardBin(pan, expDate, transData.isBankSwipeFlow());
        cv.block();
    }

    @Override
    public final Amounts onGetAmounts() {
        Amounts amt = new Amounts();


        if (transData.getTransType().equals(ETransType.SALE_CASH) || transData.getTransType().equals(ETransType.CASH_ONLY)) {
            amt.setTransAmount(transData.getAmount());
            amt.setCashBackAmount(transData.getCashAmount());
        } else {
            amt.setTransAmount(transData.getAmount());
        }
        return amt;
    }

    @Override
    protected boolean updateTransDataFromKernel() throws PedDevException {
        if (!super.updateTransDataFromKernel()) {
            return false;
        }
        EmvTransProcess.saveCardInfoAndCardSeq(emv, transData);
        return true;
    }

    @Override
    public EOnlineResult onOnlineProc() throws PedDevException {
        String name = "";
        if (transData.getIssuer() != null) {
            name = transData.getIssuer().getName();
        }
        return onlineProc((Utils.allowDcc(TransData.EnterMode.INSERT)) && !ISSUER_RUPAY.equalsIgnoreCase(name));
    }

    @Override
    public final int onWaitAppSelect(final boolean isFirstSelect, final List<CandList> candList) {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }
        cv = new ConditionVariable();
        // ignore Sonar's lambda suggestion cuz the Sonar runs JAVA8, but EDC runs JAVA7,
        // there are same cases, ignore them as well.
        FinancialApplication.getApp().runOnUiThread(new SelectAppRunnable(isFirstSelect, candList));

        cv.block();
        return intResult;
    }

    /**
     * set timeout action
     */
    public void onTimeOut() {
        intResult = EEmvExceptions.EMV_ERR_TIMEOUT.getErrCodeFromBasement();
        EmvImpl.isTimeOut = true;
        cv.open();
    }

    @Override
    public EOnlineResult onDccProcess() throws PedDevException {
        boolean isDccTransType = transData.getTransType() == ETransType.SALE || transData.getTransType() == ETransType.PREAUTH;
        // 非Sale和PreAuth或者非DCC直接跳过当前流程
        if ((transData.getRate() == null && isDccTransType) || !isDccTransType) {
            return EOnlineResult.APPROVE;
        }
        if (transData.getRate() != null) {
            ActionSelectCurrency selectCurrencyAction = new ActionSelectCurrency(new AAction.ActionStartListener() {
                @Override
                public void onStart(AAction action) {
                    ((ActionSelectCurrency) action).setParam(context, transData.getTransType().getTransName(),
                            transData.getAmount(), transData.getRate(), transData.getInternationalCurrencyAmount(),
                            transData.getLocalCurrency(), transData.getInternationalCurrency(), transData.getMakeUp(), false);
                }
            });
            selectCurrencyAction.setEndListener(new AAction.ActionEndListener() {
                @Override
                public void onEnd(AAction action, ActionResult result) {
                    if (result.getRet() == TransResult.ERR_TIMEOUT) {
                        transData.setDcc(false);
                        dccResult = EOnlineResult.FAILED;
                    } else {
                        boolean selectDcc = result.getData() != Utils.LOCAL_CURRENCY;
                        transData.setDcc(selectDcc);
                    }
                    cv.open();
                }
            });
            selectCurrencyAction.execute();
            cv = new ConditionVariable();
            cv.close();
            cv.block();
            if(  dccResult == EOnlineResult.FAILED){
                return dccResult;
            }
            // Sale和 PreAuth的Advice和原交易的TraceNo会自增，因此需要重新设置
            transData.setTraceNo(Component.getTransNo());
            ETransType tempTranstype;
            ETransType realTranstype = transData.getTransType();
            if (realTranstype == ETransType.SALE) {
                tempTranstype = ETransType.SALE_ADVICE;
            } else {
                tempTranstype = ETransType.PREAUTH_ADVICE;
            }
            transData.setOrigTransType(realTranstype);
            transData.setTransType(tempTranstype);
            dccResult = onOnlineProc();
            // Sale Advice和PreAuth Advice两种情况下的连接失败需要标记Reversal状态，此处保留当前的TransType类型是为了方便后面流程拿到该类型进行筛选
            if (!((transData.getTransType() == ETransType.SALE_ADVICE || transData.getTransType() == ETransType.PREAUTH_ADVICE)
                    && (dccResult== EOnlineResult.ERR_CONNECT))) {
                transData.setTransType(realTranstype);
                transData.setOrigTransType(null);
            }
        } else {
            // 汇率为null证明失败，拒绝
            dccResult = EOnlineResult.DENIAL;
        }
        return dccResult;
    }

    public void offlinePinEnterReady() {
        cv.open();
    }

    public void cardNumConfigErr() {
        intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
        cv.open();
    }

    public void cardNumConfigSucc() {
        intResult = EEmvExceptions.EMV_OK.getErrCodeFromBasement();
        cv.open();
    }

    public void cardNumConfigSucc(String[] amount) {
        if (amount != null && amount.length == 2) {
            transData.setAmount(String.valueOf(CurrencyConverter.parse(amount[0])));
            transData.setTipAmount(String.valueOf(CurrencyConverter.parse(amount[1])));
        }
        cardNumConfigSucc();
    }

    public static class CardInfo {
        private String cardNum;
        private String holderName;
        private String expDate;
        private float adjustPercent;

        CardInfo(String cardNum, String holderName, String expDate, float adjustPercent) {
            this.cardNum = cardNum;
            this.holderName = holderName;
            this.expDate = expDate;
            this.adjustPercent = adjustPercent;
        }

        public String getCardNum() {
            return cardNum;
        }

        public String getHolderName() {
            return holderName;
        }

        public String getExpDate() {
            return expDate;
        }

        public float getAdjustPercent() {
            return adjustPercent;
        }
    }

    private class SelectAppRunnable implements Runnable {
        private final boolean isFirstSelect;
        private final List<CandList> candList;

        SelectAppRunnable(final boolean isFirstSelect, final List<CandList> candList) {
            this.isFirstSelect = isFirstSelect;
            this.candList = candList;
        }

        @Override
        public void run() {
            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            if (isFirstSelect) {
                builder.setTitle(context.getString(R.string.emv_application_choose));
            } else {
                SpannableString sstr = new SpannableString(context.getString(R.string.emv_application_choose_again));
                sstr.setSpan(new ForegroundColorSpan(Color.RED), 5, 9, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                builder.setTitle(sstr);
            }
            String[] appNames = new String[candList.size()];
            for (int i = 0; i < appNames.length; i++) {
                appNames[i] = candList.get(i).getAppName();
            }
            builder.setSingleChoiceItems(appNames, -1, new DialogInterface.OnClickListener() {

                @Override
                public void onClick(DialogInterface dialog, int which) {
                    intResult = which;
                    close(dialog);
                }
            });

            builder.setPositiveButton(context.getString(R.string.dialog_cancel),
                    new DialogInterface.OnClickListener() {

                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
                            close(dialog);
                        }
                    });
            builder.setCancelable(false);
            builder.create().show();
        }

        private void close(DialogInterface dialog) {
            dialog.dismiss();
            cv.open();
        }
    }
}
