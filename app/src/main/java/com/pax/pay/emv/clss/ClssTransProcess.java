/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         lixc                    Create
 * ===========================================================================================
 */
package com.pax.pay.emv.clss;

import android.util.Log;

import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.eemv.IClss;
import com.pax.eemv.IClssListener;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.entity.Config;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.enums.EKernelType;
import com.pax.eemv.enums.ETransResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.TlvException;
import com.pax.glwrapper.impl.GL;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;

import java.io.UnsupportedEncodingException;
import java.util.List;

public class ClssTransProcess {

    private static final String TAG = "ClssTransProcess";

    private final IClss clss;

    public ClssTransProcess(IClss clss) {
        this.clss = clss;
    }

    public static Config genClssConfig() {
        Config cfg = Component.genCommonEmvConfig();
        cfg.setCapability("E0E0C8");
        cfg.setExCapability("E000F0A001");
        cfg.setTransType((byte) 0);
        cfg.setUnpredictableNumberRange("0060");
        cfg.setSupportOptTrans(true);
        cfg.setTransCap("D8F04000");
        cfg.setDelayAuthFlag(false);
        return cfg;
    }

    public static void clssTransResultProcess(CTransResult result, IClss clss, TransData transData) {
        updateEmvInfo(clss, transData);
        List<ClssDE55Tag> clssDE55TagList = ClssDE55Tag.genClssDE55Tags();

        if (result.getTransResult() == ETransResult.CLSS_OC_ONLINE_REQUEST ||
                ((result.getTransResult() == ETransResult.CLSS_OC_DECLINED || result.getTransResult() == ETransResult.CLSS_OC_APPROVED)
                        && transData.getTransType() == ETransType.REFUND)) {// REFUND need approved
            try {
                clss.setTlv(TagsTable.CRYPTO, ConvertHelper.getConvert().strToBcdPaddingLeft("80"));
            } catch (EmvException e) {
                LogUtils.w(TAG, "", e);
                transData.setEmvResult(ETransResult.ABORT_TERMINATED);
                return;
            }

            // prepare online DE55 data
            if (setStdDe55(clss, result, transData, clssDE55TagList) != 0) {
                transData.setEmvResult(ETransResult.ABORT_TERMINATED);
            }
        } else if (result.getTransResult() == ETransResult.CLSS_OC_APPROVED) {
            // save for upload
            setStdDe55(clss, result, transData, clssDE55TagList);
            transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
            transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            GreendaoHelper.getTransDataHelper().insert(transData);

            // increase trans no.
            Component.incTransNo();
        }
    }

    private static void updateEmvInfo(IClss clss, TransData transData) {
        //AppLabel
        byte[] value = clss.getTlv(TagsTable.APP_LABEL);
        if (value != null) {
            transData.setEmvAppLabel(new String(value));
        }

        //TVR
        value = clss.getTlv(TagsTable.TVR);
        if (value != null) {
            transData.setTvr(ConvertHelper.getConvert().bcdToStr(value));
            LogUtils.i(TAG, "TVR :  " + ConvertHelper.getConvert().bcdToStr(value));
        }

        //TSI
        value = clss.getTlv(TagsTable.TSI);
        if (value != null) {
            transData.setTsi(ConvertHelper.getConvert().bcdToStr(value));
            LogUtils.i(TAG, "TSI :  " + ConvertHelper.getConvert().bcdToStr(value));
        }

        //ATC
        value = clss.getTlv(TagsTable.ATC);
        if (value != null) {
            transData.setAtc(ConvertHelper.getConvert().bcdToStr(value));
        }

        //AppCrypto
        value = clss.getTlv(TagsTable.APP_CRYPTO);
        if (value != null) {
            transData.setArqc(ConvertHelper.getConvert().bcdToStr(value));
        }

        //AppName
        byte[] issuerCodeTableIndex = clss.getTlv(TagsTable.ISSUER_CODE_TABLE_INDEX);
        if (issuerCodeTableIndex != null && issuerCodeTableIndex.length > 0 && issuerCodeTableIndex[0] == 0x01) {
            //AppName
            value = clss.getTlv(TagsTable.APP_NAME);
            if (value != null) {
                transData.setEmvAppName(new String(value));
            }
        }

        //AID
        value = clss.getTlv(TagsTable.CAPK_RID);
        if (value != null) {
            transData.setAid(ConvertHelper.getConvert().bcdToStr(value));
        }

        //TC
        value = clss.getTlv(TagsTable.APP_CRYPTO);
        if (value != null) {
            transData.setTc(ConvertHelper.getConvert().bcdToStr(value));
        }
        //update emv info
        byte[] holderNameBCD = clss.getTlv(0x5F20);
        if (holderNameBCD == null) {
            holderNameBCD = " ".getBytes();
        }

        // get card holderName
        String holderName = " ";
        try {
            holderName = new String(holderNameBCD, "GB2312"); //兼容cardholder name为中文的情况
            transData.setCardHolderName(holderName);
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException : ", e);
        }
        //非接交易Online之如果没有存储到数据库先存储一下
        FinancialApplication.getApp().runInBackground(() ->{
            if (transData.getId() != null) {
                GreendaoHelper.getTransDataHelper().update(transData);
            }
        });
    }

    // set ADVT/TIP bit 55
    private static int setStdDe55(IClss clss, CTransResult result, TransData transData, List<ClssDE55Tag> clssDE55TagList) {
        ITlv tlv = GL.getGL().getPacker().getTlv();
        ITlv.ITlvDataObjList list = tlv.createTlvDataObjectList();

        for (ClssDE55Tag i : clssDE55TagList) {
            ITlv.ITlvDataObj tag = tlv.createTlvDataObject();

            byte[] value = clss.getTlv(i.getEmvTag());
            //if tag value is null , the host will no response
            if (value == null || value.length == 0) {
                if (i.getEmvTag() == 0x9f03) { //非接交易需强制上送tag 9F03
                    value = new byte[6];
                } else if (i.getEmvTag() == 0x5F34 &&
                        (clss.getKernelType() == EKernelType.KERNTYPE_RUPAY || clss.getKernelType() == EKernelType.JCB || clss.getKernelType() == EKernelType.PBOC || clss.getKernelType() == EKernelType.ZIP)) {
                    value = new byte[1];
                } else {
                    continue;
                }
            }
            tag.setTag(i.getEmvTag());
            tag.setValue(value);
            list.addDataObj(tag);
        }

        if (clss.getKernelType() == EKernelType.MC) {
            ITlv.ITlvDataObj tag = tlv.createTlvDataObject();
            tag.setTag(0x9F53);
            tag.setValue(ConvertHelper.getConvert().strToBcdPaddingLeft("82"));
            list.addDataObj(tag);

            if (result.getTransResult() == ETransResult.CLSS_OC_APPROVED) {
                tag.setTag(0x91);
                tag.setValue(clss.getTlv(0x91));
                list.addDataObj(tag);
            }
        }

        byte[] f55Data;
        try {
            f55Data = tlv.pack(list);
        } catch (TlvException e) {
            LogUtils.e(TAG, "", e);
            return TransResult.ERR_PACK;
        }

        if (f55Data.length > 255) {
            return TransResult.ERR_PACKET;
        }
        transData.setSendIccData(ConvertHelper.getConvert().bcdToStr(f55Data));
        return TransResult.SUCC;
    }

    public CTransResult transProcess(TransData transData, IClssListener listener) throws EmvException {
        clss.setListener(listener);
        CTransResult result = clss.process(Component.toInputParam(transData));
        // 如果是SALE ADVICE或者Preauth Advice的err_connect需要抛出该错误，保证最后冲正成功的提示弹窗内容为Reversal Executed
        if ((transData.getTransType() == ETransType.SALE_ADVICE || transData.getTransType() == ETransType.PREAUTH_ADVICE)
                && result.getTransResult() == ETransResult.ERR_CONNECT) {
            throw new EmvException(EEmvExceptions.EMV_ERR_REVELSAL);
        }
        LogUtils.i(TAG, "clss PROC:" + result.getCvmResult() + " " + result.getTransResult());
        return result;
    }
}
