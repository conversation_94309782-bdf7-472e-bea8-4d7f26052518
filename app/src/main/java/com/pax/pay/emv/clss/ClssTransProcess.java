/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-2-28
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv.clss;

import android.util.Log;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.IClss;
import com.pax.eemv.IClssListener;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.entity.Config;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.enums.EKernelType;
import com.pax.eemv.enums.ETransResult;
import com.pax.eemv.exception.EmvException;
import com.pax.eemv.utils.Tools;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.TlvException;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Clss trans process.
 */
public class ClssTransProcess {

    private static final String TAG = "ClssTransProcess";
    private static final int[] AMEX_SALE = {
            0x9F26, 0x9F10, 0x9F37, 0x9F36, 0x95, 0x9A, 0x9C, 0x9F02, 0x5F2A,
            0x9F1A, 0x82, 0x9F03, 0x5F34, 0x9F27, 0x9F06, 0x9F09, 0x9F34, 0x9F0E, 0x9F0F, 0x9F0D
    };
    private IClss clss;

    /**
     * Instantiates a new Clss trans process.
     *
     * @param clss the clss
     */
    public ClssTransProcess(IClss clss) {
        this.clss = clss;
    }

    /**
     * Gen clss config config.
     *
     * @return the config
     */
    public static Config genClssConfig() {
        Config cfg = Component.genCommonEmvConfig();
        cfg.setCapability("E0B0C8");
        cfg.setExCapability("E000F0A001");
        cfg.setTransType((byte) 0);
        cfg.setUnpredictableNumberRange("0060");
        cfg.setSupportOptTrans(true);
        cfg.setTransCap("D8B04000");
        cfg.setDelayAuthFlag(false);
        return cfg;
    }

    /**
     * Trans process c trans result.
     *
     * @param transData the trans data
     * @param listener the listener
     * @return the c trans result
     * @throws EmvException the emv exception
     */
    public CTransResult transProcess(TransData transData, IClssListener listener)
            throws EmvException {
        clss.setListener(listener);
        CTransResult result = clss.process(Component.toInputParam(transData));
        Log.i(TAG, "clss PROC:" + result.getCvmResult() + " " + result.getTransResult());
        return result;
    }

    /**
     * clssTransResultProcess
     *
     * @param result result
     * @param clss clss
     * @param transData transData
     */
    public static void clssTransResultProcess(CTransResult result, IClss clss,
            TransData transData) {
        updateEmvInfo(clss, transData);
        List<ClssDE55Tag> clssDE55TagList;
        List<ClssDE56Tag> clssDE56TagList;
        if (transData.isDcc()){
            clssDE55TagList = ClssDE55Tag.genClssDccDE55Tags();
            clssDE56TagList = ClssDE56Tag.genClssDE56Tags();
        } else if("HASE_JCB".equals(transData.getIssuer().getName()) || "JCB".equals((transData.getIssuer().getName()))){
            clssDE55TagList = ClssDE55Tag.genJCBClssDE55Tags();
            clssDE56TagList = ClssDE56Tag.genClssDE56Tags();
        } else {
            clssDE55TagList = ClssDE55Tag.genClssDE55Tags();
            clssDE56TagList = ClssDE56Tag.genClssDE56Tags();
        }
        if (result.getTransResult() == ETransResult.CLSS_OC_ONLINE_REQUEST) {
            try {
                clss.setTlv(TagsTable.CRYPTO, Utils.str2Bcd("80"));
            } catch (EmvException e) {
                Log.w(TAG, "", e);
                transData.setEmvResult(ETransResult.ABORT_TERMINATED);
                return;
            }

            // prepare online DE55 data
            if(Component.isAmex(transData.getAcquirer().getName())){
                if(setAmexDe55(clss, transData) != 0){
                    transData.setEmvResult(ETransResult.ABORT_TERMINATED);
                }
            }else {
                if (setStdDe55(clss, result, transData, clssDE55TagList) != 0) {
                    transData.setEmvResult(ETransResult.ABORT_TERMINATED);
                }
            }
            if ("HASE_CUP".equals(transData.getAcquirer().getName())) {
                if (setStdDe56(clss, transData, clssDE56TagList) != 0) {
                    transData.setEmvResult(ETransResult.ABORT_TERMINATED);
                }
            }
        } else if (result.getTransResult() == ETransResult.CLSS_OC_APPROVED) {
            // save for upload
            if(Component.isAmex(transData.getAcquirer().getName())){
                setAmexDe55(clss,transData);
            }else{
                setStdDe55(clss, result, transData, clssDE55TagList);
            }
            if ("HASE_CUP".equals(transData.getAcquirer().getName())) {
                setStdDe56(clss, transData, clssDE56TagList);
            }
            transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
            transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            FinancialApplication.getTransDataDbHelper().insertTransData(transData);

            // increase trans no.
            Component.incTransNo();
        }
    }

    /**
     * updateEmvInfo
     * @param clss clss
     * @param transData transData
     */
    private static void updateEmvInfo(IClss clss, TransData transData) {
        //AppLabel
        byte[] value = clss.getTlv(TagsTable.APP_LABEL);
        if (value != null) {    //Jerry Modify
            transData.setEmvAppLabel(new String(value));
        }

        //TVR
        value = clss.getTlv(TagsTable.TVR);
        if (value != null) {
            transData.setTvr(Utils.bcd2Str(value));
        }

        //TSI
        value = clss.getTlv(TagsTable.TSI);
        if (value != null) {
            transData.setTsi(Utils.bcd2Str(value));
        }

        //ATC
        value = clss.getTlv(TagsTable.ATC);
        if (value != null) {
            transData.setAtc(Utils.bcd2Str(value));
        }

        //AppCrypto
        value = clss.getTlv(TagsTable.APP_CRYPTO);
        if (value != null) {
            transData.setArqc(Utils.bcd2Str(value));
        }

        //AppName
        value = clss.getTlv(TagsTable.APP_NAME);
        if (value != null) {
            transData.setEmvAppName(Utils.bcd2Str(value));
        }

        //AID
        value = clss.getTlv(TagsTable.CAPK_RID);
        if (value != null) {
            transData.setAid(Utils.bcd2Str(value));
        }

        //TC
        value = clss.getTlv(TagsTable.APP_CRYPTO);
        if (value != null) {
            transData.setTc(Utils.bcd2Str(value));
        }
    }

    /**
     * set ADVT/TIP bit 55
     */
    private static int setStdDe55(IClss clss, CTransResult result, TransData transData,
            List<ClssDE55Tag> clssDE55TagList) {
        ITlv tlv = FinancialApplication.getPacker().getTlv();
        ITlv.ITlvDataObjList list = tlv.createTlvDataObjectList();

        for (ClssDE55Tag i : clssDE55TagList) {
            ITlv.ITlvDataObj tag = tlv.createTlvDataObject();
            if (0x9F03 == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                if (clss.getTlv(i.getEmvTag()) == null) {
                    tag.setValue(new byte[6]);
                } else {
                    tag.setValue(clss.getTlv(i.getEmvTag()));
                }
            } else if (0x9F08 == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                if (clss.getTlv(i.getEmvTag()) == null) {
                    tag.setValue(new byte[2]);
                } else {
                    tag.setValue(clss.getTlv(i.getEmvTag()));
                }
            } else if (0x9F09 == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                if (clss.getTlv(i.getEmvTag()) == null) {
                    tag.setValue(new byte[2]);
                } else {
                    tag.setValue(clss.getTlv(i.getEmvTag()));
                }
            } else if (0x9F34 == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                if (clss.getTlv(i.getEmvTag()) == null) {
                    tag.setValue(new byte[3]);
                } else {
                    tag.setValue(clss.getTlv(i.getEmvTag()));
                }
            } else if (0x9F41 == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                if (clss.getTlv(i.getEmvTag()) == null) {
                    tag.setValue(new byte[3]);
                } else {
                    tag.setValue(clss.getTlv(i.getEmvTag()));
                }
            } else if (0x9F6E == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                if (clss.getTlv(i.getEmvTag()) == null) {
                    tag.setValue(new byte[4]);
                } else {
                    tag.setValue(clss.getTlv(i.getEmvTag()));
                }
            } else {
                tag.setTag(i.getEmvTag());
                tag.setValue(clss.getTlv(i.getEmvTag()));
            }
            list.addDataObj(tag);
        }

        if (clss.getKernelType() == EKernelType.MC) {
            ITlv.ITlvDataObj tag = tlv.createTlvDataObject();
            tag.setTag(0x9F53);
            tag.setValue(Utils.str2Bcd("52"));      //82 -> 52
            list.addDataObj(tag);
            list.removeByTag(0x9F6E);

            if (result.getTransResult() == ETransResult.CLSS_OC_APPROVED) {
                tag.setTag(0x91);
                tag.setValue(clss.getTlv(0x91));
                list.addDataObj(tag);
            }
        }

        byte[] f55Data;
        try {
            f55Data = tlv.pack(list);
        } catch (TlvException e) {
            Log.e(TAG, "", e);
            return TransResult.ERR_PACK;
        }

        if (f55Data.length > 255) {
            return TransResult.ERR_PACKET;
        }
        transData.setSendIccData(Utils.bcd2Str(f55Data));

        String tag5A = transData.getPan();
        String f55 = Utils.bcd2Str(f55Data);
        int length = tag5A.length() / 2;
        tag5A = "5A" + Component.getPaddedString(Integer.toString(length), 2, '0') + tag5A;
        int position = f55.indexOf("5F2A");
        if (position == -1) {
            return TransResult.ERR_PACKET;
        }
        transData.setSendIccDataForOffline(tag5A + f55.substring(position));

        return TransResult.SUCC;
    }

    /**
     * Set F55 for AMEX
     */
    private static int setAmexDe55(IClss clss, TransData transData) {
        Map<Integer, String> values = new LinkedHashMap<>();

        for (int tag : AMEX_SALE) {
            byte[] value = clss.getTlv(tag);
            if (value == null || value.length == 0) {
                if (tag == 0x9f03) {
                    value = new byte[6];
                } else {
                    continue;
                }
            }
            values.put(tag, Tools.bcd2Str(value));
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Integer, String> entry : values.entrySet()) {
            if (entry.getKey() == 0x9F10 || entry.getKey() == 0x9F06) {
                int vl = entry.getValue().length() / 2;
                String len = Integer.toHexString(vl).toUpperCase();
                if (len.length() == 1) {
                    len = "0" + len;
                }
                sb.append(len);
                sb.append(entry.getValue());
            } else {
                sb.append(entry.getValue());
            }
        }

        String header = "C1C7D5E2";
        String version = "0001";
        String df55 = header + version + sb.toString();
        if (df55.length() > 256) {
            return TransResult.ERR_PACKET;
        }
        transData.setSendIccData(df55);
        return TransResult.SUCC;
    }

    /**
     * set bit 56
     */
    private static int setStdDe56(IClss clss, TransData transData,
            List<ClssDE56Tag> clssDE56TagList) {
        ITlv tlv = FinancialApplication.getPacker().getTlv();
        ITlv.ITlvDataObjList list = tlv.createTlvDataObjectList();

        for (ClssDE56Tag i : clssDE56TagList) {
            ITlv.ITlvDataObj tag = tlv.createTlvDataObject();
            if (0xDF5C == i.getEmvTag()) {
                tag.setTag(i.getEmvTag());
                String buffer = "01" + Component.getPaddedNumber(Component.getInvoiceNo(), 6) +
                        Component.getPaddedNumber(Component.getTransNo(), 6);
                tag.setValue(Utils.str2Bcd(buffer));
            } else {
                tag.setTag(i.getEmvTag());
                tag.setValue(clss.getTlv(i.getEmvTag()));
            }
            list.addDataObj(tag);
        }

        byte[] f56Data;
        try {
            f56Data = tlv.pack(list);
        } catch (TlvException e) {
            Log.e(TAG, "", e);
            return TransResult.ERR_PACK;
        }

        if (f56Data.length > 256) {
            return TransResult.ERR_PACKET;
        }
        transData.setSendField56(Utils.bcd2Str(f56Data));

        return TransResult.SUCC;
    }
}
