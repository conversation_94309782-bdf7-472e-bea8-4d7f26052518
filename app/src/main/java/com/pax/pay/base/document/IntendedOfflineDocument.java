package com.pax.pay.base.document;

import android.text.TextUtils;
import android.util.Log;
import com.pax.abl.utils.EncUtils;
import com.pax.pay.app.FinancialApplication;
import com.pax.settings.SysParam;
import com.pax.settings.SysParam.NumberParam;
import com.pax.settings.SysParam.StringParam;
import java.util.Iterator;
import org.dom4j.Document;
import org.dom4j.Element;

/**
 * 增加EcrDocument用来进行pax store ecr参数模板的推送和设置
 */

public class IntendedOfflineDocument extends BaseDocument {
    private IntendedOfflineParam intendedOfflineParam = new IntendedOfflineParam();

    /**
     * constructor
     *
     * @param filePath filePath
     */
    public IntendedOfflineDocument(String filePath) {
        super(filePath);
    }

    @Override
    public Object getObject() {
        return intendedOfflineParam;
    }

    /**
     * 解析参数文件
     *
     * @return int
     */
    @Override
    public int parse() {
        Document document = getDocument();
        if (document == null) {
            return -1;
        }

        Element root = document.getRootElement();
        Iterator iterator = root.elementIterator();

        try {
            //Param Template Version
            String text = getNextText(iterator);
            //set limit money
            if (!TextUtils.isEmpty(text)) {
                intendedOfflineParam.setLimitMoney(text);
            }

            //set echo time
            text = getNextText(iterator);
            if (!TextUtils.isEmpty(text)) {
                intendedOfflineParam.setEchoTime(text);
            }

            //set password
            text = getNextText(iterator);
            if (!TextUtils.isEmpty(text)) {
                intendedOfflineParam.setPassword(text);
            }

        } catch (Exception e) {
            Log.e("IntendedOfflineDocument", "", e);
            return -1;
        }

        return 0;
    }

    /**
     * save to db
     */
    @Override
    public void save() {
        SysParam sysParam = FinancialApplication.getSysParam();
        sysParam.set(NumberParam.INTENDED_OFFLINE_MONEY_LIMIE, Integer.parseInt(intendedOfflineParam.getLimitMoney()));
        sysParam.set(NumberParam.INTENDED_OFFLINE_ECHO_TIME, Integer.parseInt(intendedOfflineParam.getEchoTime()));
        sysParam.set(StringParam.INTENDED_OFFLINE_PASSWORD, EncUtils.pwdSha(intendedOfflineParam.getPassword()));

    }

    /**
     * The type Intended Offline param.
     */
    static class IntendedOfflineParam {
        private String limitMoney;
        private String echoTime;
        private String password;

        /**
         * Gets limit money.
         *
         * @return the boolean
         */
        public String getLimitMoney() {
            return limitMoney;
        }

        /**
         * Sets limit money.
         *
         * @param limitMoney the limit money for Intended Offline Mode
         */
        public void setLimitMoney(String limitMoney) {
            this.limitMoney = limitMoney;
        }

        /**
         * Gets echo time.
         *
         * @return the echo time
         */
        public String getEchoTime() {
            return echoTime;
        }

        /**
         * Sets echo time.
         *
         * @param echoTime the echo time
         */
        public void setEchoTime(String echoTime) {
            this.echoTime = echoTime;
        }

        /**
         * Gets intended offline mode password.
         *
         * @return the password for intended offline mode
         */
        public String getPassword() {
            return password;
        }

        /**
         * Sets password.
         *
         * @param password the password for intended offline mode
         */
        public void setPassword(String password) {
            this.password = password;
        }

    }
}

