/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/4/21                      Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.pay.base;

import androidx.annotation.NonNull;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import java.io.Serializable;

/**
 * card range table
 */
@DatabaseTable(tableName = "yuu_card_range")
public class YUUCardRange implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    public static final String ID_FIELD_NAME = "card_id";
    /**
     * name
     */
    public static final String NAME_FIELD_NAME = "card_name";
    public static final String RANGE_LOW_FIELD_NAME = "card_range_low";
    public static final String RANGE_HIGH_FIELD_NAME = "card_range_high";

    public static final String LENGTH_FIELD_NAME = "card_length";

    @DatabaseField(generatedId = true, columnName = ID_FIELD_NAME)
    private int id;

    @DatabaseField(columnName = NAME_FIELD_NAME)
    private String name;

    @DatabaseField(unique = true, canBeNull = false, columnName = RANGE_LOW_FIELD_NAME, width = 10)
    private String panRangeLow;

    @DatabaseField(unique = true, canBeNull = false, columnName = RANGE_HIGH_FIELD_NAME, width = 10)
    private String panRangeHigh;

    @DatabaseField(columnName = LENGTH_FIELD_NAME)
    private int panLength;

    /**
     * constructor
     */
    public YUUCardRange() {
    }

    /**
     * constructor
     */
    public YUUCardRange(String name, String panRangeLow, String panRangeHigh, int panLength) {
        this.setName(name);
        this.setPanRangeLow(panRangeLow);
        this.setPanRangeHigh(panRangeHigh);
        this.setPanLength(panLength);
    }

    /**
     * constructor
     */
    public YUUCardRange(int id, String name, String panRangeLow, String panRangeHigh,
            int panLength) {
        this.setId(id);
        this.setName(name);
        this.setPanRangeLow(panRangeLow);
        this.setPanRangeHigh(panRangeHigh);
        this.setPanLength(panLength);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPanRangeLow() {
        return panRangeLow;
    }

    public void setPanRangeLow(String panRangeLow) {
        this.panRangeLow = panRangeLow;
    }

    public String getPanRangeHigh() {
        return panRangeHigh;
    }

    public void setPanRangeHigh(String panRangeHigh) {
        this.panRangeHigh = panRangeHigh;
    }

    public int getPanLength() {
        return panLength;
    }

    public void setPanLength(int panLength) {
        this.panLength = panLength;
    }

    public void update(@NonNull YUUCardRange YUUCardRange) {
        name = YUUCardRange.getName();
        panLength = YUUCardRange.getPanLength();
    }
}
