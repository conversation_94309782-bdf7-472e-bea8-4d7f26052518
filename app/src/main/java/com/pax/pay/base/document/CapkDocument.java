/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-10-23
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.pay.base.document;

import android.text.TextUtils;
import android.util.Log;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.db.EmvDb;
import com.pax.pay.emv.EmvCapk;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.dom4j.Document;
import org.dom4j.Element;

public class CapkDocument extends BaseDocument {
    private List<EmvCapk> emvCapkList = new ArrayList<>();

    /**
     * constructor
     *
     * @param filePath filePath
     */
    public CapkDocument(String filePath) {
        super(filePath);
    }

    @Override
    public Object getObject() {
        return emvCapkList;
    }

    /**
     * 解析参数文件
     *
     * @return int
     */
    @Override
    public int parse() {
        emvCapkList.clear();
        Document document = getDocument();
        if (document == null) {
            return -1;
        }

        Element employees = document.getRootElement();
        Iterator iterator = employees.elementIterator();
        String text;

        try {
            while (iterator.hasNext()) {
                EmvCapk emvCapk = new EmvCapk();

                //set rID
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setRID(text);

                //set keyID
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setKeyID(Integer.parseInt(text));

                //set hashInd
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setHashInd(Integer.parseInt(text));

                //set arithInd
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setArithInd(Integer.parseInt(text));

                //set module
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setModule(text);

                //set exponent
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setExponent(text);

                //set expDate
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setExpDate(text);

                //set checkSum
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvCapk.setCheckSum(text);

                emvCapkList.add(emvCapk);
            }
        } catch (Exception e) {
            Log.e(TAG, "", e);
            return -1;
        }

        return 0;
    }

    /**
     * save to db
     */
    @Override
    public void save() {
        if (!emvCapkList.isEmpty()) {
            EmvDb emvDb = FinancialApplication.getEmvDbHelper();

            emvDb.deleteCAPK();

            emvDb.insertAllCAPK(emvCapkList);

            emvCapkList.clear();
        }
    }
}
