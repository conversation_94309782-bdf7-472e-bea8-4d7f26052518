/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         laiyi                   Create
 * ===========================================================================================
 */
package com.pax.pay.base.document;

import static com.pax.pay.utils.Utils.getString;

import android.text.TextUtils;

import com.pax.appstore.DocumentBase;
import com.pax.appstore.DownloadManager;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.btprinter.util.StringUtils;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.io.File;
import java.time.LocalDate;

/**
 * The type Issuer document.
 */
public class SysDocument extends DocumentBase<String[]> {

    /**
     * Instantiates a new Issuer document.
     *
     * @param filePath the file path
     */
    public SysDocument(String filePath) {
        super(filePath);
    }

    //备份参数
    @Override
    public boolean backup() {
        backupParamList = SysParam.getInstance().getAllParam();
        return true;
    }

    @Override
    public boolean restore() {
        paramList.clear();
        paramList = backupParamList;
        return updateParam(null);
    }

    //获取参数字典
    @Override
    public int parse(DownloadManager.UpdateParamListener updateParamListener) {
        map = getMapResult();
        if (map == null) {
            updateParamListener.Error("System parameter file does not exist");
            return -1;
        }
        return 0;
    }

    //更新参数
    @Override
    public boolean updateParam(DownloadManager.UpdateParamListener updateParamListener) {
        if (paramList == null || paramList.isEmpty()) {
            return false;
        }
        String[] pa = null;
        try {
            for (String[] param : paramList) {
                pa = param;
                if (Utils.isA50() && (Utils.getString(R.string.EDC_ENABLE_PRINT_LOG).equals(param[0])
                        || Utils.getString(R.string.EDC_ENABLE_PRINT).equals(param[0]))) {
                    //A50不支持打印日志
                    continue;
                }
                switch (param[2]) {
                    case "0":
                        if (Utils.getString(R.string.EDC_AUTO_SETTLEMENT_TIME).equals(param[0])) {
                            if (!Utils.getString(R.string.disable).equals(param[1])) {
                                // 设置了时间则同时把DATE设置上，方便处理自动结算问题
                                String hour = param[1].substring(0, 2);
                                String minute = param[1].substring(2, 4);
                                // 如果当前时间超过了，就设置下一天
                                if (Utils.isTimeHourPassed(hour + ":" + minute)) {
                                    SysParam.getInstance().set(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE), LocalDate.now().plusDays(1).toString());
                                } else {
                                    SysParam.getInstance().set(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE), LocalDate.now().toString());
                                }
                                SysParam.getInstance().set(param[0], hour + ":" + minute);
                            } else {
                                // 如果推送的是空参数则重置时间为空
                                SysParam.getInstance().set(param[0], Utils.getString(R.string.disable));
                                SysParam.getInstance().set(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE), Utils.getString(R.string.disable));
                            }
                        } else if (Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE).equals(param[0]) || Utils.getString(R.string.COMM_TYPE).equals(param[0])
                                || Utils.getString(R.string.provider_selected_label).equals(param[0])) {
                            //防止被覆盖
                        }  else if (Utils.getString(R.string.EDC_BANNER_IMAGE_FILE).equals(param[0])) {
                            // 存储之前删掉以前的文件
                            String oldCerName = SysParam.getInstance().getString(Utils.getString(R.string.EDC_BANNER_IMAGE_FILE));
                            // 同名文件会自动覆盖，不用手动删除
                            FinancialApplication.getApp().runInBackground(() -> {
                                if (!TextUtils.isEmpty(oldCerName) && !oldCerName.equals(param[1].toString())) {
                                    File oldFile = new File(DownloadManager.getInstance().getFilePath() + File.separator + oldCerName);
                                    deleteFileRecursively(oldFile);
                                }
                            });
                            SysParam.getInstance().set(param[0], param[1]);
                        } else {
                            SysParam.getInstance().set(param[0], param[1]);
                        }
                        break;
                    case "1":
                        SysParam.getInstance().set(param[0], (param[1].equals("True") || param[1].equals("Y")) ? true : false);
                        break;
                    case "2":
                        SysParam.getInstance().set(param[0], Integer.parseInt(param[1]));
                        break;
                    case "3":
                        SysParam.getInstance().set(param[0], param[1]);
                        break;
                }
            }
            if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
                MQTTUtils.startMQTT();
            } else {
                MQTTUtils.destoryMQTT();
            }
            if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO))) {
                MQTTUtils.startTTS();
            } else {
                MQTTUtils.destoryTTS();
            }
            return true;
        } catch (Exception e) {
            if (updateParamListener != null) {
                if (pa != null) {
                    updateParamListener.Error("The parameter value of " + pa[0] + " is wrong");
                } else {
                    updateParamListener.Error("System parameter update failed");
                }
            }
            return false;
        }
    }

    //保存参数
    @Override
    public boolean save(DownloadManager.UpdateParamListener updateParamListener) {
        if (map == null || map.isEmpty()) {
            return true;
        }
        String bannerImageFileName = map.get(getString(R.string.EDC_BANNER_IMAGE_FILE).replace("_", ".").toLowerCase());
        if (bannerImageFileName != null && (!bannerImageFileName.endsWith(".jpg") && !bannerImageFileName.endsWith(".png"))) {
            deleteFileRecursively(new File(DownloadManager.getInstance().getFilePath()+File.separator+ bannerImageFileName));
            return false;
        }

        if (paramList != null) {
            paramList.clear();
            paramList.addAll(backupParamList);
        }
        int k = 0;
        for (String[] param : paramList
        ) {
            String value = map.get(param[0].toLowerCase().replace("_", "."));
            if(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_TIME).equals(param[0]) && StringUtils.isNullOrEmpty(value)){
                param[1] = Utils.getString(R.string.disable);
                k += 1;
            }else if (!StringUtils.isNullOrEmpty(value)) {
                param[1] = value;
                k += 1;
            }
        }
        if (k != map.size()) {
            updateParamListener.Error("Some sys parameters are lost");
        }

        map.clear();
        return updateParam(updateParamListener);
    }

}
