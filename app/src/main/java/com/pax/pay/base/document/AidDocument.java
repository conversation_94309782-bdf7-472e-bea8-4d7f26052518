/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-10-23
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.pay.base.document;

import android.text.TextUtils;
import android.util.Log;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.db.EmvDb;
import com.pax.pay.emv.EmvAid;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.dom4j.Document;
import org.dom4j.Element;

public class AidDocument extends BaseDocument {
    private List<EmvAid> emvAidList = new ArrayList<>();

    /**
     * constructor
     *
     * @param filePath filePath
     */
    public AidDocument(String filePath) {
        super(filePath);
    }

    @Override
    public Object getObject() {
        return emvAidList;
    }

    /**
     * 解析参数文件
     *
     * @return int
     */
    @Override
    public int parse() {
        emvAidList.clear();
        Document document = getDocument();
        if (document == null) {
            return -1;
        }

        Element employees = document.getRootElement();
        Iterator iterator = employees.elementIterator();
        String text;

        try {
            while (iterator.hasNext()) {
                EmvAid emvAid = new EmvAid();

                //set AppName
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setAppName(text);

                //set aid
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setAid(text);

                //set sel flag
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setSelFlag("0".equals(text) ? EmvAid.PART_MATCH : EmvAid.FULL_MATCH);

                //set priority
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setPriority(Integer.parseInt(text));

                //set onlinePin
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setOnlinePin("Y".equals(text));

                //set targetPer
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setTargetPer(Integer.parseInt(text));

                //set maxTargetPer
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setMaxTargetPer(Integer.parseInt(text));

                //set floorLimit
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setFloorLimit(Long.parseLong(text));

                //set floorLimitCheckFlg
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setFloorLimitCheckFlg(Integer.parseInt(text));

                //set rdClssTxnLmt
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRdClssTxnLmt(Long.parseLong(text));

                //set rdClssTxnLmtFlg
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRdClssTxnLmtFlg(Integer.parseInt(text));

                //set rdClssFLmt
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRdClssFLmt(Long.parseLong(text));

                //set rdClssFLmtFlg
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRdClssFLmtFlg(Integer.parseInt(text));

                //set rdCVMLmt
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRdCVMLmt(Long.parseLong(text));

                //set rdCVMLmtFlg
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRdCVMLmtFlg(Integer.parseInt(text));

                //set randTransSel
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setRandTransSel("Y".equals(text));

                //set velocityCheck
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setVelocityCheck("Y".equals(text));

                //set threshold
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setThreshold(Long.parseLong(text));

                //set tacDenial
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setTacDenial(text);

                //set tacOnline
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setTacOnline(text);

                //set tacDefault
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setTacDefault(text);

                //set acquirerId
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setAcquirerId(text);

                //set dDOL
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setDDOL(text);

                //set tDOL
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setTDOL(text);

                //set version
                text = getNextText(iterator);
                if (TextUtils.isEmpty(text)) {
                    return -1;
                }
                emvAid.setVersion(text);

                //set riskManData
                text = getNextText(iterator);
                if (text != null && !text.isEmpty()) {
                    emvAid.setRiskManageData(text);
                }

                emvAidList.add(emvAid);
            }
        } catch (Exception e) {
            Log.e(TAG, "", e);
            return -1;
        }

        return 0;
    }

    /**
     * save to db
     */
    @Override
    public void save() {
        if (!emvAidList.isEmpty()) {
            EmvDb emvDb = FinancialApplication.getEmvDbHelper();

            emvDb.deleteAID();

            emvDb.insertAllAID(emvAidList);

            emvAidList.clear();
        }
    }
}
