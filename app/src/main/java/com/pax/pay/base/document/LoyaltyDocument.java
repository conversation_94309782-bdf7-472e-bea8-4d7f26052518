package com.pax.pay.base.document;

import android.text.TextUtils;
import com.pax.pay.app.FinancialApplication;
import com.pax.settings.SysParam;
import java.util.Iterator;
import org.dom4j.Document;
import org.dom4j.Element;

/**
 * Created by liliang on 2018/1/16.
 */

public class LoyaltyDocument extends BaseDocument {
    private LoyalParam mLoyalParam = new LoyalParam();

    /**
     * constructor
     *
     * @param filePath filePath
     */
    public LoyaltyDocument(String filePath) {
        super(filePath);
    }

    @Override
    public Object getObject() {
        return mLoyalParam;
    }

    /**
     * 解析参数文件
     *
     * @return int
     */
    @Override
    public int parse() {
        Document document = getDocument();
        if (document == null) {
            return -1;
        }

        Element employees = document.getRootElement();
        Iterator iterator = employees.elementIterator();

        //Merchant$ select type
        String text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            switch (text) {
                case "CODS_NOSEL":
                    mLoyalParam.setCdsType(SysParam.Constant.CashDolSelType.CDS_NOSEL.toString());
                    break;
                case "CDS_FULL_OR_MD":
                    mLoyalParam.setCdsType(SysParam.Constant.CashDolSelType.CDS_FULL_OR_MD.toString());
                    break;
                case "CDS_DIFFERENT":
                    mLoyalParam.setCdsType(SysParam.Constant.CashDolSelType.CDS_DIFFERENT.toString());
                    break;
                default:
                    return -1;
            }
        }

        //Merchant1$ name
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setMerch1DolName(text);
        }

        //Merchant2$ name
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setMerch2DolName(text);
        }

        //Bonus$ name
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setBonusDolName(text);
        }

        //HaseCash$ Name
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setHaseCashDolName(text);
        }

        //Redeem cash$
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setRedmCash("Y".equals(text));
        }

        //Redeem M1$
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setRedmM1("Y".equals(text));
        }

        //Redeem M2$
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setRedmM2("Y".equals(text));
        }

        //Default Trans
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setDefaultTrans("Y".equals(text));
        }

        //Cash$ Choice
        text = getNextText(iterator);
        if (!TextUtils.isEmpty(text)) {
            mLoyalParam.setCashDolChoice("Y".equals(text));
        }

        return 0;
    }

    /**
     * save to db
     */
    @Override
    public void save() {
        SysParam sysParam = FinancialApplication.getSysParam();
        sysParam.set(SysParam.StringParam.CDS_TYPE, mLoyalParam.cdsType);
        sysParam.set(SysParam.StringParam.MERCHANT1_DOL_NAME, mLoyalParam.merch1DolName);
        sysParam.set(SysParam.StringParam.MERCHANT2_DOL_NAME, mLoyalParam.merch2DolName);
        sysParam.set(SysParam.StringParam.BONUS_DOL_NAME, mLoyalParam.bonusDolName);
        sysParam.set(SysParam.StringParam.HASE_CASH_DOL_NAME, mLoyalParam.haseCashDolName);
        sysParam.set(SysParam.BooleanParam.REDM_CASH, mLoyalParam.isRedmCash);
        sysParam.set(SysParam.BooleanParam.REDM_MERCHANT1_DOL, mLoyalParam.isRedmM1);
        sysParam.set(SysParam.BooleanParam.REDM_MERCHANT2_DOL, mLoyalParam.isRedmM2);
        sysParam.set(SysParam.BooleanParam.DEFAULT_TRAN, mLoyalParam.isDefaultTrans);
        sysParam.set(SysParam.BooleanParam.CASH_CHOICE, mLoyalParam.isCashDolChoice);
    }

    private static class LoyalParam {
        String cdsType;
        String merch1DolName;
        String merch2DolName;
        String bonusDolName;
        String haseCashDolName;
        boolean isRedmCash;
        boolean isRedmM1;
        boolean isRedmM2;
        boolean isDefaultTrans;
        boolean isCashDolChoice;

        public String getCdsType() {
            return cdsType;
        }

        public void setCdsType(String cdsType) {
            this.cdsType = cdsType;
        }

        public String getMerch1DolName() {
            return merch1DolName;
        }

        public void setMerch1DolName(String merch1DolName) {
            this.merch1DolName = merch1DolName;
        }

        public String getMerch2DolName() {
            return merch2DolName;
        }

        public void setMerch2DolName(String merch2DolName) {
            this.merch2DolName = merch2DolName;
        }

        public String getBonusDolName() {
            return bonusDolName;
        }

        public void setBonusDolName(String bonusDolName) {
            this.bonusDolName = bonusDolName;
        }

        public String getHaseCashDolName() {
            return haseCashDolName;
        }

        public void setHaseCashDolName(String haseCashDolName) {
            this.haseCashDolName = haseCashDolName;
        }

        public boolean isRedmCash() {
            return isRedmCash;
        }

        public void setRedmCash(boolean redmCash) {
            isRedmCash = redmCash;
        }

        public boolean isRedmM1() {
            return isRedmM1;
        }

        public void setRedmM1(boolean redmM1) {
            isRedmM1 = redmM1;
        }

        public boolean isRedmM2() {
            return isRedmM2;
        }

        public void setRedmM2(boolean redmM2) {
            isRedmM2 = redmM2;
        }

        public boolean isDefaultTrans() {
            return isDefaultTrans;
        }

        public void setDefaultTrans(boolean defaultTrans) {
            isDefaultTrans = defaultTrans;
        }

        public boolean isCashDolChoice() {
            return isCashDolChoice;
        }

        public void setCashDolChoice(boolean cashDolChoice) {
            isCashDolChoice = cashDolChoice;
        }
    }

}
