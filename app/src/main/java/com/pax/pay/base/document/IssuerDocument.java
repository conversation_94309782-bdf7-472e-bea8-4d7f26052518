/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-8-31
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.base.document;

import android.text.TextUtils;
import android.util.Log;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Issuer;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.model.AcqManager;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.dom4j.Document;
import org.dom4j.Element;

public class IssuerDocument extends BaseDocument {
    private List<Issuer> issuerList = new ArrayList<>();
    //private final String defaultPanMaskPattern = "(?<=\\d{0})\\d(?=\\d{4})";
    private Boolean isEnableCard = false;

    /**
     * constructor
     *
     * @param filePath filePath
     */
    public IssuerDocument(String filePath) {
        super(filePath);
    }

    @Override
    public Object getObject() {
        return null;
    }

    /**
     * 解析参数文件
     *
     * @return int
     */
    @Override
    public int parse() {
        issuerList.clear();
        Document document = getDocument();
        if (document == null) {
            return -1;
        }

        Element employees = document.getRootElement();
        Iterator iterator = employees.elementIterator();
        String text;

        try {
            while (iterator.hasNext()) {
                Issuer issuer = new Issuer();
                //set name
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setName(text);
                } else {
                    return -1;
                }

                //is enable
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    isEnableCard = "Y".equals(text);
                }

                //set FloorLimit
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setFloorLimit(Long.parseLong(text));
                }

                //set AdjustPercent
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAdjustPercent(Float.parseFloat(text));
                }

                //Enable adjust
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setEnableAdjust("Y".equals(text));
                }

                //set isEnableOffline
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setEnableOffline("Y".equals(text));
                }

                //set isEnablePreAuth
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setEnablePreAuth("Y".equals(text));
                }

                //set isEnableRefund
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setEnableRefund("Y".equals(text));
                }

                //set isEnableVoid
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setEnableVoid("Y".equals(text));
                }

                //set isAllowExpiry
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowExpiry("Y".equals(text));
                }

                //set isAllowManualPan
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowManualPan("Y".equals(text));
                }

                //set isAllowCheckExpiry
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowCheckExpiry("Y".equals(text));
                }

                //Check pan
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowCheckPanMod10("Y".equals(text));
                }

                //set isAllowPrint
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowPrint("Y".equals(text));
                }

                //set isRequirePIN
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setRequirePIN("Y".equals(text));
                }

                //Security Code
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowSecurityCodeSwipe("Y".equals(text));
                }

                //Secu. Code Manual
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setAllowSecurityCodeManual("Y".equals(text));
                }

                //Pan mask pattern
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    switch (text) {
                        case "4-6":
                            issuer.setPanMaskPattern(Constants.PAN_MASK_PATTERN1);
                            break;
                        case "6-4":
                            issuer.setPanMaskPattern(Constants.PAN_MASK_PATTERN_CUP);
                            break;
                        //Amex专用pan mask格式
                        case "6-3":
                            issuer.setPanMaskPattern(Constants.PAN_MASK_PATTERN_AMEX);
                            break;
                        case "ALL_MASK":
                            issuer.setPanMaskPattern(Constants.PAN_MASK_PATTERN_ALL_MASK);
                            break;
                        case "NO_MASK":
                            issuer.setPanMaskPattern(Constants.PAN_MASK_PATTERN_NO_MASK);
                            break;
                        default:
                            issuer.setPanMaskPattern(Constants.DEF_PAN_MASK_PATTERN);
                            break;
                    }
                }

                //mask expiry
                text = getNextText(iterator);
                if (!TextUtils.isEmpty(text)) {
                    issuer.setRequireMaskExpiry("Y".equals(text));
                }

                if (isEnableCard) {
                    issuerList.add(issuer);
                }
            }
        } catch (Exception e) {
            Log.e("IssuerDocument", "", e);
            return -1;
        }

        return 0;
    }

    /**
     * save to db
     */
    @Override
    public void save() {
        if (!issuerList.isEmpty()) {
            AcqManager acqManager = FinancialApplication.getAcqManager();

            acqManager.deleteAllIssuer();

            acqManager.insertAllIssuer(issuerList);

            issuerList.clear();
        }
    }
}
