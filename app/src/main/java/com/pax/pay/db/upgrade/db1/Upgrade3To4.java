/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/04/21                     Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.pay.db.upgrade.db1;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.pax.pay.db.upgrade.DbUpgrader;
import com.pax.pay.trans.model.TransTotal;
import java.sql.SQLException;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
public class Upgrade3To4 extends DbUpgrader {

    @Override
    protected void upgrade(SQLiteDatabase db, ConnectionSource cs) throws SQLException {
        DbUpgrader.upgradeTable(db, cs, TransTotal.class, ADD, null);
    }
}
