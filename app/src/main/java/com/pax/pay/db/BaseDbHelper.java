/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-2-17
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.db;

import android.database.sqlite.SQLiteDatabase;
import android.util.Log;
import com.j256.ormlite.android.apptools.OrmLiteSqliteOpenHelper;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.AcqIssuerRelation;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.CardRange;
import com.pax.pay.base.Issuer;
import com.pax.pay.base.YUUCardRange;
import com.pax.pay.db.upgrade.DbUpgrader;
import com.pax.pay.emv.CardBin;
import com.pax.pay.emv.CardBinBlack;
import com.pax.pay.emv.EmvAid;
import com.pax.pay.emv.EmvCapk;
import com.pax.pay.emv.clss.ClssTornLog;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransTotal;
import java.sql.SQLException;

/**
 * DB helper
 */
public class BaseDbHelper extends OrmLiteSqliteOpenHelper {
    protected static final String TAG = "DB";
    // DB Name
    private static final String DATABASE_NAME = "data.db";
    // DB version
    /**
     * 4: Acquirer: +QR
     * 3: TransData: +signPath
     * 2: test
     * 1: init
     */
    private static final int DATABASE_VERSION = 4;
    // DB Upgrader packagePath
    private static final String UPGRADER_PATH = "com.pax.pay.db.upgrade.db1";
    private static BaseDbHelper instance;

    private BaseDbHelper() {
        super(FinancialApplication.getApp(), DATABASE_NAME, null, DATABASE_VERSION);
    }

    public static int getDbVersion() {
        return DATABASE_VERSION;
    }

    /**
     * create table when class is creating if table not exists
     *
     * @param sqliteDatabase db
     * @param connectionSource connectionSource
     */
    @Override
    public void onCreate(SQLiteDatabase sqliteDatabase, ConnectionSource connectionSource) {
        try {
            TableUtils.createTableIfNotExists(connectionSource, AcqIssuerRelation.class);
            TableUtils.createTableIfNotExists(connectionSource, Acquirer.class);
            TableUtils.createTableIfNotExists(connectionSource, Issuer.class);
            TableUtils.createTableIfNotExists(connectionSource, CardRange.class);
            TableUtils.createTableIfNotExists(connectionSource, YUUCardRange.class);

            TableUtils.createTableIfNotExists(connectionSource, CardBin.class);
            TableUtils.createTableIfNotExists(connectionSource, CardBinBlack.class);

            TableUtils.createTableIfNotExists(connectionSource, EmvAid.class);
            TableUtils.createTableIfNotExists(connectionSource, EmvCapk.class);

            TableUtils.createTableIfNotExists(connectionSource, TransData.class);
            TableUtils.createTableIfNotExists(connectionSource, TransTotal.class);
            TableUtils.createTableIfNotExists(connectionSource, ClssTornLog.class);
        } catch (SQLException e) {
            Log.e(TAG, "", e);
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase sqliteDatabase, ConnectionSource connectionSource, int oldVersion,
                          int newVersion) {
        try {
            for (int i = oldVersion; i < newVersion; ++i) {
                DbUpgrader.upgrade(sqliteDatabase, connectionSource, i, i + 1, UPGRADER_PATH);
            }
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
    }

    /**
     * get the Singleton of the DB Helper
     *
     * @return the Singleton of DB helper
     */
    public static synchronized BaseDbHelper getInstance() {
        if (instance == null) {
            instance = new BaseDbHelper();
        }

        return instance;
    }
}
