/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/8/24                      Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.pay.password;

import com.pax.abl.utils.EncUtils;
import com.pax.pay.app.FinancialApplication;
import com.pax.settings.SysParam;

/**
 * The type Change management pwd activity.
 */
public class ChangeManagementPwdActivity extends BaseChangePwdActivity {
    @Override
    protected void savePwd() {
        FinancialApplication.getSysParam()
                .set(SysParam.StringParam.SEC_MANAGEMENT_PWD, EncUtils.pwdSha(pwd));
    }
}
