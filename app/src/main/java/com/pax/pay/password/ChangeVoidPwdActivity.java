/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Rim.Z                   Create
 * ===========================================================================================
 */
package com.pax.pay.password;

import com.pax.abl.utils.EncUtils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

/**
 * Change Void password
 */
public class ChangeVoidPwdActivity extends BaseChangePwdActivity {

    @Override
    protected void savePwd() {
        SysParam.getInstance().set(R.string.SEC_VOID_PWD, EncUtils.sha1(pwd));
    }
}
