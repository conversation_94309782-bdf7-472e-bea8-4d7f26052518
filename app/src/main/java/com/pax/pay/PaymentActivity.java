/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay;

import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.SystemClock;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;
import android.view.Window;
import android.widget.Toast;
import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionEndListener;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ATransaction.TransEndListener;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.edc.opensdk.AdjustRequest;
import com.pax.edc.opensdk.BaseRequest;
import com.pax.edc.opensdk.BaseResponse;
import com.pax.edc.opensdk.Constants;
import com.pax.edc.opensdk.InstallmentMsg;
import com.pax.edc.opensdk.MessageUtils;
import com.pax.edc.opensdk.PreAuthVoidRequest;
import com.pax.edc.opensdk.PrintLastSettleMsg;
import com.pax.edc.opensdk.PrintSummaryMsg;
import com.pax.edc.opensdk.PrintTransDetailMsg;
import com.pax.edc.opensdk.RequestArgsConstants;
import com.pax.edc.opensdk.RequestTotalAmount;
import com.pax.edc.opensdk.RequestTraceNo;
import com.pax.edc.opensdk.ResponseLogger;
import com.pax.edc.opensdk.SaleRequest;
import com.pax.edc.opensdk.SettleMsg;
import com.pax.edc.opensdk.TransResult;
import com.pax.edc.opensdk.YuuCreationMsg;
import com.pax.edc.opensdk.YuuCreationRetrievalMsg;
import com.pax.edc.opensdk.YuuEnquiryMsg;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.menu.TransMenuActivity;
import com.pax.pay.record.Printer;
import com.pax.pay.service.ParseResp;
import com.pax.pay.service.RequestUtil;
import com.pax.pay.trans.AdjustTrans;
import com.pax.pay.trans.AuthorizationTrans;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.DCCOptOutTrans;
import com.pax.pay.trans.InstMultiUpTrans;
import com.pax.pay.trans.InstalmentTrans;
import com.pax.pay.trans.MultipleUpTrans;
import com.pax.pay.trans.OfflineSaleTrans;
import com.pax.pay.trans.OuterSettleTrans;
import com.pax.pay.trans.PreAuthCMTrans;
import com.pax.pay.trans.PreAuthCMVoidTrans;
import com.pax.pay.trans.PreAuthTrans;
import com.pax.pay.trans.PreAuthVoidTrans;
import com.pax.pay.trans.PureRedeemTrans;
import com.pax.pay.trans.RedeemInstalmentTrans;
import com.pax.pay.trans.RefundTrans;
import com.pax.pay.trans.SaleTrans;
import com.pax.pay.trans.SaleVoidTrans;
import com.pax.pay.trans.SettleTrans;
import com.pax.pay.trans.SignOnTrans;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.YUUTrans;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionUpdateParam;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;
import com.pax.settings.SettingsActivity;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PaymentActivity extends AppCompatActivity {

    public static final int REQ_SELF_TEST = 1;

    public static final int SLEEP_MILLIS = 300;

    private static final String TAG = "EDC PAYMENT";
    public static final String TAG_EXIT = "EXIT";

    public static final String LAST_FLAG = "******";
    public static final int MICROPAY = 0x0A;

    private boolean needSelfTest = true;

    private int mTransType;
    //与transType的字段类型保持统一
    private int mTransTypeDetail;
    private BaseRequest request = null;

    private CustomAlertDialog mDialog;
    //TransType类型与具体整数值的对应集合
    private Map<String,Integer> transTypeMap = new HashMap<>();

    private ActionBar mActionBar;

    private TransEndListener endListener = new TransEndListener() {

        @Override
        public void onEnd(ActionResult result) {
            transFinish(result);
        }
    };

    private TransEndListener settleEndListener = new TransEndListener() {

        @Override
        public void onEnd(final ActionResult result) {
            if (result.getRet() != TransResult.SUCC) {
                transFinish(result);
                return;
            }

            ActionUpdateParam actionUpdateParam = new ActionUpdateParam(new ActionStartListener() {
                @Override
                public void onStart(AAction action) {
                    ((ActionUpdateParam) action).setParam(ActivityStack.getInstance().top(), false);
                }
            });
            actionUpdateParam.setEndListener(new ActionEndListener() {
                @Override
                public void onEnd(AAction action, ActionResult result1) {
                    autoLogon(result);
                }
            });
            actionUpdateParam.execute();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.activity_null);
        super.onCreate(savedInstanceState);
        mActionBar = getSupportActionBar();
        //屏蔽Home键
        Device.enableStatusBar(false);
        Device.enableHomeRecentKey(false);

        enableActionBar(false);
        WeakReference<PaymentActivity> weakReference = new WeakReference<>(this);
        ActivityStack.getInstance().push(weakReference.get());
        onCheckArgs();
        //初始化交易类型和对应数字的集合
        transTypeMap.put(Utils.getString(R.string.trans_sale),1);
        transTypeMap.put(Utils.getString(R.string.trans_offline_send),2);
        transTypeMap.put(Utils.getString(R.string.trans_refund),3);
        transTypeMap.put(Utils.getString(R.string.trans_void),4);
        transTypeMap.put(Utils.getString(R.string.trans_adjust),5);
        transTypeMap.put(Utils.getString(R.string.trans_instalment),6);
        transTypeMap.put(Utils.getString(R.string.trans_yuu_registration),8);
        transTypeMap.put(Utils.getString(R.string.trans_dcc_opt_out),11);
        transTypeMap.put(Utils.getString(R.string.trans_preAuth),12);
        transTypeMap.put(Utils.getString(R.string.trans_pre_auth_cancelltion),13);
        transTypeMap.put(Utils.getString(R.string.trans_pre_auth_completion),14);
        transTypeMap.put(Utils.getString(R.string.trans_pre_auth_comletion_cancelltion),15);
        transTypeMap.put(Utils.getString(R.string.trans_authorization),25);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        onExit(intent);
    }

    private boolean onExit(Intent intent) {
        if (intent != null) {
            boolean isExit = intent.getBooleanExtra(TAG_EXIT, false);
            if (isExit) {
                transFinish(new ActionResult(TransResult.ERR_ABORTED, null));
                return true;
            }
        }
        return false;
    }

    private void onCheckArgs() {
        if (getIntent() != null) {
            if (onExit(getIntent())) {
                return;
            }
            Intent intent = getIntent();
            mTransType = MessageUtils.getTransType(intent);
            request = MessageUtils.generateRequest(intent, mTransType);
            if (request == null || !RequestArgsConstants.SUCCESS.equals(MessageUtils.checkArgs(request))) {
                transFinish(new ActionResult(TransResult.ERR_PARAM, null));
                return;
            }

            if (getIntent().getData() == null) {
                transFinish(new ActionResult(TransResult.ERR_PARAM, null));
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        ActivityStack.getInstance().popTo(this);
        if (needSelfTest)
            SelfTestActivity.onSelfTest(PaymentActivity.this, REQ_SELF_TEST);
        //FinancialApplication.getSysParam().init();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQ_SELF_TEST:
                if (resultCode == RESULT_CANCELED) {
                    transFinish(new ActionResult(TransResult.ERR_ABORTED, null));
                    return;
                }
                onHandleSelfTest(resultCode, data);
                break;
            case Constants.REQUEST_CODE:
            case TransMenuActivity.REQUEST_CODE_CUP_QR:
                transFinish(new ActionResult(TransResult.SUCC, data));
                return;
            default:
                finish();
                break;
        }
    }

    private void onHandleSelfTest(int resultCode, Intent data) {
        needSelfTest = false;
        Controller controller = FinancialApplication.getController();
        int cnt = 0;
        while (controller.get(Controller.NEED_DOWN_AID) == Controller.Constant.YES && controller.get(Controller.NEED_DOWN_CAPK) == Controller.Constant.YES) {
            SystemClock.sleep(500);
            ++cnt;
            if (cnt > 3) {
                transFinish(new ActionResult(TransResult.ERR_PARAM, null));
                return;
            }
        }
        doTrans();
    }

    private void doTrans() {
        ActivityStack.getInstance().popTo(this);
        boolean isEnableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP);
        boolean isTipBefore = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_TIP_TYPE);
        try {
            if(amountEmpty(mTransType,request)) throw new Exception("wrong processing");
            switch (mTransType) {
                case Constants.AUTH:
                    doAuth((RequestTotalAmount) request);
                    break;
                case Constants.PRE_AUTH:
                    doPreAuth((RequestTotalAmount) request);
                    break;
                case Constants.PRE_AUTH_CM:
                    doPreAuthCm((RequestTotalAmount) request);
                    break;
                case Constants.PRE_AUTH_VOID:{
                    doPreAuthVoid((PreAuthVoidRequest)request);
                    break;
                }
                case Constants.PRE_AUTH_CM_VOID:{
                    doPreAuthCmVoid((RequestTraceNo)request);
                    break;
                }
                case Constants.SALE:
                    //如果是售后小费类型，传入了小费，则返回参数错误
                    if((!isEnableTip || (isEnableTip &&!isTipBefore)) && ((SaleRequest)request).getTipAmount()!=0) {
                        throw new Exception("wrong processing");
                    }else if(isEnableTip && isTipBefore &&((SaleRequest)request).getTipAmount()*11>((SaleRequest)request).getAmount()) {
                        throw new Exception("wrong processing");
                    }else doSale((SaleRequest) request);
                    break;
                case Constants.VOID:
                    doVoid((RequestTraceNo) request);
                    break;
                case Constants.REFUND: {
                    doRefund((RequestTotalAmount) request);
                    break;
                }
                case Constants.SETTLE:
                    doSettle((SettleMsg.Request)request);
                    break;
                case Constants.REPRINT_TRANS:
                    doReprintTrans((RequestTraceNo) request);
                    break;
                case Constants.PRINT_ALL_TRANSACTION_DETAIL:
                    doPrintAllTransDetail();
                    break;
                case Constants.DCC_OPT_OUT: {
                    doDccOptOut((RequestTraceNo) request);
                    break;
                }
                case Constants.OFFLINE:{
                    doOffline((RequestTotalAmount)request);
                    break;
                }
                case Constants.ADJUST: {
                    doAdjust((AdjustRequest) request);
                    break;
                }
                case Constants.INSTALLMENT: {
                    doInstallment((InstallmentMsg.Request) request);
                    break;
                }
                case Constants.TRANS_RETRIEVAL:{
                    doTransRetrieval((RequestTraceNo)request);
                    break;
                }
                case Constants.YUU_CREATION:{
                    doYuuCreation((YuuCreationMsg.Request)request);
                    break;
                }
                case Constants.YUU_ENQUIRY:{
                    doYuuEnquiry((YuuEnquiryMsg.Request)request);
                    break;
                }
                case Constants.YUU_CREATION_RETRIEVAL:{
                    doYuuCreationRetrieval((YuuCreationRetrievalMsg.Request)request);
                    break;
                }
                case Constants.PRINT_SUMMARY:{
                    doPrintSummary((PrintSummaryMsg.Request) request);
                    break;
                }
                case Constants.PRINT_TRANSACTION_DETAIL:{
                    printTransDetail((PrintTransDetailMsg.Request)request);
                    break;
                }
                case Constants.PRINT_LAST_SETTLE:{
                    printLastSettle((PrintLastSettleMsg.Request)request);
                    break;
                }

                default:
                    throw new Exception("wrong processing");
            }
        } catch (Exception e) {
            Log.w(TAG, "", e);
            transFinish(new ActionResult(TransResult.ERR_PARAM, null));
        }
    }

    private void doPreAuthCmVoid(RequestTraceNo request) {
        // if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_CUP)){
            new PreAuthCMVoidTrans(PaymentActivity.this, endListener, request).execute();
        // }
        /*Raymond 20220706: DCC workflow correction, the transaction operation not applicable
        else if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_DCC)){
            if (isDccEnabled()) {
                new PreAuthCMVoidTrans(PaymentActivity.this, endListener, true, request).execute();
            }
        }*/
    }

    private void doPreAuthVoid(PreAuthVoidRequest request) {
        // if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_CUP)){
            new PreAuthVoidTrans(PaymentActivity.this, endListener, request).execute();
        // }
        /*Raymond 20220706: DCC workflow correction, the transaction operation not applicable
        else if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_DCC)){
            if (isDccEnabled()) {
                new SaleVoidTrans(PaymentActivity.this, request, endListener, true).execute();
            }
        }*/
    }

    private void transFinish(final ActionResult result) {
        Intent intent = new Intent();
        setResult(intent, result);
        ActivityStack.getInstance().popAll(); //finish
        TransContext.getInstance().setCurrentAction(null);
        Device.enableStatusBar(true);
        Device.enableHomeRecentKey(true);
    }

    private void setHaseResult(Intent intent, ActionResult result){
        BaseResponse response = ParseResp.getResponse(mTransType, result);
        if (response != null) {
            ResponseLogger.logResponse(response);
            intent.putExtras(MessageUtils.toBundle(response, new Bundle()));
            setResult(RESULT_OK, intent);
        } else {
            setResult(RESULT_CANCELED, intent);
        }
    }

    /**
     * sale
     *
     * @param requestData
     */
    private void doHaseSale(SaleRequest requestData) {
        int transTypeDetail = requestData.getTransTypeDetail();
        BaseTrans baseTrans;
        switch (transTypeDetail){
            case BaseRequest.TRANS_DETAIL_PURE_REDEEM:{
                baseTrans = new PureRedeemTrans(PaymentActivity.this, requestData, endListener);
                break;
            }
            case BaseRequest.TRANS_DETAIL_MULTI_UP_REDEEM:{
                baseTrans = new MultipleUpTrans(PaymentActivity.this, requestData, endListener);
                break;
            }
            case BaseRequest.TRANS_DETAIL_INSTAL_REDEEM:{
                baseTrans = new RedeemInstalmentTrans(PaymentActivity.this, requestData,endListener);
                break;
            }
            case BaseRequest.TRANS_DETAIL_INSTAL_MULTI_UP_REDEEM:{
                baseTrans = new InstMultiUpTrans(PaymentActivity.this, requestData, endListener);
                break;
            }
            default:{
                baseTrans = new SaleTrans(PaymentActivity.this, requestData, endListener);
            }
        }
        baseTrans.execute();
    }
    /**
     * void
     *
     * @param requestData
     */
    private void doVoid(RequestTraceNo requestData) {
        if (isCupQr(requestData)) {
            //TODO
            //CupQRTransProc.getInstance().doCupQRTrans(RequestUtil.generateEcrRequest(requestData), endListener);
        } else {
            new SaleVoidTrans(PaymentActivity.this, requestData, endListener).execute();
        }
    }

    /**
     * refund
     *
     * @param requestData
     */
    private void doRefund(RequestTotalAmount requestData) {
        new RefundTrans(PaymentActivity.this, requestData, endListener).execute();
    }

    /**
     * auth
     *
     * @param requestData
     */
    private void doAuth(RequestTotalAmount requestData) {
        new AuthorizationTrans(PaymentActivity.this, requestData, endListener).execute();
    }

    /**
     * pre-authorization
     *
     * @param requestData
     */
    private void doPreAuth(RequestTotalAmount requestData) {
        /*Raymond 20220706: after DCC workflow correction, the 3rd party interface DCC parameter
        if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_CUP)){
            new PreAuthTrans(this, true, endListener, false, requestData).execute();
        } else if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_DCC)){
            if (isDccEnabled()) {
                new PreAuthTrans(this, true, endListener, true, requestData).execute();
            }
        }*/
        // if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_CUP)){
            new PreAuthTrans(this, true, endListener, requestData).execute();
        // }
    }

    /**
     * pre-authorization-completion
     *
     * @param requestData
     */
    private void doPreAuthCm(RequestTotalAmount requestData) {
        // if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_CUP)){
            new PreAuthCMTrans(PaymentActivity.this, endListener,requestData).execute();
        // }
        /*Raymond 20220706: DCC workflow correction, the transaction operation not applicable
        else if (transTypeDetail.equals(BaseRequest.TRANS_DETAIL_PRE_AUTH_DCC)){
            if (isDccEnabled()) {
                new PreAuthCMTrans(PaymentActivity.this, endListener,
                        true, false, requestData).execute();
            }
        }*/
    }

    private boolean isDccEnabled(){
        boolean isDccEnabled =
                FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC);
        if (!isDccEnabled){
            Toast.makeText(this, getString(R.string.please_open_dcc_first), Toast.LENGTH_SHORT).show();
        }
        return isDccEnabled;
    }

    /**
     * settle
     */
    private void doSettle() {
        new SettleTrans(PaymentActivity.this, settleEndListener).execute();
    }


    /**
     * dcc_opt_out
     *
     * @param requestData
     */
    private void doDccOptOut(RequestTraceNo requestData) {
        new DCCOptOutTrans(PaymentActivity.this, requestData, endListener).execute();
    }

    /**
     * reprint the last transaction, or reprint the transaction by transNo
     *
     * @param requestData
     */
    private void doReprintTrans(RequestTraceNo requestData) {
        int traceNo = Utils.parseIntSafe(requestData.getTraceNo(),0);
        //根据是否能够成功转换数值来判断是否是特殊符号
        if (traceNo == 0) {
            //如果是"******"则进行打印上一次交易
            if(LAST_FLAG.equals(requestData.getTraceNo()))
                doPrnLast();
            else
                transFinish(new ActionResult(TransResult.ERR_PARAM, null));//如果不是"******"则返回参数错误
        } else {
            doPrnTransByTransNo(traceNo);
        }
    }

    /**
     * print last transaction
     */
    private void doPrnLast() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                int result = Printer.printLastTrans(PaymentActivity.this);
                onPrintResult(result);
            }
        }).start();
    }

    private void doPrnTransByTransNo(int transNo) {
        final TransData transData = FinancialApplication.getTransDataDbHelper().findTransDataByInvoiceNo(transNo);

        if (transData == null) {
            onPrintResult(TransResult.ERR_NO_ORIG_TRANS);
        } else {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    Printer.printTransAgain(PaymentActivity.this, transData);
                    transFinish(new ActionResult(TransResult.SUCC, null));
                }
            }).start();
        }
    }

    private void doPrintAllTransDetail(){
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                //AET-112
                final int result = Printer.printTransAll(getString(R.string.print_trans_all),
                        PaymentActivity.this);
                onPrintResult(result);
            }
        });
    }

    /**
     * print detail
     */
    private void doPrnDetail() {
        new Thread(new Runnable() {
            @Override
            public void run() {
//                int result = Printer.printTransDetail(getString(R.string.print_history_detail),
//                        PaymentActivity.this, FinancialApplication.getAcqManager().getCurAcq());

                transFinish(new ActionResult(TransResult.SUCC, null));
            }
        }).start();
    }

    /**
     * print total
     */
    private void doPrnTotal() {
        //FIXME may have bug the getCurAcq
        new Thread(new Runnable() {
            @Override
            public void run() {
//                Printer.printTransTotal(PaymentActivity.this, FinancialApplication.getAcqManager().getCurAcq());

                transFinish(new ActionResult(TransResult.SUCC, null));
            }
        }).start();
    }

    /**
     * print last batch
     */
    private void doPrnLastBatch() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                int result = Printer.printLastBatch(PaymentActivity.this);

                transFinish(new ActionResult(result, null));
            }
        }).start();
    }

    /**
     * terminal setting
     */
    private void doSetting() {
        ActionInputPassword inputPasswordAction = new ActionInputPassword(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(PaymentActivity.this, 8,
                        getString(R.string.prompt_sys_pwd), null);
            }
        });

        inputPasswordAction.setEndListener(new ActionEndListener() {

            @Override
            public void onEnd(AAction action, ActionResult result) {

                if (result.getRet() != TransResult.SUCC) {
                    transFinish(result);
                    return;
                }

                String data = EncUtils.pwdSha((String) result.getData());
                if (!data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_SYS_PWD))) {
                    transFinish(new ActionResult(TransResult.ERR_PASSWORD, null));
                    return;
                }

                Intent intent = new Intent(PaymentActivity.this, SettingsActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.settings_title));
                bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intent.putExtras(bundle);
                startActivityForResult(intent, Constants.REQUEST_CODE);
            }
        });

        inputPasswordAction.execute();
    }

    /**
     *
     * @param requestData
     */
    private void doOffline(RequestTotalAmount requestData) {
        new OfflineSaleTrans(this, requestData, endListener).execute();
    }



    /**
     *
     * @param request
     */
    private void doAdjust(AdjustRequest request) {
        new AdjustTrans(this, request, endListener).execute();
    }

    /**
     *
     * @param request
     */
    private void doInstallment(InstallmentMsg.Request request) {
        new InstalmentTrans(this, request, endListener).execute();
    }

    /**
     *
     * @param request
     */
    private void doTransRetrieval(final RequestTraceNo request) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                SystemClock.sleep(SLEEP_MILLIS);
                String invoiceNo = request.getTraceNo();
                TransData transData = null;
                long traceNumber = Utils.parseLongSafe(invoiceNo, 0);
                //根据是否能成功转换来判断是否是正确的参数
                if(traceNumber == 0){
                    //如果是"*******"则是查询上一次交易记录
                    if (com.pax.pay.ecr.Constants.RETRIEVE_LAST_TRANS_INVOICE_NO.equals(invoiceNo)) {
                        transData = FinancialApplication.getTransDataDbHelper().findLastTransData();
                    } else{
                        //如果不是"******"则视为参数错误，结束交易
                        transFinish(new ActionResult(TransResult.ERR_PARAM, null));
                    }
                } else {
                    //如果能够转换成正常数字，则进行指定数字的订单查询。
                    transData = FinancialApplication.getTransDataDbHelper()
                            .findTransDataByInvoiceNo(traceNumber);
                }
                //传入交易类型，防止在后面被空置覆盖导致响应数据无法正确处理。
                if(transData!=null){
                    int transType = 0;
                    //仅在特殊交易时才重新设置transTypeDetail
                    int transDetail ;
                    if(transData.getTransType()!=null) {
                        //根据积分交易的具体类型重新设置交易类型和交易细节
                        if(ETransType.PURE_REDEEM.getTransName().equals(transData.getTransType().getTransName())){
                            transType = Constants.SALE;
                            transDetail = BaseRequest.TRANS_DETAIL_PURE_REDEEM;
                            //将原来的transtypeDetail设置上，防止被后面的覆盖
                            transData.setTransTypeDetail(transDetail);
                        } else if (ETransType.MULTIPLE_UP.getTransName().equals(transData.getTransType().getTransName())) {
                            transType = Constants.SALE;
                            transDetail = BaseRequest.TRANS_DETAIL_MULTI_UP_REDEEM;
                            //将原来的transtypeDetail设置上，防止被后面的覆盖
                            transData.setTransTypeDetail(transDetail);
                        }else if (ETransType.REDEEM_INST.getTransName().equals(transData.getTransType().getTransName())) {
                            transType = Constants.SALE;
                            transDetail = BaseRequest.TRANS_DETAIL_INSTAL_REDEEM;
                            transData.setTransTypeDetail(transDetail);
                        }else if (ETransType.INST_MULTI_UP.getTransName().equals(transData.getTransType().getTransName())) {
                            transType = Constants.SALE;
                            transDetail = BaseRequest.TRANS_DETAIL_INSTAL_MULTI_UP_REDEEM;
                            transData.setTransTypeDetail(transDetail);
                        }else if (ETransType.OFFLINE_TRANS_SEND.getTransName().equals(transData.getTransType().getTransName())
                        && ETransType.DCC_OPT_OUT.getTransName().equals(transData.getOrigTransType().getTransName())) {
                            //DCC交易的特殊情况，会将交易类型置为离线交易类型，因此这里需要特殊处理
                            transType = Constants.DCC_OPT_OUT;
                            transDetail = BaseRequest.TRANS_DETAIL_CREDIT_CARD;
                            //将transTypeDetail设置上，防止覆盖
                            transData.setTransTypeDetail(transDetail);
                            transData.setEcrOrigTransType((byte) Constants.SALE);
                        }else {
                            //如果不是积分交易，则按正常交易处理
                            //如果交易已撤销，则将交易类型切换为VOID交易，并设置交易类型为原类型
                            if(TransData.ETransStatus.VOIDED == transData.getTransState()){
                                //如果是预授权或者预授权完成，则使用预授权或者预授权完成撤销的交易类型
                                if(ETransType.PREAUTH.getTransName().equals(transData.getTransType().getTransName())) {
                                    transType = Constants.PRE_AUTH_VOID;
                                }else if (ETransType.PREAUTHCM.getTransName().equals(transData.getTransType().getTransName())) {
                                    transType = Constants.PRE_AUTH_CM_VOID;
                                }else{
                                    transType = Constants.VOID;
                                }
                                if(transData.getOrigTransType()!=null) {
                                    int originTransType = transTypeMap.get(transData.getOrigTransType().getTransName());
                                    transData.setEcrOrigTransType((byte) originTransType);
                                }
                            } else if (TransData.ETransStatus.ADJUSTED == transData.getTransState()) {
                                //如果交易已调整，则将交易类型切换为ADJUST交易
                                transType = Constants.ADJUST;
                                transData.setEcrOrigTransType((byte) Constants.SALE);
                            }else{
                                transType = transTypeMap.get(transData.getTransType().getTransName());
                            }
                        }
                    }
                    transData.setEcrMsgType((byte) transType);
                    if(transData.getOrigTransType()!=null &&
                            ETransType.PURE_REDEEM.getTransName().equals(transData.getTransType().getTransName()) &&
                            ETransType.MULTIPLE_UP.getTransName().equals(transData.getTransType().getTransName()) &&
                            ETransType.REDEEM_INST.getTransName().equals(transData.getTransType().getTransName()) &&
                            ETransType.INST_MULTI_UP.getTransName().equals(transData.getTransType().getTransName())) {
                        int originTransType = transTypeMap.get(transData.getOrigTransType().getTransName());
                        transData.setEcrOrigTransType((byte) originTransType);
                    }
                }
                int isTransExist = transData != null ? TransResult.SUCC : TransResult.ERR_NO_ORIG_TRANS;
                endListener.onEnd(new ActionResult(isTransExist, transData));
            }
        };
        showDialog(getString(R.string.trans_retrieval));
        FinancialApplication.getApp().runInBackground(runnable);
    }

    private void doYuuCreation(YuuCreationMsg.Request request) {
        new YUUTrans(this, request, true, (byte) -1, endListener,
                ETransType.YUU_REGISTRATION).execute();
    }

    private void doYuuEnquiry(YuuEnquiryMsg.Request request){
        new YUUTrans(this, request, true, (byte) -1, endListener, ETransType.YUU_ENQUIRY).execute();
    }

    private void doYuuCreationRetrieval(final YuuCreationRetrievalMsg.Request request) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                SystemClock.sleep(SLEEP_MILLIS);
                TransData transData = null;
                //YUU registration只保存了最近一笔交易
                List<ETransType> list = new ArrayList<>();
                list.add(ETransType.YUU_REGISTRATION);
                List<TransData.ETransStatus> filter = new ArrayList<>();
                List<TransData> transDataList =
                        FinancialApplication.getTransDataDbHelper().findTransData(list, filter);
                if(transDataList.size() > 0){
                    transData = transDataList.get(0);
                }
                int isTransExist = transData != null ? TransResult.SUCC : TransResult.ERR_NO_ORIG_TRANS;
                endListener.onEnd(new ActionResult(isTransExist, transData));
            }
        };
        showDialog(getString(R.string.yuu_creation_retrieval));
        FinancialApplication.getApp().runInBackground(runnable);
    }

    private void doPrintSummary(PrintSummaryMsg.Request request) {
        final Acquirer acquirer = getAcquire(request.getAcquirerIndex());
        //如果是Mircropay则不用处理
        if(request.getAcquirerIndex() ==MICROPAY){}
        else {
            FinancialApplication.getApp().runInBackground(new Runnable() {
                @Override
                public void run() {
                    if (null != acquirer) {
                        Printer.printTransTotal(PaymentActivity.this, acquirer);
                    } else {
                        Runnable runnable = new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(PaymentActivity.this,
                                        getString(R.string.no_acquirer), Toast.LENGTH_LONG).show();
                            }
                        };
                        FinancialApplication.getApp().runOnUiThread(runnable);
                    }
                    transFinish(new ActionResult(TransResult.SUCC, null));
                }
            });
        }
    }


    private Acquirer getAcquire(int acquireIndex){
        String acquireName = RequestUtil.getAcquirerInfo(acquireIndex);
        if(null == acquireName){
            return null;
        }
        List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();
        for (Acquirer acquirer : listAcquirers){
            if (acquirer.getName().equals(acquireName)){
                return acquirer;
            }
        }
        return null;
    }

    private void showDialog(String info) {
        mDialog = new CustomAlertDialog(ActivityStack.getInstance().top(), CustomAlertDialog.WARN_TYPE);
        mDialog.setCancelable(false);
        mDialog.setImage(BitmapFactory.decodeResource(FinancialApplication.getApp().getResources(), R.drawable.ic16));
        mDialog.setTimeout(1);
        mDialog.setContentText(info);
        mDialog.show();
    }

    private void doSettle(SettleMsg.Request request) {
        //如果是Mircropay则不用处理
        if(request.getAcquirerIndex() == MICROPAY){}
        else {
            new OuterSettleTrans(PaymentActivity.this, settleEndListener, request).execute();
        }
    }

    //Auto logon Hase Cup after Settlement
    private void autoLogon(final ActionResult result) {
        boolean isHaseCupSettleSuccess = false;
        if (result.getData() instanceof Boolean) {
            isHaseCupSettleSuccess = (boolean) result.getData();
        }
        if (!isHaseCupSettleSuccess){
            transFinish(result);
            return;
        }
        new SignOnTrans(this, new TransEndListener() {
            @Override
            public void onEnd(ActionResult res) {
                transFinish(result);
            }
        }, com.pax.pay.constant.Constants.UPI_TYPE).execute();
    }

    private void printTransDetail(PrintTransDetailMsg.Request request){
        int acquirerIndex = request.getAcquirerIndex();
        final Acquirer acquirer = getAcquire(acquirerIndex);
        if(acquirerIndex == MICROPAY){}
        else {
            FinancialApplication.getApp().runInBackground(new Runnable() {
                @Override
                public void run() {
                    //AET-112
                    int result = Printer.printTransDetail(getString(R.string.print_history_detail),
                            PaymentActivity.this, acquirer);
                    onPrintResult(result);
                }
            });
        }
    }

    private void printLastSettle(PrintLastSettleMsg.Request request) {
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                int result = Printer.printLastBatch(PaymentActivity.this);
                onPrintResult(result);
            }
        });
    }

    /**
     * sale
     *
     * @param requestData
     */
    private void doSale(SaleRequest requestData) {
        if (isCupQr(requestData)) {
            doCupQrSale(requestData);
        } else {
            doHaseSale(requestData);
        }
    }

    private void doCupQrSale(SaleRequest requestData) {
        //TODO
        //CupQRTransProc.getInstance().doCupQRTrans(RequestUtil.generateEcrRequest(requestData), endListener);
    }

    private void setResult(Intent intent, ActionResult result) {
        if (isCupQr(null)) {
            setCupQrResult(intent, result);
        } else {
            setHaseResult(intent, result);
        }
    }

    private void setCupQrResult(Intent intent, ActionResult result) {
        com.pax.edc.upiopensdk.BaseResponse baseResponse =
                FinancialApplication.getiUpiTransAPI().onResult(
                        TransMenuActivity.REQUEST_CODE_CUP_QR, 0, (Intent) result.getData());
        if (null == baseResponse){
            setResult(RESULT_CANCELED, intent);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.TRANS_RESULT_SERIALIZABLE, baseResponse);
        intent.putExtras(bundle);
        setResult(RESULT_OK, intent);
    }

    private boolean isCupQr(BaseRequest baseRequest) {
        if (null != baseRequest) {
            mTransTypeDetail = baseRequest.getTransTypeDetail();
        }
        return mTransTypeDetail != 0 && mTransTypeDetail ==
                BaseRequest.TRANS_DETAIL_CUP_OR_UPLAN_QR;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null != mDialog && mDialog.isShowing()) {
            mDialog.dismiss();
        }
    }

    private void onPrintResult(final int result) {
        if (result == TransResult.SUCC) {
            transFinish(new ActionResult(result, null));
            return;
        }
        DialogInterface.OnDismissListener onDismissListener =
                new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialogInterface) {
                        transFinish(new ActionResult(result, null));
                    }
                };
        DialogUtils.showErrMessage(PaymentActivity.this,
                getString(R.string.transType_print), getString(R.string.err_no_trans),
                onDismissListener, com.pax.pay.constant.Constants.FAILED_DIALOG_SHOW_TIME);
    }


    //判断输入的金额是否为空
    private boolean amountEmpty(int mTransType,BaseRequest request){
        boolean amountEmpty =false;
        switch(mTransType){
            case Constants.AUTH:
            case Constants.PRE_AUTH:
            case Constants.PRE_AUTH_CM:
            case Constants.REFUND:
            case Constants.OFFLINE:
                if(((RequestTotalAmount)request).getAmount() == 0) {
                    amountEmpty = true;
                }
                break;
            case Constants.SALE:
                if(((SaleRequest)request).getAmount() == 0) {
                    amountEmpty = true;
                }
                break;
            case Constants.ADJUST:
                if(((AdjustRequest)request).getAmount() == 0) {
                    amountEmpty = true;
                }
                break;
            case Constants.INSTALLMENT:
                if(((InstallmentMsg.Request)request).getAmount() == 0) {
                    amountEmpty = true;
                }
                break;
        }
        return amountEmpty;
    }

    //控制ActionBar的禁用和启用
    protected void enableActionBar(boolean showActionBar) {
        if (mActionBar != null) {
            if (showActionBar) {
                mActionBar.show();
            } else {
                mActionBar.hide();
            }
        }
    }
}
