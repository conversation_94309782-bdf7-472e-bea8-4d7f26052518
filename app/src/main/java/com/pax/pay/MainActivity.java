/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;

import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.lifecycle.ViewModelProvider;

import com.pax.abl.core.ATransaction;
import com.pax.abl.utils.AlarmUtils;
import com.pax.abl.utils.EncUtils;
import com.pax.appstore.DownloadManager;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.EmvAid;
import com.pax.data.entity.EmvCapk;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.eventbus.TerminalIdleTriggerEvent;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.menu.AuthMenuActivity;
import com.pax.pay.menu.HelpNeededActivity;
import com.pax.pay.menu.ManageMenuActivity;
import com.pax.pay.menu.QsparcMenuActivity;
import com.pax.pay.mqtt.MQTTForegroundService;
import com.pax.pay.mqtt.MQTTViewModel;
import com.pax.pay.mqtt.worker.PreciseAlarmScheduler;
import com.pax.pay.trans.AdjustTrans;
import com.pax.pay.trans.QRTrans;
import com.pax.pay.trans.CashOnlyTrans;
import com.pax.pay.trans.OfflineSaleTrans;
import com.pax.pay.trans.RefundTrans;
import com.pax.pay.trans.SaleCashTrans;
import com.pax.pay.trans.SaleTrans;
import com.pax.pay.trans.SaleVoidTrans;
import com.pax.pay.trans.SettleTrans;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionAutoSettle;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionUpdateParam;
import com.pax.pay.trans.action.activity.DccMassageActivity;
import com.pax.pay.trans.action.activity.emi.EmiTransActivity;
import com.pax.pay.trans.action.activity.YonoTransActivity;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.EnterAmountTextWatcher;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.MenuPage;
import com.pax.view.UserGuideManager;
import com.pax.view.dialog.DialogUtils;
import com.pax.view.keyboard.CustomKeyboardEditText;
import com.shizhefei.guide.GuideHelper;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class MainActivity extends BaseActivity implements MenuPage.OnProcessTitleListener {
    // AET-87 remove payType
    private CustomKeyboardEditText edtAmount; // input amount
    private MenuPage menuPage;
    private LinearLayout mLayout;
    private ImageView bannerImage;
    private final EnterAmountTextWatcher amountWatcher = new EnterAmountTextWatcher();
    private FrameLayout sidebarView;
    private FrameLayout floatingBallView;
    private ImageView fabIcon;
    private MQTTViewModel mqttViewModel;
    private boolean isBound = false;
    private MQTTForegroundService mqttService;
    private final ATransaction.TransEndListener transEndListener = result -> FinancialApplication.getApp().runOnUiThread(this::handleTransactionCompletion);
    private static final String TAG = "MainActivity";
    private boolean isSaleProcessing = false;

    private boolean needShowPwd;

    @Override
    public void process(String title) {
        if (isSaleProcessing) {
            Log.i(TAG, "sale交易过程屏蔽菜单点击");
            return;
        }
        switch (title) {
            case Constants.VOID:
                new SaleVoidTrans(MainActivity.this, transEndListener).execute();
                break;
            case Constants.SALE_CASH:
                new SaleCashTrans(MainActivity.this, null, (byte) -1, transEndListener).execute();
                break;
            case Constants.CASH_ONLY:
                new CashOnlyTrans(MainActivity.this, null, (byte) -1, transEndListener).execute();
                break;
            case Constants.PRE_AUTH:
                Intent intent = new Intent(this, AuthMenuActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_preAuth));
                bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case Constants.SETTLEMENT:
                new SettleTrans(MainActivity.this, transEndListener).execute();
                break;
            case Constants.MANAGEMENT:
                Intent intentForManage = new Intent(this, ManageMenuActivity.class);
                Bundle bundleForManage = new Bundle();
                bundleForManage.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_manage));
                bundleForManage.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intentForManage.putExtras(bundleForManage);
                startActivity(intentForManage);
                break;
            case Constants.QSPARC:
                Intent intentForQsparc = new Intent(this, QsparcMenuActivity.class);
                Bundle bundleForQsaprc = new Bundle();
                bundleForQsaprc.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_qsparc));
                bundleForQsaprc.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intentForQsparc.putExtras(bundleForQsaprc);
                startActivity(intentForQsparc);
                break;
            case Constants.CALL_HELP:
                Intent intentForHelp = new Intent(this, HelpNeededActivity.class);
                Bundle bundleForHelp = new Bundle();
                bundleForHelp.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_call_help));
                bundleForHelp.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intentForHelp.putExtras(bundleForHelp);
                startActivity(intentForHelp);
                break;
            case Constants.YONO:
                Intent intentForYONO = new Intent(this, YonoTransActivity.class);
                Bundle bundleForYONO = new Bundle();
                bundleForYONO.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_yono));
                bundleForYONO.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intentForYONO.putExtras(bundleForYONO);
                startActivity(intentForYONO);
                break;
            case Constants.OFFLINE:
                new OfflineSaleTrans(MainActivity.this, transEndListener).execute();
                break;
            case Constants.ADJUST:
                new AdjustTrans(MainActivity.this, transEndListener).execute();
                break;
            case Constants.REFUND:
                new RefundTrans(MainActivity.this, transEndListener).execute();
                break;
            case Constants.QR:
                new QRTrans(MainActivity.this, transEndListener).execute();
                break;
            case Constants.EMI:
                Intent intentForEMI = new Intent(this, EmiTransActivity.class);
                Bundle bundleForEMI = new Bundle();
                bundleForEMI.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_emi));
                bundleForEMI.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intentForEMI.putExtras(bundleForEMI);
                startActivity(intentForEMI);
                break;
            case Constants.DCC_DOWNLOAD:
                Intent intentForDcc = new Intent(this, DccMassageActivity.class);
                Bundle bundleForDcc = new Bundle();
                bundleForDcc.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_dcc_download));
                bundleForDcc.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
                intentForDcc.putExtras(bundleForDcc);
                startActivity(intentForDcc);
                break;
            default:
                break;

        }
    }

    private void handleTransactionCompletion() {
        resetUI();
        isSaleProcessing = false;
    }

    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            MQTTForegroundService.LocalBinder binder = (MQTTForegroundService.LocalBinder) service;
            mqttService = binder.getService();
            mqttService.setViewModel(mqttViewModel); // 关键！传递ViewModel给Service
            isBound = true;
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            isBound = false;
            mqttService = null;
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        FinancialApplication.setIsExitApp(false);
        // 重置显示参数更新弹窗显示的标志位
        FinancialApplication.setUpdateDialogShowing(false);
        mqttViewModel = new ViewModelProvider(this).get(MQTTViewModel.class);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        AlarmUtils.setDailyAlarm();
        FinancialApplication.getApp().runInBackground(() -> {
            FinancialApplication.setAidParamList(EmvAid.toAidParams());
            FinancialApplication.setCapkList(EmvCapk.toCapk());
        });
        super.onCreate(savedInstanceState);
        //从应用管理安装APK打开应用，点击home再点击图标启动应用，会调用onCreate
        if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
            ActivityStack.getInstance().pop();
        }

        edtAmount.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                initUserGuideView();
                edtAmount.removeOnAttachStateChangeListener(this);
            }

            @Override
            public void onViewDetachedFromWindow(View v) {

            }
        });
        // 检查是否需要自动结算(考虑还未到自动结算时间后关机的情况)
        needAutoSettlement();
        MQTTUtils.deleteExpireData();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setupClickListeners() {
        // 悬浮球点击切换侧边栏
        floatingBallView.setOnClickListener(v -> showSidebar());

        // 侧边栏点击返回悬浮球
        sidebarView.setOnClickListener(v -> showFloatingBall());
    }

    private void showFloatingBall() {
        if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
            // 移除侧边栏
            if (sidebarView != null) {
                sidebarView.setVisibility(View.GONE);
            }
            if (floatingBallView != null) {
                floatingBallView.setVisibility(View.VISIBLE);
            }
        }
    }

    private void showSidebar() {
        if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
            if (floatingBallView != null) {
                floatingBallView.setVisibility(View.GONE);
            }
            if (sidebarView != null) {
                sidebarView.setVisibility(View.VISIBLE);
            }
        }
    }

    private void hideAllFloatingViews() {
        // 移除悬浮球
        if (floatingBallView != null) {
            floatingBallView.setVisibility(View.GONE);
        }
        // 移除侧边栏
        if (sidebarView != null) {
            sidebarView.setVisibility(View.GONE);
        }
    }

    /**
     * 根据 MQTT 开关状态动态管理服务绑定
     */
    public void updateMqttServiceBinding() {
        boolean mqttEnabled = SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT));

        if (mqttEnabled && !isBound) {
            // MQTT 功能开启且服务未绑定，则绑定服务
            mqttViewModel = new ViewModelProvider(this).get(MQTTViewModel.class);
            mqttViewModel.getConnectionState().observe(this, state -> {
                if (state == null) return;
                Log.d(TAG, "MQTT connection state changed: " + state.getStatus());
                switch (state.getStatus()) {
                    case CONNECTED:
                        fabIcon.setImageResource(R.drawable.mqtt_connected);
                        break;
                    case DISCONNECTED:
                    case ERROR:
                        fabIcon.setImageResource(R.drawable.mqtt_disconnected);
                        break;
                }
                floatingBallView.setVisibility(View.VISIBLE);
            });

            Intent intent = new Intent(this, MQTTForegroundService.class);
            bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
            //开机自启动之后MQTT 服务通过Bind机制开启这时候需要手动开启TTS 服务
            MQTTUtils.startTTS();
            Log.i(TAG, "绑定 MQTT 服务");
        } else if (!mqttEnabled && isBound) {
            // MQTT 功能关闭且服务已绑定，则解绑服务
            unbindService(serviceConnection);
            isBound = false;
            if (floatingBallView != null) {
                floatingBallView.setVisibility(View.GONE);
            }
            Log.i(TAG, "解绑 MQTT 服务");
        } else if (mqttEnabled && isBound && mqttService != null) {
            // 已绑定但可能需要刷新状态
            Log.d(TAG, "MQTT service already bound, refreshing connection status");
            // 确保服务使用最新的ViewModel
            mqttService.setViewModel(mqttViewModel);
        }
    }

    private void initUserGuideView() {
        if (!UserGuideManager.getInstance().isSaleGuideEnabled() || !FinancialApplication.isUpdateFinished()) {
            return;
        }
        final GuideHelper guideHelper = new GuideHelper(this);
        GuideHelper.TipData tipData = new GuideHelper.TipData(FinancialApplication.isJapanese() ? R.drawable.tip_enter_amount_jap : R.drawable.tip_enter_amount, Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL, edtAmount);
        tipData.setViewBg(getResources().getDrawable(R.color.primary));
        GuideHelper.TipData tipOk = new GuideHelper.TipData(R.drawable.tip_ok_btn_en, Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL);
        tipOk.setLocation(0, -300);
        tipOk.setOnClickListener(v -> {
            guideHelper.nextPage();
            UserGuideManager.getInstance().setEnable(false);
        });
        guideHelper.addPage(false, tipData, tipOk);
        guideHelper.show(false);
        UserGuideManager.getInstance().setEnable(false);
    }

    @Override
    protected void onResume() {
        FinancialApplication.setIsEnteringSaleAmount(false);
        super.onResume();
        updateMqttServiceBinding();
        showFloatingBall(); // 显示悬浮窗
        EventBus.getDefault().post(new TerminalIdleTriggerEvent(true));
        Device.enablePhysicalPowerButton(true);
        isSaleProcessing = false;
        // 变更了相应的菜单开关才重新设置菜单
        if (FinancialApplication.isNeedChangeMenu()) {
            FinancialApplication.setNeedChangeMenu(false);
            mLayout.removeView(menuPage);
            menuPage = createMenu();
            menuPage.setOnProcessTitleListener(this);
            mLayout.addView(menuPage);
        }
        ActivityStack.getInstance().popTo(this);
        // 重新設置位數
        amountWatcher.setMaxValue();
        resetUI();
        //参数更新检查
        ActionUpdateParam actionUpdateParam = new ActionUpdateParam(action -> ((ActionUpdateParam) action).setParam(MainActivity.this, false));

        //应用更新监测
        List<ETransType> list = new ArrayList<>();
        list.add(ETransType.PREAUTH);
        List<TransData> preAuthData = GreendaoHelper.getTransDataHelper().findTransData(list);
        if (FinancialApplication.isAppNeedUpdate() && GreendaoHelper.getTransDataHelper().countOf() > 0 && preAuthData.size() != GreendaoHelper.getTransDataHelper().countOf()) {

            DialogUtils.showUpdateDialog(MainActivity.this, Utils.getString(R.string.app_need_update_please_settle));
            FinancialApplication.setAppNeedUpdate(false);
        } else {
            FinancialApplication.setAppNeedUpdate(false);
            actionUpdateParam.execute(false);
        }
    }

    @Override
    protected void loadParam() {
        //If widget call MainActivity, need to show keyboard immediately.
        CurrencyConverter.setDefCurrency(SysParam.getInstance().getString(R.string.EDC_CURRENCY_LIST));
    }

    /**
     * reset MainActivity
     */
    private void resetUI() {
        menuPage.setCurrentPager(0);
        updateAmount();
        String bannerImageName = SysParam.getInstance().getString(Utils.getString(R.string.EDC_BANNER_IMAGE_FILE));
        String bannerImageFilePath = DownloadManager.getInstance().getFilePath() + File.separator + bannerImageName;
        File bannerImageFile = new File(bannerImageFilePath);
        if (!TextUtils.isEmpty(bannerImageName) && bannerImageFile.exists()) {
            bannerImage.setImageBitmap(BitmapFactory.decodeFile(bannerImageFilePath));
        } else {
            bannerImage.setImageDrawable(getDrawable(R.drawable.main_banner));
            Log.d(TAG, "resetUI: banner image does not exist");
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        hideAllFloatingViews(); // 隐藏所有悬浮视图
    }

    @Override
    protected int getLayoutId() {
        if (Utils.isA50()) {
            return R.layout.activity_main_a50;
        } else {
            return R.layout.activity_main;
        }
    }

    @Override
    protected void initViews() {
        enableActionBar(false);
        // set amount input box
        edtAmount = findViewById(R.id.amount_editText);
        edtAmount.setText("");

        mLayout = findViewById(R.id.ll_gallery);
        menuPage = createMenu();
        mLayout.addView(menuPage);
        edtAmount.clearFocus();
        floatingBallView = findViewById(R.id.fab_container);
        fabIcon = findViewById(R.id.fab_icon);
        sidebarView = findViewById(R.id.sidebar);
        setupClickListeners();
        bannerImage = findViewById(R.id.banner_image);

    }

    /*RINT
     * create menu
     */
    private MenuPage createMenu() {
        MenuPage.Builder builder = new MenuPage.Builder(MainActivity.this, 30, 3);
        // void
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_VOID)) {
            builder.addTransItem(Constants.VOID, R.drawable.app_void);
        }
        //BQR
        builder.addMenuItem(Constants.QR, R.drawable.app_bharat_qr)
                // pre-authorization
                .addMenuItem(Constants.PRE_AUTH, R.drawable.app_auth)
                // 结算 AET-14
                .addTransItem(Constants.SETTLEMENT, R.drawable.app_settlement)
                // management
                .addMenuItem(Constants.MANAGEMENT, R.drawable.app_manage)
                // call help
                .addMenuItem(Constants.CALL_HELP, R.drawable.app_call_help)
                // YONO trans
                // .addMenuItem(Constants.YONO, R.drawable.app_yono)
                /*// offline sale
                .addMenuItem(Constants.OFFLINE, R.drawable.app_offline)*/
                // adjust
                .addMenuItem(Constants.ADJUST, R.drawable.app_adjust);
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_REFUND)) {
            builder.addTransItem(Constants.REFUND, R.drawable.app_refund);
        }
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_EMI)) {
            builder.addTransItem(Constants.EMI, R.drawable.emi);
        }
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_CASH_ONLY)) {
            // Cash@POS
            builder.addTransItem(Constants.CASH_ONLY, R.drawable.app_cash_only);
        }
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_SALE_CASH)) {
            //sale cash
            builder.addMenuItem(Constants.SALE_CASH, R.drawable.app_sale_cash);
        }
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_MONEY_ADD_CASH) ||
                SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_MONEY_ADD_ACCOUNT) ||
                SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_BALANCE_UPDATE) ||
                SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_BALANCE_ENQUIRY)) {
            // qsparc
            builder.addMenuItem(Constants.QSPARC, R.drawable.app_qsparc);
        }
        if (SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_DCC_SUPPORT_CHIP_CARD) ||
                SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_DCC_SUPPORT_MANUAL_CARD) ||
                SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_DCC_SUPPORT_SWIPE_CARD) ||
                SysParam.getInstance().getBoolean(R.string.EDC_ENABLE_DCC_SUPPORT_TAP_CARD)) {
            builder.addMenuItem(Constants.DCC_DOWNLOAD, R.drawable.app_dcc_dowload);
        }
        return builder.create();
    }

    @Override
    protected void setListeners() {
        edtAmount.addTextChangedListener(amountWatcher);
        edtAmount.setOnEditorActionListener(new MainEditorActionListener(this));
        menuPage.setOnProcessTitleListener(this);
    }

    @Override
    protected void onDestroy() {
        hideAllFloatingViews();
        if (isBound) {
            unbindService(serviceConnection);
            isBound = false;
        }
        if (edtAmount != null) {
            edtAmount.removeTextChangedListener(amountWatcher);
            edtAmount.setOnEditorActionListener(null);
        }
        super.onDestroy();
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        // 允许显示才显示
        if (needShowPwd) {
            inputPwd();
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    protected boolean onKeyBackDown() {
        // 按键按下时设置需要显示键盘的标志位
        needShowPwd = true;
        return true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        LogUtils.d(TAG, keyCode + "," + event.getAction());
        if (KeyEvent.KEYCODE_ENTER == keyCode
                && KeyEvent.ACTION_DOWN == event.getAction()) {
            edtAmount.requestFocus();
        }
        return super.onKeyDown(keyCode, event);
    }

    // AET-48
    private synchronized void updateAmount() {
        edtAmount.setText("");
    }

    private static class MainEditorActionListener extends EditorActionListener {

        private final MainActivity mainActivity;

        public MainEditorActionListener(MainActivity mainActivity) {
            this.mainActivity = mainActivity;
        }

        @Override
        public void onKeyOk() {
            mainActivity.isSaleProcessing = true;
            String strAmount = Objects.requireNonNull(mainActivity.edtAmount.getText()).toString().trim();
            String amount = CurrencyConverter.parse(strAmount).toString();
            if (!"0".equals(amount)) {
                LogUtils.d(TAG, "amount:" + amount);
                if (CurrencyConverter.parse(strAmount) < 100) {
                    ToastUtils.showMessage(R.string.minimum_amount_is_1_rupee);
                    mainActivity.updateAmount();
                } else {
                    doSale(amount);
                }

            } else {
                ToastUtils.showMessage(mainActivity.getString(R.string.money_input_zero));
                mainActivity.updateAmount();
            }
        }

        @Override
        public void onKeyCancel() {
            mainActivity.isSaleProcessing = false;
            DialogUtils.showErrMessage(mainActivity, ETransType.SALE.getTransName(),
                    TransResultUtils.getMessage(TransResult.ERR_USER_CANCEL), null, Constants.FAILED_DIALOG_SHOW_TIME);
            mainActivity.updateAmount();
            //回复电源按键
            Device.enablePhysicalPowerButton(true);
            FinancialApplication.setIsEnteringSaleAmount(false);
        }

        /**
         * @param amount "like 12313"
         */
        private void doSale(String amount) {

            new SaleTrans(mainActivity, amount, (byte) -1, mainActivity.transEndListener).execute();
        }
    }

    private void inputPwd() {
        ActionInputPassword inputPasswordAction = new ActionInputPassword(action -> ((ActionInputPassword) action).setParam(MainActivity.this, 6,
                getString(R.string.prompt_sys_pwd), null));

        inputPasswordAction.setEndListener((action, result) -> {
            TransContext.getInstance().setCurrentAction(null); //fix leaks

            if (result.getRet() != TransResult.SUCC) {
                return;
            }

            String data = EncUtils.sha1((String) result.getData());
            if (!data.equals(SysParam.getInstance().getString(R.string.SEC_SYS_PWD))) {
                DialogUtils.showErrMessage(MainActivity.this, getString(R.string.exit),
                        getString(R.string.err_password), null, Constants.FAILED_DIALOG_SHOW_TIME);
                return;
            }
            exitApp();
        });

        inputPasswordAction.execute();
        // 显示键盘之后重置标志位
        needShowPwd = false;
    }

    private void exitApp() {
        FinancialApplication.setIsExitApp(true);
        Device.enableStatusBar(true);
        Device.enableHomeRecentKey(true);
        Device.enableScreenShot(true);
        PreciseAlarmScheduler.cancelAlarm(FinancialApplication.getAppContext());
        ActivityStack.getInstance().popAll();
    }

    private void needAutoSettlement() {
        String settleTime = SysParam.getInstance().getString(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_TIME));
        String settleDate = SysParam.getInstance().getString(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE));
        if (!Utils.getString(R.string.disable).equals(settleTime) &&
                !Utils.getString(R.string.disable).equals(settleDate) && Utils.isTimePassed(settleDate + " " + settleTime)) {
            new ActionAutoSettle(null).execute();
        }
    }
}
