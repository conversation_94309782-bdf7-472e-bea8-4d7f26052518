/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.utils;

import static android.content.Context.TELEPHONY_SERVICE;
import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alibaba.fastjson.JSONReader;
import com.csvreader.CsvReader;
import com.pax.appstore.DownloadManager;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransData;
import com.pax.data.local.GreendaoHelper;
import com.pax.gl.utils.impl.Convert;
import com.pax.pay.MainActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.action.ActionPreview;
import com.pax.pay.trans.action.activity.TransPreviewActivity;
import com.pax.pay.trans.model.ETransType;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.dialog.DialogUtils;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.ref.WeakReference;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

public class Utils {

    private static final String TAG = "Utils";
    public static final String LOCAL_CURRENCY = "local currency";
    public static final String INTERNATIONAL_CURRENCY = "international currency";
    private static final String[] BT_PRINT_DEVICE = {"A60", "Aries8", "Aries6"};
    private static final String A50 = "A50";
    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter TIME_HOUR_FORMAT = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter logFormatter = DateTimeFormatter.ofPattern("yyyy_MM_dd");
    private static int autoSettlementHour;
    private static int autoSettlementMinute;

    private Utils() {
        //do nothing
    }

    /**
     * 获取主秘钥索引
     *
     * @param index 0~99的主秘钥索引值
     * @return 1~100的主秘钥索引值
     */
    public static int getMainKeyIndex(int index) {
        return index + 1;
    }

    public static boolean checkIp(String ip) {
        return ip.matches("((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)");
    }

    public static boolean checkPort(String portInput) {
        try {
            // 将输入转换为整数
            int port = Integer.parseInt(portInput);

            // 校验端口号是否在合法范围 1-65535 之间
            return port >= 1 && port <= 65535;
        } catch (NumberFormatException e) {
            // 如果输入的内容不是数字，捕获异常并返回 false
            return false;
        }
    }

    public static boolean checkKeyString(String str) {
        String regex = "^[a-z0-9A-Z]+$";
        return str.matches(regex);
    }

    public static void changeAppLanguage(Context context, Locale locale) {
        if (context == null) {
            return;
        }
        Resources res = context.getResources();
        DisplayMetrics dm = res.getDisplayMetrics();
        Configuration conf = res.getConfiguration();
        conf.locale = locale;
        res.updateConfiguration(conf, dm);
    }

    @NonNull
    public static String getString(@StringRes int resId) {
        return FinancialApplication.getApp().getString(resId);
    }

    @NonNull
    public static String getString(@StringRes int resId, Object... formatArgs) {
        return FinancialApplication.getApp().getResources().getString(resId, formatArgs);
    }

    // easy way to get permission, for getting more interactions, should show request permission rationale
    public static Disposable callPermission(@NonNull Activity activity, String permission, @NonNull Action action, final String failedMsg) {
        RxPermissions rxPermissions = new RxPermissions(activity); // where this is an Activity instance
        return rxPermissions
                .request(permission)
                .subscribe(new Consumer<Boolean>() {
                    @Override
                    public void accept(Boolean granted) throws Exception {
                        LogUtils.e(TAG, "{accept Boolean}");
                        if (!granted) {
                            // 未获取权限
                            ToastUtils.showMessage(failedMsg);
                        }
                        // 在android 6.0之前会默认返回true
                        // 已经获取权限 do nothing
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        LogUtils.e(TAG, "{accept Throwable}");//可能是授权异常的情况下的处理
                    }
                }, action);
    }

    public static Disposable callPermission(@NonNull Activity activity, String permission, @NonNull Action action, final Action failAction) {
        RxPermissions rxPermissions = new RxPermissions(activity); // where this is an Activity instance
        return rxPermissions
                .request(permission)
                .subscribe(new Consumer<Boolean>() {
                    @Override
                    public void accept(Boolean granted) throws Exception {
                        LogUtils.e(TAG, "{accept Boolean}");
                        if (!granted) {
                            // 未获取权限
                            failAction.run();
                        }
                        // 在android 6.0之前会默认返回true
                        // 已经获取权限 do nothing
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        LogUtils.e(TAG, "{accept Throwable}");//可能是授权异常的情况下的处理
                        failAction.run();
                    }
                }, action);
    }

    public static Disposable callPermissions(@NonNull Activity activity, String[] permission, @NonNull Action action, final String failedMsg) {
        RxPermissions rxPermissions = new RxPermissions(activity);
        List<Observable<Boolean>> permissionList = new ArrayList<>();
        for (String s : permission) {
            permissionList.add(rxPermissions.request(s));
        }
        return Observable.concat(permissionList)
                .subscribe(new Consumer<Boolean>() {
                    @Override
                    public void accept(Boolean granted) throws Exception {
                        if (!granted) {
                            // 未获取权限
                            ToastUtils.showMessage(failedMsg);
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        //可能是授权异常的情况下的处理
                        LogUtils.e(TAG, "", throwable);
                    }
                }, action);
    }

    public static String getSimNumber(Context context) {
        TelephonyManager manager = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
            return null;
        }
        return manager.getSimSerialNumber();
    }

    public static boolean isSMSAvailable(Context context) {
        TelephonyManager manager = (TelephonyManager) context.getSystemService(TELEPHONY_SERVICE);
        return (manager.getSimState() == TelephonyManager.SIM_STATE_READY) && (
                ContextCompat.checkSelfPermission(context, Manifest.permission.SEND_SMS) != PackageManager.PERMISSION_GRANTED);
    }

    public static void wakeupScreen(int timeout) {
        PowerManager pm = (PowerManager) FinancialApplication.getApp().getSystemService(Context.POWER_SERVICE);
        @SuppressLint("InvalidWakeLockTag") final PowerManager.WakeLock wl = pm.newWakeLock(PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.SCREEN_BRIGHT_WAKE_LOCK, "bright");
        wl.acquire();
        FinancialApplication.getApp().runOnUiThreadDelay(new Runnable() {
            @Override
            public void run() {
                wl.release();
            }
        }, 1000L * (timeout + 1));
    }

    public static void restart() {
        ActivityStack.getInstance().popAll();
        Intent intent = new Intent();
        intent.addFlags(FLAG_ACTIVITY_NEW_TASK);
        intent.setClass(FinancialApplication.getApp(), MainActivity.class);
        FinancialApplication.getApp().startActivity(intent);
    }

    public static void callSystemSettings(Context context, String action) {
        context.startActivity(new Intent(action));
    }

    public static <T> List<T> readObjFromJSON(String fileName, Class<T> clz) {
        List<T> list = new ArrayList<>();
        try (InputStreamReader reader = new InputStreamReader(FinancialApplication.getApp().getAssets().open(fileName))) {
            JSONReader jsonReader = new JSONReader(reader);
            jsonReader.startArray();
            while (jsonReader.hasNext()) {
                T obj = jsonReader.readObject(clz);
                list.add(obj);
            }
            jsonReader.endArray();
            jsonReader.close();
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }
        return list;
    }

    public static long parseLongSafe(String longStr) {
        return parseLongSafe(longStr, 0);
    }

    public static long parseLongSafe(String longStr, long safeValue) {
        if (longStr == null) {
            return safeValue;
        }
        try {
            return Long.parseLong(longStr);
        } catch (NumberFormatException e) {
            return safeValue;
        }
    }

    public static double parseDoubleSafe(String doubleStr) {
        return parseDoubleSafe(doubleStr, 0.0);
    }

    public static double parseDoubleSafe(String doubleStr, double defaultValue) {
        try {
            return Double.parseDouble(doubleStr);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    public static int parseIntSafe(String intStr) {
        return parseIntSafe(intStr, 0);
    }

    public static int parseIntSafe(String intStr, int defaultValue) {
        try {
            return Integer.parseInt(intStr);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    public static boolean needBtPrint() {
        for (String s : BT_PRINT_DEVICE) {
            if (s.equals(Build.MODEL)) {
                return true;
            }
        }
        return false;
    }

    public static void jumpActivity(Context context, Class<? extends Activity> target, Bundle bundle) {
        Intent intent = new Intent(context, target);
        intent.putExtras(bundle);
        context.startActivity(intent);
    }

    public static int getColor(int colorId) {
        return ContextCompat.getColor(FinancialApplication.getApp(), colorId);
    }

    public static List<String> getMutableList(int id) {
        return Arrays.asList(FinancialApplication.getApp().getResources().getStringArray(id));
    }

    public static byte[] str2Bcd(String str) {
        return Convert.strToBcd(str, Convert.EPaddingPosition.PADDING_LEFT);
    }

    public static String bcd2Str(byte[] bcd) {
        return Convert.bcdToStr(bcd);
    }


    /**
     * Init rate.
     *
     * @param rate the rate
     * @return the string
     */
    public static String initRate(String rate) {
        if (TextUtils.isEmpty(rate)) {
            return "";
        }
        String temp = rate.substring(1);
        int decimalLength = rate.charAt(0) - '0';
        int length = temp.length();
        String integer = length - decimalLength == 0 ? "0" : temp.substring(0, length - decimalLength);
        String decimal = temp.substring(length - decimalLength);
        Double rateDouble = Double.parseDouble(integer + "." + decimal);
        String initRate = String.valueOf(rateDouble);
        if (initRate.length() < 8) {
            initRate = initRate + "00000000".substring(initRate.length(), 8);
        }
        return initRate.substring(0, 8);
    }

    /**
     * Check the value is null or not
     *
     * @param tag   the tag
     * @param line  the line
     * @param key   the key
     * @param value the value
     * @return value with check
     * @throws ParseParamException the parse param exception
     */
    public static String getValueWithCheck(String tag, int line, String key, String value) throws ParseParamException {
        // apn允许为空
        if (TextUtils.isEmpty(value) && !"apn".equals(key)) {
            if (line == -1) {
                throw new ParseParamException(tag, key);
            } else {
                throw new ParseParamException(tag, line, key);
            }
            //throw new Exception(tag + " line:" + line + " " + key + " value is null");
        }
        return value;
    }

    public static String getTerminalAndAppVersion() {
        Map<ETermInfoKey, String> map = FinancialApplication.getDal().getSys().getTermInfo();
        return map.get(ETermInfoKey.MODEL) + " " + FinancialApplication.getVersion();
    }

    /**
     * Check whether the value is a number
     *
     * @param tag    the tag
     * @param line   the line
     * @param key    the key
     * @param values the values
     * @param type   the type
     * @return num with check
     * @throws ParseParamException the parse param exception
     */
    public static Number getNumWithCheck(String tag, int line, String key, String values, int type) throws ParseParamException {
        if (TextUtils.isEmpty(values)) {
            throw new ParseParamException(tag, line, values);
        }
        if (!values.matches("\\d+")) {
            throw new ParseParamException(tag, line, key, type);
        }
        if (type == Constants.INTEGER) {
            return Integer.parseInt(values);
        } else {
            return Long.parseLong(values);
        }
    }

    /**
     * Read values from the CSV file
     *
     * @param <T>     the type parameter
     * @param file    The file
     * @param parser  The parser
     * @param isAsset The asset
     * @return list
     */
    public static <T> List<T> readFromCsv(String file, ICsvParser<T> parser, boolean isAsset, DownloadManager.UpdateParamListener updateParamListener) {
        //Keep the asset input stream reader,or it will throw NullPointerException
        try (InputStream is = isAsset ? FinancialApplication.getApp().getAssets().open(file) : new FileInputStream(new File(file))) {
            CsvReader csvReader = new CsvReader(is, StandardCharsets.UTF_8);
            List<T> list = new ArrayList<>();
            csvReader.readHeaders();
            int line = 1;
            while (csvReader.readRecord()) {
                list.add(parser.parse(line, csvReader));
                line++;
            }
            return list;
        } catch (Exception e) {
            LogUtils.e("", e);
            if (e instanceof ParseParamException) {
                SysParam.getInstance().set(Utils.getString(R.string.IS_PARSE_PARAM_EXCEPTION), true);
                SysParam.getInstance().set(Utils.getString(R.string.PARSE_PARAM_EXCEPTION), ((ParseParamException) e).getTag());
                LogUtils.e("ParseParamException", e.getMessage());
                updateParamListener.Error("CardBin File is wrong");
            }
        }
        return null;
    }

    /**
     * check whether isScreenOn
     *
     * @param context applicationContext
     * @return isScreenOn
     */
    public static boolean isScreenOn(Context context) {
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        boolean interactive;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT_WATCH) {
            interactive = powerManager.isInteractive();
        } else {
            interactive = powerManager.isScreenOn();
        }
        return interactive;
    }

    //除了最后两位其他全部替换为*
    public static String maskString(String input) {
        if (input == null || input.length() <= 2) {
            return input;
        }

        StringBuilder masked = new StringBuilder();
        int length = input.length();
        for (int i = 0; i < length - 2; i++) {
            masked.append("*");
        }
        masked.append(input.substring(length - 2));

        return masked.toString();
    }

    // 将十六进制字符串转换为 ASCII 码
    public static String hexToAscii(String hexString) {
        StringBuilder output = new StringBuilder();

        // 每两个字符表示一个字节
        for (int i = 0; i < hexString.length(); i += 2) {
            String str = hexString.substring(i, i + 2);

            // 将每个字节转换为 ASCII 码
            output.append((char) Integer.parseInt(str, 16));
        }

        return output.toString();
    }

    public static boolean isA50() {
        return Build.MODEL.equalsIgnoreCase(A50);
    }

    public static boolean showA50NoPrint(boolean isToast, Context context, DialogInterface.OnDismissListener listener,
                                         boolean additionalCondition) {
        if (Utils.isA50() && additionalCondition) {
            if (isToast) {
                ToastUtils.showMessage(R.string.not_supported_print);
            } else {
                WeakReference<Context> weakReference = new WeakReference<>(context);
                DialogUtils.showErrMessage(weakReference.get(), null, Utils.getString(R.string.not_supported_print),
                        listener, Constants.FAILED_DIALOG_SHOW_TIME);
            }
            return true;
        }
        return false;
    }

    public static void showTransPreview(TransData transData, Activity activity, String transType) {
        // preview action
        ActionPreview previewAction = new ActionPreview(action ->
                ((ActionPreview) action).setParam(activity, transData, transType));

        WeakReference<Activity> activityRef = new WeakReference<>(activity);

        previewAction.setEndListener((action, result) -> {
            String string = (String) result.getData();
            Activity activityInstance = activityRef.get(); // 获取弱引用指向的 Activity
            if (activityInstance != null && string != null && string.equals(TransPreviewActivity.OK_BUTTON)) {
                ActivityStack.getInstance().popTo(activityInstance);
            }
        });

        previewAction.execute();
    }

    /**
     * 工具方法：将 Bitmap 包装成 ImageView
     */
    public static ImageView createImageView(Context context, Bitmap bitmap) {
        ImageView imageView = new ImageView(context);
        imageView.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        ));
        imageView.setImageBitmap(bitmap);
        return imageView;
    }

    public static String insertDecimalPoint(String amount) {
        int len = amount.length();
        if (len < 2) {
            return "0." + String.format("%02d", amount);
        }
        return amount.substring(0, len - 2) + "." + amount.substring(len - 2);
    }

    public static boolean isTimePassed(String targetTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime target = LocalDateTime.parse(targetTime, TIME_FORMAT);
        return localDateTime.isAfter(target);
    }

    public static boolean isTimeHourPassed(String targetTime) {
        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 解析目标时间
        LocalTime target = LocalTime.parse(targetTime, TIME_HOUR_FORMAT);
        // 比较时间
        return currentTime.isAfter(target);
    }

    public static int getHour(String targetTime) {
        parseTime(targetTime);
        return autoSettlementHour;
    }

    public static int getMinute() {
        return autoSettlementMinute;
    }

    private static void parseTime(String targetTime) {
        LocalDateTime target = LocalDateTime.parse(targetTime, TIME_FORMAT);
        autoSettlementHour = target.getHour();
        autoSettlementMinute = target.getMinute();
    }

    public static boolean isWifiConnected(Context context) {
        boolean isWifi = false;
        WeakReference<Context> contextWeakReference = new WeakReference<>(context);
        ConnectivityManager connectivityManager = (ConnectivityManager) contextWeakReference.get().getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.getActiveNetwork());
        if (networkCapabilities != null) {
            isWifi = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
        }
        return isWifi;
    }

    public static boolean allowDcc(TransData.EnterMode enterMode) {
        switch (enterMode) {
            case INSERT:
                return SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_DCC_SUPPORT_CHIP_CARD));
            case CLSS:
                return SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_DCC_SUPPORT_TAP_CARD));
            default:
                return false;
        }
    }

    /*/
     * 如果是null则返回空字符串
     */
    public static String emptyIfNull(String str) {
        return str == null ? "" : str;
    }

    /**
     * 从给定的字符串中解析出与指定表名相关的数据。
     * 数据格式为：表名后面紧跟一个指定位数的总数据长度，之后是多个两字符长度+对应长度的数据块组合。
     *
     * @param tableId            表名
     * @param totalDataLengthNum 包含表名数据总长度的位数
     * @param input              包含表名和数据的字符串
     * @return 解析出的数据列表
     */
    public static List<String> parseData(String tableId, int totalDataLengthNum, String input) {
        List<String> parseList = new LinkedList<>();
        if (TextUtils.isEmpty(input)) {
            return parseList;
        }
        int index = input.indexOf(tableId);
        // 防止外币的货币代码和表名冲突
        if (index > 0 && !Character.isDigit(input.charAt(index - 1))) {
            index = input.indexOf(tableId, index + 1);
        }
        // 检查字符串是否包含指定的表名
        if (index == -1 || totalDataLengthNum < 1) {
            return parseList; // 如果不包含，则直接返回空列表
        }

        index = index + tableId.length(); // 移动到表名后
        int totalDataLength = Integer.parseInt(input.substring(index, index + totalDataLengthNum), 10); // 解析紧随表名后的3个字符作为总数据长度

        // 验证剩余字符串长度是否满足总数据长度要求
        if (input.length() - totalDataLengthNum < totalDataLength) {
            return parseList; // 不满足则返回空列表
        }

        index = index + totalDataLengthNum; // 移动到总数据长度后
        // 循环读取数据直到字符串结束
        int totalNum = index + totalDataLength;
        while (index < totalNum) {
            // 读取接下来的两个字符作为当前数据块的长度信息
            int tempDataLength = Integer.parseInt(input.substring(index, index + 2), 10);
            index += 2; // 移动到当前数据块开始位置

            // 检查是否有足够的数据可以读取
            if (index + tempDataLength > input.length()) {
                parseList.clear(); // 清空列表并返回
                return parseList;
            }

            // 读取数据
            String specificData = input.substring(index, index + tempDataLength);
            parseList.add(specificData); // 添加数据到列表
            index += tempDataLength; // 移动到下一个数据块
        }
        return parseList;
    }

    public static boolean checkValueWithRegular(String regularString, String valueCheck) {
        Pattern pattern = Pattern.compile(regularString);
        // 创建matcher对象
        Matcher matcher = pattern.matcher(valueCheck);
        return matcher.matches();
    }

    public static String getCurrentLogTime() {
        LocalDate currentDate = LocalDate.now();
        // 格式化日期
        return currentDate.format(logFormatter);
    }

    public static String getHolderName(String track1) {
        if (track1 == null) {
            return null;
        }

        int index1 = track1.indexOf('^');
        if (index1 < 0) {
            return null;
        }

        int index2 = track1.lastIndexOf('^');
        if (index2 < 0) {
            return null;
        }

        return track1.substring(index1 + 1, index2);
    }

    public static String formatToIp(String ipString) {
        try {
            InetAddress[] inetAddresses = InetAddress.getAllByName(ipString);
            return inetAddresses[0].getHostAddress();
        } catch (UnknownHostException e) {
            Log.e(TAG, "getMainHostIp: " + e.getMessage());
            return "";
        }
    }

    /**
     * @param
     * @return
     * @description 清除过期的预授权交易
     * <AUTHOR>
     * @time 2020/9/24 11:19
     */

    public static void clearExpiredPreAuthData() {
        List<ETransType> list = new ArrayList<>();
        list.add(ETransType.PREAUTH);
        List<TransData.ETransStatus> filter = new ArrayList<>();
        filter.add(TransData.ETransStatus.NORMAL);
        List<TransData> transDatas = GreendaoHelper.getTransDataHelper().findAllTransData(list, filter);
        if (transDatas == null || transDatas.size() < 1) return;
        for (TransData transData : transDatas) {
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.TIME_PATTERN_TRANS, Locale.US);
                Date transDate = dateFormat.parse(transData.getDateTime());
                long nd = 1000L * 24 * 60 * 60;
                long day = new Date().getTime() - transDate.getTime();
                if (day > SysParam.getInstance().getInt(Utils.getString(R.string.EDC_PREAUTH_RECORD_SAVING_DAY), 15) * nd) {
                    GreendaoHelper.getTransDataHelper().delete(transData);
                }
            } catch (Exception e) {
                LogUtils.e(e);
            }
        }
    }

    public static String generateCardPrompt(boolean mag, boolean icc, boolean picc) {
        StringBuilder message = new StringBuilder();
        // 记录有几种寻卡方式为 true
        int validCount = 0;
        if (mag && !Utils.isA50()) {
            message.append("Swipe");
            validCount++;
        }
        if (icc) {
            if (validCount > 0) {
                message.append("/");
            }
            message.append("Insert");
            validCount++;
        }
        if (picc) {
            if (validCount > 0) {
                message.append("/");
            }
            message.append("Tap");
        }
        message.insert(0, "Please ");
        message.append(" Card");
        return message.toString();
    }

    public static String generatePurchaseId() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMddHHmmssSSS", Locale.US);
        String timePart = dateFormat.format(new Date());
        Acquirer acquirer = FinancialApplication.getAcqManager().getCurAcq();
        String terminalId = acquirer.getTerminalId();
        String purchaseId = timePart + terminalId;
        // 计算长度并格式化为两位
        String lengthStr = String.format(Locale.US, "%02d", purchaseId.length());
        return "PU" + lengthStr + purchaseId;
    }
}
