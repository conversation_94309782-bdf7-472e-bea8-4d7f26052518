/*
 *COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *
 * Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 */

package com.pax.pay.utils

/**
 * @<NAME_EMAIL>
 * @date 2023/12/13
 * 本项目较为简单，无需太多注释，为解决sonarlint扫描注释率问题，故用此文件用于存放文本
 */

// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience dif
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
//That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introd
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//
//    Dependency injection (DI): Dependency injection allows classes to define their dependencies without constructing them. At runtime, another class is responsible for providing these dependencies.
// Service locator: The service locator pattern provides a registry where classes can obtain their dependencies instead of constructing them.
// These patterns allow you to scale your code because they provide clear patterns for managing dependencies without duplicating code or adding complexity. Furthermore, these patterns allow you to quickly switch between test and production implementations.
//
// We recommend following dependency injection patterns and using the Hilt library in Android apps. Hilt automatically constructs objects by walking the dependency tree, provides compile-time guarantees on dependencies, and creates dependency containers for Android framework classes.
//
// General best practices
// Programming is a creative field, and building Android apps isn't an exception. There are many ways to solve a problem; you might communicate data between multiple activities or fragments, retrieve remote data and persist it locally for offline mode, or handle any number of other common scenarios that nontrivial apps encounter.
//
// Although the following recommendations aren't mandatory, in most cases following them makes your code base more robust, testable, and maintainable in the long run:
//
// Don't store data in app components.
//
// Avoid designating your app's entry points—such as activities, services, and broadcast receivers—as sources of data. Instead, they should only coordinate with other components to retrieve the subset of data that is relevant to that entry point. Each app component is rather short-lived, depending on the user's interaction with their device and the overall current health of the system.
//
// Reduce dependencies on Android classes.
//
// Your app components should be the only classes that rely on Android framework SDK APIs such as Context, or Toast. Abstracting other classes in your app away from them helps with testability and reduces coupling within your app.
//
// Create well-defined boundaries of responsibility between various modules in your app.
//
// For example, don't spread the code that loads data from the network across multiple classes or packages in your code base. Similarly, don't define multiple unrelated responsibilities—such as data caching and data binding—in the same class. Following the recommended app architecture will help you with this.
//
// Expose as little as possible from each module.
//
// For example, don't be tempted to create a shortcut that exposes an internal implementation detail from a module. You might gain a bit of time in the short term, but you are then likely to incur technical debt many times over as your codebase evolves.
//
// Focus on the unique core of your app so it stands out from other apps.
//
// Don't reinvent the wheel by writing the same boilerplate code again and again. Instead, focus your time and energy on what makes your app unique, and let the Jetpack libraries and other recommended libraries handle the repetitive boilerplate.
//
// Consider how to make each part of your app testable in isolation.
//
// For example, having a well-defined API for fetching data from the network makes it easier to test the module that persists that data in a local database. If instead, you mix the logic from these two modules in one place, or distribute your networking code across your entire code base, it becomes much more difficult—if not impossible—to test effectively.
//
// Types are responsible for their concurrency policy.
//
// If a type is performing long-running blocking work, it should be responsible for moving that computation to the right thread. That particular type knows the type of computation that it is doing and in which thread it should be executed. Types should be main-safe, meaning they're safe to call from the main thread without blocking it.
//
// Persist as much relevant and fresh data as possible.
//
// That way, users can enjoy your app's functionality even when their device is in offline mode. Remember that not all of your users enjoy constant, high-speed connectivity—and even if they do, they can get bad reception in crowded places.
//
// Benefits of Architecture
// Having a good Architecture implemented in your app brings a lot of benefits to the project and engineering teams:
//
// It improves the maintainability, quality and robustness of the overall app.
// It allows the app to scale. More people and more teams can contribute to the same codebase with minimal code conflicts.
// It helps with onboarding. As Architecture brings consistency to your project, new members of the team can quickly get up to speed and be more efficient in less amount of time.
// It is easier to test. A good Architecture encourages simpler types which are generally easier to test.
// Bugs can be investigated methodically with well defined processes.
// Investing in Architecture also has a direct impact in your users. They benefit from a more stable application, and more features due to a more productive engineering team. However, Architecture also requires an up-front time investment. To help you justify this time to the rest of your company, take a look at these case studies where other companies share their success stories when having a good architecture in their app.

// Introduction to activities
// The Activity class is a crucial component of an Android app, and the way activities are launched and put together is a fundamental part of the platform's application model. Unlike programming paradigms in which apps are launched with a main() method, the Android system initiates code in an Activity instance by invoking specific callback methods that correspond to specific stages of its lifecycle.
//
// This document introduces the concept of activities, and then provides some lightweight guidance about how to work with them. For additional information about best practices in architecting your app, see Guide to App Architecture.
//
// The concept of activities
// The mobile-app experience differs from its desktop counterpart in that a user's interaction with the app doesn't always begin in the same place. Instead, the user journey often begins non-deterministically. For instance, if you open an email app from your home screen, you might see a list of emails. By contrast, if you are using a social media app that then launches your email app, you might go directly to the email app's screen for composing an email.
//
// The Activity class is designed to facilitate this paradigm. When one app invokes another, the calling app invokes an activity in the other app, rather than the app as an atomic whole. In this way, the activity serves as the entry point for an app's interaction with the user. You implement an activity as a subclass of the Activity class.
//
// An activity provides the window in which the app draws its UI. This window typically fills the screen, but may be smaller than the screen and float on top of other windows. Generally, one activity implements one screen in an app. For instance, one of an app’s activities may implement a Preferences screen, while another activity implements a Select Photo screen.
//
// Most apps contain multiple screens, which means they comprise multiple activities. Typically, one activity in an app is specified as the main activity, which is the first screen to appear when the user launches the app. Each activity can then start another activity in order to perform different actions. For example, the main activity in a simple e-mail app may provide the screen that shows an e-mail inbox. From there, the main activity might launch other activities that provide screens for tasks like writing e-mails and opening individual e-mails.
//
// Although activities work together to form a cohesive user experience in an app, each activity is only loosely bound to the other activities; there are usually minimal dependencies among the activities in an app. In fact, activities often start up activities belonging to other apps. For example, a browser app might launch the Share activity of a social-media app.
//
// To use activities in your app, you must register information about them in the app’s manifest, and you must manage activity lifecycles appropriately. The rest of this document introduces these subjects.
//
// Configuring the manifest
// For your app to be able to use activities, you must declare the activities, and certain of their attributes, in the manifest.
//
// Declare activities
// To declare your activity, open your manifest file and add an <activity> element as a child of the <application> element. For example:
//
//
// <manifest ... >
// <application ... >
// <activity android:name=".ExampleActivity" />
// ...
// </application ... >
// ...
// </manifest >
// The only required attribute for this element is android:name, which specifies the class name of the activity. You can also add attributes that define activity characteristics such as label, icon, or UI theme. For more information about these and other attributes, see the <activity> element reference documentation.
//
// Note: After you publish your app, you should not change activity names. If you do, you might break some functionality, such as app shortcuts. For more information on changes to avoid after publishing, see Things That Cannot Change.
//
// Declare intent filters
// Intent filters are a very powerful feature of the Android platform. They provide the ability to launch an activity based not only on an explicit request, but also an implicit one. For example, an explicit request might tell the system to “Start the Send Email activity in the Gmail app". By contrast, an implicit request tells the system to “Start a Send Email screen in any activity that can do the job." When the system UI asks a user which app to use in performing a task, that’s an intent filter at work.
//
// You can take advantage of this feature by declaring an <intent-filter> attribute in the <activity> element. The definition of this element includes an <action> element and, optionally, a <category> element and/or a <data> element. These elements combine to specify the type of intent to which your activity can respond. For example, the following code snippet shows how to configure an activity that sends text data, and receives requests from other activities to do so:
//
//
// <activity android:name=".ExampleActivity" android:icon="@drawable/app_icon">
// <intent-filter>
// <action android:name="android.intent.action.SEND" />
// <category android:name="android.intent.category.DEFAULT" />
// <data android:mimeType="text/plain" />
// </intent-filter>
// </activity>
// In this example, the <action> element specifies that this activity sends data. Declaring the <category> element as DEFAULT enables the activity to receive launch requests. The <data> element specifies the type of data that this activity can send. The following code snippet shows how to call the activity described above:
//
// Kotlin
// Java
//
// val sendIntent = Intent().apply {
//    action = Intent.ACTION_SEND
//    type = "text/plain"
//    putExtra(Intent.EXTRA_TEXT, textMessage)
// }
// startActivity(sendIntent)
// If you intend for your app to be self-contained and not allow other apps to activate its activities, you don't need any other intent filters. Activities that you don't want to make available to other applications should have no intent filters, and you can start them yourself using explicit intents. For more information about how your activities can respond to intents, see Intents and Intent Filters.
// Declare permissions
// You can use the manifest's <activity> tag to control which apps can start a particular activity. A parent activity cannot launch a child activity unless both activities have the same permissions in their manifest. If you declare a <uses-permission> element for a parent activity, each child activity must have a matching <uses-permission> element.
//
// For example, if your app wants to use a hypothetical app named SocialApp to share a post on social media, SocialApp itself must define the permission that an app calling it must have:
//
//
// <manifest>
// <activity android:name="...."
// android:permission=”com.google.socialapp.permission.SHARE_POST”
//
// />
// Then, to be allowed to call SocialApp, your app must match the permission set in SocialApp's manifest:
//
//
// <manifest>
// <uses-permission android:name="com.google.socialapp.permission.SHARE_POST" />
// </manifest>
// For more information on permissions and security in general, see Security and Permissions.
//
// Managing the activity lifecycle
// Over the course of its lifetime, an activity goes through a number of states. You use a series of callbacks to handle transitions between states. The following sections introduce these callbacks.
//
// onCreate()
// You must implement this callback, which fires when the system creates your activity. Your implementation should initialize the essential components of your activity: For example, your app should create views and bind data to lists here. Most importantly, this is where you must call setContentView() to define the layout for the activity's user interface.
//
// When onCreate() finishes, the next callback is always onStart().
//
// onStart()
// As onCreate() exits, the activity enters the Started state, and the activity becomes visible to the user. This callback contains what amounts to the activity’s final preparations for coming to the foreground and becoming interactive.
//
// onResume()
// The system invokes this callback just before the activity starts interacting with the user. At this point, the activity is at the top of the activity stack, and captures all user input. Most of an app’s core functionality is implemented in the onResume() method.
//
// The onPause() callback always follows onResume().
//
// onPause()
// The system calls onPause() when the activity loses focus and enters a Paused state. This state occurs when, for example, the user taps the Back or Recents button. When the system calls onPause() for your activity, it technically means your activity is still partially visible, but most often is an indication that the user is leaving the activity, and the activity will soon enter the Stopped or Resumed state.
//
// An activity in the Paused state may continue to update the UI if the user is expecting the UI to update. Examples of such an activity include one showing a navigation map screen or a media player playing. Even if such activities lose focus, the user expects their UI to continue updating.
//
// You should not use onPause() to save application or user data, make network calls, or execute database transactions. For information about saving data, see Saving and restoring activity state.
//
// Once onPause() finishes executing, the next callback is either onStop() or onResume(), depending on what happens after the activity enters the Paused state.
//
// onStop()
// The system calls onStop() when the activity is no longer visible to the user. This may happen because the activity is being destroyed, a new activity is starting, or an existing activity is entering a Resumed state and is covering the stopped activity. In all of these cases, the stopped activity is no longer visible at all.
//
// The next callback that the system calls is either onRestart(), if the activity is coming back to interact with the user, or by onDestroy() if this activity is completely terminating.
//
// onRestart()
// The system invokes this callback when an activity in the Stopped state is about to restart. onRestart() restores the state of the activity from the time that it was stopped.
//
// This callback is always followed by onStart().
//
// onDestroy()
// The system invokes this callback before an activity is destroyed.
//
// This callback is the final one that the activity receives. onDestroy() is usually implemented to ensure that all of an activity’s resources are released when the activity, or the process containing it, is destroyed.
//
// This section provides only an introduction to this topic. For a more detailed treatment of the activity lifecycle and its callbacks, see The Activity Lifecycle.
// Dependency injection in Android
// Dependency injection (DI) is a technique widely used in programming and well suited to Android development. By following the principles of DI, you lay the groundwork for good app architecture.
//
// Implementing dependency injection provides you with the following advantages:
//
// Reusability of code
// Ease of refactoring
// Ease of testing
// Fundamentals of dependency injection
// Before covering dependency injection in Android specifically, this page provides a more general overview of how dependency injection works.
//
// What is dependency injection?
// Classes often require references to other classes. For example, a Car class might need a reference to an Engine class. These required classes are called dependencies, and in this example the Car class is dependent on having an instance of the Engine class to run.
//
// There are three ways for a class to get an object it needs:
//
// The class constructs the dependency it needs. In the example above, Car would create and initialize its own instance of Engine.
// Grab it from somewhere else. Some Android APIs, such as Context getters and getSystemService(), work this way.
// Have it supplied as a parameter. The app can provide these dependencies when the class is constructed or pass them in to the functions that need each dependency. In the example above, the Car constructor would receive Engine as a parameter.
// The third option is dependency injection! With this approach you take the dependencies of a class and provide them rather than having the class instance obtain them itself.
//
// Here's an example. Without dependency injection, representing a Car that creates its own Engine dependency in code looks like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//
//    private val engine = Engine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
//
// This is not an example of dependency injection because the Car class is constructing its own Engine. This can be problematic because:
//
// Car and Engine are tightly coupled - an instance of Car uses one type of Engine, and no subclasses or alternative implementations can easily be used. If the Car were to construct its own Engine, you would have to create two types of Car instead of just reusing the same Car for engines of type Gas and Electric.
//
// The hard dependency on Engine makes testing more difficult. Car uses a real instance of Engine, thus preventing you from using a test double to modify Engine for different test cases.
//
// What does the code look like with dependency injection? Instead of each instance of Car constructing its own Engine object on initialization, it receives an Engine object as a parameter in its constructor:
//
// Error
// Kotlin
// Java
//
// class Car(private val engine: Engine) {
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val engine = Engine()
//    val car = Car(engine)
//    car.start()
// }
//
// The main function uses Car. Because Car depends on Engine, the app creates an instance of Engine and then uses it to construct an instance of Car. The benefits of this DI-based approach are:
//
// Reusability of Car. You can pass in different implementations of Engine to Car. For example, you might define a new subclass of Engine called ElectricEngine that you want Car to use. If you use DI, all you need to do is pass in an instance of the updated ElectricEngine subclass, and Car still works without any further changes.
//
// Easy testing of Car. You can pass in test doubles to test your different scenarios. For example, you might create a test double of Engine called FakeEngine and configure it for different tests.
//
// There are two major ways to do dependency injection in Android:
//
// Constructor Injection. This is the way described above. You pass the dependencies of a class to its constructor.
//
// Field Injection (or Setter Injection). Certain Android framework classes such as activities and fragments are instantiated by the system, so constructor injection is not possible. With field injection, dependencies are instantiated after the class is created. The code would look like this:
//
// Error
// Kotlin
// Java
//
// class Car {
//    lateinit var engine: Engine
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.engine = Engine()
//    car.start()
// }
// Note: Dependency injection is based on the Inversion of Control principle in which generic code controls the execution of specific code.
// Automated dependency injection
// In the previous example, you created, provided, and managed the dependencies of the different classes yourself, without relying on a library. This is called dependency injection by hand, or manual dependency injection. In the Car example, there was only one dependency, but more dependencies and classes can make manual injection of dependencies more tedious. Manual dependency injection also presents several problems:
//
// For big apps, taking all the dependencies and connecting them correctly can require a large amount of boilerplate code. In a multi-layered architecture, in order to create an object for a top layer, you have to provide all the dependencies of the layers below it. As a concrete example, to build a real car you might need an engine, a transmission, a chassis, and other parts; and an engine in turn needs cylinders and spark plugs.
//
// When you're not able to construct dependencies before passing them in — for example when using lazy initializations or scoping objects to flows of your app — you need to write and maintain a custom container (or graph of dependencies) that manages the lifetimes of your dependencies in memory.
//
// There are libraries that solve this problem by automating the process of creating and providing dependencies. They fit into two categories:
//
// Reflection-based solutions that connect dependencies at runtime.
//
// Static solutions that generate the code to connect dependencies at compile time.
//
// Dagger is a popular dependency injection library for Java, Kotlin, and Android that is maintained by Google. Dagger facilitates using DI in your app by creating and managing the graph of dependencies for you. It provides fully static and compile-time dependencies addressing many of the development and performance issues of reflection-based solutions such as Guice.
//
// Alternatives to dependency injection
// An alternative to dependency injection is using a service locator. The service locator design pattern also improves decoupling of classes from concrete dependencies. You create a class known as the service locator that creates and stores dependencies and then provides those dependencies on demand.
//
// Error
// Kotlin
// Java
//
// object ServiceLocator {
//    fun getEngine(): Engine = Engine()
// }
//
// class Car {
//    private val engine = ServiceLocator.getEngine()
//
//    fun start() {
//        engine.start()
//    }
// }
//
// fun main(args: Array) {
//    val car = Car()
//    car.start()
// }
// The service locator pattern is different from dependency injection in the way the elements are consumed. With the service locator pattern, classes have control and ask for objects to be injected; with dependency injection, the app has control and proactively injects the required objects.
//
// Compared to dependency injection:
//
// The collection of dependencies required by a service locator makes code harder to test because all the tests have to interact with the same global service locator.
//
// Dependencies are encoded in the class implementation, not in the API surface. As a result, it's harder to know what a class needs from the outside. As a result, changes to Car or the dependencies available in the service locator might result in runtime or test failures by causing references to fail.
//
// Managing lifetimes of objects is more difficult if you want to scope to anything other than the lifetime of the entire app.
//
// Use Hilt in your Android app
// Hilt is Jetpack's recommended library for dependency injection in Android. Hilt defines a standard way to do DI in your application by providing containers for every Android class in your project and managing their lifecycles automatically for you.
//
// Hilt is built on top of the popular DI library Dagger to benefit from the compile time correctness, runtime performance, scalability, and Android Studio support that Dagger provides.
//
// To learn more about Hilt see Dependency Injection with Hilt.
//
// Conclusion
// Dependency injection provides your app with the following advantages:
//
// Reusability of classes and decoupling of dependencies: It's easier to swap out implementations of a dependency. Code reuse is improved because of inversion of control, and classes no longer control how their dependencies are created, but instead work with any configuration.
//
// Ease of refactoring: The dependencies become a verifiable part of the API surface, so they can be checked at object-creation time or at compile time rather than being hidden as implementation details.
//
// Ease of testing: A class doesn't manage its dependencies, so when you're testing it, you can pass in different implementations to test all of your different cases.
//
// To fully understand the benefits of dependency injection, you should try it manually in your app as shown in Manual dependency injection.
//
// Additional resources
// To learn more about dependency injection, see the following additional resources.

// Guide to Android app modularization
// A project with multiple Gradle modules is known as a multi-module project. This guide encompasses best practices and recommended patterns for developing multi-module Android apps.
//
// Note: This page assumes a basic familiarity with the recommended app architecture.
// The growing codebase problem
// In an ever-growing codebase, scalability, readability, and overall code quality often decrease through time. This comes as a result of the codebase increasing in size without its maintainers taking active measures to enforce a structure that is easily maintainable. Modularization is a means of structuring your codebase in a way that improves maintainability and helps avoid these problems.
//
// What is modularization?
// Modularization is a practice of organizing a codebase into loosely coupled and self contained parts. Each part is a module. Each module is independent and serves a clear purpose. By dividing a problem into smaller and easier to solve subproblems, you reduce the complexity of designing and maintaining a large system.
//
//
// Figure 1: Dependency graph of a sample multi-module codebase
// Benefits of modularization
// The benefits of modularization are many, though they each center upon improving the maintainability and overall quality of a codebase. The table below summarizes the key benefits.
//
// Benefit	Summary
// Reusability	Modularization enables opportunities for code sharing and building multiple apps from the same foundation. Modules are effectively building blocks. Apps should be a sum of their features where the features are organized as separate modules. The functionality that a certain module provides may or may not be enabled in a particular app. For example, a :feature:news can be a part of the full version flavor and wear app but not part of the demo version flavor.
// Strict visibility control	Modules enable you to easily control what you expose to other parts of your codebase. You can mark everything but your public interface as internal or private to prevent it from being used outside the module.
// Customizable delivery	Play Feature Delivery uses the advanced capabilities of app bundles, allowing you to deliver certain features of your app conditionally or on demand.
// The benefits above are only achievable with a modularized codebase. The following benefits might be achieved with other techniques but modularization can help you enforce them even more.
//
// Benefit	Summary
// Scalability	In a tightly coupled codebase a single change can trigger a cascade of alterations in seemingly unrelated parts of code. A properly modularized project will embrace the separation of concerns principle and therefore limit the coupling. This empowers the contributors through greater autonomy.
// Ownership	In addition to enabling autonomy, modules can also be used to enforce accountability. A module can have a dedicated owner who is responsible for maintaining the code, fixing bugs, adding tests, and reviewing changes.
// Encapsulation	Encapsulation means that each part of your code should have the smallest possible amount of knowledge about other parts. Isolated code is easier to read and understand.
// Testability	Testability characterizes how easy it is to test your code. A testable code is one where components can be easily tested in isolation.
// Build time	Some Gradle functionalities such as incremental build, build cache or parallel build, can leverage modularity to improve build performance.
// Common pitfalls
// The granularity of your codebase is the extent to which it is composed of modules. A more granular codebase has more, smaller modules. When designing a modularized codebase, you should decide on a level of granularity. To do so, take into account the size of your codebase and its relative complexity. Going too fine-grained will make the overhead a burden, and going too coarse will lessen the benefits of modularization.
//
// Some common pitfalls are as follows:
//
// Too fine-grained: Every module brings a certain amount of overhead in the form of increased build complexity and boilerplate code. A complex build configuration makes it difficult to keep configurations consistent across modules. Too much boilerplate code results in a cumbersome codebase that is difficult to maintain. If overhead counteracts scalability improvements, you should consider consolidating some modules.
// Too coarse-grained: Conversely, if your modules are growing too large you might end up with yet another monolith and miss the benefits that modularity has to offer. For example, in a small project it’s ok to put the data layer inside a single module. But as it grows, it might be necessary to separate repositories and data sources into standalone modules.
// Too complex: It doesn't always make sense to modularize your project. A dominating factor is the size of the codebase. If you don't expect your project to grow beyond a certain threshold, the scalability and build time gains won't apply.
// Is modularization the right technique for me?
// If you need the benefits of reusability, strict visibility control or to use the Play Feature Delivery, then modularization is a necessity for you. If you don't, but still want to benefit from improved scalability, ownership, encapsulation, or build times, then modularization is something worth considering.

// Guide to app architecture
// This guide encompasses best practices and recommended architecture for building robust, high-quality apps.
//
// Note: This page assumes a basic familiarity with the Android Framework. If you are new to Android app development, check out the Android Basics course to get started and learn more about the concepts mentioned in this guide.
// Mobile app user experiences
// A typical Android app contains multiple app components, including activities, fragments, services, content providers, and broadcast receivers. You declare most of these app components in your app manifest. The Android OS then uses this file to decide how to integrate your app into the device's overall user experience. Given that a typical Android app might contain multiple components and that users often interact with multiple apps in a short period of time, apps need to adapt to different kinds of user-driven workflows and tasks.
//
// Keep in mind that mobile devices are also resource-constrained, so at any time, the operating system might kill some app processes to make room for new ones.
//
// Given the conditions of this environment, it's possible for your app components to be launched individually and out-of-order, and the operating system or user can destroy them at any time. Because these events aren't under your control, you shouldn't store or keep in memory any application data or state in your app components, and your app components shouldn't depend on each other.
//
// Common architectural principles
// If you shouldn't use app components to store application data and state, how should you design your app instead?
//
// As Android apps grow in size, it's important to define an architecture that allows the app to scale, increases the app's robustness, and makes the app easier to test.
//
// An app architecture defines the boundaries between parts of the app and the responsibilities each part should have. In order to meet the needs mentioned above, you should design your app architecture to follow a few specific principles.
//
// Separation of concerns
// The most important principle to follow is separation of concerns. It's a common mistake to write all your code in an Activity or a Fragment. These UI-based classes should only contain logic that handles UI and operating system interactions. By keeping these classes as lean as possible, you can avoid many problems related to the component lifecycle, and improve the testability of these classes.
//
// Keep in mind that you don't own implementations of Activity and Fragment; rather, these are just glue classes that represent the contract between the Android OS and your app. The OS can destroy them at any time based on user interactions or because of system conditions like low memory. To provide a satisfactory user experience and a more manageable app maintenance experience, it's best to minimize your dependency on them.
//
// Drive UI from data models
// Another important principle is that you should drive your UI from data models, preferably persistent models. Data models represent the data of an app. They're independent from the UI elements and other components in your app. This means that they are not tied to the UI and app component lifecycle, but will still be destroyed when the OS decides to remove the app's process from memory.
//
// Persistent models are ideal for the following reasons:
//
// Your users don't lose data if the Android OS destroys your app to free up resources.
//
// Your app continues to work in cases when a network connection is flaky or not available.
//
// If you base your app architecture on data model classes, you make your app more testable and robust.
//
// Single source of truth
// When a new data type is defined in your app, you should assign a Single Source of Truth (SSOT) to it. The SSOT is the owner of that data, and only the SSOT can modify or mutate it. To achieve this, the SSOT exposes the data using an immutable type, and to modify the data, the SSOT exposes functions or receive events that other types can call.
//
// This pattern brings multiple benefits:
//
// It centralizes all the changes to a particular type of data in one place.
// It protects the data so that other types cannot tamper with it.
// It makes changes to the data more traceable. Thus, bugs are easier to spot.
// In an offline-first application, the source of truth for application data is typically a database. In some other cases, the source of truth can be a ViewModel or even the UI.
//
// Unidirectional Data Flow
// The single source of truth principle is often used in our guides with the Unidirectional Data Flow (UDF) pattern. In UDF, state flows in only one direction. The events that modify the data flow in the opposite direction.
//
// In Android, state or data usually flow from the higher-scoped types of the hierarchy to the lower-scoped ones. Events are usually triggered from the lower-scoped types until they reach the SSOT for the corresponding data type. For example, application data usually flows from data sources to the UI. User events such as button presses flow from the UI to the SSOT where the application data is modified and exposed in an immutable type.
//
// This pattern better guarantees data consistency, is less prone to errors, is easier to debug and brings all the benefits of the SSOT pattern.
//
// Recommended app architecture
// This section demonstrates how to structure your app following recommended best practices.
//
// Note: The recommendations and best practices present in this page can be applied to a broad spectrum of apps to allow them to scale, improve quality and robustness, and make them easier to test. However, you should treat them as guidelines and adapt them to your requirements as needed.
// Considering the common architectural principles mentioned in the previous section, each application should have at least two layers:
//
// The UI layer that displays application data on the screen.
// The data layer that contains the business logic of your app and exposes application data.
// You can add an additional layer called the domain layer to simplify and reuse the interactions between the UI and data layers.
//
//
// Figure 1. Diagram of a typical app architecture.
// Note: The arrows in the diagrams in this guide represent dependencies between classes. For example, the domain layer depends on data layer classes.
// Modern App Architecture
// This Modern App Architecture encourages using the following techniques, among others:
//
// A reactive and layered architecture.
// Unidirectional Data Flow (UDF) in all layers of the app.
// A UI layer with state holders to manage the complexity of the UI.
// Coroutines and flows.
// Dependency injection best practices.
// For more information, see the following sections, the other Architecture pages in the table of contents, and the recommendations page that contains a summary of the most important best practices.
//
// UI layer
// The role of the UI layer (or presentation layer) is to display the application data on the screen. Whenever the data changes, either due to user interaction (such as pressing a button) or external input (such as a network response), the UI should update to reflect the changes.
//
// The UI layer is made up of two things:
//
// UI elements that render the data on the screen. You build these elements using Views or Jetpack Compose functions.
// State holders (such as ViewModel classes) that hold data, expose it to the UI, and handle logic.
//
// Figure 2. The UI layer's role in app architecture.
// To learn more about this layer, see the UI layer page.
//
// Data layer
// The data layer of an app contains the business logic. The business logic is what gives value to your app—it's made of rules that determine how your app creates, stores, and changes data.
//
// The data layer is made of repositories that each can contain zero to many data sources. You should create a repository class for each different type of data you handle in your app. For example, you might create a MoviesRepository class for data related to movies, or a PaymentsRepository class for data related to payments.
//
//
// Figure 3. The data layer's role in app architecture.
// Repository classes are responsible for the following tasks:
//
// Exposing data to the rest of the app.
// Centralizing changes to the data.
// Resolving conflicts between multiple data sources.
// Abstracting sources of data from the rest of the app.
// Containing business logic.
// Each data source class should have the responsibility of working with only one source of data, which can be a file, a network source, or a local database. Data source classes are the bridge between the application and the system for data operations.
//
// To learn more about this layer, see the data layer page.
//
// Domain layer
// The domain layer is an optional layer that sits between the UI and data layers.
//
// The domain layer is responsible for encapsulating complex business logic, or simple business logic that is reused by multiple ViewModels. This layer is optional because not all apps will have these requirements. You should use it only when needed—for example, to handle complexity or favor reusability.
//
//
// Figure 4. The domain layer's role in app architecture.
// Classes in this layer are commonly called use cases or interactors. Each use case should have responsibility over a single functionality. For example, your app could have a GetTimeZoneUseCase class if multiple ViewModels rely on time zones to display the proper message on the screen.
//
// To learn more about this layer, see the domain layer page.
//
// Manage dependencies between components
// Classes in your app depend on other classes in order to function properly. You can use either of the following design patterns to gather the dependencies of a particular class:
//