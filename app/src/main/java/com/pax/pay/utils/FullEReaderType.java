package com.pax.pay.utils;

public enum FullEReaderType {
    MAG(1),
    ICC(2),
    PICC(4),
    PICCEXTERNAL(8),
    MAG_ICC(3),
    MAG_PICC(5),
    MAG_PICCEXTERNAL(9),
    ICC_PICC(6),
    ICC_PICCEXTERNAL(10),
    MAG_ICC_PICC(7),
    MAG_ICC_PICCEXTERNAL(11),
    MAG_ICC_PICC_PICCEXTERNAL(15),
    DEFAULT(0);

    private byte readertype;

    FullEReaderType(int readertype) {
        this.readertype = (byte) readertype;
    }

    public byte getEReaderType() {
        return this.readertype;
    }
}
