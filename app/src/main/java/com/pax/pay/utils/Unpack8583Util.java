/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * Description: // Detail description about the function of this module,
 *  *             // interfaces with the other modules, and dependencies.
 *  * Revision History:
 *  * Date                  Author                 Action
 *  * 20190715         yura            Create/Add/Modify/Delete
 *  * ===========================================================================================
 *
 */

package com.pax.pay.utils;

import android.util.Log;

import com.pax.commonlib.utils.LogUtils;

import static java.lang.System.arraycopy;

public class Unpack8583Util {
    //****Just for GHL temporary.*******//

    private static final String TAG = "Unpack8583Util";

    private String errorMsg = "PARSE ISO ERROR";
    /**
     * 报文长度(2字节)+TPDU(5字节)+报文头(6字节)+域数据(指令码(0域  2字节)+位图(8字节)+其他域数据)
     * <p>
     * 报文长度：从TPDU-报文结尾
     * <p>
     * 一个域数据对象可以包括：
     * 域长度类型：FIX_LEN（0-9）、LLVAR_LEN（0-99）、LLLVAR_LEN（0-999）
     * 域长度：0-999
     * 域编码类型：BCD、ASCII 、BINARY
     * <p>
     * 其他域数据： 如果 域长度类型是FIX_LEN（0-9）则其他域数据为：域数据
     * 如果 域长度类型是LLVAR_LEN（0-99）则其他域数据为：计算的1字节域数据长度+域数据
     * 如果 域长度类型是LLLVAR_LEN（0-999）则其他域数据为：计算的2字节域数据长度+域数据
     */

    private Pack iso8583Pack;
    private boolean isResponse;
    private boolean isReceipt;
    private boolean isLogOn;
    private byte[] debugInfoData;


    public Unpack8583Util(byte[] debugData, boolean isResponse) {
        iso8583Pack = null;
        debugInfoData = debugData;
        this.isResponse = isResponse;
    }

    public Unpack8583Util(byte[] debugData, boolean isResponse, boolean isReceipt, boolean isLogOn) {
        iso8583Pack = null;
        debugInfoData = debugData;
        this.isResponse = isResponse;
        this.isReceipt = isReceipt;
        this.isLogOn = isLogOn;
    }

    public String getPackedPacket() {
        if (debugInfoData == null || debugInfoData.length == 0) {
            return errorMsg;
        }
        FieldEntity[] fields = new FieldEntity[64];
        init8583(fields);

        try {
            processData(debugInfoData);
            init8583Fields(fields);
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
            return errorMsg;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (FieldEntity field : fields) {
            stringBuilder.append(field.toString());
        }
        return stringBuilder.toString();
    }


    private void processData(byte[] data) {

        iso8583Pack = new Pack();
        Log.i(TAG, "processData: pack =" + Utils.bcd2Str(data));
        try {

            Log.i(TAG, "processData: datas.len=" + data.length);

            arraycopy(data, 0, iso8583Pack.getTpdu(), 0, 5);
            arraycopy(data, 5, iso8583Pack.getMsgType(), 0, 2);
            arraycopy(data, 7, iso8583Pack.getBitMap(), 0, 8);
            iso8583Pack.setTxBuffer(new byte[data.length - 15]);
            arraycopy(data, 15, iso8583Pack.getTxBuffer(), 0, data.length - 15);
            LogUtils.d(TAG, "processData: pack:  " + iso8583Pack.toString());
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        }
    }

    /**
     * 各个域的配置，初始化p
     *
     * @param field
     */
    private void init8583(FieldEntity[] field) {
        for (int i = 0; i < field.length; i++) {
            field[i] = new FieldEntity();
            field[i].setName("" + (i + 1));
        }
    }

    private void processBitmap(FieldEntity[] fields) {
        int step = 0;
        for (int i = 0; i < iso8583Pack.bitMap.length; i++) {
            boolean[] boolArr = getBooleanArray(iso8583Pack.bitMap[i]);
            for (int j = 0; j < boolArr.length; j++) {
                fields[step].setExist(boolArr[j]);
                step++;
            }
        }


        for (int i = 0; i < fields.length; i++) {
            if (!fields[i].isExist()) {
                continue;
            }
            LogUtils.d(TAG, "Field:" + (i + 1));
        }

    }

    private void processFiledLenType(FieldEntity[] fields) {
        //注意每个域顺延1
        fields[0].setLenType(FieldLenType.FIX_LEN);
        fields[1].setLenType(FieldLenType.LL_LEN);//pan
        fields[2].setLenType(FieldLenType.FIX_LEN);//process code
        fields[2].setLen(3);
        fields[3].setLenType(FieldLenType.FIX_LEN);//amount
        fields[3].setLen(6);
        fields[4].setLenType(FieldLenType.FIX_LEN);
        fields[4].setLen(6);

        fields[6].setLenType(FieldLenType.FIX_LEN);
        fields[6].setLen(5);

        fields[10].setLenType(FieldLenType.FIX_LEN);
        fields[10].setLen(3);
        fields[11].setLenType(FieldLenType.FIX_LEN);
        fields[11].setLen(3);

        fields[12].setLenType(FieldLenType.FIX_LEN);
        fields[12].setLen(2);
        fields[13].setLenType(FieldLenType.FIX_LEN);
        fields[13].setLen(2);

        fields[14].setLenType(FieldLenType.FIX_LEN);
        fields[14].setLen(2);

        fields[21].setLenType(FieldLenType.FIX_LEN);
        fields[21].setLen(2);
        fields[22].setLenType(FieldLenType.FIX_LEN);
        fields[22].setLen(2);
        fields[23].setLenType(FieldLenType.FIX_LEN);
        fields[23].setLen(2);
        fields[24].setLenType(FieldLenType.FIX_LEN);
        fields[24].setLen(1);
        fields[25].setLenType(FieldLenType.FIX_LEN);
        fields[25].setLen(1);

        fields[27].setLenType(FieldLenType.FIX_LEN);
        fields[27].setLen(9);
        fields[27].setCodingType(FieldCodingType.ASCII);


        fields[34].setLenType(FieldLenType.LL_LEN);
        fields[35].setLenType(FieldLenType.LL_LEN);

        fields[36].setLenType(FieldLenType.FIX_LEN);
        fields[36].setLen(12);
        fields[36].setCodingType(FieldCodingType.ASCII);

        fields[37].setLenType(FieldLenType.FIX_LEN);
        fields[37].setLen(6);
        fields[37].setCodingType(FieldCodingType.ASCII);

        fields[38].setLenType(FieldLenType.FIX_LEN);
        fields[38].setLen(2);
        fields[38].setCodingType(FieldCodingType.ASCII);


        fields[40].setLenType(FieldLenType.FIX_LEN);
        fields[40].setLen(8);
        fields[40].setCodingType(FieldCodingType.ASCII);

        fields[41].setLenType(FieldLenType.FIX_LEN);
        fields[41].setLen(15);
        fields[41].setCodingType(FieldCodingType.ASCII);

        fields[42].setLenType(FieldLenType.LLL_LEN);

        fields[43].setLenType(FieldLenType.LLL_LEN);
//        fields[43].setCodingType(FieldCodingType.ASCII);//暂时不确定
        fields[44].setLenType(FieldLenType.LLL_LEN);
//        fields[44].setCodingType(FieldCodingType.ASCII);//暂时不确定

        fields[47].setLenType(FieldLenType.LLL_LEN);
        fields[47].setCodingType(FieldCodingType.ASCII);

        fields[48].setLenType(FieldLenType.FIX_LEN);
        fields[48].setLen(3);
        fields[48].setCodingType(FieldCodingType.ASCII);

        fields[49].setLenType(FieldLenType.FIX_LEN);
        fields[49].setLen(2);

        fields[51].setLenType(FieldLenType.FIX_LEN);
        fields[51].setLen(8);

        fields[52].setLenType(FieldLenType.FIX_LEN);
        fields[52].setLen(22);

        fields[53].setLenType(FieldLenType.LLL_LEN);
        fields[53].setCodingType(FieldCodingType.ASCII);

        fields[54].setLenType(FieldLenType.LLL_LEN);

        fields[55].setLenType(FieldLenType.FIX_LEN);
        fields[55].setLen(6);
        fields[55].setCodingType(FieldCodingType.ASCII);

        fields[56].setLenType(FieldLenType.LLL_LEN);

        fields[57].setLenType(FieldLenType.LLL_LEN);


        fields[59].setLenType(FieldLenType.LLL_LEN);
        if (!isReceipt) {
            fields[59].setCodingType(FieldCodingType.ASCII);
        }

        fields[60].setLenType(FieldLenType.LLL_LEN);
        if (!isReceipt) {
            fields[60].setCodingType(FieldCodingType.ASCII);
        }


        fields[61].setLenType(FieldLenType.LLL_LEN);
        if (!isReceipt && !isLogOn) {
            fields[61].setCodingType(FieldCodingType.ASCII);
        }

        fields[62].setLenType(FieldLenType.LLL_LEN);
        fields[62].setCodingType(FieldCodingType.ASCII);

        fields[63].setLenType(FieldLenType.FIX_LEN);
        fields[63].setLen(8);

    }

    private void processFiledLenData(FieldEntity[] fields) {
        int srcIndex = 0;
//        LogUtils.w(TAG1, "txBuffer:" + Utils.bcd2Str(iso8583Pack.txBuffer));
        try {
            for (int i = 0; i < fields.length; i++) {
                if (!fields[i].isExist()) {
                    continue;
                }

                LogUtils.i(TAG, "has filed:" + (i + 1) + ", field type:" + fields[i].getLenType() + ", code type:" + fields[i].codingType + ", srcIndex:" + srcIndex);
                if (fields[i].getLenType() == FieldLenType.FIX_LEN) {
                    int dataLen = fields[i].getLen();
                    LogUtils.i(TAG, "FIX_LEN=" + dataLen);
                    fields[i].data = new byte[dataLen];
                    arraycopy(iso8583Pack.txBuffer, srcIndex, fields[i].data, 0, dataLen);
                    LogUtils.i(TAG, "F field data=" + Utils.bcd2Str(fields[i].data));

                    srcIndex += fields[i].getLen();
                } else if (fields[i].getLenType() == FieldLenType.LL_LEN) {
                    byte[] len = new byte[1];
                    arraycopy(iso8583Pack.txBuffer, srcIndex, len, 0, 1);
                    int fieldLen = Integer.parseInt(Utils.bcd2Str(len));
                    if (i == 34 || i == 1) {
                        float tempLen = (float) fieldLen / 2;
                        fieldLen = Math.round(tempLen);
                    }
                    LogUtils.i(TAG, "LL fieldLen=" + fieldLen);
                    fields[i].setLen(fieldLen);
                    fields[i].data = new byte[fieldLen];
                    arraycopy(iso8583Pack.txBuffer, srcIndex + 1, fields[i].data, 0, fields[i].getLen());
                    LogUtils.i(TAG, "LL field data=" + Utils.bcd2Str(fields[i].data));
                    srcIndex += fields[i].getLen() + 1;
                } else if (fields[i].getLenType() == FieldLenType.LLL_LEN) {
                    byte[] len = new byte[2];
                    arraycopy(iso8583Pack.txBuffer, srcIndex, len, 0, 2);
                    int fieldLen = Integer.parseInt(Utils.bcd2Str(len));
                    LogUtils.i(TAG, "LLL fieldLen=" + fieldLen);
                    fields[i].setLen(fieldLen);
                    fields[i].data = new byte[fieldLen];
                    arraycopy(iso8583Pack.txBuffer, srcIndex + 2, fields[i].data, 0, fields[i].getLen());
                    LogUtils.i(TAG, "LLL field data=" + Utils.bcd2Str(fields[i].data));

                    srcIndex += fields[i].getLen() + 2;
                }
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        }
    }

    public void init8583Fields(FieldEntity[] fields) {
        try {
            processBitmap(fields);
            processFiledLenType(fields);
            processFiledLenData(fields);
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        }
    }

    /**
     * 将byte转换为一个长度为8的boolean数组（每bit代表一个boolean值）
     *
     * @param b byte
     * @return boolean数组
     */
    private boolean[] getBooleanArray(byte b) {
        boolean[] array = new boolean[8];
        //对于byte的每bit进行判定
        for (int i = 7; i >= 0; i--) {
            //判定byte的最后一位是否为1，若为1，则是true；否则是false
            array[i] = (b & 1) == 1;
            //将byte右移一位
            b = (byte) (b >> 1);
        }
        return array;
    }

    enum FieldLenType {
        FIX_LEN(0),
        LL_LEN(1),
        LLL_LEN(2);

        private int type;

        FieldLenType(int type) {
            this.type = type;
        }
    }

    enum FieldCodingType {
        BCD,
        ASCII,
        BINARY
    }

    /**
     * 定义8583的报文协议包结构 Len+Tpdu+Head+MsgType+BitMap+Data
     */
    public static class Pack {

        private byte[] tpdu;
        private byte[] head;
        private byte[] randomData;//3byte
        private byte[] status;//1byte
        private byte[] msgType;
        private byte[] bitMap;
        private byte[] txBuffer;
        private int txLen;

        public Pack() {
            tpdu = new byte[5];//ghl 6009880000
            head = new byte[22];//ghl: FME明文 22byte string
            randomData = new byte[3];//ghl:  3byte random
            status = new byte[1];//ghl:  1byte status
            msgType = new byte[2];
            bitMap = new byte[8];
            txBuffer = null;
            txLen = 0;
        }

        public byte[] getTpdu() {
            return tpdu;
        }

        public void setTpdu(byte[] tpdu) {
            this.tpdu = tpdu;
        }

        public byte[] getHead() {
            return head;
        }

        public void setHead(byte[] head) {
            this.head = head;
        }

        public byte[] getMsgType() {
            return msgType;
        }

        public void setMsgType(byte[] msgType) {
            this.msgType = msgType;
        }

        public byte[] getBitMap() {
            return bitMap;
        }

        public void setBitMap(byte[] bitMap) {
            this.bitMap = bitMap;
        }

        public byte[] getTxBuffer() {
            return txBuffer;
        }

        public void setTxBuffer(byte[] txBuffer) {
            this.txBuffer = txBuffer;
        }

        public byte[] getRandomData() {
            return randomData;
        }

        public void setRandomData(byte[] randomData) {
            this.randomData = randomData;
        }

        public byte[] getStatus() {
            return status;
        }

        public void setStatus(byte[] status) {
            this.status = status;
        }

        public int getTxLen() {
            return txLen;
        }

        public void setTxLen(int txLen) {
            this.txLen = txLen;
        }

        @Override
        public String toString() {
            return "Pack{" +
//                    "len=" + Utils.bcd2Str(len) +
                    " tpdu=" + Utils.bcd2Str(tpdu) +
//                    ", head=" + Utils.bcd2Str(head) +
//                    ", random=" + Utils.bcd2Str(randomData) +
//                    ", status=" + Utils.bcd2Str(status) +
                    ", msgType=" + Utils.bcd2Str(msgType) +
                    ", bitMap=" + Utils.bcd2Str(bitMap) +
//                    ", txLen=" + txLen +
                    ", txBuffer=" + Utils.bcd2Str(txBuffer) +
                    '}';
        }
    }

    /**
     * 域定义
     */
    public class FieldEntity {
        private boolean isExist;   /*是否存在*/
        private FieldLenType lenType;    /*类型(取值范围0-2，组帧时0:表示不计长度 1:表示长度占1字节，2:表示2长度占2字节)*/
        private int len;     /*域长度（需根据各个域的定义要求正确取值）*/
        private byte[] data; /*域内容*/
        private String name;//域名字
        private FieldCodingType codingType;

        FieldEntity() {
            isExist = false;
            lenType = FieldLenType.FIX_LEN;
            len = 0;
            codingType = FieldCodingType.BCD;
            data = null;
        }

        public boolean isExist() {
            return isExist;
        }

        public void setExist(boolean exist) {
            isExist = exist;
        }

        public FieldLenType getLenType() {
            return lenType;
        }

        public void setLenType(FieldLenType type) {
            this.lenType = type;
        }

        public int getLen() {
            return len;
        }

        public void setLen(int len) {
            this.len = len;
        }

        public byte[] getData() {
            return data;
        }

        public void setData(byte[] data) {
            this.data = data;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public FieldCodingType getCodingType() {
            return codingType;
        }

        public void setCodingType(FieldCodingType codingType) {
            this.codingType = codingType;
        }

        @Override
        public String toString() {
            if (!isExist) {
                return "";
            }

            String fieldData = "";
            if (codingType == FieldCodingType.ASCII) {
                fieldData = new String(data);
            } else {
                fieldData = Utils.bcd2Str(data);
                if ("55".equals(name)) {
                    TlvParseUtil tlvParse = new TlvParseUtil();
                    int parseRet = tlvParse.pbocTlvAns(data, data.length);
                    if (parseRet == 0) {
                        fieldData = "\n" + tlvParse.getTags();
                    }
                } else if ("35".equals(name) || "2".equals(name)) {

                    String buffer = Utils.bcd2Str(iso8583Pack.txBuffer);
                    int index = buffer.indexOf(fieldData);
                    int length = Integer.parseInt(buffer.substring(index - 2, index));
                    if (length % 2 != 0) {//奇数位，不显示补的0
                        fieldData = fieldData.substring(0, length);
                    }

                }
            }

            return name + "|" + fieldData + "\n";
        }
    }


}
