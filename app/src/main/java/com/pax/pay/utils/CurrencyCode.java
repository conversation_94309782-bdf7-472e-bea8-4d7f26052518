/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-10-26
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.utils;


import com.pax.sbipay.R;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CurrencyCode implements Serializable {
    /**
     * Chinese Yuan
     */
    private static final CurrencyCode RMB = new CurrencyCode("156", "CNY", 2, R.drawable.flag_chinese);

    /**
     * Hong Kong Dollars
     */
    private static final CurrencyCode HKD = new CurrencyCode("344", "HKD", 2, R.drawable.flag_hong_kong);

    /**
     * Macao Pataca
     */
    private static final CurrencyCode MOP = new CurrencyCode("446", "MOP", 2, R.drawable.flag_macao);

    /**
     * Malaysian Ringgit
     */
    private static final CurrencyCode MYR = new CurrencyCode("458", "MYR", 2, R.drawable.flag_malaysian);

    /**
     * Singapore Dollars
     */
    private static final CurrencyCode SGD = new CurrencyCode("702", "SGD", 2, R.drawable.flag_singapore);

    /**
     * Thai Baht
     */
    private static final CurrencyCode THB = new CurrencyCode("764", "THB", 2, R.drawable.flag_thai);

    /**
     * Indonesia Rupiah
     */
    private static final CurrencyCode IDR = new CurrencyCode("360", "IDR", 0, R.drawable.flag_indonesia);

    /**
     * Japanese Yen
     */
    private static final CurrencyCode JPY = new CurrencyCode("392", "JPY", 0, R.drawable.flag_japanese);

    /**
     * Euro
     */
    private static final CurrencyCode EUR = new CurrencyCode("978", "EUR", 2, R.drawable.flag_euro);

    /**
     * Philippine Pesos
     */
    private static final CurrencyCode PHP = new CurrencyCode("608", "PHP", 2, R.drawable.flag_philippine);

    /**
     * New Taiwanese Dollars
     */
    private static final CurrencyCode TWD = new CurrencyCode("901", "TWD", 2, R.drawable.flag_new_taiwanese);

    /**
     * US Dollars
     */
    private static final CurrencyCode USD = new CurrencyCode("840", "USD", 2, R.drawable.flag_us);

    /**
     * Vietnam DONG
     */
    private static final CurrencyCode VND = new CurrencyCode("704", "VND", 0, R.drawable.flag_vietnam);

    /**
     * United Arab Durham
     */
    private static final CurrencyCode AED = new CurrencyCode("784", "AED", 2, R.drawable.flag_united_arab);

    /**
     * Australian Dollars
     */
    private static final CurrencyCode AUD = new CurrencyCode("036", "AUD", 2, R.drawable.flag_australian);

    /**
     * Canadian Dollars
     */
    private static final CurrencyCode CAD = new CurrencyCode("124", "CAD", 2, R.drawable.flag_canadian);

    /**
     * Cypriot Pounds
     */
    private static final CurrencyCode CYP = new CurrencyCode("196", "CYP", 2, R.drawable.flag_cypriot);

    /**
     * Swiss Francs
     */
    private static final CurrencyCode CHF = new CurrencyCode("756", "CHF", 2, R.drawable.flag_swiss);

    /**
     * Danish Krone
     */
    private static final CurrencyCode DKK = new CurrencyCode("208", "DKK", 2, R.drawable.flag_danish);

    /**
     * British Pounds Sterling
     */
    private static final CurrencyCode GBP = new CurrencyCode("826", "GBP", 2, R.drawable.flag_united_kingdom);

    /**
     * Indian Rupee
     */
    private static final CurrencyCode INR = new CurrencyCode("356", "INR", 2, R.drawable.flag_indian);

    /**
     * Icelandic krone
     */
    private static final CurrencyCode ISK = new CurrencyCode("352", "ISK", 2, R.drawable.flag_icelandic);

    /**
     * South Korean Won
     */
    private static final CurrencyCode KRW = new CurrencyCode("410", "KRW", 0, R.drawable.flag_south_korean);

    /**
     * Sri-Lanka Rupee
     */
    private static final CurrencyCode LKR = new CurrencyCode("144", "LKR", 2, R.drawable.flag_sri_lanka);

    /**
     * Maltese Lira
     */
    private static final CurrencyCode MTL = new CurrencyCode("470", "MTL", 2, R.drawable.flag_maltese);

    /**
     * Norwegian Krone
     */
    private static final CurrencyCode NOK = new CurrencyCode("578", "NOK", 2, R.drawable.flag_norwegian);

    /**
     * New Zealand Dollars
     */
    private static final CurrencyCode NZD = new CurrencyCode("554", "NZD", 2, R.drawable.flag_new_zealand);

    /**
     * Russian Ruble
     */
    private static final CurrencyCode RUB = new CurrencyCode("643", "RUB", 2, R.drawable.flag_russian);

    /**
     * Saudi Riyal
     */
    private static final CurrencyCode SAR = new CurrencyCode("682", "SAR", 2, R.drawable.flag_saudi);

    /**
     * Swedish krone
     */
    private static final CurrencyCode SEK = new CurrencyCode("752", "SEK", 2, R.drawable.flag_swedish);

    /**
     * Turkey Lira
     */
    private static final CurrencyCode TRL = new CurrencyCode("792", "TRL", 2, R.drawable.flag_turkey);

    /**
     * Bolivar Fuerte (Venezuela)
     */
    private static final CurrencyCode VEF = new CurrencyCode("937", "VEF", 2, R.drawable.flag_weinaruila);

    /**
     * South African Rand
     */
    private static final CurrencyCode ZAR = new CurrencyCode("710", "ZAR", 2, R.drawable.flag_south_african);

    /**
     * Kuwaiti Dinar
     */
    private static final CurrencyCode KWD = new CurrencyCode("414", "KWD", 3, R.drawable.flag_kuwaiti);

    /**
     * Chilean Piso
     */
    private static final CurrencyCode CLP = new CurrencyCode("152", "CLP", 0, R.drawable.flag_chilean);

    /**
     * Hryvnia
     */
    private static final CurrencyCode UAH = new CurrencyCode("980", "UAH", 2, R.drawable.flag_hryvnia);

    /**
     * Qatar
     */
    private static final CurrencyCode QAR = new CurrencyCode("634", "QAR", 2, R.drawable.flag_qatar);

    /**
     * Tenge
     */
    private static final CurrencyCode KZT = new CurrencyCode("398", "KZT", 2, R.drawable.flag_tenge);

    /**
     * Egyptian Pound
     */
    private static final CurrencyCode EGP = new CurrencyCode("818", "EGP", 2, R.drawable.flag_egyptian_pound);

    /**
     * Rial Omani
     */
    private static final CurrencyCode OMR = new CurrencyCode("512", "OMR", 3, R.drawable.flag_rial_omani);

    /**
     * Bahraini Dinar
     */
    private static final CurrencyCode BHD = new CurrencyCode("048", "BHD", 3, R.drawable.flag_bahraini_dinar);

    /**
     * Jordanian Dinar
     */
    private static final CurrencyCode JOD = new CurrencyCode("400", "JOD", 3, R.drawable.flag_jordanian_dinar);

    /**
     * Pakistan Rupee
     */
    private static final CurrencyCode PKR = new CurrencyCode("586", "PKR", 2, R.drawable.flag_pakistan_rupee);

    /**
     * Lebanese Pound
     */
    private static final CurrencyCode LBP = new CurrencyCode("422", "LBP", 2, R.drawable.flag_lebanese_pound);

    /**
     * Brzilian Real
     */
    private static final CurrencyCode BRL = new CurrencyCode("986", "BRL", 2, R.drawable.flag_brzilian_real);

    /**
     * Czech Koruna
     */
    private static final CurrencyCode CZK = new CurrencyCode("203", "CZK", 2, R.drawable.flag_czech_koruna);

    /**
     * Mauritius Rupee
     */
    private static final CurrencyCode MUR = new CurrencyCode("480", "MUR", 2, R.drawable.flag_mauritius_rupee);

    /**
     * Brunei dollar
     */
    private static final CurrencyCode BND = new CurrencyCode("096", "BND", 2, R.drawable.flag_brunei_dollar);

    /**
     * Naira
     */
    private static final CurrencyCode NGN = new CurrencyCode("566", "NGN", 2, R.drawable.flag_naira);

    /**
     * Latvian Lats
     */
    private static final CurrencyCode LVL = new CurrencyCode("428", "LVL", 2, R.drawable.flag_latvian_lats);

    /**
     * Maldivian rufiyaa
     */
    private static final CurrencyCode MVR = new CurrencyCode("462", "MVR", 2, R.drawable.flag_maldivian);


    private static List<CurrencyCode> allCurrencyCodes = new ArrayList<>();

    static {
        allCurrencyCodes.add(RMB);
        allCurrencyCodes.add(HKD);
        allCurrencyCodes.add(MOP);
        allCurrencyCodes.add(MYR);
        allCurrencyCodes.add(SGD);
        allCurrencyCodes.add(THB);
        allCurrencyCodes.add(IDR);
        allCurrencyCodes.add(JPY);
        allCurrencyCodes.add(EUR);
        allCurrencyCodes.add(PHP);
        allCurrencyCodes.add(TWD);
        allCurrencyCodes.add(USD);
        allCurrencyCodes.add(VND);
        allCurrencyCodes.add(AED);
        allCurrencyCodes.add(AUD);
        allCurrencyCodes.add(CAD);
        allCurrencyCodes.add(CYP);
        allCurrencyCodes.add(CHF);
        allCurrencyCodes.add(DKK);
        allCurrencyCodes.add(GBP);
        allCurrencyCodes.add(INR);
        allCurrencyCodes.add(ISK);
        allCurrencyCodes.add(KRW);
        allCurrencyCodes.add(LKR);
        allCurrencyCodes.add(MTL);
        allCurrencyCodes.add(NOK);
        allCurrencyCodes.add(NZD);
        allCurrencyCodes.add(RUB);
        allCurrencyCodes.add(SAR);
        allCurrencyCodes.add(SEK);
        allCurrencyCodes.add(TRL);
        allCurrencyCodes.add(VEF);
        allCurrencyCodes.add(ZAR);
        allCurrencyCodes.add(KWD);
        allCurrencyCodes.add(CLP);
        allCurrencyCodes.add(UAH);
        allCurrencyCodes.add(QAR);
        allCurrencyCodes.add(KZT);
        allCurrencyCodes.add(EGP);
        allCurrencyCodes.add(OMR);
        allCurrencyCodes.add(BHD);
        allCurrencyCodes.add(JOD);
        allCurrencyCodes.add(PKR);
        allCurrencyCodes.add(LBP);
        allCurrencyCodes.add(BRL);
        allCurrencyCodes.add(CZK);
        allCurrencyCodes.add(MUR);
        allCurrencyCodes.add(BND);
        allCurrencyCodes.add(NGN);
        allCurrencyCodes.add(LVL);
        allCurrencyCodes.add(MVR);

    }

    private String code;
    private String symbol;
    private int decimals;
    private int nationalFlagImageId;

    public CurrencyCode(String code, String symbol, int decimals, int nationalFlagImageId) {
        if (decimals < 0 || decimals > 3) {
            throw new IllegalArgumentException("decimals must be 0-3.");
        }
        this.code = code;
        this.symbol = symbol;
        this.decimals = decimals;
        this.nationalFlagImageId = nationalFlagImageId;
    }

    public CurrencyCode() {

    }

    /**
     * Update currency code list.
     *
     * @param currencyCode the currency code
     */
    public static void updateCurrencyCodeList(CurrencyCode currencyCode) {
        if (currencyCode == null) {
            return;
        }
        for (CurrencyCode currency : allCurrencyCodes) {
            if (currency.getCode().equals(currencyCode.getCode())) {
                allCurrencyCodes.remove(currency);
                break;
            }
        }
        allCurrencyCodes.add(currencyCode);
    }

    /**
     * Gets by code.
     *
     * @param code the code
     * @return CurrencyCode
     */
    public static CurrencyCode getByCode(String code) {
        if (code == null) {
            return null;
        }
        String currencyCode;
        if (code.length() == 2) {
            currencyCode = "0" + code;
        } else {
            currencyCode = code;
        }
        for (CurrencyCode currency : allCurrencyCodes) {
            if (currency.getCode().equals(currencyCode)) {
                return currency;
            }
        }
        return null;
    }

    /**
     * Gets by symbol.
     *
     * @param symbol the symbol
     * @return CurrencyCode by symbol
     */
    public static CurrencyCode getBySymbol(String symbol) {
        if (symbol == null) {
            return null;
        }
        for (CurrencyCode currency : allCurrencyCodes) {
            if (currency.getSymbol().equals(symbol)) {
                return currency;
            }
        }
        return null;
    }

    /**
     * Gets flag id by symbol.
     *
     * @param symbol the symbol
     * @return the flag id by symbol
     */
    public static int getFlagIdBySymbol(String symbol) {
        if (symbol == null) {
            return 0;
        }
        for (CurrencyCode currency : allCurrencyCodes) {
            if (currency.getSymbol().equals(symbol)) {
                return currency.getNationalFlagImageId();
            }
        }
        return 0;
    }

    public String getCode() {
        return code;
    }

    public String getSymbol() {
        return symbol;
    }

    public int getDecimals() {
        return decimals;
    }

    public int getNationalFlagImageId() {
        return nationalFlagImageId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public void setDecimals(int decimals) {
        this.decimals = decimals;
    }

    public void setNationalFlagImageId(int nationalFlagImageId) {
        this.nationalFlagImageId = nationalFlagImageId;
    }
}
