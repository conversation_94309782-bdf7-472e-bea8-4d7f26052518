/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.pay.utils;

import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import com.pax.commonlib.utils.LogUtils;
import com.pax.pay.app.FinancialApplication;

public abstract class EditorActionListener implements TextView.OnEditorActionListener {


    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        LogUtils.d("silly muhua", "actionId=" + actionId + ", event=" + event);
        if (actionId == EditorInfo.IME_ACTION_UNSPECIFIED) {
            if (event != null && event.getAction() == KeyEvent.ACTION_DOWN) {
                LogUtils.d("silly muhua", "物理按键的Enter");
                FinancialApplication.getApp().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        onKeyOk();
                    }
                });
                return true;
            }
        } else if (actionId == EditorInfo.IME_ACTION_DONE) {
            FinancialApplication.getApp().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onKeyOk();
                }
            });
            return true;
        } else if (actionId == EditorInfo.IME_ACTION_NONE) {
            FinancialApplication.getApp().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onKeyCancel();
                }
            });
            return true;
        }
        return false;
    }

    protected abstract void onKeyOk();

    protected abstract void onKeyCancel();
}
