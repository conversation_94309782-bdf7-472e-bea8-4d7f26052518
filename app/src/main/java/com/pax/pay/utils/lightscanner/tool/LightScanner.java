/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.S                Create
 * ===========================================================================================
 */

package com.pax.pay.utils.lightscanner.tool;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.pax.commonlib.utils.LogUtils;
import com.pax.pay.utils.lightscanner.LightScannerActivity;

/**
 * Created by Steven.S on 2018/5/23/0023.
 */
public class LightScanner {
    public static final String SCAN_INTENT_ACTION = "android.intent.action.OpenLightScanner";
    public static final String FLAGS = "FLAGS";
    public static final String QR_CODE_STR = "QR_CODE_STR";
    public static final String TIMEOUT = "TIMEOUT";
    public static final int SUCCESS_FLAG = 0;
    public static final int CANCEL_FLAG = 1;
    public static final int TIMEOUT_FLAG = 2;

    private Context context;
    private LightScannerListener listener;
    private int mTimeout = 2 * 60; //默认超时时间为2分钟
    private boolean isBroadcastRegistered = false;
    private String qrCodeStr;

    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(SCAN_INTENT_ACTION)) {
                int flag = intent.getIntExtra(FLAGS, CANCEL_FLAG);
                switch (flag) {
                    case SUCCESS_FLAG:
                        qrCodeStr = intent.getStringExtra(QR_CODE_STR);
                        LogUtils.i("LightScanner", "qrCode: " + qrCodeStr);
                        if (listener != null) {
                            listener.onReadSuccess(qrCodeStr);
                        }
                        break;
                    case CANCEL_FLAG:
                        if (listener != null) {
                            listener.onCancel();
                        }
                        break;
                    case TIMEOUT_FLAG:
                        if(listener != null){
                            listener.onTimeOut();
                        }
                        break;
                    default:
                        if (listener != null) {
                            listener.onReadError();
                        }
                        break;
                }
            }
        }
    };

    public LightScanner(Context context) {
        this.context = context;
    }

    public LightScanner(Context context, int timeoutSec) {
        this.context = context;
        this.mTimeout = timeoutSec;
    }

    public void start(LightScannerListener listener) {
        this.listener = listener;
    }

    public void open() {
        Intent intent = new Intent(context, LightScannerActivity.class);
        intent.putExtra(TIMEOUT, mTimeout);
        context.startActivity(intent);

        registerOpenBroadcastReceiver();
    }

    public void close() {
        unRegisterMyBroadcastReceiver();

        Intent intent = new Intent(LightScannerActivity.CLOSE_SCANNER_INTENT_ACTION);
        context.sendBroadcast(intent);
    }

    private void registerOpenBroadcastReceiver() {
        if (!isBroadcastRegistered) {
            isBroadcastRegistered = !isBroadcastRegistered;
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(SCAN_INTENT_ACTION);
            context.registerReceiver(receiver, intentFilter);
        }
    }

    private void unRegisterMyBroadcastReceiver() {
        if (isBroadcastRegistered) {
            isBroadcastRegistered = !isBroadcastRegistered;
            context.unregisterReceiver(receiver);
        }
    }

    public interface LightScannerListener {
        /**
         * on scan success
         */
        void onReadSuccess(String result);
        /**
         * on error
         */
        void onReadError();
        /**
         * on cancel
         */
        void onCancel();

        /**
         * on timeOut
         */
        void onTimeOut();
    }
}
