package com.pax.pay.utils;

import com.pax.pay.constant.Constants;

/**
 * The type Parse param exception.
 */
public class ParseParamException extends Exception {

    private String mErrorMessage;
    private String mTag;

    /**
     * Instantiates a new Parse param exception.
     *
     * @param tag the tag
     * @param key the key
     */
    public ParseParamException(String tag, String key) {
        mTag = tag;
        mErrorMessage = tag + key + " value is null";
    }

    /**
     * Instantiates a new Parse param exception.
     *
     * @param tag  the tag
     * @param line the line
     * @param key  the key
     */
    public ParseParamException(String tag, int line, String key) {
        mTag = tag;
        mErrorMessage = tag + " line:" + line + " " + key + " value is null";
    }

    /**
     * Instantiates a new Parse param exception.
     *
     * @param tag  the tag
     * @param key  the key
     * @param type the type
     */
    public ParseParamException(String tag, String key, int type) {
        mTag = tag;
        if (type == Constants.INTEGER) {
            mErrorMessage = tag + " " + key + "'s values is not a integer";
        } else {
            mErrorMessage = tag + " " + key + "'s values is not a long";
        }
    }

    /**
     * Instantiates a new Parse param exception.
     *
     * @param tag  the tag
     * @param line the line
     * @param key  the key
     * @param type the type
     */
    public ParseParamException(String tag, int line, String key, int type) {
        mTag = tag;
        if (type == Constants.INTEGER) {
            mErrorMessage = tag + " line:" + line + " " + key + "'s values is not a integer";
        } else {
            mErrorMessage = tag + " line:" + line + " " + key + "'s values is not a long";
        }
    }

    /**
     * Instantiates a new Parse param exception.
     *
     * @param tag  the tag
     * @param line the line
     */
    public ParseParamException(String tag, int line) {
        mTag = tag;
        mErrorMessage = "File format is not correct, at line: " + line;
    }

    @Override
    public String getMessage() {
        return mErrorMessage;
    }

    /**
     * Get tag string.
     *
     * @return the string
     */
    public String getTag(){
        return mTag;
    }
}
