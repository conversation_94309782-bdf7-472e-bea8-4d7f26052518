/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-30
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.utils;

import android.util.Log;
import com.pax.settings.SysParam;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.MissingResourceException;

import static com.pax.pay.app.FinancialApplication.getSysParam;

public class CurrencyConverter {

    private static final String TAG = "CurrencyConv";
    private static final List<Locale> locales = new ArrayList<>();

    //    private static Locale defLocale = Locale.US;
    private static Locale defLocale = Locale.forLanguageTag("zh-Hant-HK");

    static {
        Locale[] tempLocales = Locale.getAvailableLocales();
        for (Locale i : tempLocales) {
            try {
                CountryCode country = CountryCode.getByCode(i.getISO3Country());
                Currency.getInstance(i); // just for filtering
                if (country != null) {
                    locales.add(i);
                }
            } catch (IllegalArgumentException | MissingResourceException e) {
                //Log.d(TAG, "", e);
            }
        }
    }

    private CurrencyConverter() {
        //do nothing
    }

    public static List<Locale> getSupportedLocale() {
        return locales;
    }

    /**
     * @param countryName : {@see Locale#getDisplayName(Locale)}
     */
    public static Locale setDefCurrency(String countryName) {
        for (Locale i : locales) {
            if (i.getDisplayName(Locale.US).equals(countryName)) {
                if (!i.equals(defLocale)) {
                    defLocale = i;
                    Locale.setDefault(defLocale);
                }
                return defLocale;
            }
        }
        return defLocale;
    }

    /**
     *
     */
    public static Locale getDefCurrency() {
        return defLocale;
    }

    /**
     *
     */
    public static String convert(Long amount) {
        return convert(amount, defLocale);
    }

    public static String convertWithCode(Long amount) {
        Currency currency = Currency.getInstance(defLocale);
        NumberFormat formatter = NumberFormat.getNumberInstance(defLocale);
        formatter.setMinimumFractionDigits(currency.getDefaultFractionDigits());
        formatter.setMaximumFractionDigits(currency.getDefaultFractionDigits());
        long newAmount = amount < 0 ? -amount : amount; // AET-58
        String prefix = amount < 0 ? "-" : "";
        try {
            double amt = (double) newAmount / (Math.pow(10, currency.getDefaultFractionDigits()));
            String currencyCode = currency.getCurrencyCode();
            if ("CNY".equals(currencyCode)) {
                currencyCode = "RMB";
            }
            return prefix + currencyCode + formatter.format(amt);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
        return "";
    }

    /**
     *
     */
    public static String convert(Long amount, Locale locale) {
        Currency currency = Currency.getInstance(locale);
        NumberFormat formatter = NumberFormat.getCurrencyInstance(locale);
        formatter.setMinimumFractionDigits(currency.getDefaultFractionDigits());
        formatter.setMaximumFractionDigits(currency.getDefaultFractionDigits());
        long newAmount = amount < 0 ? -amount : amount; // AET-58
        String prefix = amount < 0 ? "-" : "";
        try {
            double amt = (double) newAmount / (Math.pow(10, currency.getDefaultFractionDigits()));
            if ("SGD".equals(currency.getCurrencyCode())) {
                return prefix + formatter.format(amt).replace("$", "S$");
            }
            return prefix + formatter.format(amt);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
        return "";
    }

    /**
     * Convert.
     *
     * @param amount           the amount
     * @param isSymbolNegative is symbol negative
     * @return the string
     */
    public static String convert(Long amount, boolean isSymbolNegative) {
        CurrencyConverter.setDefCurrency(getSysParam().get(SysParam.StringParam.EDC_CURRENCY_LIST));
        return convert(amount, defLocale, isSymbolNegative);
    }

    /**
     * Convert.
     *
     * @param amount           the amount
     * @param locale           the locale
     * @param isSymbolNegative is symbol negative
     * @return the string
     */
    public static String convert(Long amount, Locale locale, boolean isSymbolNegative) {
        Currency currency = Currency.getInstance(locale);
        NumberFormat formatter = NumberFormat.getNumberInstance(locale);
        formatter.setMinimumFractionDigits(currency.getDefaultFractionDigits());
        formatter.setMaximumFractionDigits(currency.getDefaultFractionDigits());
        Long newAmount = amount < 0 ? Long.valueOf(-amount) : amount; // AET-58
        String prefix = isSymbolNegative ? "-" : "";
        try {
            double amt = Double.valueOf(newAmount) / (Math.pow(10,
                    currency.getDefaultFractionDigits()));
            String currencyCode = currency.getCurrencyCode().equalsIgnoreCase("CNY") ? "RMB"
                    : currency.getCurrencyCode();
            return prefix + currencyCode + formatter.format(amt);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
        return "";
    }

    /**
     *
     */
    public static String convert(Long amount, CurrencyCode currencyCode) {
        boolean isSymbolNegative = false;
        if (amount < 0) {
            isSymbolNegative = true;
        }
        if (currencyCode == null) {
            return convert(amount, defLocale, isSymbolNegative);
        }
        return convert(amount, currencyCode, isSymbolNegative);
    }

    /**
     *
     */
    public static String convert(Long amount, CurrencyCode currencyCode, boolean isSymbolNegative) {
        if (currencyCode == null) {
            return convert(amount, defLocale, isSymbolNegative);
        }
        NumberFormat formatter = NumberFormat.getNumberInstance();
        formatter.setMinimumFractionDigits(currencyCode.getDecimals());
        formatter.setMaximumFractionDigits(currencyCode.getDecimals());
        Long newAmount = amount < 0 ? Long.valueOf(-amount) : amount; // AET-58
        String prefix = isSymbolNegative ? "-" : "";
        try {
            double amt = Double.valueOf(newAmount) / (Math.pow(10, currencyCode.getDecimals()));
            return prefix + currencyCode.getSymbol() + formatter.format(amt);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
        return "";
    }

    public static String dccConvert(Long amount, CurrencyCode currencyCode, boolean hasTip) {
        boolean isSymbolNegative = false;
        if (amount < 0) {
            isSymbolNegative = true;
        }
        if (currencyCode == null) {
            return convert(amount, defLocale, isSymbolNegative);
        }
        return dccConvert(amount, currencyCode, isSymbolNegative, hasTip);
    }

    public static String dccConvert(Long amount, CurrencyCode currencyCode, boolean isSymbolNegative, boolean hasTip) {
        if (currencyCode == null) {
            return convert(amount, defLocale, isSymbolNegative);
        }
        NumberFormat formatter = NumberFormat.getNumberInstance();
        formatter.setMinimumFractionDigits(currencyCode.getDecimals());
        formatter.setMaximumFractionDigits(currencyCode.getDecimals());
        Long newAmount = amount < 0 ? Long.valueOf(-amount) : amount; // AET-58
        String prefix = isSymbolNegative ? "-" : "";
        try {
            double amt = Double.valueOf(newAmount) / (Math.pow(10, currencyCode.getDecimals()));
            String[] div = String.valueOf(amt).split("\\.");
            int decimal = div[1].length();
            if (decimal >= 3 && (!currencyCode.getSymbol().equals("BHD") && !currencyCode.getSymbol().equals("OMR")) && hasTip){
                String result = String.format("%.2f",amt) + "0";
                amt = Double.valueOf(result);
            }
            return prefix + currencyCode.getSymbol() + formatter.format(amt);
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "", e);
        }
        return "";
    }

    public static Long parse(String formatterAmount) {
        return parse(formatterAmount, defLocale);
    }

    /**
     *
     */
    public static Long parse(String formatterAmount, Locale locale) {
        Currency currency = Currency.getInstance(locale);
        if (currency.getCurrencyCode().equals("CNY")) {
            String newFormatterAmount = formatterAmount;
            if ("-".equals(formatterAmount.substring(0, 1))) {
                newFormatterAmount = formatterAmount.substring(1);
            }
            String amount;
            String currencyCode =
                    currency.getCurrencyCode().equals("CNY") ? "RMB" : currency.getCurrencyCode();
            if (currencyCode.equals(newFormatterAmount.substring(0, currencyCode.length()))) {
                amount = newFormatterAmount.substring(currencyCode.length());
            } else {
                amount = newFormatterAmount;
            }
            NumberFormat formatter = NumberFormat.getNumberInstance(locale);
            formatter.setMinimumFractionDigits(currency.getDefaultFractionDigits());
            formatter.setMaximumFractionDigits(currency.getDefaultFractionDigits());
            try {
                Number num = formatter.parse(amount);

                return Math.round(num.doubleValue() * Math.pow(10, currency.getDefaultFractionDigits()));
            } catch (ParseException | NumberFormatException e) {
                Log.e(TAG, "", e);
            }
        } else {
            NumberFormat formatter = NumberFormat.getCurrencyInstance(locale);
            formatter.setMinimumFractionDigits(currency.getDefaultFractionDigits());
            formatter.setMaximumFractionDigits(currency.getDefaultFractionDigits());
            try {
                Number num = formatter.parse(formatterAmount);

                return Math.round(num.doubleValue() * Math.pow(10, currency.getDefaultFractionDigits()));
            } catch (ParseException | NumberFormatException e) {
                Log.e(TAG, "", e);
            }
        }
        return 0L;
    }

    /**
     *
     * @param formatterAmount
     * @param currency
     * @return
     */
    public static Long parse(String formatterAmount, CurrencyCode currency) {
        String newFormatterAmount = formatterAmount;
        if ("-".equals(formatterAmount.substring(0, 1))) {
            newFormatterAmount = formatterAmount.substring(1);
        }
        String amount;
        String currencyCode = currency.getSymbol().equals("CNY") ? "RMB" : currency.getSymbol();
        if (currencyCode.equals(newFormatterAmount.substring(0, currencyCode.length()))) {
            amount = newFormatterAmount.substring(currencyCode.length());
        } else {
            amount = newFormatterAmount;
        }
        NumberFormat formatter = NumberFormat.getNumberInstance();
        formatter.setMinimumFractionDigits(currency.getDecimals());
        formatter.setMaximumFractionDigits(currency.getDecimals());
        try {
            Number num = formatter.parse(amount);

            return Math.round(num.doubleValue() * Math.pow(10, currency.getDecimals()));
        } catch (ParseException | NumberFormatException e) {
            Log.e(TAG, "", e);
        }
        return 0L;
    }
}
