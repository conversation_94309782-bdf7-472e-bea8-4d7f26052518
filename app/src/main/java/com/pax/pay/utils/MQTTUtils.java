package com.pax.pay.utils;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.pax.commonbusiness.card.PanUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.Provider;
import com.pax.data.local.GreendaoHelper;
import com.pax.data.local.db.helper.SQRDataDbHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.mqtt.MQTTForegroundService;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.service.TTSService;
import com.pax.pay.trans.model.ETransType;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class MQTTUtils {
    private static final String TAG = "MQTTUtils";
    private static Provider currentDisplayProvider;
    private static Provider currentUseProvider;
    // 存储连接wifi时临时使用的Provider，因为多处会用到该provider，在connect时查询一次，后续都用这个配置
    private static Provider tempPublicProvider;
    private static boolean needToRestart = false;
    private static final Pattern AMOUNT_PATTERN = Pattern.compile("^\\d+(\\.\\d{1,2})?$");

    private MQTTUtils() {
    }

    public static Provider getCurrentDisplayProvider() {
        return currentDisplayProvider;
    }

    public static void setCurrentDisplayProvider(Provider currentDisplayProvider) {
        MQTTUtils.currentDisplayProvider = currentDisplayProvider;
    }

    public static Provider getCurrentUseProvider() {
        return currentUseProvider;
    }

    public static void setCurrentUseProvider(Provider currentUseProvider) {
        // 更新Acquirer信息因为IP，port，NII等信息是存储在Acquire里并且存储到Transdata中的
        FinancialApplication.getApp().runInBackground(() -> {
            Acquirer acquirer = FinancialApplication.getAcqManager().getCurAcq();
            acquirer.setNii(currentUseProvider.getNii());
            acquirer.setIp(currentUseProvider.getProviderHost());
            acquirer.setPort(currentUseProvider.getProviderPort());
            acquirer.setSslType(currentUseProvider.getSslType());
            FinancialApplication.getAcqManager().setCurAcq(acquirer);
            FinancialApplication.getAcqManager().updateAcquirer(acquirer);
        });
        MQTTUtils.currentUseProvider = currentUseProvider;
    }

    public static boolean isNeedToRestart() {
        return needToRestart;
    }

    public static void setNeedToRestart(boolean needToRestart) {
        MQTTUtils.needToRestart = needToRestart;
    }

    public static void startMQTT() {
        // 防止未打开开关但是启动了MQTT
        if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
            FinancialApplication.getApp().runOnUiThread(() -> {
                // 启动 MQTTService
                Intent intent = new Intent(FinancialApplication.getApp(), MQTTForegroundService.class);
                FinancialApplication.getApp().startForegroundService(intent);
                Log.d(TAG, "Start  MQTTService");
                startTTS();
            });
        }
    }

    public static void startTTS() {
        FinancialApplication.getApp().runInBackground(() -> {
            // 延迟启动 TTSService，避免同时执行多个耗时任务
            Intent ttsIntent = new Intent(FinancialApplication.getApp(), TTSService.class);
            FinancialApplication.getApp().startForegroundService(ttsIntent);
            Log.d(TAG, "Start TTSService");
        });
    }

    public static void updateMQTTParam() {
        Intent intent = new Intent(MQTTForegroundService.ACTION_UPDATE_MQTT);
        LocalBroadcastManager.getInstance(FinancialApplication.getApp()).sendBroadcast(intent);
    }

    public static void startMqttOptional(Provider provider) {
        if (isNeedToRestart()) {
            setCurrentUseProvider(provider);
            setNeedToRestart(false);
            updateMQTTParam();
        }
    }

    public static boolean validateTIDAndMID(String tid, String mid) {
        //校验TID MID是否匹配
        String name = SysParam.getInstance().getString(R.string.ACQ_NAME);
        Acquirer acquirer = GreendaoHelper.getAcquirerHelper().findAcquirer(name);
        String acqTid = acquirer.getTerminalId();
        String acqMid = acquirer.getMerchantId();
        return acqTid.equals(tid) && acqMid.equals(mid);
    }

    public static void destoryMQTT() {
        new Handler(Looper.getMainLooper()).post(() -> {
            // 停止 MQTT 服务
            Intent intent = new Intent(FinancialApplication.getApp(), MQTTForegroundService.class);
            FinancialApplication.getApp().stopService(intent);
            Log.d("MQTT", "stop mqtt services");
            // 停止 TTS 服务
            destoryTTS();
        });
    }

    public static void destoryTTS() {
        boolean mqttSwitch = SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT));
        boolean audioSwitch = SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_TRANSACTION_SUCCESS_AUDIO));
        if (!mqttSwitch && !audioSwitch) {
            // 停止 TTS 服务
            Intent intent = new Intent(FinancialApplication.getApp(), TTSService.class);
            FinancialApplication.getApp().stopService(intent);
            Log.d("TTSService", "stop tts services");
        }
    }

    public static void pushMQTTMessage(String playStatus, String amount, String language) {
        //语音播报之后，回传消息给MQTT服务器
        Intent intent = new Intent(FinancialApplication.getApp(), MQTTForegroundService.class);
        intent.setAction("ACTION_PUSH_MQTT_MESSAGE");
        intent.putExtra("PLAY_STATUS", playStatus);
        intent.putExtra("Amount", amount);
        intent.putExtra("Language", language);
        FinancialApplication.getApp().startService(intent);
    }

    public static Provider getTempPublicProvider() {
        return tempPublicProvider;
    }

    public static Map<String, String> prepareValuesForDispSQR(SQRData transData) {

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        String amount = "₹" + transData.getAmount();
        String formattedDate = TimeConverter.convert(transData.getTranDate(), Constants.TIME_PATTERN_MQTT_QR,
                Constants.TIME_PATTERN_CUSTOM);
        map.put(Utils.getString(R.string.history_detail_type), ETransType.QR.getTransName());
        map.put(Utils.getString(R.string.history_detail_amount), amount);
        Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(transData.getCardNo());
        String pattern = issuer == null ? "" : issuer.getPanMaskPattern();
        map.put(Utils.getString(R.string.history_detail_card_no),
                PanUtils.maskCardNo(transData.getCardNo(), pattern));
        map.put(Utils.getString(R.string.history_detail_auth_code), transData.getAuthCode());
        map.put(Utils.getString(R.string.history_detail_ref_no), transData.getRrn());
        map.put(Utils.getString(R.string.history_detail_invoice_no), transData.getInvoiceNr());
        map.put(Utils.getString(R.string.dateTime), formattedDate);
        return map;
    }

    public static void setTempPublicProvider(Provider tempPublicProvider) {
        MQTTUtils.tempPublicProvider = tempPublicProvider;
    }

    /**
     * 判断网络是否连接并具有互联网访问能力
     */
    public static boolean isConnectIsNormal() {
        ConnectivityManager connectivityManager = (ConnectivityManager) FinancialApplication.getAppContext()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            // 获取当前活跃的网络
            Network network = connectivityManager.getActiveNetwork();
            if (network == null) {
                Log.w(TAG, "There is no network available");
                return false;
            }

            // 获取网络的能力
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
            if (capabilities != null && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                return true;
            }

        }
        Log.w(TAG, "terminal does not have an available network or the network lacks internet access capability");
        return false;
    }

    public static boolean validAmount(String amount) {
        if (!AMOUNT_PATTERN.matcher(amount).matches() || Long.parseLong(amount.replaceAll("\\D", "")) > 999999999999L) {
            LogUtils.d(TAG, "MQTT Transaction Amount invalid");
            return false;
        }
        return true;
    }

    public static String ConvertToSpeak(String amount, boolean isHindi) {
        if (amount.contains(".")) {
            String[] parts = amount.split("\\.");
            String rupeesPart = parts[0];
            String paisaPart = parts[1];

            if (paisaPart.length() == 1) {
                paisaPart = paisaPart + "0";
            } else if (paisaPart.length() > 2) {
                paisaPart = paisaPart.substring(0, 2);
            }

            if (isHindi) {
                String rupeesDevanagari = convertToDevanagari(rupeesPart);
                String paisaDevanagari = convertToDevanagari(paisaPart);
                if ("00".equals(paisaPart)) {
                    return "SBI पेमेंट्स पर" + rupeesDevanagari + "रुपये" + "का भुगतान प्राप्त हुआ है";
                } else {
                    return "SBI पेमेंट्स पर" + rupeesDevanagari + "रुपये" + paisaDevanagari + "पैसे का भुगतान प्राप्त हुआ है";
                }
            } else {
                if ("00".equals(paisaPart)) {
                    return "Received Payment of" + rupeesPart + "Rupees on SBI Payments";
                } else {
                    return "Received Payment of" + rupeesPart + " Rupees and " + paisaPart + " Paisa on SBI Payments";
                }
            }
        } else {
            if (isHindi) {
                return convertToDevanagari(amount);
            } else {
                return amount;
            }
        }
    }

    private static String convertToDevanagari(String input) {
        StringBuilder sb = new StringBuilder();
        for (char ch : input.toCharArray()) {
            if (Character.isDigit(ch)) {
                char devanagari = (char) ((ch - '0') + '\u0966');
                sb.append(devanagari);
            } else {
                sb.append(ch);
            }
        }
        return sb.toString();
    }

    /**
     * 执行定期任务清理过期数据
     */
    public static void deleteExpireData() {
        FinancialApplication.getApp().runInBackground(() -> {
            LogUtils.d(TAG, "Starting cleanup of expired MQTT data");
            SQRDataDbHelper dbHelper = GreendaoHelper.getSQRDataHelper();
            int deletedCount = dbHelper.deleteExpiredData(24);
            if (deletedCount > 0) {
                LogUtils.i(TAG, "Successfully deleted " + deletedCount + " expired records");
            } else if (deletedCount == 0) {
                LogUtils.d(TAG, "No expired records found to delete");
            } else {
                LogUtils.e(TAG, "Error occurred during data cleanup");
            }
        });
    }
}
