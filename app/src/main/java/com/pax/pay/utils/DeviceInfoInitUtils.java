package com.pax.pay.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.IDAL;
import com.pax.dal.IDalCommManager;
import com.pax.dal.IPhoneManager;
import com.pax.dal.ISys;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.dal.exceptions.PhoneDevException;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.DeviceDetails;
import com.pax.data.entity.Provider;
import com.pax.data.local.GreendaoHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.sbipay.BuildConfig;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class DeviceInfoInitUtils {
    private static final String TAG = "DeviceInfoInitUtils";
    private static Acquirer acquirer;
    private static IDAL dal = FinancialApplication.getDal();
    private static ISys sys = dal.getSys();

    public static final int WIFI = 1;
    public static final int MOBILE_DATA = 2;

    private static ExecutorService ioExecutor = Executors.newSingleThreadExecutor(); // 创建IO线程池


    private DeviceInfoInitUtils() {
    }

    public static Future<DeviceDetails> initDeviceInfo(Context context) {
        @SuppressLint("DefaultLocale") Callable<DeviceDetails> callable = () -> {
            DeviceDetails deviceDetails = new DeviceDetails();
            String name = SysParam.getInstance().getString(R.string.ACQ_NAME);
            sys.setInfoCollect(true);
            acquirer = GreendaoHelper.getAcquirerHelper().findAcquirer(name);
            IDalCommManager commManager = dal.getCommManager();
            IPhoneManager phoneManager = dal.getPhoneManager();
            WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            WifiInfo info = wifiManager.getConnectionInfo();

            deviceDetails.setTid(acquirer.getTerminalId());
            deviceDetails.setMid(acquirer.getMerchantId());
            deviceDetails.setHostIp(acquirer.getIp());
            deviceDetails.setHostPort(acquirer.getPort());
            deviceDetails.setHostNii(acquirer.getNii());
            if (commManager.getCurrentApn() == null || commManager.getCurrentApn().getApn() == null) {
                deviceDetails.setHostApn("");
            } else {
                deviceDetails.setHostApn(getSafeString(() -> commManager.getCurrentApn().getApn()));
            }

            deviceDetails.setSoftwareVersion(BuildConfig.VERSION_NAME);
            deviceDetails.setBuildNum(String.valueOf(BuildConfig.VERSION_CODE));
            deviceDetails.setBuildType(BuildConfig.BUILD_TYPE);
            deviceDetails.setBuildDate(BuildConfig.BUILD_TIME);

            String firmwareVersion = "";
            String securityFirmwareVersion = "";
            String sn = "";
            String resourcePackageVersion = "";
            String hardwareVersion = "";
            String modelName = "";
            String simStatus = "";
            String neptuneServiceVersion = "";
            String sslEnable = "";
            String selectedMedia = "";
            try {
                Bundle securityInfo = sys.getSecurityInfo();
                firmwareVersion = securityInfo.getString("firmver");
                securityFirmwareVersion = securityInfo.getString("secver");
                Map<ETermInfoKey, String> terminalInfo = sys.getTermInfo();
                sn = terminalInfo.get(ETermInfoKey.SN);
                resourcePackageVersion = sys.getCustomerResVer();
                hardwareVersion = terminalInfo.get(ETermInfoKey.AP_VER);
                modelName = terminalInfo.get(ETermInfoKey.MODEL);
                neptuneServiceVersion = sys.getDevInterfaceVer();
                if (commManager.isMobileEnabled()) {
                    simStatus = "Available";
                } else {
                    simStatus = "Not Available";
                }

                if (SysParam.CommSslType.SSL == acquirer.getSslType()) {
                    sslEnable = "Enabled";
                } else {
                    sslEnable = "Disabled";
                }

                if (WIFI == getConnectivityStatus(context)) {
                    selectedMedia = "Wi-Fi";
                } else {
                    selectedMedia = getNetworkTypeName(context);
                }

            } catch (Exception e) {
                LogUtils.e(TAG, "", e);
            }

            deviceDetails.setFirmwareVersion(firmwareVersion);
            deviceDetails.setResourcePackageVersion(resourcePackageVersion);
            deviceDetails.setNeptuneServiceVersion(neptuneServiceVersion);
            deviceDetails.setHardwareVersion(hardwareVersion);
            deviceDetails.setSpVersion(securityFirmwareVersion);
            deviceDetails.setModelName(modelName);
            deviceDetails.setSerialNumber(sn);
            deviceDetails.setSelectedMedia(selectedMedia);
            deviceDetails.setSimStatus(simStatus);
            deviceDetails.setSimNumber(getSafeString(() -> phoneManager.getSIMInfo()[2]));
            deviceDetails.setNetworkName(getSimNetworkName(context));
            deviceDetails.setSslEnable(sslEnable);
            deviceDetails.setMacAddress(getSafeString(() -> info.getMacAddress()));
            deviceDetails.setTerminalIp(getIPAddress(context));
            deviceDetails.setMerchantName(getSafeString(() -> SysParam.getInstance().getString(R.string.EDC_MERCHANT_NAME)));
            if (simStatus.equals("Available")) {
                deviceDetails.setSignalStrength(FinancialApplication.getTelephoneStrength());
            } else {
                deviceDetails.setSignalStrength(-120);
            }
            setMqttInfo(deviceDetails);
            deviceDetails.setBatteryStatus(BatteryUtlis.getBatteryHealthInfo());
            deviceDetails.setPaperUsage(String.valueOf(FinancialApplication.getDal().getDeviceInfo().getPaperUsage()));
            deviceDetails.setBatteryLeve(BatteryUtlis.getBatteryLevelOnce());
            return deviceDetails;
        };

        return ioExecutor.submit(callable);
    }

    public static int getConnectivityStatus(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        if (null != activeNetwork) {
            if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                return WIFI;
            }
        }
        return MOBILE_DATA;
    }

    // 封装一个安全获取字符串值的方法，此方法接受一个Supplier接口的实现，用来执行实际的获取操作
    private static String getSafeString(Supplier<String> supplier) {
        try {
            return (supplier != null && supplier.get() != null) ? supplier.get() : "";
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
            return "";
        }
    }

    @FunctionalInterface
    interface Supplier<T> {
        T get() throws PhoneDevException;

    }

    private static String getSimNetworkName(Context context) {
        // 获取TelephonyManager的实例
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

        // 获取SIM卡的运营商名称
        String operatorName = telephonyManager.getSimOperatorName();

        return getSafeString(() -> operatorName);
    }

    public static String getNetworkTypeName(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            int networkType = telephonyManager.getNetworkType();
            switch (networkType) {
                case TelephonyManager.NETWORK_TYPE_GPRS:
                    return "GPRS";
                case TelephonyManager.NETWORK_TYPE_EDGE:
                    return "EDGE";
                case TelephonyManager.NETWORK_TYPE_UMTS:
                    return "UMTS";
                case TelephonyManager.NETWORK_TYPE_HSDPA:
                    return "HSDPA";
                case TelephonyManager.NETWORK_TYPE_HSUPA:
                    return "HSUPA";
                case TelephonyManager.NETWORK_TYPE_HSPA:
                    return "HSPA";
                case TelephonyManager.NETWORK_TYPE_CDMA:
                    return "CDMA";
                case TelephonyManager.NETWORK_TYPE_EVDO_0:
                    return "EVDO_0";
                case TelephonyManager.NETWORK_TYPE_EVDO_A:
                    return "EVDO_A";
                case TelephonyManager.NETWORK_TYPE_EVDO_B:
                    return "EVDO_B";
                case TelephonyManager.NETWORK_TYPE_1xRTT:
                    return "1xRTT";
                case TelephonyManager.NETWORK_TYPE_LTE:
                    return "LTE";
                case TelephonyManager.NETWORK_TYPE_EHRPD:
                    return "eHRPD";
                case TelephonyManager.NETWORK_TYPE_IDEN:
                    return "iDen";
                case TelephonyManager.NETWORK_TYPE_HSPAP:
                    return "HSPA+";
                case TelephonyManager.NETWORK_TYPE_GSM:
                    return "GSM";
                case TelephonyManager.NETWORK_TYPE_TD_SCDMA:
                    return "TD-SCDMA";
                case TelephonyManager.NETWORK_TYPE_IWLAN:
                    return "IWLAN";
                default:
                    return "UNKNOWN";
            }
        }
        return "UNKNOWN";
    }

    public static String getIPAddress(Context context) {
        NetworkInfo info = ((ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();
        if (info != null && info.isConnected()) {
            if (info.getType() == ConnectivityManager.TYPE_MOBILE) {//当前使用2G/3G/4G网络
                try {
                    for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                        NetworkInterface intf = en.nextElement();
                        for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                            InetAddress inetAddress = enumIpAddr.nextElement();
                            if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                                return inetAddress.getHostAddress();
                            }
                        }
                    }
                } catch (SocketException e) {
                    LogUtils.e(TAG, "", e);
                    return "";
                }

            } else if (info.getType() == ConnectivityManager.TYPE_WIFI) {//当前使用无线网络
                WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                return intIP2StringIP(wifiInfo.getIpAddress());
            }
        }
        return "";
    }

    /**
     * 将得到的int类型的IP转换为String类型
     */
    @SuppressLint("DefaultLocale")
    public static String intIP2StringIP(int ipAddress) {
        return String.format("%d.%d.%d.%d", (ipAddress & 0xff), (ipAddress >> 8 & 0xff), (ipAddress >> 16 & 0xff), (ipAddress >> 24 & 0xff));
    }

    private static void setMqttInfo(DeviceDetails deviceDetails) {
        Provider currentUseProvider = MQTTUtils.getCurrentUseProvider();
        String ip = "";
        if (currentUseProvider != null) {
            ip = Utils.formatToIp(currentUseProvider.getMqttIp());
        }
        // 如果不存在则使用默认值
        ip = TextUtils.isEmpty(ip) ? Utils.getString(R.string.public_mqtt_ip) : ip;
        String port = currentUseProvider == null ? "9007" : String.valueOf(currentUseProvider.getMqttPort());
        int connectTimeout = currentUseProvider == null ? 20 : currentUseProvider.getMqttTimeout();
        int qos = currentUseProvider == null ? 1 : currentUseProvider.getQos();
        deviceDetails.setMqttIp(ip);
        deviceDetails.setMqttPort(port);
        deviceDetails.setMqttQos(String.valueOf(qos));
        deviceDetails.setMqttConnectTimeout(String.valueOf(connectTimeout));
    }

}
