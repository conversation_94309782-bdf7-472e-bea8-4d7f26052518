package com.pax.pay.utils;

import android.util.SparseIntArray;

import com.pax.commonlib.utils.LogUtils;
import com.pax.pay.trans.EMIProcessID;
import com.pax.sbipay.R;

public class EMIProcessIDUtils {
    private static final SparseIntArray messageMap = new SparseIntArray();

    static {
        messageMap.put(EMIProcessID.NO_PIN_ENTERED, R.string.no_pin_entered);
        messageMap.put(EMIProcessID.TRANSACTION_TIME_OUT, R.string.transaction_time_out);
        messageMap.put(EMIProcessID.TRANSACTION_SWITCH_DECLINED, R.string.transaction_switch_declined);
        messageMap.put(EMIProcessID.VOID, R.string.emi_void);
        messageMap.put(EMIProcessID.TRANSACTION_FAILED, R.string.transaction_failed);
        messageMap.put(EMIProcessID.NOTIFY_TRANSACTION_TIMEOUT, R.string.notify_transaction_timeout);
        messageMap.put(EMIProcessID.AUTH_REVERSAL_FAILED, R.string.auth_reversal_failed);
        messageMap.put(EMIProcessID.AUTH_REVERSAL_SUCCESS, R.string.auth_reversal_success);
        messageMap.put(EMIProcessID.NOTIFY_TRANSACTION_FAILED, R.string.notify_transaction_failed);
    }

    private EMIProcessIDUtils() {
    }

    public static String getMessage(int ret) {
        String message;
        int resourceId = messageMap.get(ret, -1);
        if (resourceId == -1) {
            try {
                message = Utils.getString(ret);
            } catch (Exception e) {
                LogUtils.e("getMessage", "", e);
                message = Utils.getString(R.string.transaction_failed) + "[" + ret + "]";
            }
        } else {
            message = Utils.getString(resourceId);
        }
        return message;
    }
}
