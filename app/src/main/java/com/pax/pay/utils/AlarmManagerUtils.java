/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20210723  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import com.pax.commonlib.LogUtils;
import java.util.ArrayList;
import java.util.List;

public class AlarmManagerUtils {
    public static final String TAG = "AlarmManagerUtils";

    private Context context;
    private AlarmManager alarmManager;
    private List<PendingIntent> list = new ArrayList<>();

    private static AlarmManagerUtils instance;

    private AlarmManagerUtils(Context context) {
        this.context = context;
        alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
    }

    public static synchronized AlarmManagerUtils getInstance(Context context) {
        if (instance == null) {
            instance = new AlarmManagerUtils(context);
        }
        return instance;
    }

    /**
     * Schedule a repeating alarm
     */
    public void setRepeating(int requestCode, long triggerAtMillis, long intervalMillis,
            Class<?> cls, String action) {
        LogUtils.e(TAG, "=======setRepeating=========");
        Intent intent = new Intent(context, cls);
        intent.setAction(action);
        PendingIntent pendingIntent =
                PendingIntent.getService(context, requestCode, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        list.add(pendingIntent);
        alarmManager.setRepeating(AlarmManager.RTC_WAKEUP, triggerAtMillis, intervalMillis, pendingIntent);
    }

    /**
     * Schedule an alarm,just only once
     */
    public void set(int requestCode, long triggerAtMillis, Class<?> cls, String action) {
        LogUtils.e(TAG, "=======set=========");
        Intent intent = new Intent(context, cls);
        intent.setAction(action);
        PendingIntent pendingIntent =
                PendingIntent.getService(context, requestCode, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        list.add(pendingIntent);
        alarmManager.set(AlarmManager.RTC_WAKEUP, triggerAtMillis, pendingIntent);
    }

    /**
     * Remove all alarms with matching {@link PendingIntent}.
     */
    public void alarCancel() {
        if (null == list) {
            list = new ArrayList<>();
        }
        if (list.isEmpty()) {
            return;
        }

        for (PendingIntent item : list) {
            alarmManager.cancel(item);
        }

        list.clear();
    }
}
