package com.pax.pay.utils;

/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2019-01-03
 * Module Author: wangyq
 * Description:
 *
 * ============================================================================
 */


import android.util.Log;

import com.pax.dal.entity.EPedDesMode;
import com.pax.dal.exceptions.PedDevException;
import com.pax.device.Device;
import com.pax.eemv.utils.Tools;

public class DesUtils {
    private static final String TAG = "DesUtils";

    private DesUtils() {
    }

    public static byte[] enTdesCBC(byte index, byte[] initVector, byte[] dataIn) {

        if (initVector == null || dataIn == null) {
            return new byte[0];
        }

        byte[] encryptedData = new byte[dataIn.length];
        byte[] cipherBlock = new byte[8];
        byte[] plianBlock = new byte[8];
        byte[] xorBuff;
        int blockNum = dataIn.length / 8;

        for (int i = 0; i < blockNum; i++) {
            Log.i(TAG, "-------------------" + i + "-------------------");
            System.arraycopy(dataIn, i * 8, plianBlock, 0, 8);
            Log.i(TAG, "plianBlock: " + Tools.bcd2Str(plianBlock));
            if (i == 0) {
                xorBuff = xor(initVector, plianBlock);
            } else {
                xorBuff = xor(plianBlock, cipherBlock);
            }

            Log.i(TAG, "xorBuff: " + Tools.bcd2Str(xorBuff));
            try {
                cipherBlock = Device.calcDes(index, xorBuff, EPedDesMode.ENCRYPT);
                Log.i(TAG, "cipherBlock: " + Tools.bcd2Str(cipherBlock));
            } catch (PedDevException e) {
                Log.e(TAG, "", e);
            }
            System.arraycopy(cipherBlock, 0, encryptedData, i * 8, 8);
        }

        return encryptedData;
    }

    public static byte[] deTdesCBC(byte index, byte[] initVector, byte[] dataIn) {
        if (initVector == null || dataIn == null) {
            return new byte[0];
        }

        byte[] decryptedData = new byte[dataIn.length];
        byte[] cipherBlock = new byte[8];
        byte[] plianBlock = new byte[8];
        byte[] xorBuff;
        int blockNum = dataIn.length / 8;

        for (int i = 0; i < blockNum; i++) {
            Log.i(TAG, "-------------------" + i + "-------------------");
            System.arraycopy(dataIn, i * 8, cipherBlock, 0, 8);
            Log.i(TAG, "cipherBlock: " + Tools.bcd2Str(cipherBlock));

            try {
                plianBlock = Device.calcDes(index, cipherBlock, EPedDesMode.DECRYPT);
                Log.i(TAG, "plianBlock: " + Tools.bcd2Str(plianBlock));
            } catch (PedDevException e) {
                Log.e(TAG, "", e);
            }
            if (i == 0) {
                xorBuff = xor(initVector, plianBlock);
            } else {
                System.arraycopy(dataIn, (i - 1) * 8, cipherBlock, 0, 8);
                xorBuff = xor(plianBlock, cipherBlock);
            }

            Log.i(TAG, "xorBuff: " + Tools.bcd2Str(xorBuff));

            System.arraycopy(xorBuff, 0, decryptedData, i * 8, 8);
        }

        return decryptedData;
    }

    private static byte[] xor(byte[] comp1, byte[] comp2) {

        if (comp1 == null || comp2 == null) {
            return new byte[0];
        }

        byte[] value = new byte[comp1.length];

        System.arraycopy(comp1, 0, value, 0, comp1.length);

        for (int i = 0; i < comp1.length; i++) {
            value[i] ^= comp2[i];
        }

        return value;
    }


}

