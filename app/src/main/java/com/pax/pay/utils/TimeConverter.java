/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.pay.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Locale;

public class TimeConverter {

    private TimeConverter() {
        //do nothing
    }

    /**
     * @param formattedTime
     * @param oldPattern
     * @param newPattern
     * @return
     */
    public static String convert(String formattedTime, final String oldPattern, final String newPattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(oldPattern, Locale.US);
        java.util.Date date;
        try {
            date = sdf.parse(formattedTime);
        } catch (ParseException e) {
            return formattedTime;
        }
        sdf = new SimpleDateFormat(newPattern, Locale.US);
        return sdf.format(date);
    }
}
