/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-26
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class TimeConverter {

    private TimeConverter() {
        //do nothing
    }

    /**
     * @param formattedTime
     * @param oldPattern
     * @param newPattern
     * @return
     */
    public static String convert(String formattedTime, final String oldPattern, final String newPattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(oldPattern, Locale.US);
        java.util.Date date;
        try {
            date = sdf.parse(formattedTime);
        } catch (ParseException e) {
            return formattedTime;
        }
        sdf = new SimpleDateFormat(newPattern, Locale.US);
        return sdf.format(date);
    }

    /**
     * format time to ecr time
     * @param oldTime the time to be format
     * @param isSettlementTime whether settlementTime
     * @return the string
     */
    public static String timeFormatEcr(String oldTime,boolean isSettlementTime){
        //partyking返回给edc的时间格式
        DateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //需要返回给ecr模拟器的时间格式
        DateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            //格式化传入的时间
            Date date = inputFormat.parse(oldTime);
            //如果是结算的时间，则只取年月日来进行格式化
            if(isSettlementTime){
                return outputFormat.format(date).substring(0,8);
            }
            //将时间格式化成ecr需要的时间
            return outputFormat.format(date);
        }catch (ParseException e){
            e.printStackTrace();
        }
        //格式化失败则传入null
        return null;
    }
}
