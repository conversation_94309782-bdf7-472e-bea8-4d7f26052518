/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * Description: // Detail description about the function of this module,
 *  *             // interfaces with the other modules, and dependencies.
 *  * Revision History:
 *  * Date                  Author                 Action
 *  * 20190715         yura            Create/Add/Modify/Delete
 *  * ===========================================================================================
 *
 */

package com.pax.pay.utils;

import android.text.TextUtils;

import com.pax.commonlib.utils.LogUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.lang.System.arraycopy;

@SuppressWarnings("SameParameterValue")
public class TlvParseUtil {

    /**
     * PBOC基本信息数据采用TLV（tag-length-value）的表示方式，即每项由tag标签(T)，长度(L)和取值(V)构成。
     * 标签：　　tag标签的属性为bit，由16进制表示，占1～2个字节长度。
     * 若tag标签的第一个字节（注：字节排序方向为从左往右数(b8~b0)，第一个字节即为最左边的字节。bit排序规则同理。）的后四个bit为“1111”，
     * 则说明该tag占两个字节，例如“9F33”；否则占一个字节，例如“95”。
     * b8和b7两位标识tag所属类别. 这个可以暂时不用理.
     * b6决定当前的TLV数据是一个单一的数据和复合结构的数据.
     * 复合的TLV是指value域里也包含一个或多个TLV, 类似嵌套的编码格式.
     * b5~b1如果全为1，则说明这个tag下面还有一个子字节. 占两个字节, 否则tag占一个字节。
     * <p>
     * 长度：　　长度（即L本身）的属性也为bit，占1～3个字节长度。具体编码规则如下：
     * 　a)当L字段最左边字节的最左bit位（即bit8）为0，表示该L字段占一个字节，它的后续7个bit位（即bit7～bit1）表示取值的长度，采用二进制数表示取值长度的十进制数。
     * 　 b)当L字段最左边字节的最左bit位（即bit8）为1，表示该L字段不止占一个字节，那么它到底占几个字节由该最左字节的后续7个bit位（即bit7～bit1）的十进制取值表示。例如，若最左字节为10000010，表示L字段除该字节外，后面还有两个字节。其后续字节的十进制取值表示取值的长度。
     */
    protected static final String TAG = "TlvParseUtil";
    protected static final String DATAFORMAT = "%02X%02X";

    protected TlvBody tagBody;
    protected List<TlvBody> tags;

    public TlvParseUtil() {
        tags = new ArrayList<>();
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString().toUpperCase();
    }

    public String getTags() {
        if (tags == null || tags.isEmpty()) {
            return "";
        }

        StringBuilder sBuilder = new StringBuilder();
        for (TlvBody tag : tags) {
            sBuilder.append(tag.toString());
        }
        return sBuilder.toString();
    }

    public Map<Integer, String> getTagValueMap() {
        if (tags == null || tags.isEmpty()) {
            return null;
        }
        Map<Integer, String> tagValueMap = new HashMap<>();
        for (TlvBody tag : tags) {
            tagValueMap.put(Integer.parseInt(tag.getTag(), 16), tag.getValue());
        }
        return tagValueMap;
    }

    protected int pbocTlvAns(byte[] rxbuf, int rxsize) {
        int cout = 0;
        int n = 0;
        int num = 0;
        //===========================================TLV解析
        while (cout < rxsize) {
            if ((((rxbuf[cout] & 0xff) & 0x1F) == 0x1F)) {
                //2字节Tag
                if (((rxbuf[cout] & 0xff) == 0xBF) && ((rxbuf[cout + 1] & 0xff) == 0x0C)) {
                    //=================================//模板
                    //模板里的数据还需要进一步解析
                    num = 0;
                } else {
                    //=================================//标签
                    num = 1;
                }
                tagBody = new TlvBody();
                tagBody.setTag(String.format(DATAFORMAT, (rxbuf[cout] & 0xff), (rxbuf[cout + 1] & 0xff)));
                cout += 2;
                if (((rxbuf[cout] & 0xff) & 0x80) == 0x80) {
                    //=============================//长度为非1字节
                    switch ((rxbuf[cout] & 0xff) & 0x7F) {
                        //目前只处理最多2字节
                        case 1:
                            n = (rxbuf[cout + 1] & 0xff);
                            tagBody.setLength(String.format(DATAFORMAT, (rxbuf[cout] & 0xff), (rxbuf[cout + 1] & 0xff)));
                            cout += 2;
                            break;
                        case 2:
                            n = (((rxbuf[cout + 1] & 0xff) << 8) & 0xFF00) | (rxbuf[cout + 2] & 0xff);
                            tagBody.setLength(String.format("%02X%02X%02X", rxbuf[cout] & 0xff, rxbuf[cout + 1] & 0xff, rxbuf[cout + 2] & 0xff));
                            cout += 3;
                            break;
                        default:
                            LogUtils.e(TAG, "L解析错误");
                            return 40;
                    }
                } else {
                    //=============================//长度为1字节
                    n = (rxbuf[cout] & 0xff);
                    tagBody.setLength(String.format("%02X", n));
                    cout += 1;
                }
                byte[] buffer = new byte[n];
                arraycopy(rxbuf, cout, buffer, 0, n);
                tagBody.setValue(bytesToHexString(buffer));
                tags.add(tagBody);
                if (num == 0) {
                    //=================================//模板
                    //模板里的数据还需要进一步解析
                } else {
                    //=================================//标签
                    cout += n;
                }
            } else {
                //1字节T
                if (((rxbuf[cout] & 0xff) >= 0x61) && ((rxbuf[cout] & 0xff) <= 0x7F)) {//=================================//模板
                    //模板里的数据还需要进一步解析
                    num = 0;
                } else if (((rxbuf[cout] & 0xff) == 0x80) || ((rxbuf[cout] & 0xff) == 0xA5)) {
                    //=================================//模板
                    //模板里的数据还需要进一步解析
                    num = 0;
                } else {
                    //=================================//标签
                    num = 1;
                }
                tagBody = new TlvBody();
                tagBody.setTag(String.format("%02X", (rxbuf[cout] & 0xff)));
                cout++;
                if (((rxbuf[cout] & 0xff) & 0x80) == 0x80) {
                    //=============================//长度为非1字节
                    switch ((rxbuf[cout] & 0xff) & 0x7F) {
                        //目前只处理最多2字节
                        case 1:
                            n = (rxbuf[cout + 1] & 0xff);
                            tagBody.setLength(String.format(DATAFORMAT, (rxbuf[cout] & 0xff), (rxbuf[cout + 1] & 0xff)));
                            cout += 2;
                            break;
                        case 2:
                            n = ((rxbuf[cout + 1] << 8) & 0xFF00) | (rxbuf[cout + 2] & 0xff);
                            tagBody.setLength(String.format("%02X%02X%02X", (rxbuf[cout] & 0xff), (rxbuf[cout + 1] & 0xff), (rxbuf[cout + 2] & 0xff)));
                            cout += 3;
                            break;
                        default:
                            LogUtils.e(TAG, "L解析错误");
                            return 40;
                    }
                } else {
                    //=============================//长度为1字节
                    n = (rxbuf[cout] & 0xff);
                    tagBody.setLength(String.format("%02X", n));
                    cout += 1;
                }
                byte[] buffer = new byte[n];
                arraycopy(rxbuf, cout, buffer, 0, n);
                tagBody.setValue(bytesToHexString(buffer));
                tags.add(tagBody);
                if (num == 0) {
                    //=================================//模板
                    //模板里的数据还需要进一步解析
                } else {
                    //=================================//标签
                    cout += n;
                }
            }
        }
        return 0;
    }

    public static class TlvBody {
        private String tag;
        private String length;
        private String value;

        @Override
        public String toString() {
            return getTag() + "|" +
                    getLength() + '|' + (TextUtils.isEmpty(getValue()) ? "" : getValue()) + "\n";
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getLength() {
            return length;
        }

        public void setLength(String length) {
            this.length = length;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

}
