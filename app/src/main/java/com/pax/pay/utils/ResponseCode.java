/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.utils;

import com.pax.pay.app.FinancialApplication;
import com.pax.sbipay.R;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class ResponseCode implements Serializable {
    private static final long serialVersionUID = 1L;
    private static ResponseCode rcCode;
    private static Map<String, Integer> responses = new HashMap<>();

    static {
        responses.put("00", R.string.response_00);
        responses.put("01", R.string.response_01);
        responses.put("02", R.string.response_02);
        responses.put("03", R.string.response_03);
        responses.put("04", R.string.response_04);
        responses.put("05", R.string.response_05);
        responses.put("06", R.string.response_06);

        responses.put("12", R.string.response_12);
        responses.put("13", R.string.response_13);
        responses.put("14", R.string.response_14);
        responses.put("15", R.string.response_15);

        responses.put("30", R.string.response_30);
        responses.put("33", R.string.response_33);
        responses.put("34", R.string.response_34);
        responses.put("36", R.string.response_36);
        responses.put("38", R.string.response_38);
        responses.put("39", R.string.response_39);

        responses.put("40", R.string.response_40);
        responses.put("41", R.string.response_41);
        responses.put("42", R.string.response_42);
        responses.put("43", R.string.response_43);

        responses.put("51", R.string.response_51);
        responses.put("52", R.string.response_52);
        responses.put("53", R.string.response_53);
        responses.put("54", R.string.response_54);
        responses.put("55", R.string.response_55);
        responses.put("57", R.string.response_57);
        responses.put("58", R.string.response_58);
        responses.put("59", R.string.response_59);

        responses.put("61", R.string.response_61);
        responses.put("62", R.string.response_62);
        responses.put("63", R.string.response_63);
        responses.put("65", R.string.response_65);

        responses.put("75", R.string.response_75);

        responses.put("81", R.string.response_81);
        responses.put("89", R.string.response_89);

        responses.put("91", R.string.response_91);
        responses.put("92", R.string.response_92);
        responses.put("95", R.string.response_95);
        responses.put("96", R.string.response_96);
        responses.put("98", R.string.response_98);

        responses.put("L1", R.string.response_L1);
        responses.put("L2", R.string.response_L2);
        responses.put("L3", R.string.response_L3);

    }

    private String code;
    private String message;
    private HashMap<String, ResponseCode> map;

    private ResponseCode() {
        if (map == null)
            map = new HashMap<>();
    }

    private ResponseCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ResponseCode getInstance() {
        if (rcCode == null) {
            rcCode = new ResponseCode();
        }
        return rcCode;
    }

    private static String findResponse(final String code) {
        Integer id = responses.get(code);
        if (id == null) {
            id = R.string.response_unknown;
        }
        return FinancialApplication.getApp().getString(id);
    }

    /**
     * init方法必须调用， 一般放在应用启动的时候
     */
    public void init() {
        for (String i : responses.keySet()) {
            String msg = findResponse(i);
            ResponseCode rspCode = new ResponseCode(i, msg);
            map.put(i, rspCode);
        }
    }

    public ResponseCode parse(String code) {
        ResponseCode rc = map.get(code);
        if (rc == null)
            return new ResponseCode(code, Utils.getString(R.string.err_undefine_info));
        return rc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return this.getCode() + "\n" + this.getMessage();
    }
}
