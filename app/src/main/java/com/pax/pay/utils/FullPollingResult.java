package com.pax.pay.utils;


public class FullPollingResult {
    private FullPollingResult.EOperationType operationType;
    private FullEReaderType readerType;
    private String track1;
    private String track2;
    private String track3;
    private byte[] serialInfo;

    public FullPollingResult() {
        this.operationType = FullPollingResult.EOperationType.DEFAULT;
        this.readerType = FullEReaderType.DEFAULT;
        this.track1 = "";
        this.track2 = "";
        this.track3 = "";
        this.serialInfo = new byte[0];
    }

    public FullPollingResult.EOperationType getOperationType() {
        return this.operationType;
    }

    public void setOperationType(FullPollingResult.EOperationType operationType) {
        this.operationType = operationType;
    }

    public FullEReaderType getReaderType() {
        return this.readerType;
    }

    public void setReaderType(FullEReaderType readerType) {
        this.readerType = readerType;
    }

    public String getTrack1() {
        return this.track1;
    }

    public void setTrack1(String track1) {
        this.track1 = track1;
    }

    public String getTrack2() {
        return this.track2;
    }

    public void setTrack2(String track2) {
        this.track2 = track2;
    }

    public String getTrack3() {
        return this.track3;
    }

    public void setTrack3(String track3) {
        this.track3 = track3;
    }

    public byte[] getSerialInfo() {
        return this.serialInfo;
    }

    public void setSerialInfo(byte[] serialInfo) {
        this.serialInfo = serialInfo;
    }

    public enum EOperationType {
        OK,
        TIMEOUT,
        CANCEL,
        /**
         * @deprecated
         */
        PAUSE,
        DEFAULT;

        EOperationType() {
        }
    }
}
