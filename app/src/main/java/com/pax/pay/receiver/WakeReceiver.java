package com.pax.pay.receiver;

import static android.os.PowerManager.PARTIAL_WAKE_LOCK;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.PowerManager;

import com.pax.pay.app.FinancialApplication;

public class WakeReceiver extends BroadcastReceiver {
    private static final String TAG = "WakeReceiver:";
    PowerManager powerManager = (PowerManager) FinancialApplication.getApp().getSystemService(Context.POWER_SERVICE);
    PowerManager.WakeLock wakeLock;
    private PendingIntent pendingIntent;
    private AlarmManager alarmManager;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
        wakeLock = powerManager.newWakeLock(PARTIAL_WAKE_LOCK, TAG);
        wakeLock.acquire(5 * 1000);
        pendingIntent = PendingIntent.getBroadcast(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, 10 * 1000, pendingIntent);
    }
}
