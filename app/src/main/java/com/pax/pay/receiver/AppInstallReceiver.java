/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-8-27
 * Module Author: wangyq
 * Description:
 *
 * ============================================================================
 */

package com.pax.pay.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.pax.pay.utils.Utils;

public class AppInstallReceiver extends BroadcastReceiver {
    private final static String TAG = "AppInstallReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String packageName = intent.getData().getSchemeSpecificPart();
        if (intent.getAction().equals(Intent.ACTION_PACKAGE_REPLACED) && "com.pax.edc.hase".equals(packageName)) {
            Log.d(TAG, "replace successfully and ready to restart " + packageName );
            Utils.restart();
        }
    }
}
