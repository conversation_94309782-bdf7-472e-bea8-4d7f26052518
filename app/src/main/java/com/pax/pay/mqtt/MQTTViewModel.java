package com.pax.pay.mqtt;

import android.os.Handler;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import android.os.Looper;


public class MQTTViewModel extends ViewModel {
    private final MutableLiveData<ConnectionState> connectionState = new MutableLiveData<>();

    public void updateState(ConnectionState state) {
        new Handler(Looper.getMainLooper()).post(() -> connectionState.setValue(state));
    }

    public LiveData<ConnectionState> getConnectionState() {
        return connectionState;
    }
}
