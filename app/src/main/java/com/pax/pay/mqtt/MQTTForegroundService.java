package com.pax.pay.mqtt;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Binder;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import java.nio.charset.StandardCharsets;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pax.commonlib.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.data.entity.Provider;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.eventbus.MQTTMessageEvent;
import com.pax.pay.MainActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.BaseTrans;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.TickTimer;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.eclipse.paho.mqttv5.client.IMqttToken;
import org.eclipse.paho.mqttv5.client.MqttActionListener;
import org.eclipse.paho.mqttv5.client.MqttAsyncClient;
import org.eclipse.paho.mqttv5.client.MqttCallback;
import org.eclipse.paho.mqttv5.client.MqttConnectionOptions;
import org.eclipse.paho.mqttv5.client.MqttDisconnectResponse;
import org.eclipse.paho.mqttv5.client.persist.MemoryPersistence;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.eclipse.paho.mqttv5.common.MqttSubscription;
import org.eclipse.paho.mqttv5.common.packet.MqttProperties;
import org.greenrobot.eventbus.EventBus;

import java.lang.ref.WeakReference;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class MQTTForegroundService extends Service {
    private static final String TAG = "MQTTForegroundService";
    private static final int NOTIFICATION_ID = 1;
    private static final String CHANNEL_ID = "MQTT_Service_Channel";
    public static final String ACTION_UPDATE_MQTT = "com.pax.sbi.UPDATE_MQTT_SETTINGS";

    private MqttAsyncClient mqttClient;
    private final MqttConnectionOptions options = new MqttConnectionOptions();
    private String serverUri;
    private String topic;
    private int qos;
    private int connectTimeout;
    private final IBinder binder = new LocalBinder();
    // 定义静态单例的 ObjectMapper
    public static final ObjectMapper MAPPER = new ObjectMapper();
    private WeakReference<MQTTViewModel> viewModelRef;
    private MQTTMessageCallback messageCallback;
    private final ExecutorService ioExecutor = Executors.newFixedThreadPool(2);
    //如果是第一次启动服务，这时候执行start service的时候，执行到onStartCommand不需要更新连接的配置
    private final AtomicBoolean isUpdatingConnection = new AtomicBoolean(false);

    static {
        // 配置 ObjectMapper（配置后不可更改，保证线程安全）
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public class LocalBinder extends Binder {
        public MQTTForegroundService getService() {
            return MQTTForegroundService.this;
        }
    }

    public interface MQTTMessageCallback {
        // 当收到 MQTT 消息时调用可以拿到接收到的消息
        void onMessageReceived(String message);

        void onMessageFailed(String error);
    }

    public interface PushCallback {
        void onPushFailure(String error);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        startForeground(NOTIFICATION_ID, buildNotification());
        initializeMQTT();
        LocalBroadcastManager.getInstance(this).registerReceiver(
                updateSettingsReceiver, new IntentFilter(ACTION_UPDATE_MQTT));
    }

    public void setViewModel(MQTTViewModel viewModel) {
        this.viewModelRef = new WeakReference<>(viewModel);
        // 立即更新ViewModel的状态，确保UI显示正确的连接状态
        updateViewModelWithCurrentState();
    }

    /**
     * 检查当前MQTT客户端的实际连接状态并更新ViewModel
     */
    private void updateViewModelWithCurrentState() {
        if (mqttClient != null) {
            if (mqttClient.isConnected()) {
                updateState(ConnectionState.connected());
            } else {
                updateState(ConnectionState.disconnected());
            }
        } else {
            updateState(ConnectionState.error("MQTT client not initialized"));
        }
    }

    private void updateState(ConnectionState state) {
        if (viewModelRef != null && viewModelRef.get() != null) {
            viewModelRef.get().updateState(state);
            Log.i(TAG, "MQTT Client ConnectionState Change: " + state.getStatus().name());
        } else {
            LogUtils.d(TAG, "ViewModel reference is null");
        }
    }

    private void initMQTTParameter() {
        Provider currentUseProvider = MQTTUtils.getCurrentUseProvider();
        String ip = "";
        if (currentUseProvider != null) {
            Future<String> future = getMqttIpFuture(currentUseProvider.getMqttIp());
            try {
                ip = future.get();
            } catch (ExecutionException e) {
                LogUtils.e(TAG, "", e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态。
                LogUtils.d(TAG, "Thread interrupted");
            }
        }
        // 如果不存在则使用默认值
        ip = TextUtils.isEmpty(ip) ? Utils.getString(R.string.public_mqtt_ip) : ip;
        String port = currentUseProvider == null ? "9007" : String.valueOf(currentUseProvider.getMqttPort());
        connectTimeout = currentUseProvider == null ? 20 : currentUseProvider.getMqttTimeout();
        serverUri = "tcp://" + ip + ":" + port;
        Map<ETermInfoKey, String> termInfo = FinancialApplication.getDal().getSys().getTermInfo();
        topic = termInfo.get(ETermInfoKey.SN) + "_SQR";
        qos = currentUseProvider == null ? 1 : currentUseProvider.getQos();
        Log.i(TAG, "the server uri: " + serverUri + " the qos is: " + qos + " the connect timeout is: " + connectTimeout);
    }

    private Future<String> getMqttIpFuture(String ipString) {
        Callable<String> callable = () -> Utils.formatToIp(ipString);
        return ioExecutor.submit(callable);
    }

    private void initializeMQTT() {
        initMQTTParameter();
        configureMqttConnectionOptions();
        try {
            mqttClient = new MqttAsyncClient(serverUri, createClientId(), new MemoryPersistence());
            setupMqttCallbacks();
            connectToBroker();
        } catch (MqttException e) {
            Log.e(TAG, "MQTT Initialization Error: ", e);
            updateState(ConnectionState.error(e.getMessage()));
        }
    }

    private void configureMqttConnectionOptions() {
        //不使用持久会话
        options.setCleanStart(true);
        options.setAutomaticReconnect(true);
        //初始连接建立阶段包含TCP连接以及MQTT协议握手的总时间不超过connectTimeout
        options.setConnectionTimeout(connectTimeout > 0 ? connectTimeout : 20);
        //连接断开后重新连接的等待时间指数退避 1s 2s ... max is 30s
        options.setAutomaticReconnectDelay(1, 30);
        Log.d(TAG, "MQTT Connection Options configured: Timeout=" + options.getConnectionTimeout() +
                ", KeepAlive=" + options.getKeepAliveInterval() + ", AutoReconnect=true");
    }

    private String createClientId() {
        Map<ETermInfoKey, String> map = FinancialApplication.getDal().getSys().getTermInfo();
        return map.get(ETermInfoKey.SN) + UUID.randomUUID().toString();
    }

    private void setupMqttCallbacks() {
        mqttClient.setCallback(new MqttCallback() {
            @Override
            public void disconnected(MqttDisconnectResponse disconnectResponse) {
                if (viewModelRef != null) {
                    updateState(ConnectionState.disconnected());
                }
                Log.i(TAG, "MQTTDisconnected: " + disconnectResponse);
                if (messageCallback != null) {
                    messageCallback.onMessageFailed("Disconnected");
                }
            }

            @Override
            public void mqttErrorOccurred(MqttException exception) {
                updateState(ConnectionState.error(exception.getMessage()));
                Log.e(TAG, "MQTT Error Occurred: ", exception);
                if (messageCallback != null) {
                    messageCallback.onMessageFailed("MQTT Error");
                }
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) {
                if (FinancialApplication.isIsExitApp() || FinancialApplication.isUpdating()) {
                    Log.i(TAG, "App is exiting or updating, ignoring MQTT message");
                    return;
                }
                handleMessageArrived(message);
            }

            @Override
            public void deliveryComplete(IMqttToken token) {
                // nothing to do
            }

            @Override
            public void connectComplete(boolean reconnect, String serverURI) {
                updateState(ConnectionState.connected());
                subscribeToTopic();
                Log.i(TAG, "MQTT Connected" + (reconnect ? " (reconnect)" : ""));
            }

            @Override
            public void authPacketArrived(int reasonCode, MqttProperties properties) {
                // nothing to do
            }
        });
    }

    private void handleMessageArrived(MqttMessage message) {
        String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
        LogUtils.d(TAG, "Message arrived: " + payload);
        Utils.wakeupScreen(TickTimer.DEFAULT_TIMEOUT);
        if (TextUtils.isEmpty(payload)) {
            showToast(Utils.getString(R.string.mqtt_data_empty));
            return;
        }
        if (messageCallback != null) {
            messageCallback.onMessageReceived(payload);
            return;
        }
        // 解析和处理JSON数据
        processMqttPayload(payload);
    }

    private void showToast(String message) {
        FinancialApplication.getApp().runOnUiThread(() -> {
            // 检查设备是否空闲
            if (BaseTrans.isTransRunning() || FinancialApplication.isIsEnteringSaleAmount()
                    || !(ActivityStack.getInstance().top() instanceof MainActivity)) {
                Log.i(TAG, "Normal Trans is running or not in idle, will not show mqtt error message");
                return;
            }
            ToastUtils.showMessage(message);
        });
    }

    private void processMqttPayload(String payload) {
        try {
            JsonNode root = MAPPER.readTree(payload);

            // 验证响应码
            if (!validateResponseCode(root)) {
                return;
            }

            // 解析SQRData数据
            SQRData data = parseSQRData(root);
            if (data == null) {
                return;
            }

            // 进行业务验证
            if (!validateBusinessData(data)) {
                return;
            }

            // 保存数据并发送事件
            saveDataAndNotify(data);

        } catch (JsonProcessingException e) {
            LogUtils.e(TAG, "MQTT Message Parsing Fail: " + e.getMessage(), e);
        } catch (Exception e) {
            LogUtils.e(TAG, "Unexpected error processing MQTT message: " + e.getMessage(), e);
        }
    }

    private SQRData parseSQRData(JsonNode root) {
        try {
            SQRData data = MAPPER.treeToValue(root, SQRData.class);
            Log.i(TAG, "Transaction RRN: " + data.getRrn());

            // 提取嵌套字段
            JsonNode rspDetails = root.path("RspDetails");
            if (!rspDetails.isMissingNode()) {
                String invoiceNo = rspDetails.path("InvoiceNr").asText();
                if (!TextUtils.isEmpty(invoiceNo)) {
                    data.setInvoiceNr(Component.getPaddedNumber(Long.parseLong(invoiceNo), 6));
                }

                String stan = rspDetails.path("Stan").asText();
                if (!TextUtils.isEmpty(stan)) {
                    data.setStan(stan);
                }
            }

            return data;
        } catch (JsonProcessingException e) {
            LogUtils.e(TAG, "Error parsing SQRData: " + e.getMessage(), e);
            return null;
        }
    }

    private boolean validateBusinessData(SQRData data) {
        // 验证TID和MID
        if (!MQTTUtils.validateTIDAndMID(data.getTid(), data.getMid())) {
            showToast(Utils.getString(R.string.tid_and_mid_not_match));
            return false;
        }

        // 验证RRN
        String rrn = data.getRrn();
        if (rrn == null || rrn.length() != 12) {
            showToast("Invalid RRN");
            return false;
        }

        // 验证金额
        if (!MQTTUtils.validAmount(data.getAmount())) {
            showToast("MQTT Transaction Amount is Invalid");
            return false;
        }

        return true;
    }

    private void saveDataAndNotify(SQRData data) {
        // 考虑将数据库操作放在异步线程中
        ioExecutor.execute(() -> {
            boolean isSuccess = GreendaoHelper.getSQRDataHelper().insertSQRData(data);
            if (isSuccess) {
                // 在主线程中发送事件
                FinancialApplication.getApp().runOnUiThread(() ->
                        EventBus.getDefault().post(new MQTTMessageEvent(data))
                );
            } else {
                LogUtils.d(TAG, "MQTT Message Insert Error");
            }
        });
    }

    private boolean validateResponseCode(JsonNode root) {
        if (!root.has("RspCode")) {
            LogUtils.w(TAG, "Missing RspCode in MQTT message");
            showToast(Utils.getString(R.string.mqtt_data_empty));
            return false;
        }

        String responseCode = root.get("RspCode").asText();
        if (!"00".equals(responseCode)) {
            Log.i(TAG, "MQTT Message Fail the response code is: " + responseCode);
            showToast(Utils.getString(R.string.mqtt_transaction_fail));
            return false;
        }

        return true;
    }

    private void connectToBroker() {
        try {
            if (MQTTUtils.isConnectIsNormal() &&
                    SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
                mqttClient.connect(options);
            } else {
                Log.e(TAG, "MQTT connection is not normal or MQTT is disabled");
                updateState(ConnectionState.error("MQTT connection is not normal or MQTT is disabled"));
            }
        } catch (MqttException e) {
            Log.e(TAG, "Connect Error: ", e);
            updateState(ConnectionState.error(e.getMessage()));
        }
    }

    private void subscribeToTopic() {
        try {
            MqttSubscription subscription = new MqttSubscription(topic, qos);
            //不接收自己发布的消息
            subscription.setNoLocal(true);
            mqttClient.subscribe(subscription).setActionCallback(new MqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    Log.i(TAG, "Subscribed to " + topic);
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    Log.e(TAG, "Subscribe failed: ", exception);
                }
            });
        } catch (MqttException e) {
            Log.e(TAG, "Subscribe Error: ", e);
        }
    }

    public void publish(String message, String publishTopic, PushCallback callback) {
        if (mqttClient == null || !mqttClient.isConnected()) {
            Log.e(TAG, "MQTT client is not connected.");
            updateState(ConnectionState.error("disconnected"));
            return;
        }

        try {
            MqttMessage mqttMessage = new MqttMessage(message.getBytes());
            mqttMessage.setQos(qos);
            mqttClient.publish(publishTopic, mqttMessage).setActionCallback(new MqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    Log.i(TAG, "Message published to topic: " + publishTopic);
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    Log.e(TAG, "Failed to publish message to topic: " + publishTopic, exception);
                    if (callback != null) {
                        callback.onPushFailure(exception.getMessage());
                    }
                }
            });
        } catch (MqttException e) {
            Log.e(TAG, "Error publishing message: ", e);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null && "ACTION_PUSH_MQTT_MESSAGE".equals(intent.getAction())) {
            String playStatus = intent.getStringExtra("PLAY_STATUS");
            String amount = intent.getStringExtra("Amount");
            String language = intent.getStringExtra("Language");
            String message = generateMQTTResponse(playStatus, amount, language);
            publish(message, topic, null);
        }
        return START_STICKY;
    }

    private String generateMQTTResponse(String playStatus, String amount, String language) {
        MQTTResponse mqttResponse = new MQTTResponse();
        Map<ETermInfoKey, String> termInfo = FinancialApplication.getDal().getSys().getTermInfo();
        mqttResponse.setSerialNo(termInfo.get(ETermInfoKey.SN));
        mqttResponse.setTxnId("BQR ID");
        mqttResponse.setSoundPlayStatus(playStatus);
        mqttResponse.setAmount(amount);
        mqttResponse.setLanguage(language);
        mqttResponse.setReason("");
        mqttResponse.setSoundPlayedOnDateTime(Device.getTime("dd/MM/yy HH:mm:ss"));
        try {
            return MAPPER.writeValueAsString(mqttResponse);
        } catch (JsonProcessingException e) {
            LogUtils.d(TAG, "Failed to generate MQTT response", e);
            return null;
        }
    }

    public void updateConnectionSettings() {
        // 使用 AtomicBoolean 防止并发更新
        if (!isUpdatingConnection.compareAndSet(false, true)) {
            Log.w(TAG, "MQTT connection update already in progress");
            return;
        }
        try {
            // 同步断开连接
            if (mqttClient != null && mqttClient.isConnected()) {
                try {
                    mqttClient.disconnect().waitForCompletion();
                    mqttClient.close();
                    mqttClient = null;
                    LogUtils.d(TAG, "MQTT client disconnected for parameter update");
                } catch (MqttException e) {
                    Log.e(TAG, "Error disconnecting client", e);
                } finally {
                    updateState(ConnectionState.error("update parameter"));
                }
            }
            // 现在用新参数初始化
            initializeMQTT();
            LogUtils.d(TAG, "MQTT参数成功更新");
        } finally {
            isUpdatingConnection.set(false);
        }
    }

    private Notification buildNotification() {
        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("MQTT Service")
                .setContentText("Listening MQTT Server")
                .setSmallIcon(R.drawable.logo)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .build();
    }

    private void createNotificationChannel() {
        NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "MQTT Service Channel",
                NotificationManager.IMPORTANCE_HIGH
        );
        channel.setSound(null, null); // 设置声音为 null，关闭声音
        channel.enableVibration(false); //可以关闭振动
        getSystemService(NotificationManager.class).createNotificationChannel(channel);
    }

    @Override
    public void onDestroy() {
        stopForeground(true);
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(updateSettingsReceiver);
        disconnectMqttClient();
        messageCallback = null;
        if (viewModelRef != null) {
            viewModelRef.clear();
        }

        if (ioExecutor != null && !ioExecutor.isShutdown()) {
            ioExecutor.shutdown();
            try {
                // 等待任务完成
                if (!ioExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    ioExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                ioExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        Log.i(TAG, "Service destroyed");
        if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_MQTT))) {
            Intent broadcastIntent = new Intent("RE_START_MQTT");
            broadcastIntent.setComponent(new ComponentName("com.pax.receiver",
                    "com.pax.receiver.AutoStartMQTTReceiver"));
            sendBroadcast(broadcastIntent, "com.pax.permission.RESTART_MQTT");
            Log.d(TAG, "Broadcast sent to restart MQTT service");
        }
    }

    private void disconnectMqttClient() {
        if (mqttClient == null) {
            return;
        }

        Log.i(TAG, "正在断开MQTT连接...");
        try {
            // 先移除回调，防止在关闭过程中触发不必要的回调
            mqttClient.setCallback(null);
            mqttClient.close(true);
            Log.i(TAG, "MQTT客户端资源已释放");
        } catch (MqttException e) {
            Log.e(TAG, "关闭MQTT客户端失败: " + e.getMessage());
        } finally {
            mqttClient = null;
            Log.i(TAG, "MQTT客户端已设置为null");
        }
    }

    public void setMessageCallback(MQTTMessageCallback callback) {
        this.messageCallback = callback;
    }

    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "Service bound");
        return binder;
    }

    private final BroadcastReceiver updateSettingsReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_UPDATE_MQTT.equals(intent.getAction())) {
                updateConnectionSettings();
            }
        }
    };
}


