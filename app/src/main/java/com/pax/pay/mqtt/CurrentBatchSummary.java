package com.pax.pay.mqtt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrentBatchSummary implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("Name")
    private String name;
    @JsonProperty("Count")
    private String count;
    @JsonProperty("Amount")
    private String amount;
}
