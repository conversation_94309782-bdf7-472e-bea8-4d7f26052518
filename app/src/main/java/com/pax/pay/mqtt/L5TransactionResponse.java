package com.pax.pay.mqtt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class L5TransactionResponse {
    @JsonProperty("MID")
    private String mid;

    @JsonProperty("TID")
    private String tid;

    @JsonProperty("RspCode")
    private String responseCode;

    @JsonProperty("Datetime")
    private String datetime;

    @JsonProperty("ResponseMessage")
    private String responseMessage;

    @JsonProperty("CurrentBatchTransactions")
    private List<Transaction> transactions;

}
