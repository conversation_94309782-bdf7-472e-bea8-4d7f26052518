package com.pax.pay.mqtt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.ArrayList;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrentBatchResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("RspCode")
    private String rspCode;
    @JsonProperty("RspDesc")
    private String rspDesc;
    @JsonProperty("BatchSummary")
    private ArrayList<CurrentBatchSummary> batchSummary;
    @JsonProperty("TotalCount")
    private String totalCount;
    @JsonProperty("TotalAmount")
    private String totalAmount;
    @JsonProperty("BatchNo")
    private String batchNo;
    @JsonProperty("DateTime")
    private String dateTime;
    @JsonProperty("TID")
    private String tid;
    @JsonProperty("MID")
    private String mid;
}
