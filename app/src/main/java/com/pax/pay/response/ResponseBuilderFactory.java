/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210802  	         jiaguang                Create
 * ===========================================================================================
 */
package com.pax.pay.response;

import com.pax.edc.opensdk.Constants;
import java.util.HashMap;
import java.util.Map;

/**
 * The factory to get specific BaseResponseBuilder according to transaction type.
 */

public class ResponseBuilderFactory {

    //transaction type as key, concrete BaseResponseBuilder as value
    private static Map<Integer, ResponseBuilder> sMap = new HashMap<>();

    /**
     * Adding all response builders.
     */
    static {
        sMap.put(Constants.SALE, new TransResponseThirdBuilder());
        sMap.put(Constants.OFFLINE, new OfflineResponseBuilder());
        sMap.put(Constants.REFUND, new RefundResponseBuilder());
        sMap.put(Constants.VOID, new TransResponseThirdBuilder());
        sMap.put(Constants.ADJUST, new AdjustResponseBuilder());
        sMap.put(Constants.INSTALLMENT, new TransResponseSecondBuilder());
        sMap.put(Constants.YUU_CREATION, new TransResponseSecondBuilder());
        sMap.put(Constants.YUU_ENQUIRY, new TransResponseSecondBuilder());
        sMap.put(Constants.YUU_CREATION_RETRIEVAL, new TransResponseSecondBuilder());
        sMap.put(Constants.TRANS_RETRIEVAL, new TransResponseThirdBuilder());
        sMap.put(Constants.SETTLE, new SettleResponseBuilder());
        sMap.put(Constants.DCC_OPT_OUT, new DccOptOutResponseBuilder());
        sMap.put(Constants.AUTH, new AuthTransResponseBuilder());
        sMap.put(Constants.PRE_AUTH, new PreAuthTransResponseBuilder());
        sMap.put(Constants.PRE_AUTH_CM, new PreAuthTransResponseBuilder());
        sMap.put(Constants.PRE_AUTH_VOID, new PreAuthTransResponseBuilder());
        sMap.put(Constants.PRE_AUTH_CM_VOID, new PreAuthTransResponseBuilder());
    }

    /**
     * Getting specific response builder, it's singleton!!!
     *
     * @param transType transaction type
     * @return concrete BaseResponseBuilder
     */
    public static ResponseBuilder get(int transType){
        return sMap.get(transType);
    }

}
