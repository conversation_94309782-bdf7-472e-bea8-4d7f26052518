/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210913 	         jiaguang                create
 *
 * ============================================================================
 */

package com.pax.pay.response;

import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.TransResponseSecond;

/**
 * the response builder for TransResponseSecond
 */
public class TransResponseSecondBuilder extends TransResponseBuilder<TransResponseSecond>  {

    /**
     * Build and get TransResponseSecond
     * @param result the actionResult contains TransData
     * @return
     */
    @Override
    public TransResponseSecond get(ActionResult result,String transType) {
        TransResponseSecond response = new TransResponseSecond();
        //给response传入交易类型，以便在解析时根据此类型判断具体实例化对象，从而解析结果
        response.setTransType(transType);
        build(response, result);
        return response;
    }

    /**
     * Setting the params belonging to TransResponseSecond.
     * @param response the transaction response carrying params
     * @param result the actionResult contains TransData
     * @return true if success, false if failed
     */
    @Override
    protected boolean build(TransResponseSecond response, ActionResult result) {
        if (!super.build(response, result)){
            return false;
        }
        if (null != mTransData){
            if (null != mTransData.getAuthCode()) {
                response.setApproveCode(String.format("%-6s", mTransData.getAuthCode()));
            }
            if (null != mTransData.getRefNo()) {
                response.setRetrievalRespNo(String.format("%-12s", mTransData.getRefNo()));
            }
            return true;
        }
        return false;
    }
}
