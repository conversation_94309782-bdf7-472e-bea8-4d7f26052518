/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210826  	         jiaguang                create
 *
 * ============================================================================
 */
package com.pax.pay.response;


import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.SettleMsg;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.service.RequestUtil;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransTotal;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

import org.javatuples.Pair;

import java.util.ArrayList;

/**
 * generating 3rd api settle transaction response
 */

public class SettleResponseBuilder extends ResponseBuilder<SettleMsg.Response> {

    private TransTotal mTransTotal;

    /**
     * get settle transaction response
     * @param result the actionResult contains TransData
     * @return
     */
    @Override
    public SettleMsg.Response get(ActionResult result,String transType) {
        SettleMsg.Response response = new SettleMsg.Response();
		//给response传入交易类型，以便在解析时根据此类型判断具体实例化对象，从而解析结果
        response.setTransType(transType);
        build(response, result);
        return response;
    }

    /**
     * Setting the params belonging to settle Response.
     * @param response the transaction response carrying woparams
     * @param result the actionResult contains TransData
     * @return true if success, false if failed
     */
    @Override
    protected boolean build(SettleMsg.Response response, ActionResult result) {
        if (!super.build(response, result)){
            return false;
        }

        response.setTransTypeDetail(Utils.decimalToHexString(mTransData.getTransTypeDetail()));
        if(mTransTotal!=null && mTransTotal.getAcquirer()!=null && mTransTotal.getAcquirer().getName()!=null)
            response.setAcquirerIndex(Utils.decimalToHexString(RequestUtil.getAcquireIndex(mTransTotal.getAcquirer().getName())));
        //重新设置批次号
        if(mTransTotal.getAcquirer()!=null)
            mTransData.setBatchNo(mTransTotal.getAcquirer().getCurrBatchNo() -1);
        response.setTransDateTime(mTransData.getDateTime()); //YYYYMMDDHHmmss
        if (null != mTransData.getAcquirer() && null != mTransData.getAcquirer().getTerminalId()) {
            response.setTerminalId(String.format("%-8s", mTransData.getAcquirer().getTerminalId()));
        }
        if (null != mTransData.getAcquirer() && null != mTransData.getAcquirer().getMerchantId()) {
            response.setMerchantId(String.format("%-15s", mTransData.getAcquirer().getMerchantId()));
        }
        response.setBatchNo(String.format("%06d", mTransData.getBatchNo()));
        if (null != mTransData.getAuthCode()) {
            response.setApproveCode(String.format("%6s", mTransData.getAuthCode()));
        }
        if (null != mTransData.getRefNo()) {
            response.setRetrievalRespNo(String.format("%12s", mTransData.getRefNo()));
        }
        setTotals(response);

        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.SEND_PRINT_INFO)) {
            if(mTransData.getAcquirer()!= null)
                response.setAcquireName(mTransData.getAcquirer().getName());
            response.setMerchantName(FinancialApplication.getSysParam().get(SysParam.StringParam.EDC_MERCHANT_NAME_EN));
            response.setMerchantAddress(FinancialApplication.getSysParam().get(SysParam.StringParam.EDC_MERCHANT_ADDRESS));
        } else {
            // response.setAcquireName("");//length要是20吗？
            // response.setMerchantName("");
            // response.setMerchantAddress("");
        }

        return true;
    }

    /**
     * set the params relevant to total
     * @param response
     */
    private void setTotals(SettleMsg.Response response){
        boolean isLoyalty = mTransTotal.getAcquirer().getName().equalsIgnoreCase("HASE_OLS");
        if (isLoyalty) {
            setLysResponse(response);
        } else {
            setResponse(response);
        }

        response.setTotalRefundCount(String.format("%06d", mTransTotal.getRefundTotalNum()));
        response.setTotalRefundAmount(String.format("%012d", mTransTotal.getRefundTotalAmt()));

        response.setVoidTotalRefundCount(String.format("%06d", mTransTotal.getRefundVoidTotalNum()));
        response.setVoidTotalRefundAmount(String.format("%012d", mTransTotal.getRefundVoidTotalAmt()));
    }

    /**
     * set YUU response
     * @param response
     */
    private void setLysResponse(SettleMsg.Response response){

        response.setTotalSaleCount(String.format("%06d", mTransTotal.getLysGrossNum()));
        response.setTotalSaleAmount(String.format("%012d", mTransTotal.getLysGrossAmt()));
        response.setTotalBaseAmount(String.format("%012d", mTransTotal.getLysGrossAmt() - mTransTotal.getTipAmt()));
        response.setTotalNetAmount(String.format("%012d", mTransTotal.getLysOkNetAmt()));//这个地方的totalNet计算方式需要和Raymond再确认一下！关于是否包含tip
        response.setTotalHASECashDollar(String.format("%012d", mTransTotal.getHaseCashAmt()));
        response.setTotalMerchantDollar(String.format("%012d", mTransTotal.getMer1Amt()));
        response.setTotalTips(String.format("%012d", mTransTotal.getTipAmt()));

        response.setVoidTotalSaleCount(String.format("%06d", mTransTotal.getLysGrossVoidNum()));
        response.setVoidTotalSaleAmount(String.format("%012d", mTransTotal.getLysGrossVoidAmt()));
//            response.setVoidTotalBaseAmount(String.format("%012d", (mTransTotal.getLysGrossVoidAmt() - mTransTotal.getlysgrossTip()));//待Raymond回复void的tip问题
        response.setVoidTotalNetAmount(String.format("%012d", mTransTotal.getLysOkNetVoidAmt()));//这个地方的totalNet计算方式需要和Raymond确定下来！！！关于是否包含tip
        response.setVoidTotalHASECashDollar(String.format("%012d", mTransTotal.getHaseCashVoidAmt()));
        response.setVoidTotalMerchantDollar(String.format("%012d", mTransTotal.getMer1VoidAmt()));
//            response.setVoidTotalTips(String.format("%012d", mTransTotal.getVoidTipAmt()));//待Raymond回复void的tip问题
    }

    private void setResponse(SettleMsg.Response response){
        response.setTotalSaleCount(String.format("%06d", mTransTotal.getSaleNum()));
        response.setTotalSaleAmount(String.format("%012d", mTransTotal.getSaleAmt()));
        response.setTotalBaseAmount(String.format("%012d", mTransTotal.getSaleAmt() - mTransTotal.getTipAmt()));
        response.setTotalNetAmount(String.format("%012d", mTransTotal.getLysOkNetAmt()));
        response.setTotalHASECashDollar(String.format("%012d", mTransTotal.getHaseCashAmt()));
        response.setTotalMerchantDollar(String.format("%012d", mTransTotal.getMer1Amt()));
        response.setTotalTips(String.format("%012d", mTransTotal.getTipAmt()));

        response.setVoidTotalSaleCount(String.format("%06d", mTransTotal.getSaleVoidNum()));
        response.setVoidTotalSaleAmount(String.format("%012d", mTransTotal.getSaleVoidAmt()));
//            response.setVoidTotalBaseAmount(String.format("%012d", (mTransTotal.getSaleVoidAmt() - mTransTotal.getVoidTip()));//待Raymond回复void的tip问题
        response.setVoidTotalNetAmount(String.format("%012d", mTransTotal.getLysOkNetVoidAmt()));
        response.setVoidTotalHASECashDollar(String.format("%012d", mTransTotal.getHaseCashVoidAmt()));
        response.setVoidTotalMerchantDollar(String.format("%012d", mTransTotal.getMer1VoidAmt()));
//            response.setVoidTotalTips(String.format("%012d", mTransTotal.getVoidTipAmt()));//待Raymond回复void的tip问题
    }

    /**
     * get transData and TransTotal and set response's response code
     * @param response the transaction response
     * @param result the actionResult contains TransData
     * @return  true if success, false if failed
     */
    @Override
    protected boolean getTransData(SettleMsg.Response response, ActionResult result){
        if (result == null) {
            setRspCode(response, Integer.toString(TransResult.ERR_HOST_REJECT));
            return false;
        }
        if (result.getRet() != TransResult.SUCC) {
            setRspCode(response, Integer.toString(result.getRet()));
            return false;
        }
        if (result.getData1() == null){
            return false;
        }
        try {
            Pair<TransData, ArrayList<TransTotal>> transDataArrayListPair
                    = (Pair<TransData, ArrayList<TransTotal>>)result.getData1();
            mTransData = transDataArrayListPair.getValue0();
            ArrayList<TransTotal> transTotals = transDataArrayListPair.getValue1();
            mTransTotal = transTotals.get(0);//there should be just one transTotal in transTotals
        } catch (Exception e) {
            setRspCode(response, Integer.toString(TransResult.ERR_ABORTED));
            return false;
        }
        return true;
    }
}
