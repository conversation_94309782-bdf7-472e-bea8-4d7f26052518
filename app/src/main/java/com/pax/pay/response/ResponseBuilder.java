/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210802  	         jiaguang                Create
 * ===========================================================================================
 */
package com.pax.pay.response;

import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.BaseResponse;
import com.pax.edc.opensdk.ParamsUtils;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.Utils;

/**
 * The util for filling Response according to the TransData in ActionResult.
 * @param <T> the subclass of Response
 */
public abstract class ResponseBuilder<T extends BaseResponse> {

    // the transData in ActionResult
    protected TransData mTransData;

    /**
     * The method for getting response according to ActionResult
     * @param result the actionResult contains TransData
     * @param transType the transType of Transaction
     * @return the transaction response
     */
    public abstract T get(ActionResult result,String transType);


    /**
     * The method for build a response according to ActionResult,
     * subclass should override this method to set its own params,
     * and call super to set superclass's params
     * @param response the transaction response carrying params
     * @param result the actionResult contains TransData
     * @return the transaction response
     */
    protected boolean build(T response, ActionResult result){
        response.setAppId(FinancialApplication.getApp().getPackageName());
        if (!getTransData(response, result)){
            return false;
        }
        setRspCode(response,String.format("%-2s", mTransData.getResponseCode()));
        response.setTransType(Utils.decimalToHexString(mTransData.getEcrMsgType()));
        if(ParamsUtils.hasOriTransType(mTransData.getEcrMsgType())) {
            response.setOriTransType(Utils.decimalToHexString(mTransData.getEcrOrigTransType()));
        }
        return true;
    }

    /**
     * get transData from ActionResult, and set transaction response's response code
     * @param response the transaction response
     * @param result the actionResult contains TransData
     * @return true if success, false if failed
     */
    protected boolean getTransData(T response, ActionResult result){
        if (result == null) {
            setRspCode(response, Integer.toString(TransResult.ERR_HOST_REJECT));
            return false;
        }
        if (result.getRet() != TransResult.SUCC) {
            setRspCode(response, Integer.toString(result.getRet()));
            return false;
        }
        if (result.getData() == null){
            return false;
        }
        try {
            mTransData = (TransData) result.getData();
        } catch (Exception e) {
            setRspCode(response, Integer.toString(TransResult.ERR_ABORTED));
            return false;
        }
        return true;
    }

    /**
     * set transaction response's response code
     * @param response
     * @param rspCode
     */
    protected void setRspCode(T response, String rspCode){
        if (null == mTransData){
            // when cannot get transData, do not judge whether this trans has response code
            // just set it, although this may seems strange!!!
            response.setRspCode(rspCode);
            return;
        }
        if (ParamsUtils.hasResponseCode(mTransData.getEcrMsgType())){
            response.setRspCode(rspCode);
        }
    }

}
