/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210802  	         jiaguang                Create
 * ===========================================================================================
 */
package com.pax.pay.response;

import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.AdjustMsg;

/**
 * The utils to build adjust response using ActionResult
 */
public class AdjustResponseBuilder extends TransResponseBuilder<AdjustMsg.Response> {

    /**
     * Build and get adjust response
     * @param result the actionResult contains TransData
     * @return Adjust transaction's response
     */
    @Override
    //
    public AdjustMsg.Response get(ActionResult result,String transType) {
        AdjustMsg.Response response = new AdjustMsg.Response();
        //给response传入交易类型，以便在解析时根据此类型判断具体实例化对象，从而解析结果
        response.setTransType(transType);
        build(response, result);
        return response;
    }

    /**
     * Setting the params belonging to adjust Response.
     * @param response the transaction response carrying params
     * @param result the actionResult contains TransData
     * @return true if success, false if failed
     */
    @Override
    protected boolean build(AdjustMsg.Response response, ActionResult result) {
        if (!super.build(response, result)){
            return false;
        }
        if (null != mTransData){

            response.setTipAmount(String.format("%012d", mTransData.getTipAmount()));

            if (mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setCurrencyCode(String.format("%-3s", Long.parseLong(mTransData.getHomeCurrency().getCode())));
            }
            if(mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setExchangeRate(String.format("%-8s", Long.parseLong(mTransData.getRate())));
            }
            if (mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setTotalAmountForeign(String.format("%012d", Long.parseLong(mTransData.getHomeCurrencyAmount())));
            }
            if (mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setTipAmountForeign(String.format("%012d", Long.parseLong(mTransData.getHomeCurrencyTipAmount())));
            }
            //修正Redeem和balance计算方式
            response.setMerchantDolRedeemed(String.format("%012d", mTransData.getMer1CashDolRedeemed()));
            response.setHaseDolRedeemed(String.format("%012d", mTransData.getCashDolRedeemed()));
            response.setNetAmount(String.format("%012d", mTransData.getNetAmount()));
            response.setMerchantDolBalance(String.format("%012d", mTransData.getMer1CashDolBalance()));
            response.setHaseDolBalance(String.format("%012d", mTransData.getCashDolBalance()));
            return true;
        }
        return false;
    }

    /**
     * set valid print info into response
     * @param response
     */
    @Override
    protected void setPrintInfoValid(AdjustMsg.Response response){
        super.setPrintInfoValid(response);
        if (null != mTransData.getCupRRN()) {
            response.setCupRrn(String.format("%-12s", mTransData.getCupRRN()));
        }
    }
    /**
     * set empty print info into response
     * @param response
     */
    @Override
    protected void setPrintInfoBlank(AdjustMsg.Response response){
        super.setPrintInfoBlank(response);
        //移除设空情况
        // response.setCupRrn(" ");
    }

}
