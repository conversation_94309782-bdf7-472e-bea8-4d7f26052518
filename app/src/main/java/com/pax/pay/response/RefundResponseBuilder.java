/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Revision History:
 * Date                  Author	                 Action
 * 20210802  	         jiaguang                Create
 * ===========================================================================================
 */
package com.pax.pay.response;

import com.pax.abl.core.ActionResult;
import com.pax.edc.opensdk.RefundMsg;

/**
 * The utils to build Refund response using ActionResult
 */
public class RefundResponseBuilder extends TransResponseBuilder<RefundMsg.Response> {

    /**
     * Build and get Refund response
     * @param result the actionResult contains TransData
     * @return the transaction response
     */
    @Override
    public RefundMsg.Response get(ActionResult result,String transType) {
        RefundMsg.Response response = new RefundMsg.Response();
        //给response传入交易类型，以便在解析时根据此类型判断具体实例化对象，从而解析结果
        response.setTransType(transType);
        build(response, result);
        return response;
    }

    /**
     * Setting the params belonging to Refund Response.
     * @param response the transaction response carrying params
     * @param result the actionResult contains TransData
     * @return  true if success, false if failed
     */
    @Override
    protected boolean build(RefundMsg.Response response, ActionResult result) {
        if (!super.build(response, result)){
            return false;
        }
        if (null != mTransData){

            if (null != mTransData.getAuthCode()) {
                response.setApproveCode(String.format("%-6s", mTransData.getAuthCode()));
            }
            if (null != mTransData.getRefNo()) {
                response.setRetrievalRespNo(String.format("%-12s", mTransData.getRefNo()));
            }
            if (mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setCurrencyCode(String.format("%-3s", Long.parseLong(mTransData.getHomeCurrency().getCode())));
            }
            if(mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setExchangeRate(String.format("%-8s", Long.parseLong(mTransData.getRate())));
            }
            if (mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setTotalAmountForeign(String.format("%012d", Long.parseLong(mTransData.getHomeCurrencyAmount())));
            }
            if (mTransData.isDcc() && !mTransData.isFullOutSource()) {
                response.setTipAmountForeign(String.format("%012d", Long.parseLong(mTransData.getHomeCurrencyTipAmount())));
            }
            return true;
        }
        return false;
    }

}
