package com.pax.pay.menu;

import android.app.ActivityManager;
import android.app.AlertDialog;
import android.content.Context;
import android.content.pm.PackageManager;

import com.pax.abl.core.AAction;
import com.pax.abl.utils.EncUtils;
import com.pax.appstore.DownloadManager;
import com.pax.commonlib.utils.LogUtils;
import com.pax.data.local.GreendaoHelper;
import com.pax.device.Device;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;
import com.pax.view.MenuPage;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;

public class TmsMenuActivity extends BaseMenuActivity implements MenuPage.OnProcessListener {
    private static final String MAXSTORE_PACKAGE_NAME = "com.pax.market.android.app";

    public MenuPage createMenuPage() {
        MenuPage.Builder builder = new MenuPage.Builder(TmsMenuActivity.this, 9, 3)
                .addMenuItem(getString(R.string.tms), R.drawable.app_tms)
                .addTransItem(getString(R.string.tms_reset_tid), R.drawable.other);
        MenuPage menuPage = builder.create();
        menuPage.setOnProcessListener(this);
        return menuPage;
    }

    /*7006 – Payment
    7007 – Technical
    7008 – Transaction
    7009 – Account Related
    7010 – Paper Roll*/
    @Override
    public void process(int index) {
        switch (index) {
            case 0:
                try {
                    PackageManager packageManager = this.getPackageManager();
                    packageManager.getApplicationInfo(MAXSTORE_PACKAGE_NAME, 0);
                } catch (PackageManager.NameNotFoundException e) {
                    LogUtils.e(TAG, "", e);
                    DialogUtils.showErrMessage(this, null, getString(R.string.no_maxstore_client), null, Constants.FAILED_DIALOG_SHOW_TIME);
                }
                DownloadManager.openDownloadPage(this);
                break;
            case 1:
                ActionInputPassword inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {
                    @Override
                    public void onStart(AAction action) {
                        ((ActionInputPassword) action).setParam(TmsMenuActivity.this, 6,
                                getString(R.string.prompt_tms_pwd), null);
                    }
                });
                inputPasswordAction.setEndListener((action, result) -> {
                    TransContext.getInstance().setCurrentAction(null); //fix leaks

                    if (result.getRet() != TransResult.SUCC) {
                        return;
                    }
                    String data = EncUtils.sha1((String) result.getData());
                    if (!data.equals(SysParam.getInstance().getString(R.string.SEC_TMS_PWD))) {
                        DialogUtils.showErrMessage(TmsMenuActivity.this, getString(R.string.tms_reset_tid),
                                getString(R.string.err_password), null, Constants.FAILED_DIALOG_SHOW_TIME);
                        return;
                    }

                    showUserProfileDialog();
                });
                inputPasswordAction.execute();
                break;
            default:
                break;
        }
    }

    private void showUserProfileDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(TmsMenuActivity.this);
        builder.setMessage(Utils.getString(R.string.reset_tms_confirm))
                .setPositiveButton("Yes", (dialog, which) -> {
                    // Perform action for "Yes"
                    dialog.dismiss();
                    conditionalClearStorage();
                })
                .setNegativeButton("No", (dialog, which) -> dialog.dismiss())
                .setCancelable(false);

        AlertDialog dialog = builder.create();
        dialog.show();
    }

    private void conditionalClearStorage() {
        //当没有需要结算的才允许清除数据
        if (GreendaoHelper.getTransDataHelper().countOf() > 0) {
            if (GreendaoHelper.getTransDataHelper().countOfNoPreAuth() == 0) {
                Device.beepPrompt();
                FinancialApplication.getApp().runOnUiThread(() ->
                        DialogUtils.showConfirmDialog(this, Utils.getString(R.string.preauth_trans_exits),
                                CustomAlertDialog::dismiss, alertDialog -> {
                                    clearStorage();
                                    alertDialog.dismiss();
                                }));
            } else {
                Device.beepPrompt();
                FinancialApplication.getApp().runOnUiThread(() -> ToastUtils.showMessage(R.string.has_trans_for_settle));
            }
        } else {
            clearStorage();
        }
    }

    private void clearStorage() {
        Device.beepOk();
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        try {
            activityManager.clearApplicationUserData();
        } catch (Exception e) {
            Device.beepErr();
            LogUtils.e(TAG, Utils.getString(R.string.clear_storage_error), e);
        }
    }
}
