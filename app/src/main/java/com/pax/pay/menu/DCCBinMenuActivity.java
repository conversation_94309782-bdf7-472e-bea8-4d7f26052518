package com.pax.pay.menu;

import android.content.DialogInterface;
import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionEndListener;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.action.ActionClearBin;
import com.pax.pay.trans.action.ActionDownloadBin;
import com.pax.pay.trans.action.ActionInputTransData;
import com.pax.pay.trans.action.ActionSelectDownloadMode;
import com.pax.pay.trans.action.ActionSetupAutoLoad;
import com.pax.pay.trans.action.activity.DispCardBinActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.view.MenuPage;
import com.pax.view.dialog.DialogUtils;

public class DCCBinMenuActivity extends BaseMenuActivity {
    @Override
    public MenuPage createMenuPage() {

        MenuPage.Builder builder = new MenuPage.Builder(DCCBinMenuActivity.this, 9, 3)
                .addActionItem(getString(R.string.download_bin), R.drawable.app_download_bin,
                        createActionForDownloadBin())
                .addActionItem(getString(R.string.check_bin), R.drawable.app_check_bin,
                        createActionForCheckBin())
                .addActionItem(Utils.getString(R.string.setup_autoload),
                        R.drawable.app_setup_auto_load, createActionForSetupAutoLoad())
                .addMenuItem(Utils.getString(R.string.display_bin), R.drawable.app_display_bin,
                        DispCardBinActivity.class)
                .addActionItem(Utils.getString(R.string.download_mode),
                        R.drawable.app_download_mode, createActionForSelectDownloadMode())
                .addActionItem(getString(R.string.clear_bin), R.drawable.app_clear_bin,
                        createActionForClearBin());
        return builder.create();
    }

    /**
     * download dcc card bin
     */
    private AAction createActionForDownloadBin() {
        ActionDownloadBin downloadBinAction = new ActionDownloadBin(new ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionDownloadBin) action).setParam(DCCBinMenuActivity.this);
            }
        });
        downloadBinAction.setEndListener(new ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(null);
            }
        });
        return downloadBinAction;
    }

    /**
     * set up auto download card bin
     */
    private AAction createActionForSetupAutoLoad() {
        ActionSetupAutoLoad setupAutoLoadAction =
                new ActionSetupAutoLoad(new AAction.ActionStartListener() {
                    @Override
                    public void onStart(AAction action) {
                        ((ActionSetupAutoLoad) action).setParam(DCCBinMenuActivity.this);
                    }
                });
        setupAutoLoadAction.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(null);
            }
        });
        return setupAutoLoadAction;
    }

    /**
     * check dcc card bin
     */
    private AAction createActionForCheckBin() {
        final ActionInputTransData inputBinAction =
                new ActionInputTransData(new AAction.ActionStartListener() {
                    @Override
                    public void onStart(AAction action) {
                        ((ActionInputTransData) action).setParam(DCCBinMenuActivity.this,
                                getString(R.string.check_bin))
                                .setInputLine(getString(R.string.prompt_input_card_bin),
                                        ActionInputTransData.EInputType.CARD_BIN, 10, 10, false);
                    }
                });
        inputBinAction.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                inputBinAction.setFinished(false);
                TransContext.getInstance().setCurrentAction(null);

                //finish InputTransData1Activity, pop to DCCBinMenuActivity
                if (result.getRet() != TransResult.SUCC) {
                    ActivityStack.getInstance().popTo(DCCBinMenuActivity.this);
                    return;
                }

                String content = (String) result.getData();
                //check card bin
                onCheckBin(action, content);
            }
        });
        return inputBinAction;
    }

    private void onCheckBin(AAction action, String content) {
        int ret = Component.checkCardBin(content);
        if (ret < 0) {
            ToastUtils.showMessage(R.string.please_input_again);
            //check again
            TransContext.getInstance().setCurrentAction(action);
        } else if (ret == 0) {
            //local card bin , can not do DCC
            DialogUtils.showSuccMessage(ActivityStack.getInstance().top(), getString(R.string.check_bin),
                    new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            ActivityStack.getInstance().pop();
                        }
                    }, Constants.SUCCESS_DIALOG_SHOW_TIME);
        } else {
            //not local card bin, can do DCC
            DialogUtils.showErrMessage(ActivityStack.getInstance().top(), null,
                    getString(R.string.dialog_check_bin_err),
                    new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            ActivityStack.getInstance().pop();
                        }
                    }, Constants.FAILED_DIALOG_SHOW_TIME);
        }
    }

    /**
     * set card bin download mode
     */
    private AAction createActionForSelectDownloadMode() {
        ActionSelectDownloadMode selectDownloadModeAction =
                new ActionSelectDownloadMode(new AAction.ActionStartListener() {

                    @Override
                    public void onStart(AAction action) {
                        ((ActionSelectDownloadMode) action).setParam(DCCBinMenuActivity.this);
                    }
                });
        selectDownloadModeAction.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(null);
            }
        });
        return selectDownloadModeAction;
    }

    /**
     * clear card bin
     */
    private AAction createActionForClearBin() {
        ActionClearBin clearBinAction = new ActionClearBin(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionClearBin) action).setParam(DCCBinMenuActivity.this);
            }
        });
        clearBinAction.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(null);
            }
        });
        return clearBinAction;
    }

}

