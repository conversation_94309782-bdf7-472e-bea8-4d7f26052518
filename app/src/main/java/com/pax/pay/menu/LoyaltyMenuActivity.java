package com.pax.pay.menu;

import android.app.ActionBar;
import android.widget.LinearLayout;

import com.pax.edc.R;
import com.pax.pay.trans.InstMultiUpTrans;
import com.pax.pay.trans.MultipleUpTrans;
import com.pax.pay.trans.OnlineEnquiryTrans;
import com.pax.pay.trans.PureRedeemTrans;
import com.pax.pay.trans.RedeemInstalmentTrans;
import com.pax.view.MenuPage;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/13.
 */

public class LoyaltyMenuActivity extends BaseMenuActivity {
    private LinearLayout mMenuLayout;

    @Override
    protected void initViews() {
        mMenuLayout = (LinearLayout) findViewById(R.id.ll_container);
    }

    @Override
    protected void onResume() {
        super.onResume();
        android.widget.LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ActionBar.LayoutParams.MATCH_PARENT,
                ActionBar.LayoutParams.MATCH_PARENT);
        mMenuLayout.removeAllViews();
        mMenuLayout.addView(createMenuPage(), params);
    }

    /**
     * 创建Loyalty菜单
     * @return MenuPage
     */
    public MenuPage createMenuPage() {
        MenuPage.Builder builder = new MenuPage.Builder(LoyaltyMenuActivity.this, 9, 3)
                .addTransItem(getString(R.string.trans_pure_redeem), R.drawable.icon2_pure_redeem,
                        new PureRedeemTrans(LoyaltyMenuActivity.this, null).setBackToMain(false))
                .addTransItem(getString(R.string.trans_online_enquiry), R.drawable.icon2_enquiry,
                        new OnlineEnquiryTrans(LoyaltyMenuActivity.this, null).setBackToMain(false))
                .addTransItem(getString(R.string.trans_redeem_instalment), R.drawable.icon2_redm_inst,
                        new RedeemInstalmentTrans(LoyaltyMenuActivity.this, null).setBackToMain(false))
                .addTransItem(getString(R.string.trans_multiple_up), R.drawable.icon2_multile_up,
                        new MultipleUpTrans(LoyaltyMenuActivity.this, null).setBackToMain(false))
                .addTransItem(getString(R.string.trans_inst_multi_up), R.drawable.icon2_instal_multi_up,
                        new InstMultiUpTrans(LoyaltyMenuActivity.this, null).setBackToMain(false));
        return builder.create();
    }
}




