/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.menu;

import android.content.Intent;
import android.os.Bundle;

import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.password.ChangeMerchantPwdActivity;
import com.pax.pay.password.ChangeRefundPwdActivity;
import com.pax.pay.password.ChangeSettlePwdActivity;
import com.pax.pay.password.ChangeVoidPwdActivity;
import com.pax.sbipay.R;
import com.pax.view.MenuPage;

public class PasswordMenuActivity extends BaseMenuActivity implements MenuPage.OnProcessListener {

    /**
     * Change password
     */
    public MenuPage createMenuPage() {

        MenuPage.Builder builder = new MenuPage.Builder(PasswordMenuActivity.this, 6, 3)
                .addMenuItem(getString(R.string.pwd_merchant), R.drawable.app_opermag)

                //.addMenuItem(getString(R.string.pwd_terminal), R.drawable.modify_mag_passwd, ChangeTerminalPwdActivity.class)
                .addMenuItem(getString(R.string.pwd_void), R.drawable.app_void)
                //The icon of adjust need to be modified
                .addMenuItem(getString(R.string.pwd_settle), R.drawable.app_settlement)
                //change refund pwd  (default 123456)
                .addMenuItem(getString(R.string.pwd_refund), R.drawable.app_refund);
        MenuPage menuPage = builder.create();
        menuPage.setOnProcessListener(this);
        return menuPage;
    }

    @Override
    public void process(int index) {
        switch (index) {
            case 0:
                menuItemProcess(ChangeMerchantPwdActivity.class, R.string.pwd_merchant);
                break;
            case 1:
                menuItemProcess(ChangeVoidPwdActivity.class, R.string.pwd_void);
                break;
            case 2:
                menuItemProcess(ChangeSettlePwdActivity.class, R.string.pwd_settle);
                break;
            case 3:
                menuItemProcess(ChangeRefundPwdActivity.class, R.string.pwd_refund);
                break;
            default:
                break;
        }
    }

    private void menuItemProcess(Class<?> cls, int resId) {
        Intent intent = new Intent(this, cls);
        Bundle bundle = new Bundle();
        bundle.putString(EUIParamKeys.NAV_TITLE.toString(), getString(resId));
        bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
        intent.putExtras(bundle);
        startActivity(intent);
    }
}
