/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.menu;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.telephony.PhoneStateListener;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.abl.utils.EncUtils;
import com.pax.commonlib.ActivityStack;
import com.pax.dal.exceptions.PedDevException;
import com.pax.pay.ConfigFirstActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.record.SQRQueryActivity;
import com.pax.pay.record.TransQueryActivity;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.ActionClearBatch;
import com.pax.pay.trans.action.ActionClearReversal;
import com.pax.pay.trans.action.ActionDispSingleLineMsg;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionPrintPreview;
import com.pax.pay.trans.action.ActionSelProvider;
import com.pax.pay.trans.action.ActionUpdateParam;
import com.pax.pay.trans.action.activity.PrintPreviewActivity;
import com.pax.pay.trans.receipt.PrintListenerImpl;
import com.pax.pay.trans.receipt.ReceiptPrintParam;
import com.pax.pay.trans.transmit.TransOnline;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.RxUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.BuildConfig;
import com.pax.sbipay.R;
import com.pax.settings.ConfigSecondActivity;
import com.pax.settings.SysParam;
import com.pax.view.MenuPage;
import com.pax.view.dialog.DialogUtils;

/**
 * The type Manage menu activity.
 */
public class ManageMenuActivity extends BaseMenuActivity implements MenuPage.OnProcessTitleListener {
    private TelephonyManager telephonyManager;
    private PhoneStateListener phoneStateListener = new PhoneStateListener() {
        @Override
        public void onSignalStrengthsChanged(SignalStrength signalStrength) {
            // 在主线程中获取信号强度
            // 使用getLevel()获取信号强度级别，这是一个大致的指标
            int level = signalStrength.getLevel(); // 范围通常是0-4或0-5
            // 将级别转换为大致的dBm值
            int dbm;
            switch (level) {
                case 4:
                    dbm = -70; // 较强信号
                    break;
                case 3:
                    dbm = -85; // 中等信号
                    break;
                case 2:
                    dbm = -100; // 较弱信号
                    break;
                case 1:
                    dbm = -115; // 非常弱的信号
                    break;
                default:
                    dbm = -120; // 信号可能非常弱或丢失
                    break;
            }
            // 更新DeviceDetails实例
            FinancialApplication.setTelephoneStrength(dbm);
        }
    };

    @Override
    public MenuPage createMenuPage() {
        MenuPage menuPage;

        MenuPage.Builder builder = new MenuPage.Builder(ManageMenuActivity.this, 16, 3);

        builder.addActionItem(getString(R.string.trans_password), R.drawable.app_opermag);
        if (SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_ENABLE_CONFIG))
                || BuildConfig.DEBUG) {
            builder.addActionItem(getString(R.string.settings_title), R.drawable.app_setting);
        }
        builder.addMenuItem(getString(R.string.trans_history), R.drawable.app_history) //, TransQueryActivity.class
                .addActionItem(getString(R.string.version), R.drawable.app_version)
                .addActionItem(getString(R.string.update_param), R.drawable.app_update_param)
                .addActionItem(getString(R.string.tms), R.drawable.app_tms)
                .addActionItem(getString(R.string.select_provider), R.drawable.app_select_provider)
                .addActionItem(getString(R.string.om_download_menu_echo_test), R.drawable.app_echo_test)
                .addActionItem(getString(R.string.device_details), R.drawable.app_device_details)
                .addActionItem(getString(R.string.om_clearTrade_menu_reversal), R.drawable.app_device_clear_reversal)
                .addActionItem(getString(R.string.om_clearTrade_menu_trade_voucher), R.drawable.app_device_clear_batch)
                .addActionItem(getString(R.string.settings_menu_communication_parameter), R.drawable.app_device_comm)
                .addMenuItem(getString(R.string.sqr_history), R.drawable.app_history);
        menuPage = builder.create();
        menuPage.setOnProcessTitleListener(this);
        return menuPage;
    }

    private AAction createDispActionForVersion() {
        ActionDispSingleLineMsg displayInfoAction =
                new ActionDispSingleLineMsg(action -> ((ActionDispSingleLineMsg) action).setParam(ManageMenuActivity.this,
                        getString(R.string.version), getString(R.string.app_version), FinancialApplication.getVersion(), 60));

        displayInfoAction.setEndListener((action, result) -> {
            ActivityStack.getInstance().pop();
            TransContext.getInstance().getCurrentAction().setFinished(false); //AET-229
            TransContext.getInstance().setCurrentAction(null); //fix leaks
        });

        return displayInfoAction;
    }

    private AAction createActionForUpdateParam() {
        ActionUpdateParam actionUpdateParam =
                new ActionUpdateParam(action -> ((ActionUpdateParam) action).setParam(ManageMenuActivity.this, true));
        actionUpdateParam.setEndListener((action, result) -> TransContext.getInstance().setCurrentAction(null));

        return actionUpdateParam;
    }

    private AAction createActionForSelProvider() {
        ActionSelProvider actionSelProvider = new ActionSelProvider(action ->
                ((ActionSelProvider) action).setParam(ManageMenuActivity.this));
        actionSelProvider.setEndListener((action, result) -> TransContext.getInstance().setCurrentAction(null));

        return actionSelProvider;
    }

    private AAction createInputPwdActionForSettings() {
        ActionInputPassword inputPasswordAction = new ActionInputPassword(action -> ((ActionInputPassword) action).setParam(ManageMenuActivity.this, 6,
                getString(R.string.prompt_sys_pwd), null));

        inputPasswordAction.setEndListener((action, result) -> {
            TransContext.getInstance().setCurrentAction(null); //fix leaks

            if (result.getRet() != TransResult.SUCC) {
                return;
            }

            String data = EncUtils.sha1((String) result.getData());
            if (!data.equals(SysParam.getInstance().getString(R.string.SEC_SYS_PWD))) {
                DialogUtils.showErrMessage(ManageMenuActivity.this, getString(R.string.settings_title),
                        getString(R.string.err_password), null, Constants.FAILED_DIALOG_SHOW_TIME);
                return;
            }
            Intent intent = new Intent(ManageMenuActivity.this, ConfigFirstActivity.class);
            startActivity(intent);
        });

        return inputPasswordAction;
    }

    private AAction createInputPwdActionForManagePassword() {
        ActionInputPassword inputPasswordAction = new ActionInputPassword(action -> ((ActionInputPassword) action).setParam(ManageMenuActivity.this, 6,
                getString(R.string.prompt_merchant_pwd), null));

        inputPasswordAction.setEndListener((action, result) -> {
            TransContext.getInstance().setCurrentAction(null); //fix leaks

            if (result.getRet() != TransResult.SUCC) {
                return;
            }

            String data = EncUtils.sha1((String) result.getData());
            if (!data.equals(SysParam.getInstance().getString(R.string.SEC_MERCHANT_PWD))) {
                DialogUtils.showErrMessage(ManageMenuActivity.this, getString(R.string.trans_password),
                        getString(R.string.err_password), null, Constants.FAILED_DIALOG_SHOW_TIME);
                return;
            }
            Intent intent = new Intent(ManageMenuActivity.this, PasswordMenuActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_password));
            bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
            intent.putExtras(bundle);
            startActivity(intent);
        });

        return inputPasswordAction;
    }

    @Override
    public void process(String title) {
        if (title.equals(getString(R.string.trans_password))) {
            createInputPwdActionForManagePassword().execute();
        } else if (title.equals(getString(R.string.settings_title))) {
            createInputPwdActionForSettings().execute();
        } else if (title.equals(getString(R.string.trans_history))) {
            Intent intent = new Intent(this, TransQueryActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.trans_history));
            bundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
            intent.putExtras(bundle);
            startActivity(intent);
        } else if (title.equals(getString(R.string.version))) {
            createDispActionForVersion().execute();
        } else if (title.equals(getString(R.string.update_param))) {
            createActionForUpdateParam().execute();
        } else if (title.equals(getString(R.string.tms))) {
            Intent tmsIntent = new Intent(this, TmsMenuActivity.class);
            Bundle tmsBundle = new Bundle();
            tmsBundle.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.tms));
            tmsBundle.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
            tmsIntent.putExtras(tmsBundle);
            startActivity(tmsIntent);
        } else if (title.equals(getString(R.string.select_provider))) {
            createActionForSelProvider().execute();
        } else if (title.equals(getString(R.string.om_download_menu_echo_test))) {
            downloadFunc();
        } else if (title.equals(getString(R.string.device_details))) {
            createActionForDeviceDetail().execute();
        } else if (title.equals(getString(R.string.om_clearTrade_menu_reversal))) {
            createActionForClearReversal().execute();
        } else if (title.equals(getString(R.string.om_clearTrade_menu_trade_voucher))) {
            createActionForClearBatch().execute();
        } else if (title.equals(getString(R.string.settings_menu_communication_parameter))) {
            Intent commIntent = new Intent(this, ConfigSecondActivity.class);
            commIntent.putExtra("title", getString(R.string.settings_menu_communication_parameter));
            startActivity(commIntent);
        } else if (title.equals(getString(R.string.sqr_history))) {
            Intent intent1 = new Intent(this, SQRQueryActivity.class);
            Bundle bundle1 = new Bundle();
            bundle1.putString(EUIParamKeys.NAV_TITLE.toString(), getString(R.string.sqr_history));
            bundle1.putBoolean(EUIParamKeys.NAV_BACK.toString(), true);
            intent1.putExtras(bundle1);
            startActivity(intent1);
        }
    }

    private void downloadFunc() {
        if (FinancialApplication.getController().isFirstRun()) {
            ToastUtils.showMessage(R.string.wait_2_init_device);
            return;
        }
        RxUtils.runInBackgroud(() -> {
            int ret = -1;
            TransProcessListener listener = new TransProcessListenerImpl(this);
            try {
                ret = new TransOnline().echo(listener);
            } catch (PedDevException e) {
                listener.onShowErrMessage(e.getMessage(), Constants.FAILED_DIALOG_SHOW_TIME, false);
            }
            listener.onHideProgress();
            if (ret == TransResult.SUCC) {
                listener.onShowOKMessage(Utils.getString(R.string.echo_success), Constants.SUCCESS_DIALOG_SHOW_TIME, false);
            } else if (ret != TransResult.ERR_ABORTED && ret != TransResult.ERR_HOST_REJECT) {
                listener.onShowErrMessage(TransResultUtils.getMessage(ret),
                        Constants.FAILED_DIALOG_SHOW_TIME, false);
            }
        });
    }

    /**
     * clear reversal data
     */
    private AAction createActionForClearReversal() {

        ActionInputPassword inputPasswordAction = new ActionInputPassword(action -> ((ActionInputPassword) action).setParam(ManageMenuActivity.this, 6,
                getString(R.string.prompt_merchant_pwd), null));

        inputPasswordAction.setEndListener((action, result) -> {
            TransContext.getInstance().setCurrentAction(null); //fix leaks

            if (result.getRet() != TransResult.SUCC) {
                return;
            }
            String data = EncUtils.sha1((String) result.getData());
            if (!data.equals(SysParam.getInstance().getString(R.string.SEC_MERCHANT_PWD))) {
                DialogUtils.showErrMessage(ManageMenuActivity.this, getString(R.string.trans_password),
                        getString(R.string.err_password), null, Constants.FAILED_DIALOG_SHOW_TIME);
                return;
            }
            ActionClearReversal actionClearReversal = new ActionClearReversal(actionReversal -> ((ActionClearReversal) actionReversal).setParam(ManageMenuActivity.this));
            actionClearReversal.setEndListener((actionReversal, resultReversal) -> TransContext.getInstance().setCurrentAction(null));
            actionClearReversal.execute();
        });
        return inputPasswordAction;
    }

    /**
     * clear batch up data
     */
    private AAction createActionForClearBatch() {

        ActionInputPassword inputPasswordAction = new ActionInputPassword(action -> ((ActionInputPassword) action).setParam(ManageMenuActivity.this, 6,
                getString(R.string.prompt_merchant_pwd), null));
        inputPasswordAction.setEndListener((action, result) -> {
            TransContext.getInstance().setCurrentAction(null); //fix leaks

            if (result.getRet() != TransResult.SUCC) {
                return;
            }

            String data = EncUtils.sha1((String) result.getData());
            if (!data.equals(SysParam.getInstance().getString(R.string.SEC_MERCHANT_PWD))) {
                DialogUtils.showErrMessage(ManageMenuActivity.this, getString(R.string.trans_password),
                        getString(R.string.err_password), null, Constants.FAILED_DIALOG_SHOW_TIME);
                return;
            }
            DialogUtils.showConfirmDialog(this, Utils.getString(R.string.clear_batch_confirm), null, alertDialog -> {
                alertDialog.dismiss();
                ActionClearBatch actionClearBatch = new ActionClearBatch(actionBatch -> ((ActionClearBatch) actionBatch).setParam(ManageMenuActivity.this));
                actionClearBatch.setEndListener((actionBatch, resultBatch) -> TransContext.getInstance().setCurrentAction(null));
                actionClearBatch.execute();
            });
        });

        return inputPasswordAction;
    }

    private AAction createActionForDeviceDetail() {

        ActionPrintPreview actionPrintPreview = new ActionPrintPreview(action -> {
            telephonyManager = (TelephonyManager) FinancialApplication.getApp().getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager != null) {
                telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_SIGNAL_STRENGTHS);
            } else {
                FinancialApplication.setTelephoneStrength(-120);
                Log.e(TAG, "TelephonyManager is null");
            }
            ((ActionPrintPreview) action).setParam(ManageMenuActivity.this, getString(R.string.device_details));
        });

        actionPrintPreview.setEndListener((action, result) -> {
            TransContext.getInstance().setCurrentAction(null); //fix leaks
            String string = (String) result.getData();
            if (string != null && string.equals(PrintPreviewActivity.PRINT_BUTTON)) {
                FinancialApplication.getApp().runInBackground(() -> {
                    ReceiptPrintParam receiptPrintParam = new ReceiptPrintParam();
                    receiptPrintParam.print(ReceiptPrintParam.DEVICE, new PrintListenerImpl(ActivityStack.getInstance().top()), ActivityStack.getInstance().top());
                    ActivityStack.getInstance().popTo(ManageMenuActivity.class);
                });
            } else {
                ActivityStack.getInstance().popTo(ManageMenuActivity.class);
            }
        });
        return actionPrintPreview;
    }

    @Override
    protected void onDestroy() {
        RxUtils.release();
        if (telephonyManager != null) {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE);
        }
        super.onDestroy();
    }
}
