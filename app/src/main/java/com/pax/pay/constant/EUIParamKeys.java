/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.pay.constant;

public enum EUIParamKeys {
    /**
     * 提示信息1
     */
    PROMPT_1,
    /**
     * 提示信息2
     */
    PROMPT_2,
    /**
     * 输入1数据类型, {@LINK COM.PAX.PAY.TRANS.ACTION.ACTIONINPUTTRANSDATA.EINPUTTYPE}
     */
    INPUT_TYPE,
    /**
     * 输入1数据最大长度
     */
    INPUT_MAX_LEN,
    /**
     * 输入数据最小长度
     */
    INPUT_MIN_LEN,
    /**
     * 显示内容
     */
    CONTENT,
    /**
     * TRANSACTION TYPE
     */
    TRANS_TYPE,
    /**
     * 交易金额
     */
    TRANS_AMOUNT,
    /**
     * TIP AMOUNT
     */
    TIP_AMOUNT,
    /**
     * 交易日期
     */
    TRANS_DATE,

    /**
     * 寻卡界面类型
     */
    SEARCH_CARD_UI_TYPE,
    /**
     * 电子签名特征码
     */
    SIGN_FEATURE_CODE,

    /**
     * 列表1的值
     */
    ARRAY_LIST_1,
    /**
     * 列表2的值
     */
    ARRAY_LIST_2,

    /**
     * 导航栏抬头
     */
    NAV_TITLE,
    /**
     * 导航栏是否显示返回按钮
     */
    NAV_BACK,
    /**
     * 寻卡模式
     */
    CARD_SEARCH_MODE,
    /**
     * 寻卡界面显示授权码
     */
    AUTH_CODE,
    /**
     * 寻卡界面刷卡提醒
     */
    SEARCH_CARD_PROMPT,
    /**
     * 是否是FALLBACK
     */
    IS_FALLBACK_SEARCH_CARD,
    /**
     * 界面定时器
     */
    TIKE_TIME,
    /**
     * 卡号
     */
    PANBLOCK,
    /**
     * 凭密
     */
    SUPPORTBYPASS,
    /**
     * 输密类型
     */
    ENTERPINTYPE,
    /**
     *
     */
    OPTIONS,
    RSA_PIN_KEY,
    /**
     * 输入内容自动补零
     */
    INPUT_PADDING_ZERO,
    /**
     * 交易查询界面支持交易
     */
    SUPPORT_DO_TRANS,
    /**
     * 原交易小费
     */
    ORI_TIPS,

    /**
     * HAS TIP
     */
    HAS_TIP,

    /**
     * IS CASH TRANS
     */
    IS_CASH,

    /**
     * TIP PERCENT
     */
    TIP_PERCENT,
    /**
     * CARD MODE
     */
    /**
     * HAS TIP
     */
    IS_PREAUTH,
    /**
     * YONO CASH
     */
    IS_YONO_CASH,

    /**
     * QR
     */
    IS_QR,

    /**
     * TIP PERCENT
     */
    PREAUTHCOMPLETE_PERCENT,
    /**
     * CARD MODE
     */
    CARD_MODE,

    /**
     * ACQUIRER NAME
     */
    ACQUIRER_NAME,

    /**
     * Clss NAME
     */
    CLSS_NAME,
    /**
     * ISSUER NAME
     */
    ISSUER_NAME,

    /**
     * PRINT BITMAP
     */
    BITMAP,

    /**
     * FROM WHICH ACTIVITY
     */
    FROM_ACTIVITY,
    /**
     * RECEIPT QR CODE
     */
    RECEIPT_QR_CODE,

    RECEIPT_BYTES,

    TRANSDATA,

    SQRDATA,

    FALLBACK,

    /**
     * 汇率
     */
    TRANS_RATE,
    /**
     * 外币金额
     */
    TRANS_INTERNATIONAL_CURRENCY_AMOUNT,
    /**
     * TRANS CURRENCY SYMBOL EUI PARAM KEYS.
     */
    TRANS_CURRENCY_SYMBOL,
    /**
     * 外币代码
     */
    TRANS_INTERNATIONAL_CURRENCY_SYMBOL,
    /**
     * 本地代码
     */
    TRANS_LOCAL_CURRENCY_SYMBOL,
    /**
     * 外币国旗
     */
    TRANS_INTERNATIONAL_FLAG_IMAGE_ID,
    /**
     * 本地国旗
     */
    TRANS_LOCAL_FLAG_IMAGE_ID,
    /**
     * DCC FEE RATE
     */
    DCC_MARKUP_RATE,

    DCC_COMMISSION,
    /**
     * isSymbolNegative
     */
    IS_SYMBOL_NEGATIVE,
    /**
     * currency code
     */
    CURRENCY_CODE,
    /**
     * ORIG_FOLLOW_DATA
     */
    ORIG_FOLLOW_DATA,
    /**
     * ERROR_MSG
     */
    ERROR_MSG,

    /**
     * QR CODE
     */
    QR_CODE,

    QR_PURCHASE_ID,

    TOKEN_REQUEST_DATA,

    BANK_EMI_OFFER_DATA,

    BANK_EMI_BIN_DATA,

    BANK_EMI_CONFIRM_DATA,

    BANK_EMI_NOTIFY_DATA,

    BANK_EMI_NOTIFY_REVERSAL_DATA,

    INSTA_EMI_DATA,

    BRAND_EMI_DATA,

    EMI_VOID_DATA,

    EMI_STATUS_CHECK_DATA,

    BANK_EMI_CHECK_CARD,

    PRINT_PREVIEW_TITLE,

    ACQUIRER,
    NO_NEED_PRINT,
    NEED_AUDIO,
    SQR_TRANS_DETAILS,
    SQR_BATCH_SUMMARY


}
