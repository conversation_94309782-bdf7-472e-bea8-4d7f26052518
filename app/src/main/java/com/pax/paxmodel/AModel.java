package com.pax.paxmodel;

public abstract class AModel {
    public abstract void searchCard();

    /**
     * MAG/ICC/PICC isolated with each other
     * used for A910/A930/A80/IM30
     * <p>
     * 111_000
     */
    public void searchCard70() {

    }

    /**
     * Only MAG/ICC conflict，
     * used for A60/A30/Aries8/Aries6
     * <p>
     * 111_110
     */
    public void searchCard76() {

    }

    /**
     * Only MAG/PICC conflict,
     * used for A920/A920PRO/A77/
     * <p>
     * 111_101
     */
    public void searchCard75() {

    }

    /**
     * No MAG, only ICC and PICC
     * used for A50
     * <p>
     * 011_000
     */
    public void searchCard30() {

    }
}
