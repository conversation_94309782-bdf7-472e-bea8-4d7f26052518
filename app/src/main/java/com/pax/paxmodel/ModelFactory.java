package com.pax.paxmodel;

import android.os.Build;

import com.pax.commonlib.utils.LogUtils;

public class ModelFactory {

    private static String mStrPkgName = "com.pax.paxmodel.";
    private static String mStrModel = Build.MODEL.toUpperCase();

    public static AModel createModel() {
        String clssName = mStrPkgName + mStrModel;
        AModel model = null;
        try {
            model = (AModel) Class.forName(clssName).newInstance();
        } catch (IllegalAccessException | InstantiationException | ClassNotFoundException e) {
            LogUtils.e(e);
        }
        return model;
    }
}
