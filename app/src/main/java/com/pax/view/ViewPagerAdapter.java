/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;

import com.pax.sbipay.R;
import com.pax.view.GridViewAdapter.GridItem;

import java.util.List;

public class ViewPagerAdapter extends PagerAdapter {
    private Context context;
    private int numPages;
    private List<GridItem> itemList;
    private int maxItemNumPerPage;
    private int columns;
    private MenuItemAdapter.OnItemClickListener listener;

    public ViewPagerAdapter(Context context, int numPages, List<GridItem> itemList,
                            int maxItemNumPerPage, int columns,
                            MenuItemAdapter.OnItemClickListener listener) {
        this.context = context;
        this.numPages = numPages;
        this.itemList = itemList;
        this.maxItemNumPerPage = maxItemNumPerPage;
        this.columns = columns;
        this.listener = listener;
    }

    @Override
    public int getCount() {
        return numPages;
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        View view = getViewPagerItem(position);
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }

    /**
     * 获取每个页面RecyclerView
     *
     * @param pageIndex
     * @return
     */
    private View getViewPagerItem(int pageIndex) {
        View layout = LayoutInflater.from(context).inflate(R.layout.viewpage_gridview, null);
        RecyclerView recyclerView = layout.findViewById(R.id.vp_gv);

        GridLayoutManager layoutManager = new GridLayoutManager(context, columns);
        recyclerView.setLayoutManager(layoutManager);

        int start = pageIndex * maxItemNumPerPage;
        int end = Math.min((pageIndex + 1) * maxItemNumPerPage, itemList.size());
        List<GridItem> pageItems = itemList.subList(start, end);

        MenuItemAdapter adapter = new MenuItemAdapter(pageItems, listener);
        recyclerView.setAdapter(adapter);
        return layout;
    }
}