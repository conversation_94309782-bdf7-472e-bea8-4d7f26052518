package com.pax.view.loadandempty;

import android.content.Context;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

/**
 * Created by zhy on 15/8/26.
 */
public class LoadingAndRetryLayout extends FrameLayout
{
    private View mLoadingView;
    private View mRetryView;
    private View mContentView;
    private View mEmptyView;
    private LayoutInflater mInflater;

    private static final String TAG = LoadingAndRetryLayout.class.getSimpleName();


    /**
     * Instantiates a new Loading and retry layout.
     *
     * @param context      the context
     * @param attrs        the attrs
     * @param defStyleAttr the def style attr
     */
    public LoadingAndRetryLayout(Context context, AttributeSet attrs, int defStyleAttr)
    {
        super(context, attrs, defStyleAttr);
        mInflater = LayoutInflater.from(context);
    }


    /**
     * Instantiates a new Loading and retry layout.
     *
     * @param context the context
     * @param attrs   the attrs
     */
    public LoadingAndRetryLayout(Context context, AttributeSet attrs)
    {
        this(context, attrs, -1);
    }

    /**
     * Instantiates a new Loading and retry layout.
     *
     * @param context the context
     */
    public LoadingAndRetryLayout(Context context)
    {
        this(context, null);
    }

    private boolean isMainThread()
    {
        return Looper.myLooper() == Looper.getMainLooper();
    }

    /**
     * Show loading.
     */
    public void showLoading()
    {
        if (isMainThread())
        {
            showView(mLoadingView);
        } else
        {
            post(new Runnable()
            {
                @Override
                public void run()
                {
                    showView(mLoadingView);
                }
            });
        }
    }

    /**
     * Show retry.
     */
    public void showRetry()
    {
        if (isMainThread())
        {
            showView(mRetryView);
        } else
        {
            post(new Runnable()
            {
                @Override
                public void run()
                {
                    showView(mRetryView);
                }
            });
        }

    }

    /**
     * Show content.
     */
    public void showContent()
    {
        if (isMainThread())
        {
            showView(mContentView);
        } else
        {
            post(new Runnable()
            {
                @Override
                public void run()
                {
                    showView(mContentView);
                }
            });
        }
    }

    /**
     * Show empty.
     */
    public void showEmpty()
    {
        if (isMainThread())
        {
            showView(mEmptyView);
        } else
        {
            post(new Runnable()
            {
                @Override
                public void run()
                {
                    showView(mEmptyView);
                }
            });
        }
    }


    private void showView(View view)
    {
        if (view == null) return;

        if (view == mLoadingView)
        {
            mLoadingView.setVisibility(View.VISIBLE);
            if (mRetryView != null)
                mRetryView.setVisibility(View.GONE);
            if (mContentView != null)
                mContentView.setVisibility(View.GONE);
            if (mEmptyView != null)
                mEmptyView.setVisibility(View.GONE);
        } else if (view == mRetryView)
        {
            mRetryView.setVisibility(View.VISIBLE);
            if (mLoadingView != null)
                mLoadingView.setVisibility(View.GONE);
            if (mContentView != null)
                mContentView.setVisibility(View.GONE);
            if (mEmptyView != null)
                mEmptyView.setVisibility(View.GONE);
        } else if (view == mContentView)
        {
            mContentView.setVisibility(View.VISIBLE);
            if (mLoadingView != null)
                mLoadingView.setVisibility(View.GONE);
            if (mRetryView != null)
                mRetryView.setVisibility(View.GONE);
            if (mEmptyView != null)
                mEmptyView.setVisibility(View.GONE);
        } else if (view == mEmptyView)
        {
            mEmptyView.setVisibility(View.VISIBLE);
            if (mLoadingView != null)
                mLoadingView.setVisibility(View.GONE);
            if (mRetryView != null)
                mRetryView.setVisibility(View.GONE);
            if (mContentView != null)
                mContentView.setVisibility(View.GONE);
        }


    }

    /**
     * Sets content view.
     *
     * @param layoutId the layout id
     * @return the content view
     */
    public View setContentView(int layoutId)
    {
        return setContentView(mInflater.inflate(layoutId, this, false));
    }

    /**
     * Sets loading view.
     *
     * @param layoutId the layout id
     * @return the loading view
     */
    public View setLoadingView(int layoutId)
    {
        return setLoadingView(mInflater.inflate(layoutId, this, false));
    }

    /**
     * Sets empty view.
     *
     * @param layoutId the layout id
     * @return the empty view
     */
    public View setEmptyView(int layoutId)
    {
        return setEmptyView(mInflater.inflate(layoutId, this, false));
    }

    /**
     * Sets retry view.
     *
     * @param layoutId the layout id
     * @return the retry view
     */
    public View setRetryView(int layoutId)
    {
        return setRetryView(mInflater.inflate(layoutId, this, false));
    }

    /**
     * Sets loading view.
     *
     * @param view the view
     * @return the loading view
     */
    public View setLoadingView(View view)
    {
        View loadingView = mLoadingView;
        if (loadingView != null)
        {
            Log.w(TAG, "you have already set a loading view and would be instead of this new one.");
        }
        removeView(loadingView);
        addView(view);
        mLoadingView = view;
        return mLoadingView;
    }

    /**
     * Sets empty view.
     *
     * @param view the view
     * @return the empty view
     */
    public View setEmptyView(View view)
    {
        View emptyView = mEmptyView;
        if (emptyView != null)
        {
            Log.w(TAG, "you have already set a empty view and would be instead of this new one.");
        }
        removeView(emptyView);
        addView(view);
        mEmptyView = view;
        return mEmptyView;
    }

    /**
     * Sets retry view.
     *
     * @param view the view
     * @return the retry view
     */
    public View setRetryView(View view)
    {
        View retryView = mRetryView;
        if (retryView != null)
        {
            Log.w(TAG, "you have already set a retry view and would be instead of this new one.");
        }
        removeView(retryView);
        addView(view);
        mRetryView = view;
        return mRetryView;

    }

    /**
     * Sets content view.
     *
     * @param view the view
     * @return the content view
     */
    public View setContentView(View view)
    {
        View contentView = mContentView;
        if (contentView != null)
        {
            Log.w(TAG, "you have already set a retry view and would be instead of this new one.");
        }
        removeView(contentView);
        addView(view);
        mContentView = view;
        return mContentView;
    }

    /**
     * Gets retry view.
     *
     * @return the retry view
     */
    public View getRetryView()
    {
        return mRetryView;
    }

    /**
     * Gets loading view.
     *
     * @return the loading view
     */
    public View getLoadingView()
    {
        return mLoadingView;
    }

    /**
     * Gets content view.
     *
     * @return the content view
     */
    public View getContentView()
    {
        return mContentView;
    }

    /**
     * Gets empty view.
     *
     * @return the empty view
     */
    public View getEmptyView()
    {
        return mEmptyView;
    }
}
