package com.pax.view;

import android.content.Context;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.pax.edc.R;

/**
 * Created by liliang on 4/10 0010.
 */

public class TransTotalItemView extends LinearLayout {
    private TextView mTransType;
    private TextView mTotoalNum;
    private TextView mTotalAmount;

    /**
     *
     */
    public TransTotalItemView(Context context) {
        this(context, null);
    }

    /**
     *
     */
    public TransTotalItemView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     *
     */
    public TransTotalItemView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public TransTotalItemView(Context context, AttributeSet attrs, int defStyleAttr,
            int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }

    /**
     *
     */
    private void initView(Context context) {
        View view = inflate(context, R.layout.trans_total_item, this);
        mTransType = (TextView) view.findViewById(R.id.trans_type);
        mTotoalNum = (TextView) view.findViewById(R.id.total_sum);
        mTotalAmount = (TextView) view.findViewById(R.id.total_amount);
    }

    /**
     *
     */
    public void setData(String type, String num, String amount) {
        mTransType.setText(type);
        mTotoalNum.setText(num);
        mTotalAmount.setText(amount);
    }
}
