/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.view.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.os.Build;
import android.provider.Settings;
import android.view.KeyEvent;
import android.view.WindowManager;

import com.pax.commonlib.ActivityStack;
import com.pax.device.Device;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.mqtt.worker.PreciseAlarmScheduler;
import com.pax.pay.trans.btprinter.util.StringUtils;
import com.pax.pay.utils.Log8583Utils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.view.dialog.CustomAlertDialog.OnCustomClickListener;

public class DialogUtils {

    private DialogUtils() {
        //do nothing
    }

    /**
     * 提示错误信息
     *
     * @param msg
     * @param listener
     * @param timeout
     */
    public static void showErrMessage(final Context context, final String title, final String msg,
                                      final OnDismissListener listener, final int timeout) {
        if (context == null) {
            return;
        }

        FinancialApplication.getApp().runOnUiThread(() -> {
            CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.ERROR_TYPE, true, timeout);
            if (context == ActivityStack.getInstance().top()) {
                dialog.setTitleText(title);
                dialog.setContentText(msg);
                dialog.setCanceledOnTouchOutside(true);
                dialog.show();
                dialog.setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
                dialog.setOnDismissListener(listener);
                Device.beepErr();
            } else {
                dialog.dismiss();
            }
        });
    }

    //添加系统弹窗判断
    public static void showErrMessage(final Context context, final String title, final String msg,
                                      final OnDismissListener listener, final int timeout, boolean isSysDialog) {
        if (context == null) {
            return;
        }

        FinancialApplication.getApp().runOnUiThread(() -> {
            CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.ERROR_TYPE, true, timeout);
            if (context == ActivityStack.getInstance().top()) {
                dialog.setTitleText(title);
                dialog.setContentText(msg);
                dialog.setCanceledOnTouchOutside(true);
                if (isSysDialog) {
                    dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//设置为系统弹窗模式
                }
                dialog.show();
                dialog.setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
                dialog.setOnDismissListener(listener);
                Device.beepErr();
            } else {
                dialog.dismiss();
            }
        });
    }

    public static CustomAlertDialog showProcessingMessage(final Context context, final String title, final int timeout) {
        if (context == null) {
            return null;
        }
        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.PROGRESS_TYPE);
        if (timeout != -1) {
            if (title.equals(Utils.getString(R.string.querying_data_for_last_5_transactions))
                    || title.equals(Utils.getString(R.string.querying_data_for_current_batch_report)) ||
                    title.equals(Utils.getString(R.string.querying_data_for_last_batch_record))) {
                dialog.setOnTimeoutListener(dialog2 ->
                        showErrMessage(context, title, Utils.getString(R.string.mqtt_response_timeout),
                                null, Constants.FAILED_DIALOG_SHOW_TIME));
            } else {
                dialog.setOnTimeoutListener(dialog2 ->
                        showErrMessage(context, title, Utils.getString(R.string.timeout),
                                null, Constants.FAILED_DIALOG_SHOW_TIME));
            }
        }

        FinancialApplication.getApp().runOnUiThread(() -> {
            dialog.showContentText(false);
            dialog.setTitleText(title);
            dialog.setCanceledOnTouchOutside(false);
            dialog.setTimeout(timeout);
            dialog.show();
            dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {

                @Override
                public boolean onKey(DialogInterface dialog1, int keyCode, KeyEvent event) {
                    return keyCode == KeyEvent.KEYCODE_BACK;
                }
            });
        });
        return dialog;
    }

    /**
     * 单行提示成功信息
     *
     * @param title
     * @param listener
     * @param timeout
     */
    public static void showSuccMessage(final Context context, final String title,
                                       final OnDismissListener listener, final int timeout) {
        if (context == null) {
            return;
        }
        FinancialApplication.getApp().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.SUCCESS_TYPE, true, timeout);
                if (FinancialApplication.getCurrentETransType() != null) {
                    dialog.setTitleText(FinancialApplication.getCurrentETransType().getTransName());
                }
                if (StringUtils.isNullOrEmpty(title)) {
                    dialog.setContentText(context.getString(R.string.dialog_trans_succ));
                } else {
                    dialog.setContentText(title);
                    dialog.setTitleText("");
                }
                dialog.setCanceledOnTouchOutside(true);
                dialog.show();
                dialog.setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
                dialog.setOnDismissListener(listener);
                Device.beepOk();
            }
        });
    }

    /**
     * 两行提示成功信息
     *
     * @param context  the context
     * @param title    the title
     * @param msg      the msg
     * @param listener the listener
     * @param timeout  the timeout
     */
    public static void showSuccMessage(final Context context, final String title, final String msg,
                                       final OnDismissListener listener, final int timeout) {
        if (context == null) {
            return;
        }
        FinancialApplication.getApp().runOnUiThread(() -> {
            CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.SUCCESS_TYPE, true, timeout);
            dialog.setTitleText(context.getString(R.string.dialog_trans_succ_liff, title));
            if (msg != null && !msg.isEmpty()) {
                dialog.setContentText(msg);
            }
            dialog.setCanceledOnTouchOutside(true);
            dialog.show();
            dialog.setOnKeyListener((dialog1, keyCode, event) -> true);
            dialog.setOnDismissListener(listener);
            Device.beepOk();
        });
    }

    /**
     * 退出当前应用
     */
    public static void showExitAppDialog(final Context context) {
        showConfirmDialog(context, context.getString(R.string.exit_app), null, alertDialog -> {
            alertDialog.dismiss();
            //退出应用的时候关闭日志记录资源
            Log8583Utils.close();
            Device.enableStatusBar(true);
            Device.enableHomeRecentKey(true);
            Device.enablePhysicalPowerButton(true);
            Device.enableScreenShot(true);
            PreciseAlarmScheduler.cancelAlarm(FinancialApplication.getAppContext());
            ActivityStack.getInstance().popAll();
        });
    }

    public static void showKeyInjectErrorExitAppDialog(final Context context) {

        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);

        dialog.setCancelClickListener(null);
        dialog.setConfirmClickListener(alertDialog -> {
            alertDialog.dismiss();
            //退出应用的时候关闭日志记录资源
            Log8583Utils.close();
            Device.enableStatusBar(true);
            Device.enableHomeRecentKey(true);
            Device.enablePhysicalPowerButton(true);
            Device.enableScreenShot(true);
            ActivityStack.getInstance().popAll();
        });
        dialog.setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
        dialog.show();
        dialog.setNormalText(context.getString(R.string.exit_app));
        dialog.showCancelButton(false);
        dialog.showConfirmButton(true);
    }

    public static void showConfirmDialog(final Context context, final String content,
                                         final OnCustomClickListener cancelClickListener, final OnCustomClickListener confirmClickListener) {
        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);

        final OnCustomClickListener clickListener = CustomAlertDialog::dismiss;

        dialog.setCancelClickListener(cancelClickListener == null ? clickListener : cancelClickListener);
        dialog.setConfirmClickListener(confirmClickListener == null ? clickListener : confirmClickListener);
        dialog.setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
        dialog.show();
        dialog.setNormalText(content);
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
    }

    /**
     * 应用更新或者参数更新提示，点击确定则进行直接结算
     */
    public static boolean isUpdateDialogShowing = false;

    public static void showUpdateDialog(final Context context, final String prompt) {
        if (isUpdateDialogShowing) return;
        if (!Settings.canDrawOverlays(context)) {
            return;
        }
        new Thread(() -> FinancialApplication.getApp().runOnUiThread(() -> {
            final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);

            } else {
                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
            }
            dialog.setConfirmClickListener(alertDialog -> {
                dialog.dismiss();
                isUpdateDialogShowing = false;
            });
            dialog.setTitleText(context.getString(R.string.app_name));
            dialog.setContentText(prompt);
            dialog.setCancelable(false);
            dialog.showCancelButton(false);
            dialog.showConfirmButton(true);
            dialog.show();
            isUpdateDialogShowing = true;
        })).start();
    }

    public static void showSingleConfirmDialog(final Context context, final String content, final OnDismissListener listener) {
        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);
        Device.beepErr();
        dialog.show();
        dialog.setNormalText(content);
        dialog.setCancelable(false);
        dialog.setOnDismissListener(listener);
        dialog.showConfirmButton(true);
    }
}
