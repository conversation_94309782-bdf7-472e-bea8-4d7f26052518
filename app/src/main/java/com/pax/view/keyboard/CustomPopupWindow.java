/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.view.keyboard;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.PopupWindow;

public class CustomPopupWindow extends PopupWindow {

    private OnEnableDismissListener onEnableDismissListener;

    public CustomPopupWindow(Context context) {
        this(context, null);
    }

    public CustomPopupWindow(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomPopupWindow(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public CustomPopupWindow(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public CustomPopupWindow() {
        this(null, 0, 0);
    }

    public CustomPopupWindow(View contentView) {
        this(contentView, 0, 0);
    }

    public CustomPopupWindow(int width, int height) {
        this(null, width, height);
    }

    public CustomPopupWindow(View contentView, int width, int height) {
        this(contentView, width, height, false);
    }

    public CustomPopupWindow(View contentView, int width, int height, boolean focusable) {
        super(contentView, width, height, focusable);
    }

    @Override
    public void dismiss() {
        if (onEnableDismissListener != null && onEnableDismissListener.onEnableDismiss()) {
            super.dismiss();
        }
    }

    public void forceDismiss() {
        super.dismiss();
    }

    public void setOnEnableDismissListener(OnEnableDismissListener onEnableDismissListener) {
        this.onEnableDismissListener = onEnableDismissListener;
    }

    /**
     * Listener that is called when this popup window is dismissed.
     */
    public interface OnEnableDismissListener {
        /**
         * Called when this popup window is dismissed.
         */
        boolean onEnableDismiss();
    }
}
