package com.pax.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.VideoView;


public class FullScreenVideo extends VideoView {
    private boolean needFullScreen;

    public FullScreenVideo(Context context) {
        super(context);
    }

    public FullScreenVideo(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void setNeedFullScreen(boolean needFullScreen){
        this.needFullScreen = needFullScreen;
    }
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if(needFullScreen) {
            int width = getDefaultSize(0, widthMeasureSpec);
            int height = getDefaultSize(0, heightMeasureSpec);
            setMeasuredDimension(width, height);
        }
    }
}