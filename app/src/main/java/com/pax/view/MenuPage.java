/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.pax.pay.app.FinancialApplication;
import com.pax.pay.app.quickclick.QuickClickProtection;
import com.pax.sbipay.R;
import com.pax.view.GridViewAdapter.GridItem;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 菜单选项
 *
 * <AUTHOR>
 */
public class MenuPage extends LinearLayout {
    private Context context;

    /*
     * 菜单项列表
     */
    private List<GridItem> itemList;

    private ViewPager mViewPager;
    /*
     * 页面指示器（。。。）容器
     */
    private LinearLayout pageIndicatorLayout;
    /*
     * 页面指示器（。。。）
     */
    private ImageView[] pageIndicator;
    /*
     * 总页面数
     */
    private int numPages;
    /*
     * 当前页面索引
     */
    private int currentPageIndex;
    /*
     * 每页最大显示item数目
     */
    private int maxItemNumPerPage = 30;
    /*
     * 列数
     */
    private int columns = 3;


    private QuickClickProtection quickClickProtection = QuickClickProtection.getInstance();
    private OnProcessListener processListener;
    private OnProcessTitleListener titleListener;

    public MenuPage(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        this.itemList = new ArrayList<>();
        initView();
    }

    public MenuPage(Context context, int maxItemNumPerPage, int columns, List<GridItem> list) {
        super(context);
        this.context = context;
        this.columns = columns;
        this.maxItemNumPerPage = maxItemNumPerPage;
        this.itemList = list;
        initView();
        initPageIndicator();
        initOptionsMenu();
    }

    /**
     * 初始化视图
     */
    private void initView() {
        View view = LayoutInflater.from(context).inflate(R.layout.fragment_menu, null);
        mViewPager = view.findViewById(R.id.view_pager);
        pageIndicatorLayout = view.findViewById(R.id.ll_dots);
        addView(view);
    }

    /**
     * 设置当前页面指示器
     *
     * @param position
     */
    private void setCurrentIndicator(int position) {
        if (position < 0 || position > numPages - 1 || currentPageIndex == position) {
            return;
        }
        for (ImageView i : pageIndicator) {
            i.setImageDrawable(FinancialApplication.getApp().getResources().getDrawable(R.drawable.guide_dot_normal));
        }
        pageIndicator[position].setImageDrawable(FinancialApplication.getApp().getResources().getDrawable(R.drawable.guide_dot_select));
        currentPageIndex = position;
    }

    /**
     * 点击菜单项目处理
     *
     * @param index
     */
    private void process(int index) {
        if (processListener != null) {
            processListener.process(index);
        } else if (titleListener != null && itemList.size() > index) {
            titleListener.process(itemList.get(index).getName());
        }
    }

    // 初始化选项菜单
    public void initOptionsMenu() {
        ViewPagerAdapter adapter = new ViewPagerAdapter(context, numPages, itemList,
                maxItemNumPerPage, columns,
                position -> {
                    // 连续多次点击未处理
                    if (quickClickProtection.isStarted()) {
                        return;
                    }
                    quickClickProtection.start();
                    int actualPosition = position + currentPageIndex * maxItemNumPerPage;
                    if (actualPosition < itemList.size()) {
                        process(actualPosition);
                    }
                });
        mViewPager.setAdapter(adapter);
        mViewPager.setOffscreenPageLimit(1);
    }

    /**
     * 设置ViewPager显示position指定的页面
     *
     * @param position
     */
    public void setCurrentPager(int position) {
        mViewPager.setCurrentItem(position);
    }

    // 初始化指示点
    public void initPageIndicator() {
        if (itemList.size() % maxItemNumPerPage == 0) {
            numPages = itemList.size() / maxItemNumPerPage;
        } else {
            numPages = itemList.size() / maxItemNumPerPage + 1;
        }
        if (0 < numPages) {
            pageIndicatorLayout.removeAllViews();
            if (1 == numPages) {
                pageIndicatorLayout.setVisibility(View.GONE);
            } else if (1 < numPages) {
                pageIndicatorLayout.setVisibility(View.VISIBLE);
                for (int j = 0; j < numPages; j++) {
                    ImageView image = new ImageView(context);
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(20, 20); // dot的宽高
                    params.setMargins(10, 0, 10, 0);
                    image.setImageDrawable(FinancialApplication.getApp().getResources().getDrawable(R.drawable.guide_dot_normal));
                    pageIndicatorLayout.addView(image, params);
                }
            }
        }
        if (numPages != 1) {
            pageIndicator = new ImageView[numPages];
            for (int i = 0; i < numPages; i++) {
                pageIndicator[i] = (ImageView) pageIndicatorLayout.getChildAt(i);
            }
            currentPageIndex = 0;
            pageIndicator[currentPageIndex].setImageDrawable(FinancialApplication.getApp().getResources().getDrawable(R.drawable.guide_dot_select));

            mViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageSelected(int position) {
                    setCurrentIndicator(position);
                }

                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                    // 不需要实现
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                    // 不需要实现
                }
            });
        }
    }

    /**
     * 回炉再造，从MenuPage变回Builder
     *
     * @return
     */
    public Builder newBuilder() {
        return new Builder(this.context, this.maxItemNumPerPage, this.columns, this.itemList);
    }

    public void setOnProcessListener(OnProcessListener processListener) {
        this.processListener = processListener;
    }

    public void setOnProcessTitleListener(OnProcessTitleListener processListener) {
        this.titleListener = processListener;
    }

    public interface OnProcessListener {
        void process(int index);
    }

    public interface OnProcessTitleListener {
        void process(String title);
    }

    public static class Builder {
        private Context context;
        private int maxItemNumPerPage;
        private int columns;
        private List<GridItem> itemList;

        public Builder(Context context, int maxItemNumPerPage, int columns) {
            this.context = context;
            this.maxItemNumPerPage = maxItemNumPerPage;
            this.columns = columns;
        }

        public Builder(Context context, int maxItemNumPerPage, int columns, List<GridItem> itemList) {
            this.context = context;
            this.maxItemNumPerPage = maxItemNumPerPage;
            this.columns = columns;
            this.itemList = new ArrayList<>();
            for (GridItem gi : itemList) {
                this.itemList.add(gi);
            }
        }

        /**
         * 设置与交易相关的菜单项
         *
         * @param title 菜单项的名称
         * @param icon  菜单项的图片ID
         * @return
         */
        public Builder addTransItem(String title, int icon) {
            if (itemList == null) {
                itemList = new ArrayList<>();
            }
            itemList.add(new GridItem(title, icon));
            return this;
        }

        /**
         * 设置与交易无关的菜单项,只负责Activity的跳转
         *
         * @param title 菜单项的名称
         * @param icon  菜单项的图片ID
         * @return
         */
        public Builder addMenuItem(String title, int icon) {
            if (itemList == null) {
                itemList = new ArrayList<>();
            }
            itemList.add(new GridItem(title, icon));
            return this;
        }

        /**
         * 按菜单项的名称来移除，后续可根据需求增加不同参数的removeMenuItem方法
         *
         * @param title
         * @return
         */
        public Builder removeMenuItem(String title) {
            Iterator<GridItem> it = itemList.iterator();
            while (it.hasNext()) {
                if (it.next().getName().equals(title)) {
                    it.remove();
                }
            }
            return this;
        }

        /**
         * 设置非交易类,使用Action跳转的菜单项
         *
         * @param title 菜单项的名称
         * @param icon  菜单项的图片ID
         * @return
         */
        public Builder addActionItem(String title, int icon) {
            if (itemList == null) {
                itemList = new ArrayList<>();
            }
            itemList.add(new GridItem(title, icon));
            return this;
        }

        /**
         * 创建并返回MenuPage视图
         *
         * @return
         */
        public MenuPage create() {
            return new MenuPage(context, maxItemNumPerPage, columns, itemList);
        }
    }
}