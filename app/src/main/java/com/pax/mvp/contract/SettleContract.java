/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         xieYb                   Create
 * ===========================================================================================
 */
package com.pax.mvp.contract;

import android.content.Context;

import com.pax.abl.core.ActionResult;
import com.pax.base.presenter.impl.BasePresenter;
import com.pax.base.view.IView;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransTotal;
import com.pax.pay.trans.transmit.TransProcessListener;

import java.util.ArrayList;

public interface SettleContract {
    interface View extends IView {
        void setCurrAcquirerContent(String acquirerName,
                                    Acquirer acquirer,
                                    String saleAmt,
                                    String refundAmt,
                                    String voidSaleAmt,
                                    String voidRefundAmt,
                                    String yonoTotalAmt,
                                    String bqrAmt,
                                    String reversalAmt,
                                    TransTotal total,
                                    String emiSaleAmt,
                                    String emiVoidSaleAmt);

        void finish(ActionResult result);

        void printFailDetail();
    }

    abstract class Presenter extends BasePresenter<View> {
        protected Presenter(Context context) {
            super(context);
        }

        public abstract void init(ArrayList<String> selectAcqs);

        public abstract void doSettlement();

        public abstract void setCurrAcquirerContent(String acquirerName, TransProcessListener transProcessListenerImpl);

    }
}
