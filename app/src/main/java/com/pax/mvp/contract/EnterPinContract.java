/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         XuShuang                Create
 * ===========================================================================================
 */

package com.pax.mvp.contract;

import android.content.Context;

import com.pax.abl.core.ActionResult;
import com.pax.base.presenter.impl.BasePresenter;
import com.pax.base.view.IView;
import com.pax.dal.exceptions.PedDevException;
import com.pax.pay.trans.action.ActionEnterPin;

import org.jetbrains.annotations.NotNull;


public interface EnterPinContract {
    interface View extends IView {
        void actionFinish(@NotNull ActionResult result);

        void showFlingNotice();

        void showErrorDialog(@NotNull PedDevException e);

        String getText();

        void setText(String temp);
    }

    abstract class Presenter extends BasePresenter<EnterPinContract.View> {
        public Presenter(Context context) {
            super(context);
        }

        public abstract void startDetectFingerR(String panBlock, boolean supportBypass, @NotNull ActionEnterPin.EEnterPinType enterPinType);
    }
}
