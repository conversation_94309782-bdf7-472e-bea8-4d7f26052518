/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         xieYb                   Create
 * ===========================================================================================
 */

package com.pax.mvp.presenter;


import android.app.Activity;
import android.content.Context;
import android.os.ConditionVariable;

import com.pax.abl.core.ActionResult;
import com.pax.commonlib.Statistic;
import com.pax.commonlib.StatisticManager;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.TransData;
import com.pax.data.entity.TransTotal;
import com.pax.data.local.GreendaoHelper;
import com.pax.data.local.db.helper.SQRDataDbHelper;
import com.pax.device.Device;
import com.pax.mvp.contract.SettleContract;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.record.Printer;
import com.pax.pay.trans.TransResult;
import com.pax.pay.trans.action.activity.SettleActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.transmit.TransOnline;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.RxUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public class SettlePresenter extends SettleContract.Presenter {
    final ConditionVariable cv = new ConditionVariable();
    public ArrayList<String> selectAcqs = new ArrayList();
    public Acquirer acquirer;
    private TransTotal total = new TransTotal();
    //AET-41
    private String acquirerDef;
    private final WeakReference<Context> weakReference;

    public SettlePresenter(Context context) {
        super(context);
        this.weakReference = new WeakReference<>(context);
    }

    @Override
    public void init(ArrayList<String> selectAcqs) {
        //AET-41
        acquirerDef = FinancialApplication.getAcqManager().getCurAcq().getName();
        this.selectAcqs = selectAcqs;
    }

    @Override
    public void doSettlement() {
        ((SettleActivity) weakReference.get()).setForbidBack(true);
        FinancialApplication.getApp().runInBackground(new Runnable() {

            private String genPromptMsg(int cnt, int total) {
                return Utils.getString(R.string.settle_settled) + "[" + cnt + "/" + total + "]";
            }

            @Override
            public void run() {
                TransProcessListener transProcessListenerImpl = new TransProcessListenerImpl(
                        getContext());
                try {
                    int ret;
                    int cnt = 0;
                    for (final String i : selectAcqs) {
                        setCurrAcquirerContent(i, transProcessListenerImpl);
                        cv.close();
                        cv.block();
                        //check if zero total
                        if (total.isZero()) {
                            transProcessListenerImpl.onShowErrMessage(Utils.getString(R.string.err_no_trans), Constants.FAILED_DIALOG_SHOW_TIME, false);
                            continue;
                        }
                        // 结算
                        ret = new TransOnline().settle(total, transProcessListenerImpl);
                        transProcessListenerImpl.onHideProgress();
                        if (ret != TransResult.SUCC && ret != TransResult.SUCC_NOREQ_BATCH) {
                            proxyView.finish(new ActionResult(ret, null));
                            return;
                        }

                        ++cnt;
                        // 记上批总计，置清除交易记录标志
                        total.setAcquirer(acquirer);
                        total.setMerchantID(acquirer.getMerchantId());
                        total.setTerminalID(acquirer.getTerminalId());
                        total.setBatchNo(SysParam.getInstance().getInt(R.string.EDC_BATCH_NO));
                        total.setDateTime(Device.getTime(Constants.TIME_PATTERN_TRANS));
                        total.setClosed(true);
                        GreendaoHelper.getTransTotalHelper().insert(total);
                        // 打印结算单
                        Printer.printSettle((Activity) getContext(), getContext().getString(R.string.history_total), total);

                        // 打印失败明细
                        long failed = GreendaoHelper.getTransDataHelper().findFailedTransDataCount(acquirer);
                        if (failed != 0) {
                            proxyView.printFailDetail();
                        }

                        saveTransCount();

                        // 批上送结算,将批上送断点赋值为0
                        FinancialApplication.getController().set(Controller.BATCH_UP_STATUS, Controller.Constant.WORKED);
                        // 清除交易流水
                        if (GreendaoHelper.getTransDataHelper().deleteAllTransData(acquirer) &&
                                GreendaoHelper.getTransDataHelper().deleteReverSuccPreAuth(acquirer)) {
                            //结算成功之后同时也删除MQTT交易
                            SQRDataDbHelper.getInstance().deleteAllSQRData();
                            Component.incBatchNo();
                        }
                    }
                    new TransOnline().statistic(transProcessListenerImpl);
                    StatisticManager.getInstance().resetStatistic();
                    if (!FinancialApplication.isAutoSettlement()) {
                        transProcessListenerImpl.onShowNormalMessage(genPromptMsg(cnt, selectAcqs.size()), Constants.SUCCESS_DIALOG_SHOW_TIME, true);
                    } else {
                        transProcessListenerImpl.onHideProgress();
                    }
                    proxyView.finish(new ActionResult(TransResult.SUCC, null));
                } catch (PedDevException e) {
                    transProcessListenerImpl.onShowErrMessage(e.getMessage(),
                            Constants.FAILED_DIALOG_SHOW_TIME, false);
                } finally {
                    //AET-41, AET-62, AET-280
                    if (weakReference.get() != null) {
                        ((SettleActivity) weakReference.get()).setForbidBack(false);
                    }
                    FinancialApplication.getAcqManager().setCurAcq(FinancialApplication.getAcqManager().findAcquirer(acquirerDef));
                }
            }
        });
    }

    private void saveTransCount() {
        List<ETransType> list = new ArrayList<>();
        list.add(ETransType.SALE);
        list.add(ETransType.PREAUTHCOMPLETE);
        list.add(ETransType.CASH_ONLY);
        list.add(ETransType.SALE_CASH);
        list.add(ETransType.OFFLINE_TRANS_SEND);
        list.add(ETransType.QSPARC_MONEY_ADD_ACCOUNT);
        list.add(ETransType.QSPARC_MONEY_ADD_CASH);
        list.add(ETransType.BANK_EMI_SALE);
        list.add(ETransType.BRAND_EMI_SALE);
        //failed offline transaction status
        ArrayList<TransData.OfflineStatus> filterForOffline = new ArrayList<>();
        filterForOffline.add(TransData.OfflineStatus.OFFLINE_ERR_SEND);
        filterForOffline.add(TransData.OfflineStatus.OFFLINE_ERR_RESP);
        filterForOffline.add(TransData.OfflineStatus.OFFLINE_ERR_UNKNOWN);

        Acquirer curAcq = FinancialApplication.getAcqManager().getCurAcq();
        int countOf = (int) GreendaoHelper.getTransDataHelper().countOf(list, filterForOffline, curAcq);
        Statistic statistic = StatisticManager.getInstance().getStatistic();
        statistic.setTransCount(countOf);
        StatisticManager.getInstance().setStatistic(statistic);
    }

    @Override
    public void setCurrAcquirerContent(String acquirerName, TransProcessListener transProcessListenerImpl) {
        acquirer = FinancialApplication.getAcqManager().findAcquirer(acquirerName);
        if (acquirer == null) {
            return;
        }
        ///set current acquirer,settle print need it
        FinancialApplication.getAcqManager().setCurAcq(acquirer);
        transProcessListenerImpl.onShowProgress(Utils.getString(R.string.wait_process), SysParam.getInstance().getInt(R.string.COMM_TIMEOUT));
        RxUtils.release();
        RxUtils.addDisposable(Observable.create((ObservableOnSubscribe<TransTotal>) emitter -> {
                    //statistic for total in background
                    TransTotal total = GreendaoHelper.getTransTotalHelper().calcTotal(acquirer);
                    emitter.onNext(total);
                    emitter.onComplete();
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(totalTrans -> {
                            total = totalTrans;
                            transProcessListenerImpl.onHideProgress();
                            String saleAmt = CurrencyConverter.convert(total.getSaleTotalAmt());
                            //AET-18
                            String refundAmt = CurrencyConverter.convert(-total.getRefundTotalAmt());
                            String voidSaleAmt = CurrencyConverter.convert(-total.getSaleVoidTotalAmt());
                            String voidRefundAmt = CurrencyConverter.convert(total.getRefundVoidTotalAmt());
                            String offlineAmt = CurrencyConverter.convert(total.getOfflineTotalAmt());
                            //新增Yono sale,Yono cash以及bharat qr统计
                            String yonoTotalAmt = CurrencyConverter.convert(total.getYonoCashTotalAmount() + total.getYonoSaleTotalAmount());
                            String bqrAmt = CurrencyConverter.convert(total.getBqrTotalAmount());
                            String reversalAmt = CurrencyConverter.convert(total.getReversalTotalAmount());

                            //新增EMI交易统计
                            String emiSaleAmt = CurrencyConverter.convert(total.getEmiSaleTotalAmount());
                            String emiVoidSaleAmt = CurrencyConverter.convert(total.getEmiVoidTotalAmount());

                            proxyView.setCurrAcquirerContent(acquirerName, acquirer, saleAmt, refundAmt, voidSaleAmt, voidRefundAmt,
                                    yonoTotalAmt, bqrAmt, reversalAmt, total, emiSaleAmt, emiVoidSaleAmt);
                            cv.open();
                        }
                ));
    }
}
