package com.pax.settings.adapter;

import android.content.Context;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.adapter.CommonAdapter;
import com.pax.adapter.ViewHolder;
import com.pax.pay.app.quickclick.QuickClickProtection;
import com.pax.sbipay.R;
import com.pax.settings.entity.ProviderEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/2
 */
public class ConfigProviderAdapter extends CommonAdapter<Pair<String,String>> {

    public ConfigProviderAdapter(Context context, int layoutId,List<Pair<String,String>> providerEntityList) {
        super(context, layoutId, providerEntityList);
    }

    @Override
    protected void convert(ViewHolder holder, Pair<String, String> stringStringPair, int position) {
        holder.setText(R.id.provider_config_key, stringStringPair.first);
        holder.setText(R.id.provider_config_value, stringStringPair.second);
    }
}
