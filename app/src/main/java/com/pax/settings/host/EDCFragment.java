/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-1-10
 * Module Author: xiawh
 * Description:
 *
 * ============================================================================
 */
package com.pax.settings.host;

import android.preference.CheckBoxPreference;
import android.preference.EditTextPreference;
import android.preference.ListPreference;
import android.preference.MultiSelectListPreference;
import android.preference.RingtonePreference;

import androidx.annotation.XmlRes;

import android.text.InputType;
import android.util.Log;

import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.emv.EmvAid;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.BasePreferenceFragment;
import com.pax.settings.SysParam;
import com.pax.view.keyboard.KeyboardUtils;
import com.pax.view.widget.BaseWidget;

import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;

public class EDCFragment extends BasePreferenceFragment {

    private static CharSequence[] entries;
    private static CharSequence[] entryValues;

    /**
     * AET-116
     */
    private static void updateEntries() {
        Map<String, String> allEntries = new TreeMap<>();
        List<Locale> locales = CurrencyConverter.getSupportedLocale();
        for (Locale i : locales) {
            try {
                Currency currency = Currency.getInstance(i);
                Log.i(TAG, i.getISO3Country()
                        + "  "
                        + i.getDisplayName(Locale.US)
                        + " "
                        + currency.getDisplayName(Locale.US));
                allEntries.put(i.getDisplayName(Locale.US) + " " + currency.getDisplayName(),
                        i.getDisplayName(Locale.US));
            } catch (IllegalArgumentException e) {
                Log.d(TAG, "", e);
            }
        }
        entries = allEntries.keySet().toArray(new CharSequence[allEntries.size()]);
        entryValues = allEntries.values().toArray(new CharSequence[allEntries.size()]);
    }

    @Override
    @XmlRes
    protected int getResourceId() {
        return R.xml.edc_para_pref;
    }

    /**
     * bind data with view
     */
    @Override
    protected void initPreference() {
        updateEntries();

        bindListPreference(SysParam.StringParam.EDC_CURRENCY_LIST, entries, entryValues);

        bindPreference(SysParam.StringParam.EDC_MERCHANT_NAME_EN);
        bindPreference(SysParam.StringParam.EDC_MERCHANT_ADDRESS);
        bindPreference(SysParam.StringParam.EDC_CURRENCY_LIST);
        bindPreference(SysParam.StringParam.EDC_PED_MODE);
        bindPreference(SysParam.NumberParam.EDC_RECEIPT_NUM);
        bindPreference(SysParam.NumberParam.EDC_TRACE_NO);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_TIP);
        bindPreference(SysParam.BooleanParam.EDC_TIP_TYPE);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_REFERRAL);
        bindPreference(SysParam.NumberParam.EDC_CVM_LIMIT_VISA);
        bindPreference(SysParam.NumberParam.EDC_FLOOR_LIMIT_VISA);
        bindPreference(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_VISA);
        bindPreference(SysParam.NumberParam.EDC_CVM_LIMIT_MASTER);
        bindPreference(SysParam.NumberParam.EDC_FLOOR_LIMIT_MASTER);
        bindPreference(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_MASTER);
        bindPreference(SysParam.NumberParam.EDC_CVM_LIMIT_JCB);
        bindPreference(SysParam.NumberParam.EDC_FLOOR_LIMIT_JCB);
        bindPreference(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_JCB);
        bindPreference(SysParam.NumberParam.EDC_CVM_LIMIT_CUP);
        bindPreference(SysParam.NumberParam.EDC_FLOOR_LIMIT_CUP);
        bindPreference(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_CUP);
        bindPreference(SysParam.NumberParam.EDC_CVM_LIMIT_AMEX);
        bindPreference(SysParam.NumberParam.EDC_FLOOR_LIMIT_AMEX);
        bindPreference(SysParam.NumberParam.EDC_TRANSACTION_LIMIT_AMEX);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_PAPERLESS);
        bindPreference(SysParam.StringParam.EDC_SMTP_HOST);
        bindPreference(SysParam.NumberParam.EDC_SMTP_PORT);
        bindPreference(SysParam.StringParam.EDC_SMTP_USERNAME);
        bindPreference(SysParam.StringParam.EDC_SMTP_PASSWORD);
        bindPreference(SysParam.BooleanParam.EDC_SMTP_ENABLE_SSL);
        bindPreference(SysParam.NumberParam.EDC_SMTP_SSL_PORT);
        bindPreference(SysParam.StringParam.EDC_SMTP_FROM);
        bindPreference(SysParam.NumberParam.EDC_NO_SIGN_AMT_AMEX);
        bindPreference(SysParam.NumberParam.EDC_NO_SIGN_AMT_CUP);
        bindPreference(SysParam.NumberParam.EDC_NO_SIGN_AMT_MC);
        bindPreference(SysParam.NumberParam.EDC_NO_SIGN_AMT_VISA);
        bindPreference(SysParam.NumberParam.EDC_NO_SIGN_AMT_JCB);
        bindPreference(SysParam.StringParam.EDC_AMEX_ORIGIN);
        bindPreference(SysParam.StringParam.EDC_AMEX_REGION);
        bindPreference(SysParam.StringParam.EDC_AMEX_COUNTRY);
        bindPreference(SysParam.StringParam.EDC_AMEX_MESSAGE);
        bindPreference(SysParam.StringParam.EDC_AMEX_RTIND);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_UPI);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_PLC);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_EPS);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_MICROPAY);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_PARTYKING);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_FPS);
        bindPreference(SysParam.BooleanParam.EDC_HANDSET_ALERT);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_INTENDED_OFFLINE_MODE);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_DCC);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_FO);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_DCC_OPT_OUT);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_DCC_INC_AUTH);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_DCC_AUTH_REVERSAL);
        bindPreference(SysParam.BooleanParam.EDC_SUPPORT_ECR_CHOOSE_DCC);
        bindPreference(SysParam.BooleanParam.EDC_DCC_SALE_NEW_FLOW);
        bindPreference(SysParam.BooleanParam.EDC_DCC_AUTH_NEW_FLOW);
        bindPreference(SysParam.BooleanParam.EDC_INVERSE_RATE);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_REFUND_RRN);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_OFFLINE_RRN);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_PRINT_LOG);
        bindPreference(SysParam.BooleanParam.EDC_IS_PRODUCTION_MODE);
        bindPreference(SysParam.BooleanParam.EDC_SKIP_MERCHANT_COPY_VM);
        bindPreference(SysParam.BooleanParam.EDC_SKIP_MERCHANT_COPY_JCB);
        bindPreference(SysParam.BooleanParam.EDC_SKIP_MERCHANT_COPY_CUP);
        bindPreference(SysParam.BooleanParam.EDC_SKIP_MERCHANT_COPY_AMEX);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_CUP_SALE);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_CUP_REFUND);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_ECR_ONLY);
        bindPreference(SysParam.StringParam.EDC_MERCHANT_SELECTION);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_LYS_DISCLAIMER);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_VM_FALLBACK_BLOCK);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_SALE_TRANS_NO_SIGN);
        bindPreference(SysParam.BooleanParam.EDC_REJECT_CUP_TRANS_WITH_PIN);
        bindPreference(SysParam.BooleanParam.EDC_NOT_ENABLE_MANAGEMENT_PWD);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_QR_PAYMENT);
        bindPreference(SysParam.BooleanParam.SEND_PRINT_INFO);
        bindPreference(SysParam.NumberParam.EDC_FREE_TIME);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_PAYMENT_LOGO);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_SCREEN_SAVER);
        bindPreference(SysParam.BooleanParam.EDC_SCREEN_SAVER_VERTICAL_SCREEN);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_ECR_HEART_BEAT);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_WIFI_MANAGE);
        bindPreference(SysParam.NumberParam.EDC_ECR_RESP_MSG_DELAY);
        bindPreference(SysParam.NumberParam.EDC_INPUT_PASSWORD_TIMEOUT);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_CUP_ECR);
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_ECR_MASK_CARD_NO);
    }

    @Override
    protected boolean onListPreferenceChanged(ListPreference preference, Object value, boolean isInitLoading) {
        String stringValue = value.toString();
        int index = preference.findIndexOfValue(stringValue);

        if (SysParam.StringParam.EDC_CURRENCY_LIST.toString().equals(preference.getKey()) && index >= 0) {
            if (!isInitLoading && FinancialApplication.getTransDataDbHelper().countOf() > 0) {
                ToastUtils.showMessage(R.string.has_trans_for_settle);
                return false;
            } else {
                preference.setSummary(preference.getEntries()[index]);
                if (!isInitLoading && !CurrencyConverter.getDefCurrency().getCountry().equals(preference.getEntryValues()[index].toString())) {
                    BaseWidget.updateWidget(FinancialApplication.getApp());
                    //Currency与Language的关联
                    Utils.changeAppLanguage(FinancialApplication.getApp(), CurrencyConverter.setDefCurrency(preference.getEntryValues()[index].toString()));
                    Utils.restart();
                }
            }
            return true;
        }
        preference.setSummary(index >= 0 ? preference.getEntries()[index] : null);
        return true;
    }

    @Override
    protected boolean onCheckBoxPreferenceChanged(CheckBoxPreference preference, Object value, boolean isInitLoading) {
        return true;
    }

    @Override
    protected boolean onRingtonePreferenceChanged(RingtonePreference preference, Object value, boolean isInitLoading) {
        return true;
    }

    @Override
    protected boolean onEditTextPreferenceChanged(EditTextPreference preference, Object value, boolean isInitLoading) {
        String stringValue = value.toString();
        if (preference.getKey().contains("EDC_CVM_LIMIT") || preference.getKey().contains("EDC_FLOOR_LIMIT")
                || preference.getKey().contains("EDC_TRANSACTION_LIMIT") || preference.getKey().contains("EDC_NO_SIGN_AMT")) {
            if (stringValue.length() > 2) {
                stringValue = "$" + stringValue.substring(0, stringValue.length() - 2) + "." + stringValue.substring(stringValue.length() - 2);
            } else if (stringValue.length() == 2) {
                stringValue = "$0." + stringValue;
            } else if (stringValue.length() == 1) {
                stringValue = "$0.0" + stringValue;
            } else {
                stringValue = "$0.00";
            }
        }
        if ((preference.getEditText().getInputType() & InputType.TYPE_TEXT_VARIATION_PASSWORD) == InputType.TYPE_TEXT_VARIATION_PASSWORD) {
            String temp = !stringValue.isEmpty() ? "******" : stringValue;
            preference.setSummary(temp);
        } else {
            preference.setSummary(stringValue);
        }
        KeyboardUtils.hideSystemKeyboard(getActivity(), preference.getEditText()); //AET-206
        return true;
    }

    @Override
    protected boolean onMultiSelectListPreferenceChanged(MultiSelectListPreference preference, Object value, boolean isInitLoading) {
        return false;
    }

    @Override
    public void onStop() {
        super.onStop();
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                EmvAid.updateClssLimit();
            }
        });
    }
}
