/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-1-10
 * Module Author: xiawh
 * Description:
 *
 * ============================================================================
 */
package com.pax.settings.host;

import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TextView.BufferType;
import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.trans.component.Component;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.TextValueWatcher;
import com.pax.pay.utils.ToastUtils;
import com.pax.settings.BaseFragment;
import com.pax.settings.NewSpinnerAdapter;
import com.pax.settings.SysParam.Constant.CommSslType;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

public class AcquirerFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener {

    private Acquirer acquirer;

    private NewSpinnerAdapter<Acquirer> adapter;
    private NewSpinnerAdapter<Integer> insPlanAdapter;

    private RelativeLayout rlAcqRefNo;

    private EditText etTerminalId;
    private EditText etMerchantId;
    private EditText etNii;
    private EditText etBatch;

    private EditText etIp;
    private EditText etPort;
    private EditText etUrl;

    private EditText etIp2;
    private EditText etPort2;
    private EditText etUrl2;
    private EditText etDescription;

    private TextView ipLabel;
    private TextView ipLabel2;

    private CheckBox enableTrickFeed;
    private CheckBox enableQr;
    private CheckBox enableRefNo;
    private Spinner sslSpinner;
    private Spinner insPlanSpinner;
    //AET-63
    private boolean isFirst = true;

    private static final int UPDATE_URL = 1;

    private String ip1;
    private String ip2;

    private RelativeLayout rlAcqDescription;
    private RelativeLayout rlAcqInstalPlan;

    private Handler handler = new Handler() {
        public void handleMessage(Message msg) {
            if (msg.what == UPDATE_URL) {
                update(ip1, ipLabel, etIp);
                update(ip2, ipLabel2, etIp2);
            }
        }
    };

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_acquirer_details;
    }

    /**
     * init data
     */
    @Override
    protected void initData() {

        List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();
        acquirer = listAcquirers.get(0);

        adapter = new NewSpinnerAdapter<>(this.context);
        adapter.setListInfo(listAcquirers);
        adapter.setOnTextUpdateListener(new NewSpinnerAdapter.OnTextUpdateListener() {
            @Override
            public String onTextUpdate(final List<?> list, int position) {
                return ((Acquirer) list.get(position)).getName();
            }
        });

        insPlanAdapter = new NewSpinnerAdapter<>(this.context);
        List<Integer> instalmentPlans = new ArrayList<>();
        for (int i = 1; i < 100; i++) {
            instalmentPlans.add(i);
        }
        insPlanAdapter.setListInfo(instalmentPlans);
        insPlanAdapter.setOnTextUpdateListener(new NewSpinnerAdapter.OnTextUpdateListener() {
            @Override
            public String onTextUpdate(List<?> list, int position) {
                return String.valueOf(list.get(position));
            }
        });
    }

    @Override
    protected void initView(View view) {
        Spinner spinner = (Spinner) view.findViewById(R.id.acquirer_list);
        spinner.setAdapter(adapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view,
                                       int pos, long id) {
                Acquirer newAcquirer = adapter.getListInfo().get(pos);
                if (newAcquirer.getId() != acquirer.getId()) {
                    //AET-36
                    FinancialApplication.getAcqManager().updateAcquirer(acquirer);
                    acquirer = newAcquirer;
                    updateItemsValue();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Another interface callback
            }
        });

        if (!adapter.getListInfo().isEmpty()) {
            updateItems(view);
            updateItemsValue();
        } else {
            view.findViewById(R.id.acquirer_details).setVisibility(View.GONE);
        }
    }

    /**
     * init items view
     */
    private void updateItems(View view) {
        etTerminalId = (EditText) view.findViewById(R.id.terminal_id);
        etTerminalId.addTextChangedListener(new Watcher(R.id.terminal_id));

        etMerchantId = (EditText) view.findViewById(R.id.merchant_id);
        etMerchantId.addTextChangedListener(new Watcher(R.id.merchant_id));

        etNii = (EditText) view.findViewById(R.id.nii_acq);
        etNii.addTextChangedListener(new Watcher(R.id.nii_acq));

        etBatch = (EditText) view.findViewById(R.id.batch_num);
        etBatch.addTextChangedListener(new Watcher(R.id.batch_num));

        etIp = (EditText) view.findViewById(R.id.acq_ip);

        etIp2 = (EditText) view.findViewById(R.id.acq_ip2);

        TextValueWatcher<Integer> textValueWatcher = new TextValueWatcher<>(0, 65535);
        textValueWatcher.setOnCompareListener(new TextValueWatcher.OnCompareListener() {
            @Override
            public boolean onCompare(String value, Object min, Object max) {
                int temp = Integer.parseInt(value);
                return temp >= (int) min && temp <= (int) max;
            }
        });
        textValueWatcher.setOnTextChangedListener(new TextValueWatcher.OnTextChangedListener() {
            @Override
            public void afterTextChanged(String value) {
                acquirer.setPort(Integer.parseInt(value));
            }
        });
        etPort = (EditText) view.findViewById(R.id.acq_ip_port);
        etPort.addTextChangedListener(textValueWatcher);

        TextValueWatcher<Integer> textValueWatcherBackups = new TextValueWatcher<>(0, 65535);
        textValueWatcherBackups.setOnCompareListener(new TextValueWatcher.OnCompareListener() {
            @Override
            public boolean onCompare(String value, Object min, Object max) {
                int temp = Integer.parseInt(value);
                return temp >= (int) min && temp <= (int) max;
            }
        });
        textValueWatcherBackups.setOnTextChangedListener(new TextValueWatcher.OnTextChangedListener() {
            @Override
            public void afterTextChanged(String value) {
                acquirer.setPortBak2(Integer.parseInt(value));
            }
        });
        etPort2 = (EditText) view.findViewById(R.id.acq_ip_port2);
        etPort2.addTextChangedListener(textValueWatcherBackups);

        etUrl = (EditText) view.findViewById(R.id.acq_url);
        etUrl.addTextChangedListener(new Watcher(R.id.acq_url));
        etUrl.setOnEditorActionListener(new UrlToIpActionListener());

        ipLabel = (TextView) view.findViewById(R.id.ip_label);

        etUrl2 = (EditText) view.findViewById(R.id.acq_url2);
        etUrl2.addTextChangedListener(new Watcher(R.id.acq_url2));
        etUrl2.setOnEditorActionListener(new UrlToIpActionListener());

        ipLabel2 = (TextView) view.findViewById(R.id.ip_label2);

        enableTrickFeed = (CheckBox) view.findViewById(R.id.acquirer_disable_trick_feed);
        enableTrickFeed.setOnCheckedChangeListener(AcquirerFragment.this);

        enableQr = (CheckBox) view.findViewById(R.id.acq_support_qr);
        enableQr.setOnCheckedChangeListener(AcquirerFragment.this);

        enableRefNo = (CheckBox) view.findViewById(R.id.acq_support_refno);
        enableRefNo.setOnCheckedChangeListener(AcquirerFragment.this);

        rlAcqRefNo = (RelativeLayout) view.findViewById(R.id.rl_acq_support_refno);

        rlAcqDescription = (RelativeLayout) view.findViewById(R.id.rl_acq_description);
        etDescription = (EditText) view.findViewById(R.id.acq_description);
        etDescription.addTextChangedListener(new Watcher(R.id.acq_description));
        rlAcqInstalPlan = (RelativeLayout) view.findViewById(R.id.rl_acq_instalment_plan);

        sslSpinner = (Spinner) view.findViewById(R.id.acquirer_ssl_type);
        sslSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view,
                    int pos, long id) {
                String[] sslType = getResources().getStringArray(R.array.acq_ssl_type_list_values);
                CommSslType selectedType = CommSslType.valueOf(sslType[pos]);
                if ((acquirer.getSslType() != selectedType)) {
                    acquirer.setSslType(selectedType);
                    FinancialApplication.getAcqManager().updateAcquirer(acquirer);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Another interface callback
            }
        });

        insPlanSpinner = (Spinner) view.findViewById(R.id.instalment_plan_list);
        insPlanSpinner.setAdapter(insPlanAdapter);

        insPlanSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                int instalmentPlan = insPlanAdapter.getListInfo().get(position);
                if (acquirer.getInstalPlan() != instalmentPlan) {
                    acquirer.setInstalPlan(instalmentPlan);
                    FinancialApplication.getAcqManager().updateAcquirer(acquirer);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    /**
     * set url to ip
     */
    private class UrlToIpActionListener extends EditorActionListener {
        @Override
        protected void onKeyOk() {
        }

        @Override
        protected void onKeyCancel() {
        }

        @Override
        protected void onKeyNext() {
            if (etUrl.isFocused()) {
                resolveUrl(etUrl.getText().toString().trim(), etIp);
                etPort.setFocusable(true);
                etPort.requestFocus();
                acquirer.setUrl(etUrl.getText().toString().trim());
            } else if (etUrl2.isFocused()) {
                resolveUrl(etUrl2.getText().toString().trim(), etIp2);
                etPort2.setFocusable(true);
                etPort2.requestFocus();
                acquirer.setUrl2(etUrl2.getText().toString().trim());
            }
        }
    }

    /**
     * update items value
     */
    private void updateItemsValue() {
        if (acquirer == null) {
            return;
        }
        etTerminalId.setText(acquirer.getTerminalId(), BufferType.EDITABLE);

        etMerchantId.setText(acquirer.getMerchantId(), BufferType.EDITABLE);

        etNii.setText(acquirer.getNii(), BufferType.EDITABLE);

        String szBatchNo = Component.getPaddedNumber(acquirer.getCurrBatchNo(), 6);
        etBatch.setText(szBatchNo, BufferType.EDITABLE);

        etUrl.setText(acquirer.getUrl());
        etPort.setText(String.valueOf(acquirer.getPort()));
        resolveUrl(acquirer.getUrl(), etIp);
        etUrl2.setText(acquirer.getUrl2());
        etPort2.setText(String.valueOf(acquirer.getPortBak2()));
        resolveUrl(acquirer.getUrl2(), etIp2);

        enableTrickFeed.setChecked(acquirer.isDisableTrickFeed());

        enableQr.setChecked(acquirer.isEnableQR());

        enableRefNo.setChecked(acquirer.isEnableRefNo());

        isFirst = false;

        sslSpinner.setSelection(acquirer.getSslType().ordinal(), true);
        if (acquirer.getName().equals("HASE_OLS")
                || acquirer.getName().equals("HASE")
                || acquirer.getName().equals("HASE_EMV")
                || acquirer.getName().equals("HASE_CUP")) {
            rlAcqRefNo.setVisibility(View.VISIBLE);
        } else {
            rlAcqRefNo.setVisibility(View.GONE);
        }

        etDescription.setText(acquirer.getAcqDescription());
        insPlanSpinner.setSelection(acquirer.getInstalPlan() - 1, true);
        if (acquirer.getName().contains("HASE_INS")) {
            rlAcqDescription.setVisibility(View.VISIBLE);
            rlAcqInstalPlan.setVisibility(View.VISIBLE);
        } else {
            rlAcqDescription.setVisibility(View.GONE);
            rlAcqInstalPlan.setVisibility(View.GONE);
        }
    }

    /**
     *
     */
    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.acquirer_disable_trick_feed:
                acquirer.setDisableTrickFeed(isChecked);
                break;
            case R.id.acq_support_qr:
                acquirer.setEnableQR(isChecked);
                break;
            case R.id.acq_support_refno:
                acquirer.setEnableRefNo(isChecked);
                break;
            default:
                break;
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        FinancialApplication.getAcqManager().updateAcquirer(acquirer);
    }

    private class Watcher implements TextWatcher {
        final int id;

        Watcher(int id) {
            this.id = id;
        }

        /**
         *
         */
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            //do nothing
        }

        @Override
        public void afterTextChanged(Editable s) {
            String content = s.toString();
            switch (id) {
                case R.id.terminal_id:
                    acquirer.setTerminalId(content);
                    break;
                case R.id.merchant_id:
                    acquirer.setMerchantId(content);
                    break;
                case R.id.nii_acq:
                    acquirer.setNii(content);
                    break;
                case R.id.batch_num:
                    updateBatchNo(content);
                    break;
                case R.id.acq_url:
                    acquirer.setUrl(content);
                    break;
                case R.id.acq_url2:
                    acquirer.setUrl2(content);
                    break;
                case R.id.acq_description:
                    acquirer.setAcqDescription(content);
                    break;
                default:
                    break;
            }
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            //do nothing for now
        }

        private void updateBatchNo(String content) {
            //AET-63
            if (!isFirst && !FinancialApplication.getTransDataDbHelper().findAllTransData(acquirer, true, true).isEmpty()) {
                ToastUtils.showMessage(R.string.has_trans_for_settle);
            } else {
                acquirer.setCurrBatchNo(Integer.parseInt(content));
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    /**
     * update ip  info
     */
    public void update(final String ip, final TextView label, final EditText text) {
        if (!TextUtils.isEmpty(ip)) {
            label.setVisibility(View.VISIBLE);
            text.setVisibility(View.VISIBLE);
            text.setText(ip);
        } else {
            label.setVisibility(View.GONE);
            text.setVisibility(View.GONE);
        }
    }

    /**
     * get url set in inet address
     *
     * @param url url
     * @param text text
     */
    public void resolveUrl(final String url, final EditText text) {

        if (TextUtils.isEmpty(url)) {
            if (text == etIp) {
                ip1 = getString(R.string.acq_ip_hint);
            } else {
                ip2 = getString(R.string.acq_ip_hint);
            }
            return;
        }

        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                try {
                    InetAddress[] inetAddresses = InetAddress.getAllByName(url);
                    if (text == etIp) {
                        ip1 = inetAddresses[0].getHostAddress();
                    } else {
                        ip2 = inetAddresses[0].getHostAddress();
                    }
                    Message message = new Message();
                    message.what = UPDATE_URL;
                    handler.sendMessage(message);
                } catch (UnknownHostException e) {
                    if (text == etIp) {
                        ip1 = getString(R.string.acq_ip_hint);
                    } else {
                        ip2 = getString(R.string.acq_ip_hint);
                    }
                }
            }
        });
    }
}
