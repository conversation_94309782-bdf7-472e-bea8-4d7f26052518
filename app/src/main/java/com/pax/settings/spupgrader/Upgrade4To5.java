/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/9/22                      Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.settings.spupgrader;

import android.content.SharedPreferences;
import com.pax.settings.SysParam;

/*
Called from DbUpgrader by reflection
 */
public class Upgrade4To5 extends SpUpgrader {

    /**
     * upgrade if version is changed
     *
     * @param editor SharedPreferences.Editor
     */
    @Override
    public void upgrade(SharedPreferences.Editor editor) {
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP1_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP2_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP3_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP4_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP5_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP6_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP7_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP8_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP9_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP10_SETTLE_STATUS.toString(), true);
    }
}
