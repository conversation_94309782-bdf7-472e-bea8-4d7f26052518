package com.pax.settings.spupgrader;

import android.content.SharedPreferences;
import com.pax.abl.utils.EncUtils;
import com.pax.settings.SysParam;

public class Upgrade6To7 extends SpUpgrader {
    @Override
    public void upgrade(SharedPreferences.Editor editor) {
        editor.putBoolean(SysParam.BooleanParam.EDC_ENABLE_MICROPAY.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.EDC_ENABLE_FPS.toString(), true);
        editor.putInt(SysParam.NumberParam.ECR_WIFI_PORT.toString(), 5000);
        editor.putString(SysParam.StringParam.ECR_COMM.toString(), "BASE RS232");
        editor.putBoolean(SysParam.BooleanParam.ECR_ENABLE.toString(), false);
        editor.putBoolean(SysParam.BooleanParam.MICRO_PAY_IS_STAND_ALONE.toString(), false);
        editor.putBoolean(SysParam.BooleanParam.MICRO_PAY_IS_DO_ECR_TRANS.toString(), false);
        editor.putString(SysParam.StringParam.HASE_CASH_DOL_NAME.toString(), "HASE + FUN$");
        editor.putString(SysParam.StringParam.WIFI_MANAGE_PASSWORD.toString(), EncUtils.pwdSha("773287"));
        editor.putBoolean(SysParam.BooleanParam.PRINT_CARDHOLDER_NAME.toString(), false);
        editor.putBoolean(SysParam.BooleanParam.EDC_ENABLE_WIFI_MANAGE.toString(), false);
    }
}
