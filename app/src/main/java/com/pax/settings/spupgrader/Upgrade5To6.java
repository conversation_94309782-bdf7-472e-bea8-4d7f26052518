/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/9/22                      Kimberley                   Create
 * ===========================================================================================
 */

package com.pax.settings.spupgrader;

import android.content.SharedPreferences;
import com.pax.settings.SysParam;

/*
Called from DbUpgrader by reflection
 */
public class Upgrade5To6 extends SpUpgrader {

    /**
     * upgrade if version is changed
     *
     * @param editor SharedPreferences.Editor
     */
    @Override
    public void upgrade(SharedPreferences.Editor editor) {
        editor.putString(SysParam.NumberParam.EDC_INPUT_PASSWORD_TIMEOUT.toString(), "60");
    }
}
