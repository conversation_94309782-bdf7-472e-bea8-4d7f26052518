/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         ligq                    Create
 * ===========================================================================================
 */
package com.pax.settings;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.ClssParam;
import com.pax.data.entity.Issuer;
import com.pax.pay.BaseConfigActivity;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.model.AcqManager;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.RxUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.adapter.ConfigSecondAdapter;
import com.pax.settings.inflater.InflaterUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

import static com.pax.settings.SettingConst.TYPE_ACQUIRER;
import static com.pax.settings.SettingConst.TYPE_CLSS;
import static com.pax.settings.SettingConst.TYPE_COMM;
import static com.pax.settings.SettingConst.TYPE_EDC;
import static com.pax.settings.SettingConst.TYPE_ISSUER;
import static com.pax.settings.SettingConst.TYPE_KEY;
import static com.pax.settings.SettingConst.TYPE_OTHER;
import static com.pax.settings.SettingConst.TYPE_PROVIDER;
import static com.pax.settings.SettingConst.TYPE_PW;
import static com.pax.settings.SysParam.KEY_NONE;

import android.content.Intent;

public class ConfigSecondActivity extends BaseConfigActivity {
    private ConfigSecondAdapter adapter = null;
    private boolean needRefresh = false;
    private String type = "";

    @Override
    protected int getLayoutId() {
        return R.layout.activity_config_second;
    }

    @Override
    protected void initViews() {
        EventBus.getDefault().register(this);
        super.initViews();
        final List<ConfigSecondAdapter.ItemConfigSecond> dataList = new ArrayList<>();
        InflaterUtils.initData(dataList, new InflaterUtils.InflaterListener<List<ConfigSecondAdapter.ItemConfigSecond>>() {
            @Override
            public boolean init() {
                boolean result = getTypeResult();
                initDataList(dataList);
                return result;
            }

            @Override
            public void next(List<ConfigSecondAdapter.ItemConfigSecond> itemConfigSeconds) {
                RecyclerView rvData = findViewById(R.id.rv_config);
                rvData.setLayoutManager(new LinearLayoutManager(ConfigSecondActivity.this));
                adapter = new ConfigSecondAdapter(ConfigSecondActivity.this, itemConfigSeconds, type);
                rvData.setAdapter(adapter);

            }
        });

    }

    private boolean getTypeResult() {
        boolean result = true;
        String title = toolbar.getTitle().toString();
        if (getString(R.string.settings_menu_communication_parameter).equals(title)) {
            type = TYPE_COMM;
        } else if (getString(R.string.settings_menu_edc_parameter).equals(title)) {
            type = TYPE_EDC;
        } else if (getString(R.string.settings_menu_issuer_parameter).equals(title)) {
            type = TYPE_ISSUER;
        } else if (getString(R.string.settings_menu_acquirer_parameter).equals(title)) {
            type = TYPE_ACQUIRER;
        } else if (getString(R.string.settings_menu_clss_parameter).equals(title)) {
            type = TYPE_CLSS;
        } else if (getString(R.string.settings_menu_pwd_manage).equals(title)) {
            type = TYPE_PW;
        } else if (getString(R.string.settings_menu_otherManage).equals(title)) {
            type = TYPE_OTHER;
        } else if (getString(R.string.settings_menu_keyManage).equals(title)) {
            type = TYPE_KEY;
        } else if (getString(R.string.select_provider).equals(title)) {
            type = TYPE_PROVIDER;
        } else {
            result = false;
        }
        return result;
    }

    /**
     * init the page
     *
     * @param dataList data to be init
     */
    private void initDataList(List<ConfigSecondAdapter.ItemConfigSecond> dataList) {
        String[] titleArr;
        int[] switchArr;
        int[] valueArr;
        String[] keyArr;
        String defaultKey = KEY_NONE;
        switch (type) {
            case TYPE_COMM:
                titleArr = getResources().getStringArray(R.array.config_menu_comm_title);
                switchArr = getResources().getIntArray(R.array.config_menu_comm_type_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_comm_has_value);
                keyArr = getResources().getStringArray(R.array.config_menu_comm_key);
                break;
            case TYPE_EDC:
                titleArr = getResources().getStringArray(R.array.config_menu_edc_title);
                switchArr = getResources().getIntArray(R.array.config_menu_edc_type_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_edc_has_value);
                keyArr = getResources().getStringArray(R.array.config_menu_edc_key);
                break;
            case TYPE_ISSUER:
                defaultKey = TYPE_ISSUER;
                titleArr = getResources().getStringArray(R.array.config_menu_issuer_title);
                switchArr = getResources().getIntArray(R.array.config_menu_issuer_type_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_issuer_has_value);
                keyArr = null;
                break;
            case TYPE_ACQUIRER:
                defaultKey = TYPE_ACQUIRER;
                titleArr = getResources().getStringArray(R.array.config_menu_acquirer_title);
                switchArr = getResources().getIntArray(R.array.config_menu_acquirer_type_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_acquirer_has_value);
                keyArr = null;
                break;
            case TYPE_CLSS:
                defaultKey = TYPE_CLSS;
                titleArr = getResources().getStringArray(R.array.config_menu_clss_title);
                switchArr = getResources().getIntArray(R.array.config_menu_clss_type_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_clss_has_value);
                keyArr = null;
                break;
            case TYPE_KEY:
                defaultKey = TYPE_KEY;
                titleArr = getResources().getStringArray(R.array.config_menu_key_title);
                switchArr = getResources().getIntArray(R.array.config_menu_key_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_key_has_value);
                keyArr = null;
                break;
            case TYPE_PROVIDER:
                defaultKey = TYPE_PROVIDER;
                titleArr = getResources().getStringArray(R.array.config_menu_provider_title);
                switchArr = getResources().getIntArray(R.array.config_menu_provider_type_switch);
                valueArr = getResources().getIntArray(R.array.config_menu_provider_has_value);
                keyArr = null;
                break;
            default:
                titleArr = new String[0];
                switchArr = new int[0];
                valueArr = new int[0];
                keyArr = new String[0];
                break;
        }

        if (titleArr.length == 0) {
            return;
        }
        for (int i = 0; i < titleArr.length; i++) {
            if (Utils.getString(R.string.edc_max_transaction_number).equals(titleArr[i])) {
                // transNumber选项可选
                boolean maxTransactionsLimit = SysParam.getInstance().getBoolean(Utils.getString(R.string.EDC_TRANSACTION_NUMBER_LIMIT));
                if (!maxTransactionsLimit) {
                    continue;
                }
            }
            dataList.add(new ConfigSecondAdapter.ItemConfigSecond(titleArr[i],
                    switchArr[i] == 1, valueArr[i], (keyArr == null) ? defaultKey : keyArr[i]));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (needRefresh || adapter != null) {
            adapter.notifyDataSetChanged();
        }
        if(type == TYPE_PROVIDER){
            toolbar.setTitle(MQTTUtils.getCurrentDisplayProvider().getCommLabel());
        }
        needRefresh = (!TYPE_ISSUER.equals(type) && !TYPE_ACQUIRER.equals(type));
    }


    /**
     * handle the acquirer and issuer configs
     *
     * @param received event data
     */
    @Subscribe
    public void handleSelectChanged(ConfigEvent received) {
        LogUtils.d(TAG, "handleSelectChanged:$received");
        AcqManager acqManager = FinancialApplication.getAcqManager();
        if (TYPE_ISSUER.equals(received.type)) {
            Issuer findIssuer = acqManager.findIssuer(received.msg);
            if (findIssuer != null) {
                adapter.setIssuer(findIssuer);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.notifyDataSetChanged();
                    }
                });
            }
        } else if (TYPE_ACQUIRER.equals(received.type)) {
            Acquirer findAcquirer = acqManager.findAcquirer(received.msg);
            if (findAcquirer != null) {
                adapter.setAcquirer(findAcquirer);
                Long curAcqId = FinancialApplication.getAcqManager().getCurAcq().getId();
                if (findAcquirer.getId().equals(curAcqId)) {
                    FinancialApplication.getAcqManager().setCurAcq(findAcquirer);
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.notifyDataSetChanged();
                    }
                });
            }
        } else if (TYPE_CLSS.equals(received.type)) {
            ClssParam findClssParam = FinancialApplication.getClssParamDbHelper().findClssParam(received.msg);
            if (findClssParam != null) {
                adapter.setClssParam(findClssParam);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if ("RUPAY".equals(findClssParam.getName()) && adapter.getDatas().size() == 4) {
                            //只有Rupay需要配置 AFA参数
                            adapter.getDatas().add(new ConfigSecondAdapter.ItemConfigSecond(Utils.getString(R.string.clss_afa_limit),
                                    false, 1, SettingConst.TYPE_CLSS));
                        } else if (!"RUPAY".equals(findClssParam.getName()) && adapter.getDatas().size() == 5) {
                            adapter.getDatas().remove(4);
                        }
                        adapter.notifyDataSetChanged();
                    }
                });
            }
        } else if (getString(R.string.SSL).equals(received.type)) {
            Acquirer findAcquirer = acqManager.findAcquirer((String) received.data);
            if (findAcquirer != null) {
                findAcquirer.setSslType((getString(R.string.SSL).equals(received.msg)) ? SysParam.CommSslType.SSL : SysParam.CommSslType.NO_SSL);
                acqManager.updateAcquirer(findAcquirer);
                acqManager.setCurAcq(findAcquirer);
                adapter.setAcquirer(findAcquirer);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.notifyDataSetChanged();
                    }
                });
            }
        }
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().unregister(this);
        //释放ConfigSecondAdapter中的Disposable资源
        RxUtils.release();
        super.onDestroy();
    }

    public static final class ConfigEvent {
        private final String type;
        private final String msg;
        private final Object data;

        public ConfigEvent(String type, String msg, Object data) {
            super();
            this.type = type;
            this.msg = msg;
            this.data = data;
        }

        public final String getType() {
            return this.type;
        }

        public final String getMsg() {
            return this.msg;
        }

        public final Object getData() {
            return this.data;
        }

    }
}
