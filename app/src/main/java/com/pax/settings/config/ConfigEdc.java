/*
 *
 *  ============================================================================
 *  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  This software is supplied under the terms of a license agreement or nondisclosure
 *  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  Description:
 *  Revision History:
 *  Date	             Author	                Action
 *  20190418   	     ligq           	Create/Add/Modify/Delete
 *  ============================================================================
 *
 */

package com.pax.settings.config;

import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigThirdActivity;
import com.pax.settings.inflater.ConfigInflater;
import com.pax.settings.inflater.ConfigInputInflater;
import com.pax.settings.inflater.ConfigSelectInflater;

/**
 * <AUTHOR>
 * @date 2019/4/18 15:07
 */
public class ConfigEdc implements IConfig<ConfigInflater<ConfigThirdActivity>> {
    @Override
    public ConfigInflater<ConfigThirdActivity> getInflater(String title) {
        if (Utils.getString(R.string.edc_merchant_name).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_MERCHANT_NAME), 50, 0);
        } else if (Utils.getString(R.string.edc_merchant_address).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_MERCHANT_ADDRESS), 50, 0);
        } else if (Utils.getString(R.string.currency_list).equals(title)) {
            return new ConfigSelectInflater();
        } else if (Utils.getString(R.string.edc_receipt_no).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_RECEIPT_NUM), 1, R.string.digits_3);
        } else if (Utils.getString(R.string.edc_trace_no).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_TRACE_NO), 6, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_batch_no).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_BATCH_NO), 6, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_invoice_no).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_INVOICE_NO), 6, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_tip_percentage).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_TIP_PERCENTAGE), 2, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_pre_auth_completion_percentage).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_PRE_AUTH_COMPLETION_PERCENTAGE), 2, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_preauth_record_saving_day).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_PREAUTH_RECORD_SAVING_DAY), null, 30, 1);
        } else if (Utils.getString(R.string.edc_reversal_retry_time).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_REVERSAL_RETRY_TIME), 2, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_qr_trans_query_interval).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_QR_TRANS_QUERY_INTERVAL), 2, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_qr_trans_query_repeat_interval).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_QR_TRANS_QUERY_REPEAT_INTERVAL), 2, R.string.digits_2);
        } else if (Utils.getString(R.string.edc_max_amount_digit).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_MAX_AMOUNT_DIGIT), 2, R.string.digits_2);
        } else if(Utils.getString(R.string.edc_preauth_completion).equals(title)){
            return new ConfigSelectInflater();
        } else if (Utils.getString(R.string.edc_max_transaction_number).equals(title)) {
            return new ConfigInputInflater(Utils.getString(R.string.EDC_MAX_TRANSACTION_NUMBER), 4, R.string.digits_2);
        }
        else {
            return null;
        }
    }

    @Override
    public void doOkNext(Object... args) {

    }
}
