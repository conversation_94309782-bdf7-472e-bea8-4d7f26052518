/*
 *
 *  ============================================================================
 *  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  This software is supplied under the terms of a license agreement or nondisclosure
 *  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  Description:
 *  Revision History:
 *  Date	             Author	                Action
 *  20190418   	     ligq           	Create/Add/Modify/Delete
 *  ============================================================================
 *
 */

package com.pax.settings.config;

import android.content.Context;
import android.text.InputType;

import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigThirdActivity;
import com.pax.settings.inflater.ConfigInflater;
import com.pax.settings.inflater.ConfigInputAmountInflater;
import com.pax.settings.inflater.ConfigInputInflater;
import com.pax.settings.inflater.ConfigSelectInflater;

import static com.pax.settings.SettingConst.TYPE_ISSUER;

/**
 * <AUTHOR>
 * @date 2019/4/18 15:16
 */
public class ConfigIssuer implements IConfig<ConfigInflater<ConfigThirdActivity>> {
    private String issuerName;
    private Context context;

    public ConfigIssuer(String issuerName,Context context) {
        this.issuerName = issuerName;
        this.context=context;
    }

    @Override
    public ConfigInflater<ConfigThirdActivity> getInflater(String title) {
        if (Utils.getString(R.string.settings_menu_issuer_parameter).equals(title)) {
            return new ConfigSelectInflater(issuerName);
        } else if (Utils.getString(R.string.issuer_floor_limit).equals(title)) {
            return new ConfigInputAmountInflater(issuerName, TYPE_ISSUER,context);
        } else if (Utils.getString(R.string.issuer_adjust_percent).equals(title)) {
            return new ConfigInputInflater(TYPE_ISSUER, issuerName,
                    InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL, 5, 0, false);
        } else {
            return null;
        }
    }

    @Override
    public void doOkNext(Object... args) {

    }
}
