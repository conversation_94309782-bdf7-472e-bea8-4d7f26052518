/*
 *
 *  ============================================================================
 *  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  This software is supplied under the terms of a license agreement or nondisclosure
 *  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  Description:
 *  Revision History:
 *  Date	             Author	                Action
 *  20190418   	     ligq           	Create/Add/Modify/Delete
 *  ============================================================================
 *
 */

package com.pax.settings.config;

import android.content.res.Configuration;
import android.text.InputType;

import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigThirdActivity;
import com.pax.settings.inflater.ConfigInflater;
import com.pax.settings.inflater.ConfigInputInflater;
import com.pax.settings.inflater.ConfigSelectInflater;

import static com.pax.settings.SettingConst.TYPE_ACQUIRER;

/**
 * <AUTHOR>
 * @date 2019/4/18 14:35
 */
public class ConfigAcquirer implements IConfig<ConfigInflater<ConfigThirdActivity>> {
    private String acquirerName;

    public ConfigAcquirer(String acquirerName) {
        this.acquirerName = acquirerName;
    }

    @Override
    public ConfigInflater<ConfigThirdActivity> getInflater(String title) {
        if (Utils.getString(R.string.settings_menu_acquirer_parameter).equals(title)) {
            return new ConfigSelectInflater(acquirerName);
        } else if (Utils.getString(R.string.acq_terminal_id).equals(title)) {
            return new ConfigInputInflater(TYPE_ACQUIRER, acquirerName, -1, 8, 0, false);
        } else if (Utils.getString(R.string.acq_merchant_id).equals(title)) {
            return new ConfigInputInflater(TYPE_ACQUIRER, acquirerName, -1, 15, 0, false);
        } else if (Utils.getString(R.string.acq_nii).equals(title)) {
            return new ConfigInputInflater(TYPE_ACQUIRER, acquirerName, InputType.TYPE_CLASS_NUMBER, 3, R.string.digits_2, false);
        } else if (Utils.getString(R.string.acq_ip).equals(title)) {
            return new ConfigInputInflater(TYPE_ACQUIRER, acquirerName, Configuration.KEYBOARD_QWERTY, 15, R.string.digits_4, true);
        } else if (Utils.getString(R.string.acq_port).equals(title)) {
            return new ConfigInputInflater(TYPE_ACQUIRER, acquirerName, InputType.TYPE_CLASS_NUMBER, 6, 0, false);
        } else if (Utils.getString(R.string.SSL).equals(title)) {
            return new ConfigSelectInflater(acquirerName);
        } else {
            return null;
        }
    }

    @Override
    public void doOkNext(Object... args) {

    }
}
