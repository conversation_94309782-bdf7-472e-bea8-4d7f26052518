package com.pax.settings.config;

import static com.pax.settings.SettingConst.TYPE_PROVIDER;

import android.content.res.Configuration;
import android.text.InputType;

import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigThirdActivity;
import com.pax.settings.inflater.ConfigInflater;
import com.pax.settings.inflater.ConfigInputInflater;
import com.pax.settings.inflater.ConfigSelectInflater;

/**
 * <AUTHOR>
 * @Date 2024/2/6
 */
public class ConfigProvider implements IConfig<ConfigInflater<ConfigThirdActivity>> {
    @Override
    public ConfigInflater<ConfigThirdActivity> getInflater(String title) {
        if (Utils.getString(R.string.comm_media_provider).equals(title)) {
            return new ConfigSelectInflater(TYPE_PROVIDER);
        }else if (Utils.getString(R.string.comm_label_provider).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER, 15, 0, -1);
        } else if (Utils.getString(R.string.emi_value_provider).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER,10, 0, -1);
        } else if (Utils.getString(R.string.host_ip_provider).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER,30, 0, -1);
        } else if (Utils.getString(R.string.port_number_provider).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER, 5, R.string.digits_2, Configuration.KEYBOARD_QWERTY);
        } else if (Utils.getString(R.string.ssl_provider).equals(title)) {
            return new ConfigSelectInflater(Utils.getString(R.string.ssl_provider));
        } else if (Utils.getString(R.string.apn_provider).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER, 15, 0, -1);
        } else if (Utils.getString(R.string.nii_provider).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER,3, R.string.digits_2, Configuration.KEYBOARD_QWERTY);
        } else   if (Utils.getString(R.string.mqtt_ip).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER,30, 0, -1);
        } else if (Utils.getString(R.string.mqtt_port).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER, 5, R.string.digits_2, InputType.TYPE_CLASS_NUMBER);
        } else if (Utils.getString(R.string.mqtt_qos).equals(title)) {
            return new ConfigSelectInflater();
        } else if (Utils.getString(R.string.mqtt_connect_timeout).equals(title)) {
            return new ConfigInputInflater(TYPE_PROVIDER, 2, R.string.digits_4, Configuration.KEYBOARD_QWERTY);
        } else {
            return null;
        }
    }

    @Override
    public void doOkNext(Object... args) {

    }
}
