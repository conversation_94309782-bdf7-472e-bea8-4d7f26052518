/*
 *
 *  ============================================================================
 *  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  This software is supplied under the terms of a license agreement or nondisclosure
 *  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  Description:
 *  Revision History:
 *  Date	             Author	                Action
 *  20190419   	     ligq           	Create/Add/Modify/Delete
 *  ============================================================================
 *
 */

package com.pax.settings.inflater;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewStub;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.data.entity.Acquirer;
import com.pax.data.entity.ClssParam;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.Provider;
import com.pax.data.local.GreendaoHelper;
import com.pax.data.local.db.helper.ProviderDbHelper;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.InjectKeyUtil;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.RxUtils;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigSecondActivity;
import com.pax.settings.ConfigThirdActivity;
import com.pax.settings.SysParam;
import com.pax.settings.adapter.ConfigSelectAdapter;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static com.pax.settings.SettingConst.TYPE_ACQUIRER;
import static com.pax.settings.SettingConst.TYPE_CLSS;
import static com.pax.settings.SettingConst.TYPE_ISSUER;

/**
 * <AUTHOR>
 * @date 2019/4/19 10:24
 */
public class ConfigSelectInflater implements ConfigInflater<ConfigThirdActivity> {
    private String name;
    private String key;
    private ConfigSelectAdapter adapter;
    private OnOkListener onOkListener;
    private Provider provider;

    public ConfigSelectInflater() {
    }

    public ConfigSelectInflater(String name) {
        this.name = name;
    }

    @Override
    public void inflate(final ConfigThirdActivity act, final String title) {
        final ViewStub viewStub = act.findViewById(R.id.vs_third_content);
        provider = MQTTUtils.getCurrentDisplayProvider();
        InflaterUtils.initData(viewStub, new InflaterUtils.InflaterListener<ViewStub>() {
            boolean result;
            int selectPosition = 0;

            @Override
            public boolean init() {
                List<ConfigSelectAdapter.ItemConfigSelect> dataList = new ArrayList<>();
                List<String> contentList;
                if (Utils.getString(R.string.settings_menu_communication_type).equals(title)) {
                    key = Utils.getString(R.string.COMM_TYPE);
                    contentList = Utils.getMutableList(R.array.commParam_menu_comm_mode_values_list_entries);
                    result = true;
                } else if (Utils.getString(R.string.commParam_menu_comm_timeout).equals(title)) {
                    key = Utils.getString(R.string.COMM_TIMEOUT);
                    contentList = Utils.getMutableList(R.array.edc_connect_time_entries);
                    result = true;
                } else if (Utils.getString(R.string.currency_list).equals(title)) {
                    key = Utils.getString(R.string.EDC_CURRENCY_LIST);
                    List<Locale> locales = CurrencyConverter.getSupportedLocale();
                    contentList = new ArrayList<>();
                    for (Locale locale : locales) {
                        contentList.add(locale.getDisplayName(Locale.US));
                    }
                    result = true;
                } else if (Utils.getString(R.string.settings_menu_issuer_parameter).equals(title)) {
                    key = TYPE_ISSUER;
                    List<Issuer> listIssuers = FinancialApplication.getAcqManager().findAllIssuers();
                    contentList = new ArrayList<>();
                    for (Issuer listIssuer : listIssuers) {
                        contentList.add(listIssuer.getName());
                    }
                    result = true;
                } else if (Utils.getString(R.string.settings_menu_acquirer_parameter).equals(title)) {
                    key = TYPE_ACQUIRER;
                    List<Acquirer> listAcquirers = FinancialApplication.getAcqManager().findAllAcquirers();
                    contentList = new ArrayList<>();
                    for (Acquirer listAcquirer : listAcquirers) {
                        contentList.add(listAcquirer.getName());
                    }
                    result = true;
                } else if (Utils.getString(R.string.settings_menu_clss_parameter).equals(title)) {
                    key = TYPE_CLSS;
                    List<ClssParam> clssParams = FinancialApplication.getClssParamDbHelper().loadAll();
                    contentList = new ArrayList<>();
                    for (ClssParam clssParam : clssParams) {
                        contentList.add(clssParam.getName());
                    }
                    result = true;
                } else if (Utils.getString(R.string.SSL).equals(title)) {
                    key = Utils.getString(R.string.SSL);
                    contentList = Utils.getMutableList(R.array.acq_ssl_type_list_entries);
                    result = true;
                } else if (Utils.getString(R.string.ssl_provider).equals(title)) {
                    key = Utils.getString(R.string.ssl_provider);
                    contentList = Utils.getMutableList(R.array.acq_ssl_type_list_entries);
                    result = true;
                } else if (Utils.getString((R.string.mqtt_qos)).equals(title)) {
                    key = Utils.getString(R.string.mqtt_qos);
                    contentList = Utils.getMutableList(R.array.mqtt_qos_list_entries);
                    result = true;
                } else if (Utils.getString(R.string.comm_media_provider).equals(title)) {
                    key = Utils.getString(R.string.comm_media_provider);
                    contentList = Utils.getMutableList(R.array.commParam_menu_comm_mode_values_list_entries);
                    result = true;
                } else if (Utils.getString(R.string.edc_preauth_completion).equals(title)) {
                    key = Utils.getString(R.string.EDC_PREAUTH_COMPLETION);
                    contentList = Utils.getMutableList(R.array.edc_preauth_completion);
                    result = true;
                } else {
                    key = "";
                    contentList = new ArrayList<>();
                    result = false;
                }
                if (contentList.isEmpty()) {
                    return false;
                }
                String selectedType = getSelectType(contentList);

                for (int i = 0; i < contentList.size(); i++) {
                    ConfigSelectAdapter.ItemConfigSelect element;
                    if (selectedType.equals(contentList.get(i))) {
                        selectPosition = i;
                        element = new ConfigSelectAdapter.ItemConfigSelect(contentList.get(i), View.VISIBLE);
                    } else {
                        element = new ConfigSelectAdapter.ItemConfigSelect(contentList.get(i));
                    }
                    dataList.add(element);
                }
                adapter = new ConfigSelectAdapter(act, R.layout.item_config_select
                        , dataList);
                adapter.setCurrentSelect(dataList.get(selectPosition));
                viewStub.setLayoutResource(R.layout.layout_config_rv);
                return result;
            }

            @Override
            public void next(ViewStub viewStub) {
                viewStub.inflate();
                RecyclerView rv = act.findViewById(R.id.rv_config);
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(act);
                rv.setLayoutManager(linearLayoutManager);
                rv.setAdapter(adapter);
                rv.scrollToPosition(selectPosition);
            }
        });
    }

    private String getSelectType(List<String> contentList) {
        if (TextUtils.isEmpty(name)) {
            if (Utils.getString(R.string.COMM_TIMEOUT).equals(key)) {
                return String.valueOf(SysParam.getInstance().getInt(key));
            } else if (key.equals(Utils.getString(R.string.mqtt_qos))) {
                //MQTT QOS初始化默认选择1
                return convertMQTTQOS(provider.getQos());
            } else {
                return (String) SysParam.getInstance().get(key, contentList.get(0));
            }
        } else {
            if (key.equals(Utils.getString(R.string.SSL))) {
                return FinancialApplication.getAcqManager().findAcquirer(name).getSslType().toString();
            } else if (key.equals(Utils.getString(R.string.ssl_provider))) {
               return provider.getSslType().toString();
            } else if (key.equals(Utils.getString(R.string.comm_media_provider))) {
                return provider.getMediaName();
            } else {
                return name;
            }
        }
    }

    private String convertMQTTQOS(int content) {
        if (0 == content) {
            return Utils.getString(R.string.mqtt_qos_0);
        } else if (1 == content) {
            return Utils.getString(R.string.mqtt_qos_1);
        } else {
            return Utils.getString(R.string.mqtt_qos_2);
        }
    }

    @Override
    public boolean doNextSuccess() {
        RxUtils.release();
        String content = adapter.getCurrentSelect().getContent();
        if (content == null) {
            return true;
        }
        if (onOkListener != null) {
            onOkListener.onOkClick(key, content);
            return false;
        }
        if (!TextUtils.isEmpty(key)) {
            if (Utils.getString(R.string.SSL).equals(key)) {
                EventBus.getDefault().post(new ConfigSecondActivity.ConfigEvent(key, content, name));
            } else if (Utils.getString(R.string.comm_media_provider).equals(key)) {
                if (MQTTUtils.getCurrentUseProvider() != null && MQTTUtils.getCurrentDisplayProvider().getCommLabel().
                        equals(MQTTUtils.getCurrentUseProvider().getCommLabel())) {
                    MQTTUtils.setNeedToRestart(true);
                }
                provider.setMediaName(content);
                SysParam.getInstance().set(R.string.COMM_TYPE, provider.getMediaName());
                MQTTUtils.setCurrentDisplayProvider(provider);
                ProviderDbHelper.getInstance().update(provider);
                MQTTUtils.startMqttOptional(provider);
            } else if (Utils.getString(R.string.ssl_provider).equals(key)) {
                if (MQTTUtils.getCurrentUseProvider() != null && MQTTUtils.getCurrentDisplayProvider().getCommLabel().
                        equals(MQTTUtils.getCurrentUseProvider().getCommLabel())) {
                    MQTTUtils.setNeedToRestart(true);
                }
                provider.setSslType(SysParam.CommSslType.SSL.name().equals(content) ? SysParam.CommSslType.SSL : SysParam.CommSslType.NO_SSL);
                MQTTUtils.setCurrentDisplayProvider(provider);
                ProviderDbHelper.getInstance().update(provider);
                MQTTUtils.startMqttOptional(provider);
            } else if (Utils.getString(R.string.mqtt_qos).equals(key)) {
                if (MQTTUtils.getCurrentUseProvider() != null && MQTTUtils.getCurrentDisplayProvider().getCommLabel().
                        equals(MQTTUtils.getCurrentUseProvider().getCommLabel())) {
                    MQTTUtils.setNeedToRestart(true);
                }
                provider.setQos(Integer.parseInt(content.substring(content.length()-1)));
                MQTTUtils.setCurrentDisplayProvider(provider);
                ProviderDbHelper.getInstance().update(provider);
                MQTTUtils.startMqttOptional(provider);
            } else if (TYPE_ACQUIRER.equals(key) || TYPE_ISSUER.equals(key) || TYPE_CLSS.equals(key)) {
                EventBus.getDefault().post(new ConfigSecondActivity.ConfigEvent(key, content, null));
            } else if (Utils.getString(R.string.COMM_TIMEOUT).equals(key)) {
                SysParam.getInstance().set(key, Integer.parseInt(content));
            } else if (Utils.getString(R.string.EDC_CURRENCY_LIST).equals(key)) {
                if (GreendaoHelper.getTransDataHelper().countOf() > 0) {
                    FinancialApplication.getApp().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.showMessage(R.string.has_trans_for_settle);
                        }
                    });
                    return false;
                }
                SysParam.getInstance().set(key, content);
                Utils.changeAppLanguage(FinancialApplication.getApp(),
                        CurrencyConverter.setDefCurrency(content));
                Utils.restart();
            } else if (Utils.getString(R.string.EDC_PED_MODE).equals(key)) {
                SysParam.getInstance().set(key, content);
            } else if (Utils.getString(R.string.COMM_TYPE).equals(key)) {
                SysParam.getInstance().set(key, content);
            } else if (Utils.getString(R.string.edc_preauth_completion).equals(key)) {
                SysParam.getInstance().set(Utils.getString(R.string.EDC_PREAUTH_COMPLETION), content);
            } else {
                SysParam.getInstance().set(key, content);
            }
            return true;
        } else {
            return false;
        }

    }

    public void setOnOkListener(OnOkListener onOkListener) {
        this.onOkListener = onOkListener;
    }

    public interface OnOkListener {
        void onOkClick(String key, String content);
    }
}
