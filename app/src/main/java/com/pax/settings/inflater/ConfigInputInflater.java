/*
 *
 *  ============================================================================
 *  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  This software is supplied under the terms of a license agreement or nondisclosure
 *  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  Description:
 *  Revision History:
 *  Date	             Author	                Action
 *  20190418   	     ligq           	Create/Add/Modify/Delete
 *  ============================================================================
 *
 */

package com.pax.settings.inflater;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.ViewStub;
import android.view.inputmethod.EditorInfo;

import com.google.android.material.textfield.TextInputEditText;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.exceptions.PedDevException;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.Provider;
import com.pax.data.local.GreendaoHelper;
import com.pax.data.local.db.helper.ProviderDbHelper;
import com.pax.device.Device;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.utils.MQTTUtils;
import com.pax.pay.utils.TextValueWatcher;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigSecondActivity;
import com.pax.settings.ConfigThirdActivity;
import com.pax.settings.SettingConst;
import com.pax.settings.SysParam;
import com.pax.view.keyboard.KeyboardUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.List;
import java.util.Objects;

import static com.pax.settings.SettingConst.TYPE_ACQUIRER;
import static com.pax.settings.SettingConst.TYPE_ISSUER;
import static com.pax.settings.SettingConst.TYPE_PROVIDER;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date 2019/4/18 16:54
 */
public class ConfigInputInflater implements ConfigInflater<ConfigThirdActivity> {
    private TextInputEditText tieInput;
    private int inputType;
    private String title = "";
    private Object any = null;
    private String name = "";
    private boolean rawInputType = false;

    private final String key;
    Provider provider;
    private final int maxLen;
    private int digitsId;

    private int max;
    private int min;
    private boolean mInputType = false;

    public ConfigInputInflater(String key, int maxLen, int digitsId) {
        this.key = key;
        this.maxLen = maxLen;
        this.digitsId = digitsId;
        if (digitsId == R.string.digits_2 || digitsId == R.string.digits_3) {
            inputType = InputType.TYPE_CLASS_NUMBER;
        } else {
            inputType = -1;
        }
    }

    public ConfigInputInflater(String key, int maxLen, int digitsId, int inputType) {
        this.key = key;
        this.maxLen = maxLen;
        this.digitsId = digitsId;
        this.inputType = inputType;
        this.rawInputType = false;
    }

    public ConfigInputInflater(String key, String name, int inputType, int maxLen, int digitsId, boolean rawInputType) {
        this(key, maxLen, digitsId);
        this.name = name;
        this.inputType = inputType;
        this.rawInputType = rawInputType;
    }

    public ConfigInputInflater(String key, String name, int max, int min) {
        this.key = key;
        this.inputType = InputType.TYPE_CLASS_NUMBER;
        this.maxLen = String.valueOf(max).length();
        this.max = max;
        this.min = min;
        mInputType = true;
    }

    @Override
    public void inflate(final ConfigThirdActivity act, String title) {
        this.title = title;
        ViewStub viewStub = act.findViewById(R.id.vs_third_content);
        viewStub.setLayoutResource(R.layout.layout_config_input);
        viewStub.inflate();

        ViewStub vsInput = act.findViewById(R.id.vs_config_input);
        vsInput.setLayoutResource(R.layout.layout_config_type_input);
        vsInput.inflate();

        tieInput = act.findViewById(R.id.tie_config_input_content);
        if (inputType != -1) {
            if (rawInputType) {
                tieInput.setRawInputType(inputType);
            } else {
                tieInput.setInputType(inputType);
            }
        }
        setDigits();
        tieInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLen)});
        initEditText(act, title);
        tieInput.setText(getInitValue(key, title));
    }

    @SuppressLint("DefaultLocale")
    private String getInitValue(String key, String title) {
        if (TYPE_ISSUER.equals(key)) {
            any = FinancialApplication.getAcqManager().findIssuer(name);
            return "";
        } else if (TYPE_ACQUIRER.equals(key)) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(name);
            any = acquirer;
            if (Utils.getString(R.string.acq_terminal_id).equals(title)) {
                return acquirer.getTerminalId();
            } else if (Utils.getString(R.string.acq_merchant_id).equals(title)) {
                return acquirer.getMerchantId();
            } else if (Utils.getString(R.string.acq_nii).equals(title)) {
                return acquirer.getNii();
            } else if (Utils.getString(R.string.acq_ip).equals(title)) {
                return acquirer.getIp();
            } else if (Utils.getString(R.string.acq_port).equals(title)) {
                return String.valueOf(acquirer.getPort());
            }
        } else if (TYPE_PROVIDER.equals(key) && (provider = MQTTUtils.getCurrentDisplayProvider()) != null) {
            if (Utils.getString(R.string.comm_media_provider).equals(title)) {
                return provider.getMediaName();
            } else if (Utils.getString(R.string.comm_label_provider).equals(title)) {
                return String.valueOf(provider.getCommLabel());
            } else if (Utils.getString(R.string.emi_value_provider).equals(title)) {
                return String.valueOf(provider.getEmiValue());
            } else if (Utils.getString(R.string.host_ip_provider).equals(title)) {
                return String.valueOf(provider.getProviderHost());
            } else if (Utils.getString(R.string.port_number_provider).equals(title)) {
                return String.valueOf(provider.getProviderPort());
            } else if (Utils.getString(R.string.ssl_provider).equals(title)) {
                return String.valueOf(provider.getSslType());
            } else if (Utils.getString(R.string.apn_provider).equals(title)) {
                return String.valueOf(provider.getApn());
            } else if (Utils.getString(R.string.nii_provider).equals(title)) {
                return String.valueOf(provider.getNii());
            } else if (Utils.getString(R.string.mqtt_ip).equals(title)) {
                return String.valueOf(provider.getMqttIp());
            } else if (Utils.getString(R.string.mqtt_port).equals(title)) {
                return String.valueOf(provider.getMqttPort());
            } else if (Utils.getString(R.string.mqtt_qos).equals(title)) {
                return String.valueOf(provider.getQos());
            } else if (Utils.getString(R.string.mqtt_connect_timeout).equals(title)) {
                return String.valueOf(provider.getMqttTimeout());
            }
        } else {
            List<String[]> params = SysParam.getInstance().getAllParam();
            for (String[] param : params
            ) {
                if (param[0].equals(key)) {
                    return param[1];

                }
            }
        }

        return "";

    }

    private void initEditText(final ConfigThirdActivity act, String title) {
        if (Utils.getString(R.string.acq_port).equals(title) || Utils.getString(R.string.port_number_provider).equals(title)) {
            TextValueWatcher<Integer> textValueWatcher = getIntegerTextValueWatcher(title);
            tieInput.addTextChangedListener(textValueWatcher);
            tieInput.setOnEditorActionListener((v, actionId, event) -> {
                if (actionId == EditorInfo.IME_ACTION_UNSPECIFIED) {
                    if (event != null && event.getAction() == KeyEvent.ACTION_DOWN) {
                        KeyboardUtils.hideSystemKeyboard(act, tieInput);
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            });
        } else if (Utils.getString(R.string.acq_ip).equals(title)) {
            tieInput.addTextChangedListener(new Watcher(R.string.acq_ip));
        } else if (Utils.getString(R.string.host_ip_provider).equals(title)) {
            tieInput.addTextChangedListener(new Watcher(R.string.host_ip_provider));
        } else if (Utils.getString(R.string.nii_provider).equals(title)) {
            tieInput.addTextChangedListener(new Watcher(R.string.nii_provider));
        } else if (Utils.getString(R.string.mqtt_ip).equals(title)) {
            tieInput.addTextChangedListener(new Watcher(R.string.mqtt_ip));
        }
    }

    private @NonNull TextValueWatcher<Integer> getIntegerTextValueWatcher(String title) {
        TextValueWatcher<Integer> textValueWatcher = new TextValueWatcher<>(0, 65535);
        textValueWatcher.setOnCompareListener((value, min, max) -> {
            int temp = Integer.parseInt(value);
            return temp >= (int) min && temp <= (int) max;
        });
        textValueWatcher.setOnTextChangedListener(value -> {
            //Provider和Acquirer共用逻辑，需要排除掉Acquirer的影响
            if (Utils.getString(R.string.acq_port).equals(title)) {
                ((Acquirer) any).setPort(Integer.parseInt(value));
            }
        });
        return textValueWatcher;
    }

    private void setDigits() {
        if (digitsId != 0) {
            tieInput.addTextChangedListener(new TextWatcher() {
                String temp = "";
                String digits = Utils.getString(digitsId);
                boolean errorInput = false;

                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    temp = s.toString();
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    String s1 = s.toString();
                    if (s1.equals(temp)) {
                        errorInput = false;
                        return;
                    }

                    if (TextUtils.isEmpty(s)) {
                        temp = "";
                        errorInput = false;
                    }

                    for (int i = 0; i < s1.length(); i++) {
                        if (!digits.contains(String.valueOf(s1.charAt(i)))) {
                            errorInput = true;
                            return;
                        }
                    }
                    if (mInputType) {
                        int digital = Integer.parseInt(s.toString());
                        if (digital < min || digital > max) {
                            errorInput = true;
                            return;
                        }
                    }
                    temp = s1;
                    errorInput = false;
                }

                @Override
                public void afterTextChanged(Editable s) {
                    if (errorInput) {
                        tieInput.setText(temp);
                    }
                }
            });
        }
    }

    @SuppressLint("DefaultLocale")
    @Override
    public boolean doNextSuccess() {
        String input = Objects.requireNonNull(tieInput.getText()).toString();
        if (TextUtils.isEmpty(input)) {
            tieInput.setError(Utils.getString(R.string.err_config_empty_input));
            return false;
        }

        if ((Utils.getString(R.string.MK_VALUE).equals(key)
                || Utils.getString(R.string.PK_VALUE).equals(key)
                || Utils.getString(R.string.DK_VALUE).equals(key)) && !Utils.checkKeyString(input)) {
            tieInput.setError(Utils.getString(R.string.err_config_format_error));
            return false;
        }

        if (Utils.getString(R.string.MK_VALUE).equals(key)) {
            return injectKey(SettingConst.TYPE_TMK);
        } else if (Utils.getString(R.string.PK_VALUE).equals(key)) {
            return injectKey(SettingConst.TYPE_TPK);
        } else if (Utils.getString(R.string.DK_VALUE).equals(key)) {
            return injectKey(SettingConst.TYPE_TDK);
        } else if (TYPE_ISSUER.equals(key)) {
            return false;
        } else if (TYPE_ACQUIRER.equals(key)) {
            Acquirer acquirer = (Acquirer) any;
            boolean result = true;
            if (Utils.getString(R.string.acq_terminal_id).equals(title)) {
                acquirer.setTerminalId(input);
            } else if (Utils.getString(R.string.acq_merchant_id).equals(title)) {
                acquirer.setMerchantId(input);
            } else {
                result = false;
            }
            if (result) {
                FinancialApplication.getAcqManager().updateAcquirer(acquirer);
                EventBus.getDefault().post(new ConfigSecondActivity.ConfigEvent(TYPE_ACQUIRER, acquirer.getName(), null));
            }
            return result;
        } else if (Utils.getString(R.string.comm_media_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.comm_media_provider), input);
            return true;
        } else if (Utils.getString(R.string.comm_label_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.comm_label_provider), input);
            return true;
        } else if (Utils.getString(R.string.emi_value_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.emi_value_provider), input);
            return true;
        } else if (Utils.getString(R.string.host_ip_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.host_ip_provider), input);
            return true;
        } else if (Utils.getString(R.string.port_number_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.port_number_provider), input);
            return true;
        } else if (Utils.getString(R.string.ssl_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.ssl_provider), input);
            return true;
        } else if (Utils.getString(R.string.apn_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.apn_provider), input);
            return true;
        } else if (Utils.getString(R.string.nii_provider).equals(title)) {
            updateProvider(Utils.getString(R.string.nii_provider), input);
            return true;
        } else if (Utils.getString(R.string.mqtt_ip).equals(title)) {
            updateProvider(Utils.getString(R.string.mqtt_ip), input);
            return true;
        } else if (Utils.getString(R.string.mqtt_port).equals(title)) {
            if (Utils.checkPort(input)) {
                updateProvider(Utils.getString(R.string.mqtt_port), input);
            } else {
                ToastUtils.showMessage(R.string.invalid_port);
                return false;
            }
            return true;
        } else if (Utils.getString(R.string.mqtt_qos).equals(title)) {
            updateProvider(Utils.getString(R.string.mqtt_qos), input);
            return true;
        } else if (Utils.getString(R.string.mqtt_connect_timeout).equals(title)) {
            if (Integer.parseInt(input) < 20 || Integer.parseInt(input) > 60) {
                ToastUtils.showMessage(R.string.mqtt_timeout_should_be_between_20_and_60_seconds);
                return false;
            }
            updateProvider(Utils.getString(R.string.mqtt_connect_timeout), input);
            return true;
        } else if (Utils.getString(R.string.COMM_TIMEOUT).equals(key) || Utils.getString(R.string.EDC_TRACE_NO).equals(key)
                || Utils.getString(R.string.MK_INDEX).equals(key) || Utils.getString(R.string.MK_INDEX_MANUAL).equals(key)
                || Utils.getString(R.string.EDC_RECEIPT_NUM).equals(key) || Utils.getString(R.string.EDC_BATCH_NO).equals(key)
                || Utils.getString(R.string.EDC_INVOICE_NO).equals(key) || Utils.getString(R.string.EDC_TIP_PERCENTAGE).equals(key)
                || Utils.getString(R.string.EDC_PRE_AUTH_COMPLETION_PERCENTAGE).equals(key) || Utils.getString(R.string.EDC_REVERSAL_RETRY_TIME).equals(key)
                || Utils.getString(R.string.EDC_PREAUTH_RECORD_SAVING_DAY).equals(key)
                || Utils.getString(R.string.EDC_QR_TRANS_QUERY_INTERVAL).equals(key)
                || Utils.getString(R.string.EDC_QR_TRANS_QUERY_REPEAT_INTERVAL).equals(key)
                || Utils.getString(R.string.EDC_MAX_AMOUNT_DIGIT).equals(key)
                || Utils.getString(R.string.EDC_MAX_TRANSACTION_NUMBER).equals(key)
        ) {
            if ((Utils.getString(R.string.EDC_BATCH_NO).equals(key) || Utils.getString(R.string.EDC_TRACE_NO).equals(key)) || Utils.getString(R.string.EDC_INVOICE_NO).equals(key)) {
                if ((Utils.getString(R.string.EDC_BATCH_NO).equals(key) || Utils.getString(R.string.EDC_INVOICE_NO).equals(key)) && !GreendaoHelper.getTransDataHelper().findAllTransData(FinancialApplication.getAcqManager().getCurAcq()).isEmpty()) {
                    ToastUtils.showMessage(R.string.has_trans_for_settle);
                    return true;
                }
                if (Integer.parseInt(input) < (int) SysParam.getInstance().get(key, 1)) {
                    ToastUtils.showMessage(R.string.serial_number_cannot_be_reduced);
                    return true;
                }
            }

            if (Utils.getString(R.string.EDC_MAX_AMOUNT_DIGIT).equals(key) &&
                    (Integer.parseInt(input) > 12 || Integer.parseInt(input) < 8)) {
                ToastUtils.showMessage(R.string.out_of_range_amount);
                return false;
            }

            if (Utils.getString(R.string.EDC_MAX_TRANSACTION_NUMBER).equals(key) &&
                    (Integer.parseInt(input) > 1000 || Integer.parseInt(input) < 200)) {
                ToastUtils.showMessage(R.string.out_of_range_transaction_number);
                return false;
            }

            if (Utils.getString(R.string.EDC_QR_TRANS_QUERY_INTERVAL).equals(key) ||
                    Utils.getString(R.string.EDC_QR_TRANS_QUERY_REPEAT_INTERVAL).equals(key)) {

                if (Integer.parseInt(input) == 0) {
                    ToastUtils.showMessage(R.string.out_of_range);
                    return true;
                }
            }

            //reversal 的重试次数在1-3之间
            if (Utils.getString(R.string.EDC_REVERSAL_RETRY_TIME).equals(key) && (Integer.parseInt(input) > 3 || Integer.parseInt(input) < 1)) {
                ToastUtils.showMessage(R.string.out_of_range);
                return true;
            }
            //预授权交易的保存天数范围为1-30
            if (Utils.getString(R.string.EDC_PREAUTH_RECORD_SAVING_DAY).equals(key) && (Integer.parseInt(input) > 30 || Integer.parseInt(input) < 1)) {
                ToastUtils.showMessage(R.string.out_of_range);
                return true;
            }
            SysParam.getInstance().set(key, Integer.parseInt(input));
            return true;
        } else {
            SysParam.getInstance().set(key, input);
            return true;
        }
    }

    private boolean injectKey(String type) {
        String input = Objects.requireNonNull(tieInput.getText()).toString();
        if (input.length() != maxLen) {
            tieInput.setError(Utils.getString(R.string.input_len_err));
            return false;
        }
        writeKey(type, input);
        return true;
    }

    private void writeKey(String type, String input) {
        try {
            switch (type) {
                case SettingConst.TYPE_TMK:
                    Device.writeTMK(SysParam.getInstance().getInt(R.string.MK_INDEX)
                            , ConvertHelper.getConvert().strToBcdPaddingLeft(input));
                    break;
                case SettingConst.TYPE_TPK:
                    Device.writeTPK(ConvertHelper.getConvert().strToBcdPaddingLeft(input), null);
                    break;
                case SettingConst.TYPE_TDK:
                    Device.writeTDK(ConvertHelper.getConvert().strToBcdPaddingLeft(input), null);
                    break;
                default:
                    break;
            }
            Device.beepOk();
            ToastUtils.showMessage(R.string.set_key_success);
        } catch (PedDevException e) {
            Device.beepErr();
            ToastUtils.showMessage(Utils.getString(R.string.set_key_fail) + ":" + e.getMessage());
        }
    }

    private class Watcher implements TextWatcher {
        private int id;

        Watcher(int id) {
            this.id = id;
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            //do nothing
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            String content = s.toString();
            Acquirer acquirer = (Acquirer) any;
            switch (id) {
                case R.string.acq_terminal_id:
                    acquirer.setTerminalId(content);
                    break;
                case R.string.acq_merchant_id:
                    acquirer.setMerchantId(content);
                    break;
                default:
                    break;
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            //do nothing
        }
    }


    private <T> void updateProvider(String key, T value) {
        FinancialApplication.getApp().runInBackground(() -> {
            if (MQTTUtils.getCurrentUseProvider() != null && MQTTUtils.getCurrentDisplayProvider().getCommLabel().
                    equals(MQTTUtils.getCurrentUseProvider().getCommLabel())) {
                // 如果当前选择的和使用的是同一个，则重新设置值，防止退出之后选项错误
                if (Utils.getString(R.string.comm_label_provider).equals(key)) {
                    SysParam.getInstance().set(R.string.provider_selected_label, (String) value);
                } else if (Utils.getString(R.string.apn_provider).equals(key)) {
                    FinancialApplication.getDal().getCommManager().switchAPN(MQTTUtils.getCurrentDisplayProvider().getCommLabel(),
                            (String) value, SysParam.getInstance().getString(R.string.MOBILE_USER),
                            SysParam.getInstance().getString(R.string.MOBILE_PWD), 0);
                }
                MQTTUtils.setNeedToRestart(true);
            }
            if (Utils.getString(R.string.apn_provider).equals(key)) {
                provider.setApn((String) value);
            } else if (Utils.getString(R.string.comm_label_provider).equals(key)) {
                provider.setCommLabel((String) value);
            } else if (Utils.getString(R.string.emi_value_provider).equals(key)) {
                provider.setEmiValue((String) value);
            } else if (Utils.getString(R.string.host_ip_provider).equals(key)) {
                provider.setProviderHost((String) value);
                updateAcquirer();
            } else if (Utils.getString(R.string.port_number_provider).equals(key)) {
                provider.setProviderPort(Integer.parseInt((String) value));
                updateAcquirer();
            } else if (Utils.getString(R.string.ssl_provider).equals(key)) {
                provider.setSslType((Integer.parseInt((String) value)) == 0 ? SysParam.CommSslType.NO_SSL : SysParam.CommSslType.SSL);
                updateAcquirer();
            } else if (Utils.getString(R.string.nii_provider).equals(key)) {
                provider.setNii((String) value);
                updateAcquirer();
            } else if (Utils.getString(R.string.mqtt_ip).equals(key)) {
                provider.setMqttIp((String) value);
            } else if (Utils.getString(R.string.mqtt_port).equals(key)) {
                provider.setMqttPort(Integer.parseInt((String) value));
            } else if (Utils.getString(R.string.mqtt_qos).equals(key)) {
                provider.setQos(Integer.parseInt((String) value));
            } else if (Utils.getString(R.string.mqtt_connect_timeout).equals(key)) {
                provider.setMqttTimeout(Integer.parseInt((String) value));
            }
            MQTTUtils.setCurrentDisplayProvider(provider);
            ProviderDbHelper.getInstance().update(provider);
            MQTTUtils.startMqttOptional(provider);
        });
    }


    private void updateAcquirer() {
        Acquirer acquirer = FinancialApplication.getAcqManager().getCurAcq();
        acquirer.setIp(MQTTUtils.getCurrentDisplayProvider().getProviderHost());
        acquirer.setPort(MQTTUtils.getCurrentDisplayProvider().getProviderPort());
        acquirer.setSslType(MQTTUtils.getCurrentDisplayProvider().getSslType());
        acquirer.setNii(MQTTUtils.getCurrentDisplayProvider().getNii());
        FinancialApplication.getAcqManager().setCurAcq(acquirer);
        FinancialApplication.getAcqManager().updateAcquirer(acquirer);
    }
}
