/*
 *
 *  ============================================================================
 *  PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  This software is supplied under the terms of a license agreement or nondisclosure
 *  agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2019 -? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  Description:
 *  Revision History:
 *  Date	             Author	                Action
 *  20190418   	     ligq           	Create/Add/Modify/Delete
 *  ============================================================================
 *
 */

package com.pax.settings.inflater;

import android.content.Context;
import android.text.InputType;
import android.view.View;
import android.view.ViewStub;

import com.pax.data.entity.ClssParam;
import com.pax.data.entity.Issuer;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.EnterAmountTextWatcher;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.ConfigSecondActivity;
import com.pax.settings.ConfigThirdActivity;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.keyboard.CustomKeyboardEditText;

import org.greenrobot.eventbus.EventBus;

import static com.pax.settings.SettingConst.TYPE_CLSS;
import static com.pax.settings.SettingConst.TYPE_ISSUER;

/**
 * <AUTHOR>
 * @date 2019/4/18 16:38
 */
public class ConfigInputAmountInflater implements ConfigInflater<ConfigThirdActivity> {
    private String name;
    private String type;
    private Issuer issuer;
    private ClssParam clssParam;
    private CustomKeyboardEditText cak;
    private EnterAmountTextWatcher watcherFloorLimit;
    private String title;
    private Context context;
    private static final String RUPAY_NAME = "RUPAY";

    public ConfigInputAmountInflater(String name, String type, Context context) {
        this.name = name;
        this.type = type;
        this.context = context;
    }

    @Override
    public void inflate(ConfigThirdActivity act, String title) {
        this.title = title;
        ViewStub viewStub = act.findViewById(R.id.vs_third_content);
        viewStub.setLayoutResource(R.layout.layout_config_input);
        viewStub.inflate();
        ViewStub vsInput = act.findViewById(R.id.vs_config_input);
        vsInput.setLayoutResource(R.layout.layout_config_input_amount);
        vsInput.inflate();
        cak = act.findViewById(R.id.cke_config_input_content);
        cak.requestFocus();
        initView();
        if (TYPE_ISSUER.equals(type)) {
            issuer = FinancialApplication.getAcqManager().findIssuer(name);
            watcherFloorLimit.setAmount(0, issuer.getFloorLimit());
            cak.setText(CurrencyConverter.convert(issuer.getFloorLimit()));
        } else if (TYPE_CLSS.equals(type)) {
            clssParam = FinancialApplication.getClssParamDbHelper().findClssParam(name);
            if (Utils.getString(R.string.clss_max_limit).equals(title)) {
                watcherFloorLimit.setAmount(0, clssParam.getClssMaxLimit());
                cak.setText(CurrencyConverter.convert(clssParam.getClssMaxLimit()));
            } else if (Utils.getString(R.string.clss_offline_limit).equals(title)) {
                watcherFloorLimit.setAmount(0, clssParam.getOfflineLimit());
                cak.setText(CurrencyConverter.convert(clssParam.getOfflineLimit()));
            } else if (Utils.getString(R.string.clss_cvm_limit).equals(title)) {
                watcherFloorLimit.setAmount(0, clssParam.getCvmLimit());
                cak.setText(CurrencyConverter.convert(clssParam.getCvmLimit()));
            } else if (Utils.getString(R.string.clss_afa_limit).equals(title) && RUPAY_NAME.equals(clssParam.getName())) {
                watcherFloorLimit.setAmount(0, clssParam.getAfaLimit());
                cak.setText(CurrencyConverter.convert(clssParam.getAfaLimit()));
            }
        }
    }

    private void initView() {
        watcherFloorLimit = new EnterAmountTextWatcher();
        watcherFloorLimit.setMaxValue(99999999L);
        watcherFloorLimit.setOnTipListener(new EnterAmountTextWatcher.OnTipListener() {
            @Override
            public void onUpdateTipListener(long baseAmount, long tipAmount) {
                if (Utils.getString(R.string.issuer_floor_limit).equals(title)) {
                    issuer.setFloorLimit(tipAmount);
                } else if (Utils.getString(R.string.clss_max_limit).equals(title)) {
                    clssParam.setClssMaxLimit(tipAmount);
                } else if (Utils.getString(R.string.clss_offline_limit).equals(title)) {
                    clssParam.setOfflineLimit(tipAmount);
                } else if (Utils.getString(R.string.clss_cvm_limit).equals(title)) {
                    clssParam.setCvmLimit(tipAmount);
                } else if (Utils.getString(R.string.clss_afa_limit).equals(title) && RUPAY_NAME.equals(name)) {
                    clssParam.setAfaLimit(tipAmount);
                }
            }

            @Override
            public boolean onVerifyTipListener(long baseAmount, long tipAmount) {
                return true;
            }
        });
        cak.addTextChangedListener(watcherFloorLimit);
        //The main key should be callback
        cak.setOnEditorActionListener(new EditorActionListener() {
            @Override
            protected void onKeyOk() {
                ((ConfigThirdActivity) context).onKeyOkDown();
            }

            @Override
            protected void onKeyCancel() {
                ((ConfigThirdActivity) context).onKeyBackDown();
            }
        });
    }

    @Override
    public boolean doNextSuccess() {
        if (TYPE_ISSUER.equals(type)) {
            issuer.setFloorLimit(CurrencyConverter.parse(cak.getText().toString()));
            FinancialApplication.getAcqManager().updateIssuer(issuer);
            EventBus.getDefault().post(new ConfigSecondActivity.ConfigEvent(type, issuer.getName(), null));
        }
        if (TYPE_CLSS.equals(type)) {
            boolean result = true;
            if (Utils.getString(R.string.clss_max_limit).equals(title)) {
                clssParam.setClssMaxLimit(CurrencyConverter.parse(cak.getText().toString()));
            } else if (Utils.getString(R.string.clss_offline_limit).equals(title)) {
                clssParam.setOfflineLimit(CurrencyConverter.parse(cak.getText().toString()));
            } else if (Utils.getString(R.string.clss_cvm_limit).equals(title)) {
                clssParam.setCvmLimit(CurrencyConverter.parse(cak.getText().toString()));
            } else if (Utils.getString(R.string.clss_afa_limit).equals(title) && RUPAY_NAME.equals(name)) {
                clssParam.setAfaLimit(CurrencyConverter.parse(cak.getText().toString()));
            } else {
                result = false;
            }
            if (result) {
                FinancialApplication.getClssParamDbHelper().update(clssParam);
                EventBus.getDefault().post(new ConfigSecondActivity.ConfigEvent(TYPE_CLSS, clssParam.getName(), null));
            }
        }
        return true;
    }
}
