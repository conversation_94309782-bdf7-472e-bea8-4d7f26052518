/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-30
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.settings;

import android.content.SharedPreferences;
import android.preference.CheckBoxPreference;
import android.preference.EditTextPreference;
import android.preference.ListPreference;
import android.preference.MultiSelectListPreference;
import android.preference.Preference;
import android.preference.PreferenceManager;
import android.preference.PreferenceScreen;
import android.preference.RingtonePreference;
import android.provider.Settings;
import androidx.annotation.StringRes;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.trans.receipt.PrintListenerImpl;
import com.pax.pay.trans.receipt.ReceiptPrintParam;
import com.pax.pay.trans.transmit.TransOnline;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.trans.transmit.TransProcessListenerImpl;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;

public class OtherManageFragment extends BasePreferenceFragment {

    private boolean isFirst = FinancialApplication.getController().isFirstRun();

    @Override
    protected int getResourceId() {
        return R.xml.other_manage_pref;
    }

    @Override
    public boolean onPreferenceTreeClick(PreferenceScreen preferenceScreen, Preference preference) {
        int resId = preference.getTitleRes();
        switch (resId) {
            case R.string.om_clearTrade_menu_reversal:
            case R.string.om_clearTrade_menu_trade_voucher:
            case R.string.om_clearTrade_menu_key:
                showClearDialog(resId);
                break;
            case R.string.om_download_menu_echo_test:
                downloadFunc(resId);
                break;
            case R.string.om_paramPrint_menu_print_config_para:
            case R.string.om_paramPrint_menu_print_aid_para:
            case R.string.om_paramPrint_menu_print_capk_para:
                showPrintParamDialog(resId);
                break;
            case R.string.go_system_setting_date:
                Utils.callSystemSettings(getActivity(), Settings.ACTION_DATE_SETTINGS);
                break;
            default:
                return super.onPreferenceTreeClick(preferenceScreen, preference);
        }
        return true;
    }

    private void showClearDialog(final int resId) {
        final CustomAlertDialog dialog = new CustomAlertDialog(getActivity(), CustomAlertDialog.NORMAL_TYPE);

        dialog.setCancelClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
            }
        });
        dialog.setConfirmClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                clearFunc(resId);
            }
        });
        dialog.show();
        String prompt = getString(R.string.om_clear_prompt, getString(resId));
        dialog.setNormalText(prompt);
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
    }

    private void showPrintParamDialog(final int resId) {
        final CustomAlertDialog dialog = new CustomAlertDialog(getActivity(), CustomAlertDialog.NORMAL_TYPE);

        dialog.setCancelClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
            }
        });
        dialog.setConfirmClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                paraPrint(resId);
            }
        });
        dialog.show();
        String prompt = getString(R.string.om_paramPrint_confirm, getString(resId));
        dialog.setNormalText(prompt);
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
    }

    @Override
    protected void initPreference() {
        //do nothing
    }

    @Override
    protected boolean onEditTextPreferenceChanged(EditTextPreference preference, Object value, boolean isInitLoading) {
        return false;
    }

    @Override
    protected boolean onRingtonePreferenceChanged(RingtonePreference preference, Object value, boolean isInitLoading) {
        return false;
    }

    @Override
    protected boolean onCheckBoxPreferenceChanged(CheckBoxPreference preference, Object value, boolean isInitLoading) {
        return false;
    }

    @Override
    protected boolean onListPreferenceChanged(ListPreference preference, Object value, boolean isInitLoading) {
        return true;
    }

    @Override
    protected boolean onMultiSelectListPreferenceChanged(MultiSelectListPreference preference, Object value, boolean isInitLoading) {
        return true;
    }

    private void clearFunc(@StringRes final int resId) {
        final boolean isDone;

        final CustomAlertDialog dialog = DialogUtils.showProcessingMessage(getActivity(), getActivity().getString(R.string.wait_process), -1);

        switch (resId) {
            case R.string.om_clearTrade_menu_reversal:
                isDone = FinancialApplication.getTransDataDbHelper().deleteDupRecord();
                clearSettleStatus();
                break;
            case R.string.om_clearTrade_menu_trade_voucher:
                FinancialApplication.getController().set(Controller.BATCH_UP_STATUS, Controller.Constant.WORKED);
                isDone = FinancialApplication.getTransDataDbHelper().deleteAllTransData();
                FinancialApplication.getTransTotalDbHelper().deleteAllTransTotal();
                FinancialApplication.getController().set(Controller.CLEAR_LOG, Controller.Constant.NO);
                clearSettleStatus();
                break;
            case R.string.om_clearTrade_menu_key:
                isDone = Device.eraseKeys();
                break;
            default:
                isDone = false;
                break;

        }
        FinancialApplication.getApp().runOnUiThreadDelay(new Runnable() {
            @Override
            public void run() {
                dialog.dismiss();

                if (isDone) {
                    DialogUtils.showSuccMessage(getActivity(), "", null,
                            Constants.SUCCESS_DIALOG_SHOW_TIME);
                } else {
                    Device.beepErr();
                }
            }
        }, 3000);
    }

    private void clearSettleStatus() {
        SharedPreferences preferences =
                PreferenceManager.getDefaultSharedPreferences(getActivity());
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(SysParam.BooleanParam.AMEX_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_OLS_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_EMV_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_CUP_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_DCC_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_MACAO_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP1_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP2_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP3_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP4_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP5_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP6_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP7_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP8_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP9_SETTLE_STATUS.toString(), true);
        editor.putBoolean(SysParam.BooleanParam.HASE_INSP10_SETTLE_STATUS.toString(), true);
        editor.apply();
    }

    private void downloadFunc(@StringRes final int resId) {
        if (isFirst) { //AET-269
            ToastUtils.showMessage(R.string.wait_2_init_device);
            return;
        }
        FinancialApplication.getApp().runInBackground(new Runnable() {

            @Override
            public void run() {
                int ret = -1;
                TransProcessListener listenerImpl = new TransProcessListenerImpl(getActivity());

                if (R.string.om_download_menu_echo_test == resId) {
                    ret = new TransOnline().echo(listenerImpl);
                }

                listenerImpl.onHideProgress();

                if (ret == TransResult.SUCC) {
                    DialogUtils.showSuccMessage(getActivity(), "Connect", null,
                            Constants.SUCCESS_DIALOG_SHOW_TIME);
                    //Device.beepOk();
                } else if (ret != TransResult.ERR_ABORTED && ret != TransResult.ERR_HOST_REJECT) {
                    listenerImpl.onShowErrMessage(TransResultUtils.getMessage(ret),
                            Constants.FAILED_DIALOG_SHOW_TIME, true);
                }
                // ERR_ABORTED AND ERR_HOST_REJECT 之前已提示错误信息， 此处不需要再提示
            }
        });

    }

    //AET-133
    private void paraPrint(@StringRes final int resId) {
        FinancialApplication.getApp().runInBackground(new Runnable() {

            @Override
            public void run() {

                ReceiptPrintParam receiptPrintParam = new ReceiptPrintParam();
                switch (resId) {
                    case R.string.om_paramPrint_menu_print_aid_para:
                        receiptPrintParam.print(ReceiptPrintParam.AID, new PrintListenerImpl(getActivity()));
                        break;
                    case R.string.om_paramPrint_menu_print_capk_para:
                        receiptPrintParam.print(ReceiptPrintParam.CAPK, new PrintListenerImpl(getActivity()));
                        break;
                    case R.string.om_paramPrint_menu_print_config_para:
                        receiptPrintParam.print(ReceiptPrintParam.Config, new PrintListenerImpl(getActivity()));
                        break;
                    default:
                        break;
                }
                Device.beepOk();
            }
        });
    }
}
