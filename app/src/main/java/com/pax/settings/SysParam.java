/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-30
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.settings;

import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.preference.PreferenceManager;
import android.util.Log;
import android.util.Xml;

import com.pax.abl.utils.EncUtils;
import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.settings.spupgrader.SpUpgrader;
import com.pax.settings.spupgrader.Upgrade1To2;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

public class SysParam {

    private static final String TAG = "SysParam";

    private static final String UPGRADER_PATH = "com.pax.settings.spupgrader";

    private static final String IS_PARAM_FILE_EXIST = "IS_PARAM_FILE_EXIST";
    private static final String VERSION_TAG = "PARAM_VERSION";
    private static final int VERSION = 10;

    public enum NumberParam {
        //        COMM_TIMEOUT(Utils.getString(R.string.COMM_TIMEOUT)),
        COMM_CONNECT_TIMEOUT(Utils.getString(R.string.COMM_CONNECT_TIMEOUT)),
        COMM_SEND_TIMEOUT(Utils.getString(R.string.COMM_SEND_TIMEOUT)),
        COMM_RECEIVE_TIMEOUT(Utils.getString(R.string.COMM_RECEIVE_TIMEOUT)),
        MOBILE_LOGIN_WAIT_TIME(Utils.getString(R.string.MOBILE_LOGIN_WAIT_TIME)),
        MOBILE_HOST_PORT(Utils.getString(R.string.MOBILE_HOST_PORT)),
        MOBILE_HOST_PORT_BAK(Utils.getString(R.string.MOBILE_HOST_PORT_BAK)),
        LAN_HOST_PORT(Utils.getString(R.string.LAN_HOST_PORT)),
        LAN_HOST_PORT_BAK(Utils.getString(R.string.LAN_HOST_PORT_BAK)),
        COMM_REDIAL_TIMES(Utils.getString(R.string.COMM_REDIAL_TIMES)),
        EDC_RECEIPT_NUM(Utils.getString(R.string.EDC_RECEIPT_NUM)),
        EDC_SMTP_SSL_PORT(Utils.getString(R.string.EDC_SMTP_SSL_PORT)),
        EDC_SMTP_PORT(Utils.getString(R.string.EDC_SMTP_PORT)),
        EDC_REVERSAL_RETRY(Utils.getString(R.string.EDC_REVERSAL_RETRY)),
        EDC_CVM_LIMIT_VISA(Utils.getString(R.string.EDC_CVM_LIMIT_VISA)),
        EDC_CVM_LIMIT_MASTER(Utils.getString(R.string.EDC_CVM_LIMIT_MASTER)),
        EDC_CVM_LIMIT_JCB(Utils.getString(R.string.EDC_CVM_LIMIT_JCB)),
        EDC_CVM_LIMIT_CUP(Utils.getString(R.string.EDC_CVM_LIMIT_CUP)),
        EDC_CVM_LIMIT_AMEX(Utils.getString(R.string.EDC_CVM_LIMIT_AMEX)),
        EDC_FLOOR_LIMIT_VISA(Utils.getString(R.string.EDC_FLOOR_LIMIT_VISA)),
        EDC_FLOOR_LIMIT_MASTER(Utils.getString(R.string.EDC_FLOOR_LIMIT_MASTER)),
        EDC_FLOOR_LIMIT_JCB(Utils.getString(R.string.EDC_FLOOR_LIMIT_JCB)),
        EDC_FLOOR_LIMIT_CUP(Utils.getString(R.string.EDC_FLOOR_LIMIT_CUP)),
        EDC_FLOOR_LIMIT_AMEX(Utils.getString(R.string.EDC_FLOOR_LIMIT_AMEX)),
        EDC_TRANSACTION_LIMIT_VISA(Utils.getString(R.string.EDC_TRANSACTION_LIMIT_VISA)),
        EDC_TRANSACTION_LIMIT_MASTER(Utils.getString(R.string.EDC_TRANSACTION_LIMIT_MASTER)),
        EDC_TRANSACTION_LIMIT_JCB(Utils.getString(R.string.EDC_TRANSACTION_LIMIT_JCB)),
        EDC_TRANSACTION_LIMIT_CUP(Utils.getString(R.string.EDC_TRANSACTION_LIMIT_CUP)),
        EDC_TRANSACTION_LIMIT_AMEX(Utils.getString(R.string.EDC_TRANSACTION_LIMIT_AMEX)),
        EDC_FREE_TIME(Utils.getString(R.string.EDC_FREE_TIME)),
        EDC_ECR_RESP_MSG_DELAY(Utils.getString(R.string.EDC_ECR_RESP_MSG_DELAY)),
        EDC_INPUT_PASSWORD_TIMEOUT(Utils.getString(R.string.EDC_INPUT_PASSWORD_TIMEOUT)),

        ECR_WIFI_PORT(Utils.getString(R.string.ECR_WIFI_PORT)),

        MAX_TRANS_COUNT(Utils.getString(R.string.MAX_TRANS_COUNT)),
        MK_INDEX(Utils.getString(R.string.MK_INDEX)),
        MK_INDEX_MANUAL(Utils.getString(R.string.MK_INDEX_MANUAL)),
        //master card key index
        MASTERCARD_MK_INDEX(Utils.getString(R.string.MASTERCARD_MK_INDEX)),
        MASTERCARD_MK_INDEX_MANUAL(Utils.getString(R.string.MASTERCARD_MK_INDEX_MANUAL)),

        OFFLINE_TC_UPLOAD_TIMES(Utils.getString(R.string.OFFLINE_TC_UPLOAD_TIMES)),  // 离线上送次数
        OFFLINE_TC_UPLOAD_NUM(Utils.getString(R.string.OFFLINE_TC_UPLOAD_NUM)), // 自动上送累计笔数
        EDC_TRACE_NO(Utils.getString(R.string.EDC_TRACE_NO)),
        EDC_INVOICE_NO(Utils.getString(R.string.EDC_INVOICE_NO)),
        EDC_NO_SIGN_AMT_VISA(Utils.getString(R.string.EDC_NO_SIGN_AMT_VISA)),
        EDC_NO_SIGN_AMT_MC(Utils.getString(R.string.EDC_NO_SIGN_AMT_MC)),
        EDC_NO_SIGN_AMT_AMEX(Utils.getString(R.string.EDC_NO_SIGN_AMT_AMEX)),
        EDC_NO_SIGN_AMT_CUP(Utils.getString(R.string.EDC_NO_SIGN_AMT_CUP)),
        EDC_NO_SIGN_AMT_JCB(Utils.getString(R.string.EDC_NO_SIGN_AMT_JCB)),
        QUICK_PASS_TRANS_PIN_FREE_AMOUNT(Utils.getString(R.string.QUICK_PASS_TRANS_PIN_FREE_AMOUNT)),
        QUICK_PASS_TRANS_SIGN_FREE_AMOUNT(Utils.getString(R.string.QUICK_PASS_TRANS_SIGN_FREE_AMOUNT)),
        INTENDED_OFFLINE_MONEY_LIMIE(Utils.getString(R.string.INTENDED_OFFLINE_MONEY_LIMIE)),
        INTENDED_OFFLINE_ECHO_TIME(Utils.getString(R.string.INTENDED_OFFLINE_ECHO_TIME)),
        INTENDED_OFFLINE_CLOSE_FIRST(Utils.getString(R.string.INTENDED_OFFLINE_CLOSE_FIRST));

        private String str;

        NumberParam(String str) {
            this.str = str;
        }

        @Override
        public String toString() {
            return str;
        }
    }

    public enum StringParam {
        /**
         * 通讯方式
         */
        COMM_TYPE(Utils.getString(R.string.COMM_TYPE)),

        /**
         * 移动网络
         */
        MOBILE_TEL_NO(Utils.getString(R.string.MOBILE_TEL_NO)),
        MOBILE_APN(Utils.getString(R.string.MOBILE_APN)),
        MOBILE_USER(Utils.getString(R.string.MOBILE_USER)),
        MOBILE_PWD(Utils.getString(R.string.MOBILE_PWD)),
        MOBILE_SIM_PIN(Utils.getString(R.string.MOBILE_SIM_PIN)),
        MOBILE_AUTH(Utils.getString(R.string.MOBILE_AUTH)),
        MOBILE_HOST_IP(Utils.getString(R.string.MOBILE_HOST_IP)),
        MOBILE_HOST_IP_BAK(Utils.getString(R.string.MOBILE_HOST_IP_BAK)),
        MOBILE_DOMAIN_NAME(Utils.getString(R.string.MOBILE_DOMAIN_NAME)),

        // 以太网参数
        LAN_LOCAL_IP(Utils.getString(R.string.LAN_LOCAL_IP)),
        LAN_NETMASK(Utils.getString(R.string.LAN_NETMASK)),
        LAN_GATEWAY(Utils.getString(R.string.LAN_GATEWAY)),
        LAN_DNS1(Utils.getString(R.string.LAN_DNS1)),
        LAN_DNS2(Utils.getString(R.string.LAN_DNS2)),
        LAN_HOST_IP(Utils.getString(R.string.LAN_HOST_IP)),
        LAN_HOST_IP_BAK(Utils.getString(R.string.LAN_HOST_IP_BAK)),

        EDC_MERCHANT_NAME_EN(Utils.getString(R.string.EDC_MERCHANT_NAME_EN)),
        EDC_MERCHANT_ADDRESS(Utils.getString(R.string.EDC_MERCHANT_ADDRESS)),
        EDC_CURRENCY_LIST(Utils.getString(R.string.EDC_CURRENCY_LIST)),
        EDC_PED_MODE(Utils.getString(R.string.EDC_PED_MODE)),

        EDC_SMTP_HOST(Utils.getString(R.string.EDC_SMTP_HOST)),
        EDC_SMTP_USERNAME(Utils.getString(R.string.EDC_SMTP_USERNAME)),
        EDC_SMTP_PASSWORD(Utils.getString(R.string.EDC_SMTP_PASSWORD)),
        EDC_SMTP_FROM(Utils.getString(R.string.EDC_SMTP_FROM)),
        KEY_ALGORITHM(Utils.getString(R.string.KEY_ALGORITHM)),

        //download card bin
        DOWNLOAD_CARD_BIN_MODE(Utils.getString(R.string.DOWNLOAD_CARD_BIN_MODE)),
        SETUP_AUTO_LOAD(Utils.getString(R.string.SETUP_AUTO_LOAD)),

        //loyalty settlement Response Code
        LOYALTY_SETTEL_RESPONSE(Utils.getString(R.string.LOYALTY_SETTEL_RESPONSE)),

        //Loyalty
        CDS_TYPE(Utils.getString(R.string.CDS_TYPE)),
        MERCHANT1_DOL_NAME(Utils.getString(R.string.MERCHANT1_DOL_NAME)),
        MERCHANT2_DOL_NAME(Utils.getString(R.string.MERCHANT2_DOL_NAME)),
        BONUS_DOL_NAME(Utils.getString(R.string.BONUS_DOL_NAME)),
        HASE_CASH_DOL_NAME(Utils.getString(R.string.HASE_CASH_DOL_NAME)),

        EDC_AMEX_ORIGIN(Utils.getString(R.string.EDC_AMEX_ORIGIN)),
        EDC_AMEX_REGION(Utils.getString(R.string.EDC_AMEX_REGION)),
        EDC_AMEX_COUNTRY(Utils.getString(R.string.EDC_AMEX_COUNTRY)),
        EDC_AMEX_MESSAGE(Utils.getString(R.string.EDC_AMEX_MESSAGE)),
        EDC_AMEX_RTIND(Utils.getString(R.string.EDC_AMEX_RTIND)),

        SEC_SYS_PWD(Utils.getString(R.string.SEC_SYS_PWD)),
        SEC_MERCHANT_PWD(Utils.getString(R.string.SEC_MERCHANT_PWD)),
        SEC_MANUAL_PWD(Utils.getString(R.string.SEC_MANUAL_PWD)),
        SEC_TERMINAL_PWD(Utils.getString(R.string.SEC_TERMINAL_PWD)),
        SEC_VOID_PWD(Utils.getString(R.string.SEC_VOID_PWD)),
        SEC_REFUND_PWD(Utils.getString(R.string.SEC_REFUND_PWD)),
        SEC_ADJUST_PWD(Utils.getString(R.string.SEC_ADJUST_PWD)),
        SEC_SETTLE_PWD(Utils.getString(R.string.SEC_SETTLE_PWD)),
        SEC_DCC_OPT_OUT_PWD(Utils.getString(R.string.SEC_DCC_OPT_OUT_PWD)),
        SEC_RATE_REPORT_PWD(Utils.getString(R.string.SEC_RATE_REPORT_PWD)),
        SEC_MANAGEMENT_PWD(Utils.getString(R.string.SEC_MANAGEMENT_PWD)),

        WIFI_NAME(Utils.getString(R.string.WIFI_NAME)),
        WIFI_PASSWORD(Utils.getString(R.string.WIFI_PASSWORD)),
        WIFI_MANAGE_PASSWORD(Utils.getString(R.string.WIFI_MANAGE_PASSWORD)),

        ACQ_NAME(Utils.getString(R.string.ACQ_NAME)),  //当前收单行名字

        MK_VALUE(Utils.getString(R.string.MK_VALUE)),
        PK_VALUE(Utils.getString(R.string.PK_VALUE)),
        AK_VALUE(Utils.getString(R.string.AK_VALUE)),
        //MasterCard key
        MASTERCARD_MK_VALUE(Utils.getString(R.string.MASTERCARD_MK_VALUE)),
        MASTERCARD_PK_VALUE(Utils.getString(R.string.MASTERCARD_PK_VALUE)),
        MASTERCARD_AK_VALUE(Utils.getString(R.string.MASTERCARD_AK_VALUE)),

        //APP语言
        APP_LANGUAGE(Utils.getString(R.string.APP_LANGUAGE)),

        EDC_MERCHANT_SELECTION(Utils.getString(R.string.EDC_MERCHANT_SELECTION)),

        //ECR连接方式
        ECR_COMM(Utils.getString(R.string.ECR_COMM)),

        PARAM_TEMPLATE_VERSION(Utils.getString(R.string.PARAM_TEMPLATE_VERSION)),

        INTENDED_OFFLINE_PASSWORD(Utils.getString(R.string.INTENDED_OFFLINE_PASSWORD)),
        APPROVAL_CODE(Utils.getString(R.string.APPROVAL_CODE));

        private String str;

        StringParam(String str) {
            this.str = str;
        }

        @Override
        public String toString() {
            return str;
        }
    }

    public enum BooleanParam {
        LAN_DHCP(Utils.getString(R.string.LAN_DHCP)),
        MOBILE_KEEP_ALIVE(Utils.getString(R.string.MOBILE_KEEP_ALIVE)),
        MOBILE_NEED_USER(Utils.getString(R.string.MOBILE_NEED_USER)),

        EDC_SUPPORT_TIP(Utils.getString(R.string.EDC_SUPPORT_TIP)),     // 支持小费
        EDC_TIP_TYPE(Utils.getString(R.string.EDC_TIP_TYPE)),     // 是否为售前小费
        EDC_SUPPORT_REFERRAL(Utils.getString(R.string.EDC_SUPPORT_REFERRAL)), // 支持Referral
        EDC_ENABLE_ECR_ONLY(Utils.getString(R.string.EDC_ENABLE_ECR_ONLY)),
        EDC_MASK_CARD_NO(Utils.getString(R.string.EDC_MASK_CARD_NO)),   // 屏蔽卡号
        EDC_ENABLE_LOYALTY(Utils.getString(R.string.EDC_ENABLE_LOYALTY)),
        EDC_ENABLE_INSTALMENT(Utils.getString(R.string.EDC_ENABLE_INSTALMENT)),
        EDC_ENABLE_AUTHORIZATION(
                Utils.getString(R.string.EDC_ENABLE_AUTHORIZATION)),             //允许授权
        EDC_ENABLE_OFFLINE(Utils.getString(R.string.EDC_ENABLE_OFFLINE)),             //允许离线交易
        EDC_ENABLE_REFUND(Utils.getString(R.string.EDC_ENABLE_REFUND)),
        EDC_ENABLE_CUP_AUTH(Utils.getString(R.string.EDC_ENABLE_CUP_AUTH)),
        EDC_VOID_PWD(Utils.getString(R.string.EDC_VOID_PWD)),             //撤销密码
        EDC_MERCHANT_PWD(Utils.getString(R.string.EDC_MERCHANT_PWD)),         //人工输入密码
        EDC_MANUAL_PWD(Utils.getString(R.string.EDC_MANUAL_PWD)),         //人工输入密码
        EDC_REFUND_PWD(Utils.getString(R.string.EDC_REFUND_PWD)),         // 退货密码
        EDC_SETTLEMENT_PWD(Utils.getString(R.string.EDC_SETTLEMENT_PWD)), //结算密码
        EDC_NOT_ENABLE_MANAGEMENT_PWD(
                Utils.getString(R.string.EDC_NOT_ENABLE_MANAGEMENT_PWD)), //允许管理密码
        EDC_DCCOPTOUT_PWD(Utils.getString(R.string.EDC_DCCOPTOUT_PWD)),
        EDC_RATEREPORT_PWD(Utils.getString(R.string.EDC_RATEREPORT_PWD)),
        EDC_ADJUST_PWD(Utils.getString(R.string.EDC_ADJUST_PWD)),

        EDC_ENABLE_PAPERLESS(Utils.getString(R.string.EDC_ENABLE_PAPERLESS)),
        EDC_SMTP_ENABLE_SSL(Utils.getString(R.string.EDC_SMTP_ENABLE_SSL)),

        OTHTC_VERIFY(Utils.getString(R.string.OTHTC_VERIFY)),  // 撤销退货类交易输入主管密码

        EDC_SUPPORT_DCC(Utils.getString(R.string.EDC_SUPPORT_DCC)),
        EDC_SUPPORT_FO(Utils.getString(R.string.EDC_SUPPORT_FO)),
        EDC_SUPPORT_DCC_OPT_OUT(Utils.getString(R.string.EDC_SUPPORT_DCC_OPT_OUT)),
        EDC_SUPPORT_DCC_INC_AUTH(Utils.getString(R.string.EDC_SUPPORT_DCC_INC_AUTH)),
        EDC_SUPPORT_DCC_AUTH_REVERSAL(Utils.getString(R.string.EDC_SUPPORT_DCC_AUTH_REVERSAL)),
        EDC_SUPPORT_ECR_CHOOSE_DCC(Utils.getString(R.string.EDC_SUPPORT_ECR_CHOOSE_DCC)),
        EDC_DCC_SALE_NEW_FLOW(Utils.getString(R.string.EDC_DCC_SALE_NEW_FLOW)),
        EDC_DCC_AUTH_NEW_FLOW(Utils.getString(R.string.EDC_DCC_AUTH_NEW_FLOW)),
        EDC_INVERSE_RATE(Utils.getString(R.string.EDC_INVERSE_RATE)),
        EDC_ENABLE_REFUND_RRN(Utils.getString(R.string.EDC_ENABLE_REFUND_RRN)),
        EDC_ENABLE_OFFLINE_RRN(Utils.getString(R.string.EDC_ENABLE_OFFLINE_RRN)),
        EDC_ENABLE_PRINT_LOG(Utils.getString(R.string.EDC_ENABLE_PRINT_LOG)),

        /**
         * 银行卡闪付 参数
         **/
        QUICK_PASS_TRANS_PIN_FREE_SWITCH(Utils.getString(R.string.QUICK_PASS_TRANS_PIN_FREE_SWITCH)),
        QUICK_PASS_TRANS_FLAG(Utils.getString(R.string.QUICK_PASS_TRANS_FLAG)),
        QUICK_PASS_TRANS_SWITCH(Utils.getString(R.string.QUICK_PASS_TRANS_SWITCH)),

        QUICK_PASS_TRANS_CDCVM_FLAG(Utils.getString(R.string.QUICK_PASS_TRANS_CDCVM_FLAG)),

        QUICK_PASS_TRANS_SIGN_FREE_FLAG(Utils.getString(R.string.QUICK_PASS_TRANS_SIGN_FREE_FLAG)),

        TTS_SALE(Utils.getString(R.string.TTS_SALE)),
        TTS_VOID(Utils.getString(R.string.TTS_VOID)),
        TTS_REFUND(Utils.getString(R.string.TTS_REFUND)),
        TTS_PREAUTH(Utils.getString(R.string.TTS_PREAUTH)),
        TTS_ADJUST(Utils.getString(R.string.TTS_ADJUST)),

        //Loyalty Pure Redeem
        REDM_CASH(Utils.getString(R.string.REDM_CASH)),
        REDM_MERCHANT1_DOL(Utils.getString(R.string.REDM_MERCHANT1_DOL)),
        REDM_MERCHANT2_DOL(Utils.getString(R.string.REDM_MERCHANT2_DOL)),
        DEFAULT_TRAN(Utils.getString(R.string.DEFAULT_TRAN)),
        CASH_CHOICE(Utils.getString(R.string.CASH_CHOICE)),

        //Acquirer settlement status
        HASE_OLS_SETTLE_STATUS(Utils.getString(R.string.HASE_OLS_SETTLE_STATUS)),
        HASE_SETTLE_STATUS(Utils.getString(R.string.HASE_OLS_SETTLE_STATUS)),
        HASE_EMV_SETTLE_STATUS(Utils.getString(R.string.HASE_EMV_SETTLE_STATUS)),
        HASE_INS06_SETTLE_STATUS(Utils.getString(R.string.HASE_INS06_SETTLE_STATUS)),
        HASE_INS12_SETTLE_STATUS(Utils.getString(R.string.HASE_INS12_SETTLE_STATUS)),
        HASE_INS18_SETTLE_STATUS(Utils.getString(R.string.HASE_INS18_SETTLE_STATUS)),
        HASE_INS24_SETTLE_STATUS(Utils.getString(R.string.HASE_INS24_SETTLE_STATUS)),
        HASE_INS30_SETTLE_STATUS(Utils.getString(R.string.HASE_INS30_SETTLE_STATUS)),
        HASE_INS36_SETTLE_STATUS(Utils.getString(R.string.HASE_INS36_SETTLE_STATUS)),
        HASE_CUP_SETTLE_STATUS(Utils.getString(R.string.HASE_CUP_SETTLE_STATUS)),
        HASE_DCC_SETTLE_STATUS(Utils.getString(R.string.HASE_DCC_SETTLE_STATUS)),
        HASE_MACAO_SETTLE_STATUS(Utils.getString(R.string.HASE_MACAO_SETTLE_STATUS)),
        AMEX_SETTLE_STATUS(Utils.getString(R.string.AMEX_SETTLE_STATUS)),
        HASE_INSP1_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP1_SETTLE_STATUS)),
        HASE_INSP2_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP2_SETTLE_STATUS)),
        HASE_INSP3_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP3_SETTLE_STATUS)),
        HASE_INSP4_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP4_SETTLE_STATUS)),
        HASE_INSP5_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP5_SETTLE_STATUS)),
        HASE_INSP6_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP6_SETTLE_STATUS)),
        HASE_INSP7_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP7_SETTLE_STATUS)),
        HASE_INSP8_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP8_SETTLE_STATUS)),
        HASE_INSP9_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP9_SETTLE_STATUS)),
        HASE_INSP10_SETTLE_STATUS(Utils.getString(R.string.HASE_INSP10_SETTLE_STATUS)),

        //Reversal debug
        ALLOW_REVERSAL_DEBUG(Utils.getString(R.string.ALLOW_REVERSAL_DEBUG)),

        //Debug mode
        DEBUG_MODE(Utils.getString(R.string.DEBUG_MDOE)),

        //Base link
        BASE_ENABLE(Utils.getString(R.string.BASE_ENABLE)),

        //支持ECR
        ECR_ENABLE(Utils.getString(R.string.ECR_ENABLE)),

        //enable upi
        EDC_ENABLE_UPI(Utils.getString(R.string.EDC_ENABLE_UPI)),

        //enable plc
        EDC_ENABLE_PLC(Utils.getString(R.string.EDC_ENABLE_PLC)),

        //enable EPS
        EDC_ENABLE_EPS(Utils.getString(R.string.EDC_ENABLE_EPS)),

        //enable Micropay
        EDC_ENABLE_MICROPAY(Utils.getString(R.string.EDC_ENABLE_MICROPAY)),

        //enable FPS
        EDC_ENABLE_FPS(Utils.getString(R.string.EDC_ENABLE_FPS)),

        //enable party king
        EDC_ENABLE_PARTYKING(Utils.getString(R.string.EDC_ENABLE_PARTY_KING)),

        //Skip merchant copy if no signature
        EDC_SKIP_MERCHANT_COPY(Utils.getString(R.string.EDC_SKIP_MERCHANT_COPY)),

        //Handset alert
        EDC_HANDSET_ALERT(Utils.getString(R.string.EDC_HANDSET_ALERT)),

        //MasterCard QRCode mode
        EDC_IS_PRODUCTION_MODE(Utils.getString(R.string.EDC_IS_PRODUCTION_MODE)),

        //Tmk type
        TMK_TYPE(Utils.getString(R.string.TMK_TYPE)),

        //Intended offline mode
        EDC_ENABLE_INTENDED_OFFLINE_MODE(Utils.getString(R.string.EDC_INTENDED_OFFLINE_MODE)),


        //ENABLE_SHOW_SSID(Utils.getString(R.string.ENABLE_SHOW_SSID));

        //Skip merchant copy if no signature
        EDC_SKIP_MERCHANT_COPY_VM(Utils.getString(R.string.EDC_SKIP_MERCHANT_COPY_VM)),

        EDC_SKIP_MERCHANT_COPY_JCB(Utils.getString(R.string.EDC_SKIP_MERCHANT_COPY_JCB)),

        EDC_SKIP_MERCHANT_COPY_CUP(Utils.getString(R.string.EDC_SKIP_MERCHANT_COPY_CUP)),

        EDC_SKIP_MERCHANT_COPY_AMEX(Utils.getString(R.string.EDC_SKIP_MERCHANT_COPY_AMEX)),

        EDC_ECR_ENABLE_REDEEM(Utils.getString(R.string.ECR_ENABLE_REDEEM)),

        //CUP SALE/REFUND
        EDC_ENABLE_CUP_SALE(Utils.getString(R.string.EDC_ENABLE_CUP_SALE)),
        EDC_ENABLE_CUP_REFUND(Utils.getString(R.string.EDC_ENABLE_CUP_REFUND)),

        //LYS 交易 disclaimer
        EDC_ENABLE_LYS_DISCLAIMER(Utils.getString(R.string.EDC_ENABLE_LYS_DISCLAIMER)),

        // V/M 卡 fallback 交易
        EDC_ENABLE_VM_FALLBACK_BLOCK(Utils.getString(R.string.EDC_ENABLE_VM_FALLBACK_BLOCK)),

        //Sale 交易免签开关
        EDC_ENABLE_SALE_TRANS_NO_SIGN(Utils.getString(R.string.EDC_ENABLE_SALE_TRANS_NO_SIGN)),

        //CUP 输PIN交易开关
        EDC_REJECT_CUP_TRANS_WITH_PIN(Utils.getString(R.string.EDC_REJECT_CUP_TRANS_WITH_PIN)),

        //CUP 输PIN交易开关
        EDC_ENABLE_QR_PAYMENT(Utils.getString(R.string.EDC_ENABLE_QR_PAYMENT)),

        SEND_PRINT_INFO(Utils.getString(R.string.send_print_info_key)),

        PRINT_CARDHOLDER_NAME(Utils.getString(R.string.print_cardholder_name_key)),

        EDC_ENABLE_PAYMENT_LOGO(Utils.getString(R.string.EDC_ENABLE_PAYMENT_LOGO)),

        EDC_ENABLE_SCREEN_SAVER(Utils.getString(R.string.EDC_ENABLE_SCREEN_SAVER)),
        EDC_ENABLE_VIDEO_FIRST(Utils.getString(R.string.EDC_ENABLE_VIDEO_FIRST)),
        EDC_SCREEN_SAVER_VERTICAL_SCREEN(Utils.getString(R.string.EDC_SCREEN_SAVER_VERTICAL_SCREEN)),


        EDC_ENABLE_ECR_HEART_BEAT(Utils.getString(R.string.EDC_ECR_HEART_BEAT)),

        EDC_ENABLE_WIFI_MANAGE(Utils.getString(R.string.EDC_ENABLE_WIFI_MANAGE)),

        //Micro Pay
        MICRO_PAY_IS_STAND_ALONE(Utils.getString(R.string.MICRO_PAY_IS_STAND_ALONE)),
        MICRO_PAY_IS_DO_ECR_TRANS(Utils.getString(R.string.MICRO_PAY_IS_DO_ECR_TRANS)),

        EDC_ENABLE_INTENDED_OFFLINE(Utils.getString(R.string.EDC_ENABLE_INTENDED_OFFLINE)),

        EDC_ENABLE_CUP_ECR(Utils.getString(R.string.EDC_ENABLE_CUP_ECR)),
        EDC_ENABLE_ECR_MASK_CARD_NO(Utils.getString(R.string.EDC_ENABLE_ECR_MASK_CARD_NO)),
        EDC_ENABLE_ECR_MASK_EXPIRY_DATE(Utils.getString(R.string.EDC_ENABLE_ECR_MASK_EXPIRY_DATE)),
        //Screen Video
        SCREEN_VIDEO_NEED_LANDSCAPE(Utils.getString(R.string.SCREEN_VIDEO_NEED_LANDSCAPE));

        //Issuer parameter
        private String str;

        BooleanParam(String str) {
            this.str = str;
        }

        @Override
        public String toString() {
            return str;
        }
    }

    private static SysParam mSysParam;

    private static UpdateListener updateListener;

    private SysParam() {
        load(); // 加载参数内容到SysParam中
    }

    public static synchronized SysParam getInstance() {
        if (mSysParam == null) {
            mSysParam = new SysParam();
        }
        return mSysParam;
    }

    public interface UpdateListener {
        void onErr(String prompt);
    }

    public static void setUpdateListener(UpdateListener listener) {
        updateListener = listener;
    }

    // 系统参数加载，如果sp中不存在则添加
    private void load() {
        // 设置默认参数值
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        Editor editor = sharedPreferences.edit();

        //sp升级
        if (isParamFileExist()) {
            try {
                for (int i = getVersion(); i < VERSION; ++i) {
                    SpUpgrader.upgrade(editor, i, i + 1, UPGRADER_PATH);
                    editor.putInt(SysParam.VERSION_TAG, i + 1);
                    editor.apply();
                }
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "", e);
            }
            return;
        }

        if (editor != null) {
            PullParseService pps = new PullParseService(editor);
            pps.parse();

            editor.putBoolean(SysParam.IS_PARAM_FILE_EXIST, true);
            editor.putInt(SysParam.VERSION_TAG, VERSION);

            //special tags
            //            int commTimeout = Integer.parseInt(FinancialApplication.getApp().getResources().getStringArray(R.array.edc_connect_time_entries)[0]);
            //            set(editor, NumberParam.COMM_TIMEOUT, commTimeout);
            set(editor, NumberParam.COMM_CONNECT_TIMEOUT, 20);
            set(editor, NumberParam.COMM_SEND_TIMEOUT, 10);
            set(editor, NumberParam.COMM_RECEIVE_TIMEOUT, 30);

            // 通讯方式
            set(editor, StringParam.COMM_TYPE, Constant.CommType.WIFI.toString());

            set(editor, StringParam.EDC_CURRENCY_LIST, CurrencyConverter.getDefCurrency().getDisplayName(Locale.US));
            //            set(editor, StringParam.EDC_CURRENCY_LIST, "Chinese (Traditional Han,Hong Kong)");
            set(editor, StringParam.EDC_PED_MODE,
                    FinancialApplication.getApp().getResources().getStringArray(R.array.edc_ped_mode_value_entries)[0]);

            set(editor, StringParam.LOYALTY_SETTEL_RESPONSE, "");

            //// 终端密钥管理 初始化放在pref.xml中
            //set(editor, NumberParam.MK_INDEX, 20); // 主密码索引
            //set(editor, StringParam.MK_VALUE, ""); // 主秘钥
            //set(editor, StringParam.AK_VALUE, ""); // TAK(MAC)
            //set(editor, StringParam.KEY_ALGORITHM, Constant.Des.TRIP_DES.toString()); // 密钥算法
            //set(editor, BooleanParam.TMK_TYPE, false); // 主密码索引

            //loyalty paras
            set(editor, StringParam.CDS_TYPE, Constant.CashDolSelType.CDS_NOSEL.toString());
            set(editor, StringParam.HASE_CASH_DOL_NAME, "HASE + FUN$");

            set(editor, BooleanParam.REDM_CASH, true);
            set(editor, BooleanParam.REDM_MERCHANT1_DOL, false);
            set(editor, BooleanParam.REDM_MERCHANT2_DOL, false);
            set(editor, BooleanParam.DEFAULT_TRAN, false);
            set(editor, BooleanParam.CASH_CHOICE, false);

            //Transaction support
            set(editor, BooleanParam.EDC_ENABLE_LOYALTY, true);
            set(editor, BooleanParam.EDC_ENABLE_INSTALMENT, true);
            set(editor, BooleanParam.EDC_ENABLE_AUTHORIZATION, true);
            set(editor, BooleanParam.EDC_ENABLE_CUP_AUTH, true);
            set(editor, BooleanParam.EDC_ENABLE_OFFLINE, true);
            set(editor, BooleanParam.EDC_ENABLE_REFUND, true);
            set(editor, BooleanParam.EDC_ENABLE_ECR_ONLY, false);
            set(editor, BooleanParam.EDC_ENABLE_UPI, false);
            set(editor, BooleanParam.EDC_ENABLE_PLC, false);
            set(editor, BooleanParam.EDC_ENABLE_EPS, false);
            set(editor, BooleanParam.EDC_ENABLE_MICROPAY, true);
            set(editor, BooleanParam.EDC_ENABLE_FPS, true);
            set(editor, BooleanParam.EDC_ENABLE_PARTYKING, true);
            set(editor, BooleanParam.EDC_ENABLE_INTENDED_OFFLINE_MODE, false);
            set(editor, BooleanParam.EDC_ENABLE_CUP_SALE, true);
            set(editor, BooleanParam.EDC_ENABLE_CUP_REFUND, true);
            set(editor, BooleanParam.EDC_ENABLE_LYS_DISCLAIMER, true);
            set(editor, BooleanParam.EDC_ENABLE_VM_FALLBACK_BLOCK, false);
            set(editor, BooleanParam.EDC_ENABLE_SALE_TRANS_NO_SIGN, false);
            set(editor, BooleanParam.EDC_REJECT_CUP_TRANS_WITH_PIN, false);
            set(editor, BooleanParam.EDC_ENABLE_QR_PAYMENT, true);
            set(editor, BooleanParam.EDC_ENABLE_PAYMENT_LOGO, false);
            set(editor, BooleanParam.EDC_ENABLE_SCREEN_SAVER, false);
            set(editor, BooleanParam.EDC_SCREEN_SAVER_VERTICAL_SCREEN, false);
            set(editor, BooleanParam.EDC_ENABLE_ECR_HEART_BEAT, true);
            set(editor, BooleanParam.EDC_ENABLE_WIFI_MANAGE, false);
            set(editor, BooleanParam.EDC_ENABLE_CUP_ECR, false);
            set(editor, BooleanParam.EDC_ENABLE_ECR_MASK_CARD_NO, false);

            //Transaction password
            set(editor, BooleanParam.EDC_MANUAL_PWD, false);
            set(editor, BooleanParam.EDC_VOID_PWD, false);
            set(editor, BooleanParam.EDC_REFUND_PWD, false);
            set(editor, BooleanParam.EDC_ADJUST_PWD, false);
            set(editor, BooleanParam.EDC_SETTLEMENT_PWD, false);
            set(editor, BooleanParam.EDC_NOT_ENABLE_MANAGEMENT_PWD, false);
            set(editor, BooleanParam.EDC_DCCOPTOUT_PWD, false);
            set(editor, BooleanParam.EDC_RATEREPORT_PWD, false);

            //Micro Pay
            set(editor, BooleanParam.MICRO_PAY_IS_STAND_ALONE, false);
            set(editor, BooleanParam.MICRO_PAY_IS_DO_ECR_TRANS, false);

            //Transaction no signature amount
            set(editor, NumberParam.EDC_NO_SIGN_AMT_VISA, 50000);
            set(editor, NumberParam.EDC_NO_SIGN_AMT_MC, 50000);
            set(editor, NumberParam.EDC_NO_SIGN_AMT_CUP, 50000);
            set(editor, NumberParam.EDC_NO_SIGN_AMT_AMEX, 50000);
            set(editor, NumberParam.EDC_NO_SIGN_AMT_JCB, 50000);
            set(editor, NumberParam.MAX_TRANS_COUNT, 400);

            //Password init
            set(editor, StringParam.SEC_SYS_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_MERCHANT_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_MANUAL_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_TERMINAL_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_VOID_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_REFUND_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_ADJUST_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_SETTLE_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_MANAGEMENT_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_DCC_OPT_OUT_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.SEC_RATE_REPORT_PWD, EncUtils.pwdSha("773287"));
            set(editor, StringParam.WIFI_MANAGE_PASSWORD, EncUtils.pwdSha("773287"));
            //设置密码的默认值
            set(editor, StringParam.INTENDED_OFFLINE_PASSWORD, EncUtils.pwdSha("773287"));

            //Reversal debug
            set(editor, BooleanParam.ALLOW_REVERSAL_DEBUG, false);

            //Debug mode
            set(editor, BooleanParam.DEBUG_MODE, false);

            //init receipt number
            set(editor, NumberParam.EDC_RECEIPT_NUM, 2);

            //origin
            set(editor, StringParam.EDC_AMEX_ORIGIN, "PAX");

            //region
            set(editor, StringParam.EDC_AMEX_REGION, "JAPA");

            //country
            set(editor, StringParam.EDC_AMEX_COUNTRY, "344");

            //message
            set(editor, StringParam.EDC_AMEX_MESSAGE, "EDC JAPA");

            //rtInd
            set(editor, StringParam.EDC_AMEX_RTIND, "050");

            //APP language
            set(editor, StringParam.APP_LANGUAGE, Utils.getString(R.string.LANGUAGE_CHINESE));

            //ECR Redeem
            set(editor, BooleanParam.EDC_ECR_ENABLE_REDEEM, false);

            set(editor, StringParam.PARAM_TEMPLATE_VERSION,
                    Utils.getString(R.string.PARAM_INITIAL_VERSION));

            //download card bin
            set(editor, StringParam.DOWNLOAD_CARD_BIN_MODE, FinancialApplication.getApp()
                    .getResources()
                    .getString(R.string.mode_full));

            //merchant selection
            set(editor, StringParam.EDC_MERCHANT_SELECTION, FinancialApplication.getApp()
                    .getResources()
                    .getStringArray(R.array.edc_merchant_selection_values_list_entries)[0]);

            set(editor, NumberParam.EDC_FREE_TIME, 60);

            set(editor, NumberParam.EDC_ECR_RESP_MSG_DELAY, 150);

            set(editor, NumberParam.EDC_INPUT_PASSWORD_TIMEOUT, 60);

            set(editor, BooleanParam.ECR_ENABLE, false);

            if (VERSION >= 2) {
                new Upgrade1To2().upgrade(editor);
            }

            editor.apply();
        }
    }

    public synchronized int get(NumberParam name) {
        return get(name, 0);
    }

    public synchronized int get(NumberParam name, int defValue) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        String temp = sharedPreferences.getString(name.toString(), null);
        if (temp != null) {
            try {
                return Integer.parseInt(temp);
            } catch (NumberFormatException e) {
                Log.w(TAG, "", e);
            }
        }
        return defValue;
    }

    public synchronized String get(StringParam name) {
        return get(name, null);
    }

    public synchronized String get(StringParam name, String defValue) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        return sharedPreferences.getString(name.toString(), defValue);
    }

    public synchronized boolean get(BooleanParam name) {
        return get(name, false);
    }

    public synchronized boolean get(BooleanParam name, boolean defValue) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        return sharedPreferences.getBoolean(name.toString(), defValue);
    }

    private synchronized void set(Editor editor, String name, String value) {
        editor.putString(name, value);
        editor.apply();
    }

    private synchronized void set(Editor editor, String name, boolean value) {
        editor.putBoolean(name, value);
        editor.apply();
    }

    public synchronized void set(NumberParam name, int value) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        Editor editor = sharedPreferences.edit();
        set(editor, name, value);
    }

    private synchronized void set(Editor editor, NumberParam name, int value) {
        set(editor, name.toString(), String.valueOf(value));
    }

    public synchronized void set(StringParam name, String value) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        Editor editor = sharedPreferences.edit();
        set(editor, name, value);
    }

    private synchronized void set(Editor editor, StringParam name, String value) {
        set(editor, name.toString(), value);
    }

    public synchronized void set(BooleanParam name, boolean value) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        Editor editor = sharedPreferences.edit();
        set(editor, name, value);
    }

    private synchronized void set(Editor editor, BooleanParam name, boolean value) {
        set(editor, name.toString(), value);
    }

    private boolean isParamFileExist() {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        return sharedPreferences.getBoolean(SysParam.IS_PARAM_FILE_EXIST, false);
    }

    private int getVersion() {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(FinancialApplication.getApp());
        return sharedPreferences.getInt(SysParam.VERSION_TAG, 1);
    }

    public static class Constant {
        /**
         * 通讯类型
         */
        public enum CommType {
            LAN(Utils.getString(R.string.wifi)),
            MOBILE(Utils.getString(R.string.mobile)),
            WIFI(Utils.getString(R.string.wifi)),
            DEMO(Utils.getString(R.string.demo));

            private final String str;

            CommType(String str) {
                this.str = str;
            }

            @Override
            public String toString() {
                return str;
            }
        }

        /**
         * SSL
         */
        public enum CommSslType {
            NO_SSL(Utils.getString(R.string.NO_SSL)),
            SSL(Utils.getString(R.string.SSL)),
            Line_Encryption(Utils.getString(R.string.LINE_ENCRYPTION));

            private final String str;

            CommSslType(String str) {
                this.str = str;
            }

            @Override
            public String toString() {
                return str;
            }
        }

        /**
         * des算法
         */
        public enum Des {
            DES(Utils.getString(R.string.keyManage_menu_des)),
            TRIP_DES(Utils.getString(R.string.keyManage_menu_3des));

            private final String str;

            Des(String str) {
                this.str = str;
            }

            @Override
            public String toString() {
                return str;
            }
        }

        public enum CashDolSelType {
            /**
             * No Selection
             */
            CDS_NOSEL(Utils.getString(R.string.cash_dol_no_selection)),
            /**
             * Full Redemption or Merchant$ Only
             */
            CDS_FULL_OR_MD(Utils.getString(R.string.cash_dol_full_or_md_only)),
            /**
             * Have Control with Different Merchant$ Level(M1$,M2$,Cash$)
             */
            CDS_DIFFERENT(Utils.getString(R.string.cash_dol_different));

            private final String str;

            CashDolSelType(String str) {
                this.str = str;
            }

            @Override
            public String toString() {
                return str;
            }
        }

        private Constant() {
            //do nothing
        }
    }

    private static class PullParseService {
        private Map<String, Integer> intMap = new HashMap<>();
        private Map<String, Boolean> boolMap = new HashMap<>();
        private Map<String, String> stringMap = new HashMap<>();
        private Editor editor;

        PullParseService(Editor editor) {
            this.editor = editor;
        }

        private void setIntOrString(String tag, String value) {
            try {
                int intVal = Integer.parseInt(value);
                intMap.put(tag, intVal);
            } catch (NumberFormatException e) {
                stringMap.put(tag, value);
            }
        }

        private String safeNextText(XmlPullParser parser) throws XmlPullParserException, IOException {
            String result = parser.nextText();
            if (parser.getEventType() != XmlPullParser.END_TAG) {
                parser.nextTag();
            }
            return result;
        }

        private void setTag(XmlPullParser parser) throws XmlPullParserException, IOException {
            if ("string".equals(parser.getName())) {//判断开始标签元素是否是string
                setIntOrString(parser.getAttributeValue(0), safeNextText(parser));
            } else if ("boolean".equals(parser.getName())) {
                boolMap.put(parser.getAttributeValue(0), Boolean.valueOf(parser.getAttributeValue(1)));
            }
        }

        void parse() {
            try {
                InputStream in = FinancialApplication.getApp().getResources().openRawResource(R.raw.pref);
                XmlPullParser parser = Xml.newPullParser();
                parser.setInput(in, "UTF-8");
                int event = parser.getEventType();//产生第一个事件
                while (event != XmlPullParser.END_DOCUMENT) {
                    if (XmlPullParser.START_TAG == event) {//判断当前事件是否是标签元素开始事件
                        setTag(parser);
                    }
                    event = parser.next();//进入下一个元素并触发相应事件
                }//end while
            } catch (IOException | XmlPullParserException e) {
                Log.e(TAG, "", e);
            }
            for (Map.Entry<String, Integer> i : intMap.entrySet()) {
                editor.putString(i.getKey(), String.valueOf(i.getValue()));
            }

            for (Map.Entry<String, Boolean> i : boolMap.entrySet()) {
                editor.putBoolean(i.getKey(), i.getValue());
            }

            for (Map.Entry<String, String> i : stringMap.entrySet()) {
                editor.putString(i.getKey(), i.getValue());
            }
        }
    }
}
