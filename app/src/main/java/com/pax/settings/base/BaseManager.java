/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-5-17
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.settings.base;

import android.content.Context;
import androidx.annotation.NonNull;
import android.util.Log;
import com.pax.baselink.api.BDeviceInfo;
import com.pax.baselink.api.BWifiManageParam;
import com.pax.baselink.api.BaseLinkApi;
import com.pax.baselink.api.BaseResp;
import com.pax.baselink.api.EWifiManageType;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.settings.SysParam;
import java.util.Objects;

import static com.pax.pay.utils.Utils.getString;

public class BaseManager {
    private static final String TAG = "BaseManager";
    static final int BASE_CONNECTED_SUC = 1;
    static final int BASE_CONNECTED_FAL = -1;
    static final int BASE_DISCONNECTED = 0;
    static final int BASE_HAVE_CONNECTED = 2;
    static final int BASE_HAVE_NOT_CONNECTED = -2;
    static final int SET_WIFI_SUC = 5;
    static final int SET_WIFI_FAL = -5;
    static final int ADD_FORWARDING_RULE_SUC = 6;
    static final int ADD_FORWARDING_RULE_FAL = -6;
    static final int DELETE_FORWARDING_RULE_SUC = 7;
    static final int DELETE_FORWARDING_RULE_FAL = -7;
    static final int SET_STATIC_LAN_SUC = 8;
    static final int SET_STATIC_LAN_FAL = -8;
    private BaseLinkApi baseLinkApi;
    private String name;
    private String wifiInitPwd;
    private boolean isConnect;

    /**
     * constructor
     */
    public BaseManager(@NonNull Context context) {
        baseLinkApi = BaseLinkApi.getInstance(context);
        name = "L920_" + FinancialApplication.getDal().getSys().getTermInfo().get(ETermInfoKey.SN);
        wifiInitPwd = getString(R.string.baselink_default_wifi_pwd);
    }

    public BaseLinkApi getBaseLinkApiInstance() {
        return this.baseLinkApi;
    }

    /**
     * 连接BN底座
     */
    public int connectBNBase() {
        if (baseLinkApi == null) {
            return BASE_CONNECTED_FAL;
        }

        isConnect = baseLinkApi.wifiConnect(getString(R.string.baselink_default_ip), 60000);
        FinancialApplication.getSysParam().set(SysParam.BooleanParam.BASE_ENABLE, isConnect);
        return isConnect ? BASE_CONNECTED_SUC : BASE_CONNECTED_FAL;
    }

    /**
     * 断开与BN底座连接
     */
    public void disconnectBNBase() {
        if (baseLinkApi == null) {
            return;
        }
        baseLinkApi.wifiDisconnect();
        FinancialApplication.getSysParam().set(SysParam.BooleanParam.BASE_ENABLE, false);
    }

    public int isConnected() {
        if (baseLinkApi == null) {
            return BASE_HAVE_NOT_CONNECTED;
        }
        BaseResp<BDeviceInfo> ret = baseLinkApi.getDeviceInfo();
        if (Objects.isNull(ret.respData)) {
            Log.d(TAG, "ret.respData is null");
            return BASE_HAVE_NOT_CONNECTED;
        }
        FinancialApplication.getSysParam()
                .set(SysParam.BooleanParam.BASE_ENABLE, ret.respCode == BaseResp.SUCCESS);
        return isSuccess(ret.respCode) ? BASE_HAVE_CONNECTED : BASE_HAVE_NOT_CONNECTED;
    }

    /**
     * 打开WiFi模块
     *
     * @param name name
     * @param password password
     * @return int
     */
    int openWifiModule(String name, String password) {
        if (baseLinkApi == null || !isConnect) {
            return SET_WIFI_FAL;
        }
        BWifiManageParam param = new BWifiManageParam();
        param.setManageType(EWifiManageType.WIFI_MODE_AP_AUTO_START);
        param.setSsid(name);
        param.setPasswd(password);
        param.setApGateway(getString(R.string.baselink_default_ip));
        param.setApMask(getString(R.string.baselink_default_mask));
        param.setApIpPoolStart(getString(R.string.baselink_default_ip));
        param.setApIpPoolEnd(getString(R.string.baselink_default_pool_end));
        param.setIsShowSsid(false);
        BaseResp<?> ret = baseLinkApi.wifiManage(param, 15);
        Log.d(TAG, "openWifiModule ret:" + ret.respCode);
        return isSuccess(ret.respCode) ? SET_WIFI_SUC : SET_WIFI_FAL;
    }

    private boolean isSuccess(int result) {
        return result == BaseResp.SUCCESS;
    }

    public String getName() {
        return name;
    }

    public String getWifiInitPwd() {
        return wifiInitPwd;
    }

    public boolean isDCCTransSupported (TransData transData){
        if (transData.getTransType() == ETransType.SALES_WITH_CASH_DOLLAR
                || transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR
                || transData.getTransType() == ETransType.PURE_REDEEM
                || transData.getTransType() == ETransType.ONLINE_ENQUIRY
                || transData.getTransType() == ETransType.REDEEM_INST
                || transData.getTransType() == ETransType.MULTIPLE_UP
                || transData.getTransType() == ETransType.INST_MULTI_UP
                || transData.getTransType() == ETransType.SIGN_ON
                || transData.getTransType() == ETransType.POS_RSA_KEY_DOWN
                || transData.getTransType() == ETransType.POS_TMK_DOWN
                || transData.getTransType() == ETransType.POS_ACTIVATE_TMK) {
            return false;
        }
        /*if (transData.getIssuer().getName().equals("JCB") && transData.getEnterMode() == TransData.EnterMode.CLSS ){
            return  false;
        }*/
        String issuerName = transData.getIssuer().getName();
        if (issuerName.equals("JCB")
                || issuerName.equals("HASE_JCB")
                || issuerName.equals("AMEX")
                || issuerName.equals("HASE_CUP")
                || issuerName.equals("UnionPay")) {  // JCB/AMEX/CUP not support DCC transaction
            return false;
        }
        return true;
    }

    public boolean isMacaoTransSupported (TransData transData){
        if (transData.getTransType() == ETransType.SALES_WITH_CASH_DOLLAR
                || transData.getTransType() == ETransType.VOID_WITH_CASH_DOLLAR
                || transData.getTransType() == ETransType.PURE_REDEEM
                || transData.getTransType() == ETransType.ONLINE_ENQUIRY
                || transData.getTransType() == ETransType.REDEEM_INST
                || transData.getTransType() == ETransType.MULTIPLE_UP
                || transData.getTransType() == ETransType.INST_MULTI_UP
                || transData.getTransType() == ETransType.SIGN_ON
                || transData.getTransType() == ETransType.POS_RSA_KEY_DOWN
                || transData.getTransType() == ETransType.POS_TMK_DOWN
                || transData.getTransType() == ETransType.POS_ACTIVATE_TMK){
            return false;
        }
        return true;
    }

    //check if terminal's currency is macao
    public boolean isFullOutsorceMode(TransData transData) {
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_FO) &&
                !transData.getIssuer().getName().toUpperCase().contains("CUP")) {
            return true;
        }
        return false;
    }

    //setup trans data for macao's requirement
    public void setMacaoLocalEmvData (TransData transData){
        transData.setHomeCurrencyTipAmount(Component.localAmountToHomeAmount(transData.getHomeCurrency(), String.valueOf(transData.getTipAmount()), transData.getRate()));
        transData.setHomeCurrencyAmount(transData.getHomeCurrencyAmount());
        transData.setReceiptHomeCurrency(transData.getHomeCurrency());
        transData.setReceiptHomeCurrencyAmount(transData.getHomeCurrencyAmount());
        transData.setReceiptHomeCurrencyTipAmount(transData.getHomeCurrencyTipAmount());
        transData.setHomeCurrency(transData.getLocalCurrency());
        transData.setHomeCurrencyAmount(String.valueOf(transData.getAmount()));
        transData.setHomeCurrencyTipAmount(String.valueOf(transData.getTipAmount()));
    }


    public boolean isFallBackToDomestic (TransData transData){ //for dcc trans fall back to domestic trans
        String retCode = transData.getResponseCode();
        if (("03".equals(retCode) || "30".equals(retCode) || "96".equals(retCode) || "N1".equals(retCode) || "91".equals(retCode))
                && (transData.getTransType().getMsgType().equals("0100") || transData.getTransType().getMsgType().equals("0200"))
                && transData.getAcquirer().getName().equals("HASE_DCC")){
            return true;
        }
        return false;
    }

    /**
     * check trans is macao trans or not
     * @param transData
     * @return
     */
    public boolean isMacaoLocalTrans (TransData transData){
        if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData) &&
                FinancialApplication.getAcqManager().isPPAcquirerSupported(transData.getIssuer(), transData) &&
                FinancialApplication.getBaseManager().isMacaoTransSupported(transData)){
            return true;
        }
        return false;
    }

    /**
     * check if terminal, trans allow dcc
     * @param transData
     * @return
     */
    public boolean checkDccAllowed (TransData transData){ // check if terminal, trans allow dcc
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC) &&
                FinancialApplication.getAcqManager().isPPAcquirerSupported(transData.getIssuer(), transData) &&
                FinancialApplication.getBaseManager().isDCCTransSupported(transData)){
            return true;
        }
        return false;
    }

    //check if terminal is in intended offline mode
    public boolean isIntendedOfflineMode() {
        return FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_INTENDED_OFFLINE);
    }
}
