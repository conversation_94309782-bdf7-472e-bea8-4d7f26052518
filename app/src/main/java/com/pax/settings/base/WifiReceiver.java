package com.pax.settings.base;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

public class WifiReceiver extends BroadcastReceiver {
    private ReceiverListener mReceiverListener;
    private static final String TAG = "WifiReceiver";

    public WifiReceiver(ReceiverListener receiverListener) {
        mReceiverListener = receiverListener;
    }

    /**
     * get wifi connected status by system broadcast
     *
     * @param context 上下文
     * @param intent  intent
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        ConnectivityManager connectivityManager =
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return;
        }
        NetworkInfo.State state =
                connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI).getState();
        //wifi disconnected
        if (state == NetworkInfo.State.DISCONNECTED) {
            Log.i(TAG, "WIFI DISCONNECTED");
            mReceiverListener.onListener(false);
        }

        //wifi connected
        if (state == NetworkInfo.State.CONNECTED) {
            Log.i(TAG, "WIFI CONNECTED");
            mReceiverListener.onListener(true);
        }
    }
}
