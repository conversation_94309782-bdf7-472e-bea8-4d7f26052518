package com.pax.settings.base;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.pax.pay.app.FinancialApplication;
import com.pax.settings.SysParam;

public class MicroPayReceiver extends BroadcastReceiver {
    /**
     * 该广播接收器表明micro pay以stand alone模式运行
     */

    @Override
    public void onReceive(Context context, Intent intent) {
        FinancialApplication.getSysParam().set(SysParam.BooleanParam.MICRO_PAY_IS_STAND_ALONE, true);
    }
}
