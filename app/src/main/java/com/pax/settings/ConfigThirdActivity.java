/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         ligq                    Create
 * ===========================================================================================
 */
package com.pax.settings;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.KeyEvent;

import androidx.annotation.RequiresApi;

import com.pax.commonlib.utils.LogUtils;
import com.pax.pay.BaseConfigActivity;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.sbipay.R;
import com.pax.settings.config.ConfigAcquirer;
import com.pax.settings.config.ConfigClss;
import com.pax.settings.config.ConfigComm;
import com.pax.settings.config.ConfigEdc;
import com.pax.settings.config.ConfigIssuer;
import com.pax.settings.config.ConfigOther;
import com.pax.settings.config.ConfigProvider;
import com.pax.settings.config.ConfigPwd;
import com.pax.settings.config.ConfigQuick;
import com.pax.settings.config.IConfig;
import com.pax.settings.inflater.ConfigInflater;
import com.pax.settings.inflater.ConfigSelectInflater;

import static com.pax.settings.SettingConst.LIST_TYPE_ACQUIRER;
import static com.pax.settings.SettingConst.LIST_TYPE_CLSS;
import static com.pax.settings.SettingConst.LIST_TYPE_COMM;
import static com.pax.settings.SettingConst.LIST_TYPE_EDC;
import static com.pax.settings.SettingConst.LIST_TYPE_ISSUER;
import static com.pax.settings.SettingConst.LIST_TYPE_QUICK;
import static com.pax.settings.SettingConst.REQ_WRITE_SETTINGS;

/**
 * execute the function from ConfigFirstActivity and ConfigSecondActivity
 * all the functions are executed in their own inflater
 */
public class ConfigThirdActivity extends BaseConfigActivity {
    IConfig<ConfigInflater<ConfigThirdActivity>> mConfig = null;
    private ConfigInflater<ConfigThirdActivity> inflater = null;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_config_third;
    }

    @Override
    protected void initViews() {
        String name = "";
        String title = getToolBarTitle();
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            String issuerNameTemp = extras.getString(EUIParamKeys.ISSUER_NAME.toString());
            if (issuerNameTemp != null) {
                name = issuerNameTemp;
            }
            String acquirerNameTemp = extras.getString(EUIParamKeys.ACQUIRER_NAME.toString());
            if (acquirerNameTemp != null) {
                name = acquirerNameTemp;
            }
            String clssNameTemp = extras.getString(EUIParamKeys.CLSS_NAME.toString());
            if (clssNameTemp != null) {
                name = clssNameTemp;
            }
        }
        if (LIST_TYPE_QUICK.contains(title)) {
            mConfig = new ConfigQuick();
        } else if (LIST_TYPE_COMM.contains(title)) {
            mConfig = new ConfigComm(this);
        } else if (LIST_TYPE_EDC.contains(title)) {
            mConfig = new ConfigEdc();
        } else if (LIST_TYPE_ISSUER.contains(title)) {
            mConfig = new ConfigIssuer(name, this);
        } else if (LIST_TYPE_ACQUIRER.contains(title)) {
            mConfig = new ConfigAcquirer(name);
        } else if (LIST_TYPE_CLSS.contains(title)) {
            mConfig = new ConfigClss(name, this);
        } else if (getString(R.string.settings_menu_pwd_manage).equals(title)) {
            mConfig = new ConfigPwd();
        } else if (getString(R.string.settings_menu_otherManage).equals(title)) {
            mConfig = new ConfigOther();
        } else if (getString(R.string.comm_media_provider).equals(title) || getString(R.string.comm_label_provider).equals(title) ||
                getString(R.string.emi_value_provider).equals(title) || getString(R.string.host_ip_provider).equals(title) ||
                getString(R.string.port_number_provider).equals(title) || getString(R.string.ssl_provider).equals(title) ||
                getString(R.string.apn_provider).equals(title) || getString(R.string.nii_provider).equals(title) ||
                getString(R.string.mqtt_ip).equals(title) || getString(R.string.mqtt_qos).equals(title) ||
                getString(R.string.mqtt_port).equals(title) || getString(R.string.mqtt_connect_timeout).equals(title)) {
            mConfig = new ConfigProvider();
        } else {
            mConfig = null;
        }
        if (mConfig != null) {
            inflater = mConfig.getInflater(title);
        }
        if (getString(R.string.settings_menu_pwd_manage).equals(title) ||
                getString(R.string.settings_menu_otherManage).equals(title) ||
                inflater instanceof ConfigSelectInflater) {
            initToolBar(false);
        } else {
            initToolBar(true);
        }
        if (inflater != null) {
            inflater.inflate(this, title);
        }
    }

    @Override
    public void onKeyOkDown() {
        LogUtils.d("ConfigThird", "doNextSuccess!");
        if (inflater == null || !inflater.doNextSuccess()) {
            return;
        }
        onKeyBackDown();
    }

    @RequiresApi(Build.VERSION_CODES.M)
    public void openWriteSettings() {
        Intent gotoSettings = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
        gotoSettings.setData(Uri.parse("package:" + getPackageName()));
        startActivityForResult(gotoSettings, REQ_WRITE_SETTINGS);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mConfig != null) {
            mConfig.doOkNext(requestCode);
        }
    }

    @Override
    public boolean onKeyBackDown() {
        return super.onKeyBackDown();
    }

    @Override
    protected boolean onKeyDel() {
        return false;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        final int keyCode = event.getKeyCode();
        if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_UP) {
            onKeyOkDown();
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

}
