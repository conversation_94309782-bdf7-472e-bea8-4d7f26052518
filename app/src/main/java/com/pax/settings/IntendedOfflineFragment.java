package com.pax.settings;

import android.app.Dialog;
import android.content.DialogInterface;
import android.preference.CheckBoxPreference;
import android.preference.EditTextPreference;
import android.preference.ListPreference;
import android.preference.MultiSelectListPreference;
import android.preference.RingtonePreference;
import androidx.appcompat.app.AlertDialog;

import android.widget.EditText;

import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.view.keyboard.KeyboardUtils;

public class IntendedOfflineFragment extends BasePreferenceFragment {
    public static String approvalCode = null;
    private  boolean firstTime = true;
    @Override
    protected int getResourceId() {
        return R.xml.intended_offline_pref;
    }

    @Override
    protected void initPreference() {
        bindPreference(SysParam.BooleanParam.EDC_ENABLE_INTENDED_OFFLINE);
    }

    @Override
    protected boolean onListPreferenceChanged(ListPreference preference, Object value, boolean isInitLoading) {
        return true;
    }


    @Override
    protected boolean onCheckBoxPreferenceChanged(CheckBoxPreference preference, Object value, boolean isInitLoading) {
        boolean enable = value.toString().equals("true");
        if(enable){
            if(!firstTime){
                AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
                final EditText editText = new EditText(getActivity());
                builder.setTitle(R.string.prompt_approval_code_input)
                        .setView(editText)
                        .setPositiveButton(R.string.dialog_ok, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                                KeyboardUtils.hideSystemKeyboard(getActivity(), editText);
                                //校验输入
                                // if (!getString(R.string.demo_mode_pwd).equals(editText.getText().toString())) {
                                //     Toast.makeText(getActivity(), R.string.err_password, Toast.LENGTH_SHORT)
                                //             .show();
                                //     return;
                                // }
                                approvalCode = editText.getText().toString();
                                if(approvalCode == null || approvalCode.isEmpty()){
                                    preference.setChecked(false);
                                }else {
                                    FinancialApplication.getSysParam().set(SysParam.StringParam.APPROVAL_CODE, approvalCode);
                                    preference.setChecked(true);
                                    FinancialApplication.getSysParam().set(SysParam.NumberParam.INTENDED_OFFLINE_CLOSE_FIRST, 0);
                                }
                            }
                        }).setNegativeButton(R.string.dialog_cancel, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                KeyboardUtils.hideSystemKeyboard(getActivity(), editText);
                                preference.setChecked(false);
                            }
                        }).setOnCancelListener(new DialogInterface.OnCancelListener() {
                            @Override
                            public void onCancel(DialogInterface dialog) {
                                preference.setChecked(false);
                            }
                        });
                Dialog dialog = builder.show();
                dialog.setCanceledOnTouchOutside(false);
            }
        }else{
            approvalCode = null;
            FinancialApplication.getSysParam().set(SysParam.StringParam.APPROVAL_CODE,approvalCode);
            if(FinancialApplication.getSysParam().get(SysParam.NumberParam.INTENDED_OFFLINE_CLOSE_FIRST,-1) == 0){
                FinancialApplication.getSysParam().set(SysParam.NumberParam.INTENDED_OFFLINE_CLOSE_FIRST, 1);
            }
        }
        firstTime = false;
        return true;
    }

    @Override
    protected boolean onRingtonePreferenceChanged(RingtonePreference preference, Object value, boolean isInitLoading) {
        return true;
    }

    @Override
    protected boolean onEditTextPreferenceChanged(EditTextPreference preference, Object value, boolean isInitLoading) {
        return true;
    }

    @Override
    protected boolean onMultiSelectListPreferenceChanged(MultiSelectListPreference preference, Object value, boolean isInitLoading) {
        return false;
    }

}
