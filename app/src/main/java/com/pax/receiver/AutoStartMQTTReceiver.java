package com.pax.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.pax.pay.mqtt.MQTTForegroundService;
import com.pax.pay.service.TTSService;

public class AutoStartMQTTReceiver extends BroadcastReceiver {
    private static final String TAG = "AutoStartMQTTReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Received broadcast to start MQTT service");
        if (intent.getAction() != null && intent.getAction().equals("RE_START_MQTT")) {
            Intent serviceIntent = new Intent(context, MQTTForegroundService.class);
            context.startForegroundService(serviceIntent);
            serviceIntent = new Intent(context, TTSService.class);
            context.startForegroundService(serviceIntent);
        }
    }
}
