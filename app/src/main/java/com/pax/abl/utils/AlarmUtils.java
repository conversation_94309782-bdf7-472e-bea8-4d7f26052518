package com.pax.abl.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.utils.LogUtils;
import com.pax.pay.utils.Utils;
import com.pax.receiver.AutoSettlementReceiver;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @Date 2024/7/9
 */
public class AlarmUtils {
    private static final String TAG = "AlarmUtils";
    private static final AlarmManager alarmManager = (AlarmManager)  BaseApplication.getAppContext().getSystemService(Context.ALARM_SERVICE);
    private static PendingIntent pendingIntent;
    private AlarmUtils(){}
    public static void setDailyAlarm(int hour,int minute) {
        if (pendingIntent != null) {
            LogUtils.d(TAG, "Alarm already set, cancel.");
            cancelDailyAlarm();
        }

        // 设置要触发的Intent
        Intent intent = new Intent( BaseApplication.getAppContext(), AutoSettlementReceiver.class);
        pendingIntent = PendingIntent.getBroadcast(
                BaseApplication.getAppContext(), 0, intent, PendingIntent.FLAG_UPDATE_CURRENT
        );

        // 设置首次触发时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, hour); // 设置小时
        calendar.set(Calendar.MINUTE, minute);      // 设置分钟
        calendar.set(Calendar.SECOND, 0);      // 设置秒

        // 如果当前时间已过,则设置明天
        if (System.currentTimeMillis() > calendar.getTimeInMillis()) {
            calendar.add(Calendar.DATE, 1);
        }

        // 设置只执行一次
        alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(),
                pendingIntent
        );
    }

    public static void setDailyAlarm() {
        // 获取自动结算时间
        String settleTime = SysParam.getInstance().getString(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_TIME));
        String settleDate = SysParam.getInstance().getString(Utils.getString(R.string.EDC_AUTO_SETTLEMENT_DATE));
        if(!Utils.getString(R.string.disable).equals(settleTime) &&
                !Utils.getString(R.string.disable).equals(settleDate)) {
            int hour = Utils.getHour(settleDate + " "+ settleTime);
            int minute = Utils.getMinute();
            setDailyAlarm(hour, minute);
        }else{
            // 取消原来的Alarm
            cancelDailyAlarm();
        }
    }

    public static void cancelDailyAlarm() {
        if(alarmManager != null && pendingIntent != null){
            alarmManager.cancel(pendingIntent);
        }
    }
}
