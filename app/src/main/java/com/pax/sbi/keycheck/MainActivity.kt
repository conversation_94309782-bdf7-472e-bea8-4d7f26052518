package com.pax.sbi.keycheck

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import com.pax.sbi.pax_neptunelite.NeptuneLiteUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class MainActivity : ComponentActivity(),OnClickListener {
    private lateinit var passwordLayout:LinearLayout
    private lateinit var keyCheckLayout:LinearLayout
    private lateinit var pwdButton:Button
    private lateinit var printButton:Button
    private lateinit var keyText:TextView
    private lateinit var pwdEdit:EditText
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        NeptuneLiteUseCase.initDal()
        passwordLayout = findViewById(R.id.password_layout)
        keyCheckLayout = findViewById(R.id.key_check_layout)
        pwdButton = findViewById(R.id.pwd_button)
        keyText = findViewById(R.id.key_text)
        pwdEdit = findViewById(R.id.pwd_edit)
        pwdButton.setOnClickListener(this)
        pwdEdit.setOnEditorActionListener { _, actionId, _ ->
            if(actionId == EditorInfo.IME_ACTION_DONE){
                pwdCheck()
            }
            true
        }
        if(NeptuneLiteUseCase.isA910S()){
            printButton = findViewById(R.id.print_button)
            printButton.setOnClickListener(this)
        }
    }


    private fun startPrint(str: String, callback: (result: String) -> Unit) {
        CoroutineScope(Dispatchers.Default).launch {
            NeptuneLiteUseCase.printKeyResult(str).let {
                withContext(Dispatchers.Main) {
                    if(it.isNotEmpty())
                        it.showToast()
                }
                callback(it)
            }
        }
    }

    private suspend fun dukptCheck(): String {
        val dateTime = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")
        return withContext(Dispatchers.Default) {
            val result =
                buildString {
                    append("SN:${NeptuneLiteUseCase.getSerialNo()}\n")
                    append("Time:${dateTime.format(formatter)}\n")
                    append(resources.getString(R.string.dukpt_key)).append("\n")
                    append(resources.getString(R.string.example_message)).append("\n")
                    (1..100).forEach { i ->
                        NeptuneLiteUseCase.checkKey(i.toByte())
                            ?.let {
                                this.append(it.toString())
                                    .append("\n")
                            }
                    }
                }
            result
        }
    }

    private fun String.showToast() {
        runOnUiThread{
            Toast.makeText(applicationContext, this, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onClick(v: View?) {
        when(v?.id){
            R.id.pwd_button -> {
                pwdCheck()
            }
            R.id.print_button ->{
                startPrint(keyText.text.toString()){

                }
            }
        }
    }

    private fun pwdCheck(){
        if (pwdEdit.text.toString() == "324515") {
            val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(currentFocus?.windowToken, 0)
            passwordLayout.visibility = View.GONE
            keyCheckLayout.visibility = View.VISIBLE
            lifecycleScope.launch {
                val result = dukptCheck()
                withContext(Dispatchers.Main) {
                    keyText.text = result
                }
            }
        }else{
            pwdEdit.text.clear()
            pwdEdit.requestFocus()
            getString(R.string.error_password_prompt).showToast()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        NeptuneLiteUseCase.release()
    }
}

