package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@JsonPropertyOrder({"responseType", "statusCode", "statusMsg", "dateTime", "bankName", "interestRate",
        "tenure", "effectiveRate", "loanAmount", "totalAmount", "baseAmount", "emiAmount", "cardType",
        "amount", "bankData", "termsCondition", "brandData", "udf1", "udf2", "udf3", "udf4", "udf5", "sdkDigiSign"})
public class BankEMIConfirmResponseData implements Serializable {
    @JsonProperty("RESPONSE_TYPE")
    private String responseType; // Transaction sub type

    @JsonProperty("STATUS_CODE")
    private String statusCode; // Transaction status code

    @JsonProperty("STATUS_MSG")
    private String statusMsg; // Response status message

    @JsonProperty("DATETIME")
    private String dateTime; // Transaction date and time in YYYYMMDDHHMISS format

    @JsonProperty("BANK_NAME")
    private String bankName; // EMI selected bank name

    @JsonProperty("INTEREST_RATE")
    private String interestRate; // EMI interest rate

    @JsonProperty("TENURE")
    private String tenure; // EMI tenure

    @JsonProperty("EFFECTIVE_RATE")
    private String effectiveRate; // EMI effective rate

    @JsonProperty("LOAN_AMOUNT")
    private String loanAmount; // Transaction amount

    @JsonProperty("TOTAL_AMOUNT")
    private String totalAmount; // Amount with interest

    @JsonProperty("BASE_AMOUNT")
    private String baseAmount; // Auth amount

    @JsonProperty("EMI_AMOUNT")
    private String emiAmount; // EMI amount

    @JsonProperty("CARD_TYPE")
    private String cardType; // Card type for EMI

    @JsonProperty("AMOUNT")
    private String amount; // Transaction amount used in brand flow in payment

    @JsonProperty("BANK_DATA")
    private BankData bankData; // Bank data

    @JsonProperty("TERMS_CONDITION")
    private String termsCondition;

    @JsonProperty("BRAND_DATA")
    private BrandData brandData;

    @JsonProperty("UDF1")
    private String udf1; // Future use

    @JsonProperty("UDF2")
    private String udf2; // Future use

    @JsonProperty("UDF3")
    private String udf3; // Future use

    @JsonProperty("UDF4")
    private String udf4; // Future use

    @JsonProperty("UDF5")
    private String udf5; // Future use

    @JsonProperty("SDK_DIGI_SIGN")
    private String sdkDigiSign; // Digi sign generated by SDK Private key in EMI SDK

}
