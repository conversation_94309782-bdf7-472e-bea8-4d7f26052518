package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@JsonPropertyOrder({"responseType", "statusCode", "statusMsg", "udf1", "udf2", "udf3", "udf4", "udf5", "sdkDigiSign"})
public class BankEMINotifyReversalResponseData implements Serializable {
    @JsonProperty("RESPONSE_TYPE")
    private String responseType; // Transaction sub type

    @JsonProperty("STATUS_CODE")
    private String statusCode; // Transaction status code

    @JsonProperty("STATUS_MSG")
    private String statusMsg; // Response status message

    @JsonProperty("UDF1")
    private String udf1; // Future use

    @JsonProperty("UDF2")
    private String udf2; // Future use

    @JsonProperty("UDF3")
    private String udf3; // Future use

    @JsonProperty("UDF4")
    private String udf4; // Future use

    @JsonProperty("UDF5")
    private String udf5; // Future use

    @JsonProperty("SDK_DIGI_SIGN")
    private String sdkDigiSign; // Digi sign generated by SDK Private key in EMI SDK

}
