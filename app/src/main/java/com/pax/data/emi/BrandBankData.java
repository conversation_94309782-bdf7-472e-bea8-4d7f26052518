package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@JsonPropertyOrder({"amount", "bankName", "baseAmount", "emiAmount", "interestRate", "loanAmount", "tenure",
        "totalAmount", "additionalCashback", "cardType", "cashbackAmount", "cashbackRate", "cashbackTnc",
        "discount", "discountPer", "effectiveRate", "instantDiscountPer", "instantDiscountAmount", "integrationCode",
        "interestAmount", "payAmount", "subventionRateAmt", "subventionRatePer", "tranType"})
public class BrandBankData {
    @JsonProperty("amount")
    private String amount;

    @JsonProperty("BankName")
    private String bankName;

    @JsonProperty("BaseAmount")
    private String baseAmount;

    @JsonProperty("EmiAmount")
    private String emiAmount;

    @JsonProperty("InterestRate")
    private String interestRate;

    @JsonProperty("LoanAmount")
    private String loanAmount;

    @JsonProperty("Tenure")
    private int tenure;

    @JsonProperty("TotalAmount")
    private String totalAmount;

    @JsonProperty("additional_cashback")
    private String additionalCashback;

    @JsonProperty("card_type")
    private String cardType;

    @JsonProperty("cashback_amount")
    private String cashbackAmount;

    @JsonProperty("cashback_rate")
    private String cashbackRate;

    @JsonProperty("cashback_tnc")
    private String cashbackTnc;

    @JsonProperty("discount")
    private String discount;

    @JsonProperty("discount_per")
    private String discountPer;

    @JsonProperty("effectiverate")
    private String effectiveRate;

    @JsonProperty("instantdiscount_per")
    private String instantDiscountPer;

    @JsonProperty("instantdiscountamount")
    private String instantDiscountAmount;

    @JsonProperty("integrationcode")
    private String integrationCode;

    @JsonProperty("interestamount")
    private String interestAmount;

    @JsonProperty("payamount")
    private String payAmount;

    @JsonProperty("subvention_rate_amt")
    private String subventionRateAmt;

    @JsonProperty("subvention_rate_per")
    private String subventionRatePer;

    @JsonProperty("tranType")
    private String tranType;
}


