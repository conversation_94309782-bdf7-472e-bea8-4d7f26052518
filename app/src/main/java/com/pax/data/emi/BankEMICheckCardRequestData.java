package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@JsonPropertyOrder({"tid", "mid", "deviceModel", "token", "apiRefNumber", "cardBin", "cardExpiryDate",
        "type", "aggregatorDigiSign"})
public class BankEMICheckCardRequestData implements Serializable {
    @JsonProperty("TID")
    private String tid; // Terminal Transaction ID

    @JsonProperty("MID")
    private String mid; // Terminal Merchant ID

    @JsonProperty("DEVICE_MODEL")
    private String deviceModel; // Fixed value which identifies the calling application


    @JsonProperty("TOKEN")
    private String token; // Token received in TOKENGENERATION API from EMI

    @JsonProperty("API_REF_NUMBER")
    private String apiRefNumber; // Reference number in TOKENGENERATION API from EMI

    @JsonProperty("CARD_BIN")
    private String cardBin; // Card bin for which EMI is to be checked

    @JsonProperty("CARD_EXPIRY_DATE")
    private String cardExpiryDate; // Card expiry date for which EMI is to be checked

    @JsonProperty("TYPE")
    private String type;

    @JsonProperty("AGGREGATOR_DIGI_SIGN")
    private String aggregatorDigiSign; // Digi sign generated by aggregator private key of intent request in payment app

}
