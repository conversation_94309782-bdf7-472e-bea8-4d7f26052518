package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@JsonPropertyOrder({"tid", "mid", "deviceModel", "amount", "mode", "token", "apiRefNumber", "aggregatorDigiSign"})
public class BankEMIOfferRequestData implements Serializable {
    @JsonProperty("TID")
    private String tid; // Terminal Transaction ID

    @JsonProperty("MID")
    private String mid; // Terminal Merchant ID

    @JsonProperty("DEVICE_MODEL")
    private String deviceModel; // Fixed value which identifies the calling application

    @JsonProperty("AMOUNT")
    private String amount; // Amount for Transaction

    @JsonProperty("MODE")
    private String mode; // Transaction Mode

    @JsonProperty("TOKEN")
    private String token; // Token received in TOKENGENERATION API from EMI

    @JsonProperty("API_REF_NUMBER")
    private String apiRefNumber; // Reference number in TOKENGENERATION API from EMI

    @JsonProperty("AGGREGATOR_DIGI_SIGN")
    private String aggregatorDigiSign; // Digi sign generated by aggregator private key of intent request in payment app

}
