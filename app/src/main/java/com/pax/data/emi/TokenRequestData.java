package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // 忽略空字段
@ToString
@JsonPropertyOrder({"tid", "mid", "deviceModel", "network", "aggregatorCode", "merchantName", "aggregatorDigiSign"})
public class TokenRequestData implements Serializable {
    @JsonProperty("TID")
    private String tid; // Terminal Transaction ID

    @JsonProperty("MID")
    private String mid; // Terminal Merchant ID

    @JsonProperty("DEVICE_MODEL")
    private String deviceModel; // Fixed value which identifies the calling application

    @JsonProperty("NETWORK")
    private String network; // Network

    @JsonProperty("AGGREGATOR_CODE")
    private String aggregatorCode; // Aggregator code received offline

    @JsonProperty("MERCHANT_NAME")
    private String merchantName; // Merchant Name

    @JsonProperty("AGGREGATOR_DIGI_SIGN")
    private String aggregatorDigiSign; // Digi sign of token generation intent request

}
