package com.pax.data.emi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // 忽略空字段
@ToString
public class TokenResponseData implements Serializable {
    @JsonProperty("RESPONSE_TYPE")
    private String responseType; // Transaction sub type

    @JsonProperty("STATUS_CODE")
    private String statusCode; // Transaction status code

    @JsonProperty("STATUS_MSG")
    private String statusMsg; // Description of status code

    @JsonProperty("TOKEN")
    private String token; // Per EMI transaction Token will be generated in EMI platform

    @JsonProperty("API_REF_NUMBER")
    private String apiRefNumber; // Unique reference number from EMI platform

    @JsonProperty("SDK_PUBLICKEY")
    private String sdkPublicKey; // Unique public key generated in sdk while token generation API call using ECDSA

    @JsonProperty("EMI_DIGI_SIGN")
    private String emiDigiSign; // Digi sign data received in token generation response from EMI Server

}
