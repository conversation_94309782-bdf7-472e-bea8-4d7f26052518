/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         xieYb                   Create
 * ===========================================================================================
 */
package com.pax.data.local.db.helper;

import android.database.Cursor;
import android.text.TextUtils;

import com.pax.commonlib.utils.LogUtils;
import com.pax.data.entity.Acquirer;
import com.pax.data.entity.Issuer;
import com.pax.data.entity.TransData;
import com.pax.data.local.db.dao.TransDataDao;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.utils.Utils;
import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.greenrobot.greendao.query.QueryBuilder;
import org.greenrobot.greendao.query.WhereCondition;

import java.util.ArrayList;
import java.util.List;

/**
 * Database operation helper of TransData
 */
public class TransDataDbHelper extends BaseDaoHelper<TransData> {
    public TransDataDbHelper() {
        super(TransData.class);
    }

    public static TransDataDbHelper getInstance() {
        return LazyHolder.INSTANCE;
    }

    public final TransData findTransDataByTraceNo(long traceNo) {
        return getNoSessionQuery().where(TransDataDao.Properties.TraceNo.eq(traceNo)
                        , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL))
                .unique();
    }

    //根据RRN查询订单的记录并返回
    public final TransData findTransDataByRrn(String rrn) {
        return getNoSessionQuery().where(TransDataDao.Properties.RefNo.eq(rrn)
                        , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL))
                .unique();
    }

    public final TransData findTransDataByInvoiceNo(long invoiceNo) {
        return getNoSessionQuery().where(TransDataDao.Properties.InvoiceNo.eq(invoiceNo)
                        , TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN)// 排除BQR的Unknown状态
                        , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL))
                .unique();
    }

    public final List<TransData> findTransData(List<ETransType> types) {
        return getNoSessionQuery().where(TransDataDao.Properties.TransType.in(types)
                        , TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN)// 排除BQR的Unknown状态
                        , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL))
                .list();
    }

    public final List<TransData> findOfflineTransData(List<TransData.OfflineStatus> status) {
        return getNoSessionQuery().where(TransDataDao.Properties.OfflineSendState.in(status)
                        , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL))
                .list();
    }

    public final List<TransData> findTransData(List<ETransType> types, List<TransData.OfflineStatus> status, Acquirer acq) {
        //排除错误和null的状态
        return getNoSessionQuery().where(TransDataDao.Properties.TransType.in(types)
                , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)
                , TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN)// 排除BQR的Unknown状态
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId())).whereOr(TransDataDao.Properties.OfflineSendState.isNull(),
                TransDataDao.Properties.OfflineSendState.notIn(status)).list();
    }

    public final boolean existUnknownBqrTransData() {
        //排除错误和null的状态
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.TransState.eq(TransData.ETransStatus.UNKNOWN)).list();
        return !list.isEmpty();
    }

    public final long countOf(List<ETransType> types, List<TransData.OfflineStatus> status, Acquirer acq) {
        //排除错误和null的状态
        return getNoSessionQuery().where(TransDataDao.Properties.TransType.in(types)
                , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)
                , TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN)// 排除BQR的Unknown状态
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId())).whereOr(TransDataDao.Properties.OfflineSendState.isNull(),
                TransDataDao.Properties.OfflineSendState.notIn(status)).count();
    }

    public final List<TransData> findFailedTransData(Acquirer acq) {
        //除上送成功外，状态为not send，错误，null均视为失败交易
        return getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)
                , TransDataDao.Properties.OfflineSendState.notEq(TransData.OfflineStatus.OFFLINE_SENT)
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId())).list();
    }

    public final long findFailedTransDataCount(Acquirer acq) {
        //除上送成功外，状态为not send，错误，null均视为失败交易
        return getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)
                , TransDataDao.Properties.OfflineSendState.notEq(TransData.OfflineStatus.OFFLINE_SENT)
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId())).count();
    }

    public final TransData findLastTransData() {
        QueryBuilder queryBuilder = getNoSessionQuery();
        WhereCondition unknownBqrCondition = queryBuilder.and(TransDataDao.Properties.TransType.eq(ETransType.QR),
                TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN), TransDataDao.Properties.ReversalStatus.in(TransData.ReversalStatus.NORMAL));
        WhereCondition normalCondition = queryBuilder.and(TransDataDao.Properties.TransType.notEq(ETransType.QR),
                TransDataDao.Properties.ReversalStatus.in(TransData.ReversalStatus.NORMAL));
        List<TransData> list = queryBuilder.whereOr(unknownBqrCondition, normalCondition).list();
        return list != null && !list.isEmpty() ? list.get(list.size() - 1) : null;
    }

    public final TransData findLastNormalTransData() {
        int batchNo = SysParam.getInstance().getInt(R.string.EDC_BATCH_NO);
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)
                , TransDataDao.Properties.BatchNo.eq(batchNo), TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN)).list();
        return list != null && !list.isEmpty() ? list.get(list.size() - 1) : null;
    }

    public final List<TransData> findAllTransData(boolean includeReversal) {
        return includeReversal ? this.loadAll() : getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)).list();
    }

    public final List<TransData> findAllTransData(Acquirer acq) {
        return this.findAllTransData(acq, true);
    }

    public final List<TransData> findAllTransData(Acquirer acq, boolean includeVoid) {
        QueryBuilder<TransData> builder = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId()));
        if (!includeVoid) {
            builder.where(TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.VOIDED));
        }
        return builder.list();
    }

    public final List<TransData> findAllTransData(List<ETransType> types, List<TransData.ETransStatus> statuses) {
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.TransType.in(types)
                , TransDataDao.Properties.TransState.in(statuses)
                , TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL)).list();
        return list != null && !list.isEmpty() ? list : null;
    }

    public final boolean deleteAllTransData() {
        List<TransData> list = this.findAllTransData(true);
        return list == null || list.isEmpty() || this.deleteEntities(list);
    }

    public final boolean deleteAllTransData(Acquirer acq) {
        QueryBuilder<TransData> builder = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.in(TransData.ReversalStatus.NORMAL, TransData.ReversalStatus.REVERSAL_COM)
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId()), TransDataDao.Properties.TransType.notEq(ETransType.PREAUTH));
        List<TransData> list = builder.list();
        return list.isEmpty() || this.deleteEntities(list);
    }

    public final boolean deleteReverSuccPreAuth(Acquirer acq) {
        QueryBuilder<TransData> builder = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.in(TransData.ReversalStatus.REVERSAL_COM)
                , TransDataDao.Properties.Acquirer_id.eq(acq.getId()), TransDataDao.Properties.TransType.eq(ETransType.PREAUTH));
        List<TransData> list = builder.list();
        return list.isEmpty() || this.deleteEntities(list);
    }

    public final long countOf() {
        // Insta EMI 不参与结算，因此这里不用统计Insta相关
        return getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL),
                TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN),
                TransDataDao.Properties.TransType.notEq(ETransType.INSTA_EMI_SALE)).count();
    }

    public final long countOfWhileUpdate() {
        // 参数更新时统计交易数量时排除掉这些不参与结算的交易
        return getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL),
                TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN),
                TransDataDao.Properties.TransType.notIn(ETransType.INSTA_EMI_SALE,ETransType.CALL_HELP,ETransType.PREAUTHCANCEL
                ,ETransType.QSPARC_BALANCE_ENQUIRY,ETransType.QSPARC_BALANCE_UPDATE)).count();
    }

    public final long countOfIncludeInsta() {
        return getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL),
                TransDataDao.Properties.TransState.notEq(TransData.ETransStatus.UNKNOWN)).count();
    }

    //筛选非PreAuth的交易
    public final long countOfNoPreAuth() {
        return getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.NORMAL),
                TransDataDao.Properties.TransType.notEq(ETransType.PREAUTH),
                TransDataDao.Properties.TransType.notEq(ETransType.PREAUTHCANCEL)).count();
    }

    /**
     * select count(*),sum(amount) from trans_data where transType = type
     * and transState = status
     * and acquirer_id = acquirer.id
     * and reversalStatus = normal
     * <p>
     * SELECT count(*),sum(trans_data.amount) FROM trans_data
     * WHERE
     * trans_data.state='NORMAL'
     * AND trans_data.type = 'SALE'
     * AND trans_data.REVERSAL ='NORMAL'
     * AND trans_data.acquirer_id = 1
     */
    public final long[] countSumOfAmt(Acquirer acquirer, ETransType type, TransData.ETransStatus status) {
        ArrayList<ETransType> eTransTypes = new ArrayList<>();
        eTransTypes.add(type);
        ArrayList<TransData.ETransStatus> eTransStatuses = new ArrayList<>();
        eTransStatuses.add(status);
        return countSumOfAmt(acquirer, null, eTransTypes, eTransStatuses);
    }

    /**
     * SELECT
     * count(*),
     * sum(trans_data.amount)
     * FROM
     * trans_data
     * WHERE
     * trans_data.state IN('NORMAL','TEST')
     * AND trans_data.REVERSAL = 'NORMAL'
     * AND trans_data.acquirer_id = 1
     * AND trans_data.type = 'SALE'
     */
    public final long[] countSumOfAmt(Acquirer acquirer, ETransType type, List<TransData.ETransStatus> status) {
        ArrayList<ETransType> eTransTypes = new ArrayList<>();
        eTransTypes.add(type);
        return countSumOfAmt(acquirer, null, eTransTypes, status);
    }

    public final long[] countSumOfAmt(Acquirer acquirer, Issuer issuer, List<ETransType> type, List<TransData.ETransStatus> status) {
        return countSumOf(acquirer, issuer, type, status, TransDataDao.Properties.Amount.columnName);
    }

    public final long[] countSumOf(Acquirer acquirer, Issuer issuer, List<ETransType> type, List<TransData.ETransStatus> status, String amountType) {
        long[] longArray = new long[]{0L, 0L};

        StringBuilder str = new StringBuilder("'");
        for (int index = 0; index < type.size(); index++) {
            if (index != type.size() - 1) {
                str.append(type.get(index).toString()).append("','");
            } else {
                str.append(type.get(index).toString()).append("'");
            }
        }

        StringBuilder stringBuilder = new StringBuilder("'");
        for (int index = 0; index < status.size(); index++) {
            if (index != status.size() - 1) {
                stringBuilder.append(status.get(index).toString()).append("','");
            } else {
                stringBuilder.append(status.get(index).toString()).append("'");
            }
        }

        //脱机交易失败的状态
        ArrayList<TransData.OfflineStatus> filterForOffline = new ArrayList<>();
        filterForOffline.add(TransData.OfflineStatus.OFFLINE_ERR_SEND);
        filterForOffline.add(TransData.OfflineStatus.OFFLINE_ERR_RESP);
        filterForOffline.add(TransData.OfflineStatus.OFFLINE_ERR_UNKNOWN);

        StringBuilder string = new StringBuilder("'");
        for (int index = 0; index < filterForOffline.size(); index++) {
            if (index != filterForOffline.size() - 1) {
                string.append(filterForOffline.get(index).toString()).append("','");
            } else {
                string.append(filterForOffline.get(index).toString()).append("'");
            }
        }

        //普通交易的脱机上送状态为null，查询语句会返回false，因此需要特殊处理
        String sql = "SELECT count(*),sum(" + amountType + ") FROM " +
                TransDataDao.TABLENAME +
                " WHERE " +
                TransDataDao.Properties.TransState.columnName + " IN(" + stringBuilder.toString() + " )" +
                " AND " +
                TransDataDao.Properties.ReversalStatus.columnName + " = " + "'" + TransData.ReversalStatus.NORMAL.toString() + "'" +
                " AND " +
                TransDataDao.Properties.TransType.columnName + " IN(" + str.toString() + " )" +
                " AND " +
                "(CASE WHEN " + TransDataDao.Properties.OfflineSendState.columnName + " is not null" +
                " THEN " +
                TransDataDao.Properties.OfflineSendState.columnName + " NOT IN(" + string.toString() + " ) else 1" +
                " END) " +
                " AND " +
                TransDataDao.Properties.Acquirer_id.columnName + " = " + acquirer.getId();

        if (TransDataDao.Properties.TipAmount.columnName.equals(amountType)) {
            sql += " AND " +
                    TransDataDao.Properties.HasTip.columnName + " = 1";
        }

        if (issuer != null) {
            sql += " AND " +
                    TransDataDao.Properties.Issuer_id.columnName + " = " + issuer.getId();
        }

        Cursor cursor = null;
        try {
            cursor = getDatabase().rawQuery(sql, null);
            if (!cursor.moveToFirst()) {
                return longArray;
            }
            longArray[0] = cursor.getInt(0);
            String sum = cursor.getString(1);
            if (sum == null) {
                longArray[1] = 0;
            } else {
                longArray[1] = Utils.parseLongSafe(sum, 0);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        } finally {
            if (cursor != null && !cursor.isClosed()) {
                cursor.close();
            }
        }
        return longArray;
    }

    public final long[] countSumOfTip(Acquirer acquirer, Issuer issuer, List<ETransType> type, List<TransData.ETransStatus> status) {
        return countSumOf(acquirer, issuer, type, status, TransDataDao.Properties.TipAmount.columnName);
    }

    public final long[] countSumOfAmt(Acquirer acquirer, List<ETransType> type, List<TransData.ETransStatus> status) {
        return countSumOfAmt(acquirer, null, type, status);
    }

    public final long[] countSumOfAmt(Acquirer acquirer, Issuer issuer, ETransType type, TransData.ETransStatus status) {
        ArrayList<ETransType> eTransTypes = new ArrayList<>();
        eTransTypes.add(type);
        ArrayList<TransData.ETransStatus> eTransStatuses = new ArrayList<>();
        eTransStatuses.add(status);
        return countSumOfAmt(acquirer, issuer, eTransTypes, eTransStatuses);
    }

    public final TransData findFirstDupRecord() {
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.PENDING))
                .list();
        return list != null && !list.isEmpty() ? list.get(list.size() - 1) : null;
    }

    public final boolean deleteDupRecord() {
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.PENDING)).list();
        return list == null || list.isEmpty() || this.deleteEntities(list);
    }

    public final boolean deleteAllPreAuth() {
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.TransType.eq(ETransType.PREAUTH)).list();
        return list == null || list.isEmpty() || this.deleteEntities(list);
    }

    public final TransData findLastDupSuccTransData() {
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.REVERSAL_COM))
                .list();
        return list != null && !list.isEmpty() ? list.get(list.size() - 1) : null;
    }

    //查询reversal成功状态的统计
    public final long[] findTotalDupSuccTransCount() {
        List<TransData> list = getNoSessionQuery().where(TransDataDao.Properties.ReversalStatus.eq(TransData.ReversalStatus.REVERSAL_COM))
                .list();
        long count = list.size();
        long sumAmount = 0;
        //遍历统计金额总和
        for (TransData transData : list) {
            long amount = 0L;
            if (!TextUtils.isEmpty(transData.getAmount())) {
                amount = Long.parseLong(transData.getAmount());
            }
            sumAmount += amount;
        }

        return new long[]{count, sumAmount};
    }

    private static class LazyHolder {
        public static final TransDataDbHelper INSTANCE = new TransDataDbHelper();
    }
}
