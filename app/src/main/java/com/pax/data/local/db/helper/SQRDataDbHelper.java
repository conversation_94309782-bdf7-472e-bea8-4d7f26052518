package com.pax.data.local.db.helper;

import android.database.sqlite.SQLiteConstraintException;

import com.pax.commonlib.utils.LogUtils;
import com.pax.data.local.db.dao.SQRDataDao;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.mqtt.SQRData;
import com.pax.pay.utils.ToastUtils;
import com.pax.sbipay.R;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 数据库操作辅助类（SQRData）
 */
public class SQRDataDbHelper extends BaseDaoHelper<SQRData> {
    private static final String TAG = "SQRDataDbHelper";

    // 单例模式实现
    private SQRDataDbHelper() {
        super(SQRData.class);
    }

    public static SQRDataDbHelper getInstance() {
        return LazyHolder.INSTANCE;
    }

    private static class LazyHolder {
        private static final SQRDataDbHelper INSTANCE = new SQRDataDbHelper();
    }

    /**
     * 根据rrn查询记录
     *
     * @param rrn 系统参考号
     * @return 返回符合条件的SQRData记录
     */
    public final SQRData findSQRDataByRrn(String rrn) {
        return getNoSessionQuery().where(SQRDataDao.Properties.Rrn.eq(rrn)).unique();
    }

    /**
     * 根据打印状态查询记录
     *
     * @param printStatus 打印状态（0:未打印, 1:已打印）
     * @return 返回符合条件的记录列表
     */
    public final List<SQRData> queryByPrintStatus(int printStatus) {
        return getNoSessionQuery().where(SQRDataDao.Properties.PrintStatus.eq(printStatus)).list();
    }

    /**
     * 根据语音播报状态查询记录
     *
     * @param voiceStatus 语音播报状态（0:未播报, 1:已播报）
     * @return 返回符合条件的记录列表
     */
    public final List<SQRData> queryByVoiceStatus(int voiceStatus) {
        return getNoSessionQuery().where(SQRDataDao.Properties.VoiceStatus.eq(voiceStatus)).list();
    }

    /**
     * 查询所有SQRData记录
     *
     * @return 返回所有记录列表
     */
    public final List<SQRData> queryAllSQRData() {
        return loadAll();
    }

    public final boolean insertSQRData(SQRData data) {
        boolean isSuccess = false;
        try {
            // 插入数据时，GreenDAO 会自动检查唯一约束（RRN）并处理
            getDaoSession().getSQRDataDao().insert(data);
            isSuccess = true;
        } catch (SQLiteConstraintException e) {
            // 捕获唯一性约束异常（RRN重复）
            LogUtils.e(TAG, "Duplicate RRN detected: " + data.getRrn());
            // 弹出提示，告知用户 RRN 重复
            FinancialApplication.getApp().runOnUiThread(() ->
                    ToastUtils.showMessage(R.string.mqtt_duplicate_transaction));
        } catch (Exception e) {
            // 其他异常处理
            LogUtils.e(TAG, "Error inserting SQRData: ", e);
        }
        return isSuccess;
    }

    /**
     * 更新SQRData记录
     *
     * @param data 需要更新的SQRData对象
     */
    public final void updateSQRData(SQRData data) {
        try {
            getDaoSession().getSQRDataDao().update(data);
        } catch (Exception e) {
            LogUtils.e(TAG, "Error updating SQRData: ", e);
        }
    }

    /**
     * 删除超过指定小时数的过期数据
     *
     * @param hours 过期小时数
     * @return 删除的记录数
     */
    public final int deleteExpiredData(int hours) {
        try {
            long thresholdMillis = System.currentTimeMillis() - hours * 3_600_000L;
            Date thresholdDate = new Date(thresholdMillis);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String thresholdStr = sdf.format(thresholdDate);
            LogUtils.d(TAG, "Expiration threshold: " + thresholdStr);

            List<SQRData> expired = getNoSessionQuery()
                    .where(SQRDataDao.Properties.TranDate.lt(thresholdStr))
                    .list();

            if (expired != null && !expired.isEmpty()) {
                int count = expired.size();
                getDaoSession().getSQRDataDao().deleteInTx(expired);
                return count;
            }
            return 0;
        } catch (Exception e) {
            LogUtils.e(TAG, "Error deleting expired data", e);
            return -1;
        }
    }

    /**
     * 删除所有SQRData记录
     *
     * @return 是否删除成功
     */
    public final boolean deleteAllSQRData() {
        boolean isSuccess = false;
        try {
            // 删除所有记录
            getDaoSession().getSQRDataDao().deleteAll();
            isSuccess = true;
        } catch (Exception e) {
            LogUtils.e(TAG, "Error deleting all SQRData: ", e);
        }
        return isSuccess;
    }
}
