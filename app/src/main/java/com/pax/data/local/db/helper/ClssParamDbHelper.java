/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         xieYb                   Create
 * ===========================================================================================
 */
package com.pax.data.local.db.helper;

import com.pax.data.entity.ClssParam;
import com.pax.data.local.db.dao.ClssParamDao;

/**
 * Database operation helper of Issuer
 */
public class ClssParamDbHelper extends BaseDaoHelper<ClssParam> {
    public ClssParamDbHelper() {
        super(ClssParam.class);
    }

    public static ClssParamDbHelper getInstance() {
        return LazyHolder.INSTANCE;
    }

    public final ClssParam findClssParam(String clssParamName) {
        if (clssParamName == null || clssParamName.isEmpty()) {
            return null;
        }
        return getNoSessionQuery().where(ClssParamDao.Properties.Name.eq(clssParamName)).unique();
    }

    private static class LazyHolder {
        public static final ClssParamDbHelper INSTANCE = new ClssParamDbHelper();
    }


}
