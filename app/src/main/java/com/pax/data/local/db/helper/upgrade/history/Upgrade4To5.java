/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190621  	         xieYb                   Create
 * ===========================================================================================
 */
package com.pax.data.local.db.helper.upgrade.history;

import android.database.SQLException;

import com.pax.commonlib.utils.LogUtils;
import com.pax.data.local.db.dao.TransDataDao;
import com.pax.data.local.db.dao.TransTotalDao;
import com.pax.data.local.db.helper.upgrade.DbUpgrade;

import org.greenrobot.greendao.database.Database;

public class Upgrade4To5 extends DbUpgrade {

    @Override
    protected void upgrade(Database db) {
        try {
            DbUpgrade.upgradeTable(db, TransDataDao.class, TransTotalDao.class);
        } catch (SQLException e) {
            LogUtils.e(TAG, e);
        }
    }
}
