/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         xieYb                   Create
 * ===========================================================================================
 */
package com.pax.data.local.db.helper;

import com.pax.data.entity.CardRange;
import com.pax.data.entity.Issuer;
import com.pax.data.local.db.dao.CardRangeDao;

import java.util.List;

public class CardRangeDbHelper extends BaseDaoHelper<CardRange> {
    public CardRangeDbHelper() {
        super(CardRange.class);
    }

    public static CardRangeDbHelper getInstance() {
        return LazyHolder.INSTANCE;
    }

    public final CardRange findCardRange(Long lowLimit, Long highLimit) {
        return getNoSessionQuery().where(CardRangeDao.Properties.PanRangeLow.eq(lowLimit),
                CardRangeDao.Properties.PanRangeHigh.eq(highLimit))
                .unique();
    }

    /**
     * WHERE (low <= ? AND high >= ?) @1
     * WHERE (length = 0 OR ? = length) @2
     * WHERE @1 AND @2
     * order by (high - low)
     */
    public final CardRange findCardRange(String pan) {
        int maskLength = Math.min(pan.length(), 16);
        String subPan = pan.substring(0, maskLength);
        List<CardRange> list = getNoSessionQuery().where(CardRangeDao.Properties.PanRangeLow.le(subPan),
                CardRangeDao.Properties.PanRangeHigh.ge(subPan))
                .orderRaw(CardRangeDao.Properties.PanRangeHigh.columnName + "-" + CardRangeDao.Properties.PanRangeLow.columnName)
                .list();
        // 如果第一次未找到，尝试前 10 位
        if(list == null || list.isEmpty()){
            int secondMaskLength = Math.min(pan.length(), 10);  // 避免越界
            if (secondMaskLength == 0) {  // 卡号过短，无法继续查询
                return null;
            }
            subPan = pan.substring(0, secondMaskLength);
            list = getNoSessionQuery().where(CardRangeDao.Properties.PanRangeLow.le(subPan),
                            CardRangeDao.Properties.PanRangeHigh.ge(subPan))
                    .orderRaw(CardRangeDao.Properties.PanRangeHigh.columnName + "-" + CardRangeDao.Properties.PanRangeLow.columnName)
                    .list();
        }
        if (list != null && !list.isEmpty()) {
            return  list.get(0);
        }
        return null;
    }

    public final List<CardRange> findCardRange(Issuer issuer) {
        return getNoSessionQuery().where(CardRangeDao.Properties.Issuer_id.eq(issuer.getId())).list();
    }

    private static class LazyHolder {
        public static final CardRangeDbHelper INSTANCE = new CardRangeDbHelper();
    }

}
