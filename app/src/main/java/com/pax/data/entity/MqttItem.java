package com.pax.data.entity;

import com.pax.settings.SysParam;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.NotNull;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.converter.PropertyConverter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/2/6
 */
@Entity(nameInDb = "mqtt_item")
public class MqttItem implements Serializable {

    public static final String ID_FIELD_NAME = "mqtt_id";
    public static final String NAME_FIELD_NAME = "mqtt_name";
    public static final String IP_FIELD_NAME = "mqtt_ip";
    public static final String PORT_FIELD_NAME = "mqtt_port";
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Id(autoincrement = true)
    @Property(nameInDb = ID_FIELD_NAME)
    private Long id;

    @Property(nameInDb = NAME_FIELD_NAME)
    @NotNull
    private String mqttName;

    @Property(nameInDb = IP_FIELD_NAME)
    @NotNull
    private String mqttIp;

    @Property(nameInDb = PORT_FIELD_NAME)
    @NotNull
    private String mqttPort;

    @Generated(hash = 102396798)
    public MqttItem(Long id, @NotNull String mqttName, @NotNull String mqttIp,
            @NotNull String mqttPort) {
        this.id = id;
        this.mqttName = mqttName;
        this.mqttIp = mqttIp;
        this.mqttPort = mqttPort;
    }

    public MqttItem(@NotNull String mqttName, @NotNull String mqttIp,
                    @NotNull String mqttPort) {
        this.mqttName = mqttName;
        this.mqttIp = mqttIp;
        this.mqttPort = mqttPort;
    }

    @Generated(hash = 1397998474)
    public MqttItem() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMqttName() {
        return this.mqttName;
    }

    public void setMqttName(String mqttName) {
        this.mqttName = mqttName;
    }

    public String getMqttIp() {
        return this.mqttIp;
    }

    public void setMqttIp(String mqttIp) {
        this.mqttIp = mqttIp;
    }

    public String getMqttPort() {
        return this.mqttPort;
    }

    public void setMqttPort(String mqttPort) {
        this.mqttPort = mqttPort;
    }

}
