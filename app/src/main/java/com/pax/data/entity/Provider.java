package com.pax.data.entity;

import androidx.annotation.NonNull;

import com.pax.settings.SysParam;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.NotNull;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.converter.PropertyConverter;

import java.io.Serializable;
import com.pax.settings.SysParam.CommSslType;

/**
 * <AUTHOR>
 * @Date 2024/2/6
 */
@Entity(nameInDb = "provider")
public class Provider implements Serializable {

    public static final String ID_FIELD_NAME = "provider_id";
    public static final String Media_FIELD_NAME = "communication_media";
    public static final String LABEL_FIELD_NAME = "comm_label";
    public static final String EMI_FIELD_NAME = "emi_value";
    public static final String HOST_FIELD_NAME = "host_ip";
    public static final String PORT_FIELD_NAME = "port_number";
    public static final String SSL_TYPE_FIELD_NAME = "tls/ssl";
    public static final String APN_FIELD_NAME = "apn";
    public static final String NII_FIELD_NAME = "nii";
    public static final String MQTT_IP_FIELD_NAME = "mqtt_ip";
    public static final String MQTT_PORT_FIELD_NAME = "mqtt_port";
    public static final String MQTT_QOS_FIELD_NAME = "mqtt_qos";
    public static final String MQTT_TIMEOUT_FIELD_NAME = "mqtt_timeout";
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Id(autoincrement = true)
    @Property(nameInDb = ID_FIELD_NAME)
    private Long id;

    @Property(nameInDb = Media_FIELD_NAME)
    @NotNull
    private String mediaName;

    @Property(nameInDb = LABEL_FIELD_NAME)
    @NotNull
    private String commLabel;

    @Property(nameInDb = EMI_FIELD_NAME)
    @NotNull
    private String emiValue;

    @Property(nameInDb = HOST_FIELD_NAME)
    @NotNull
    private String providerHost;

    @Property(nameInDb = PORT_FIELD_NAME)
    @NotNull
    private int providerPort;

    @Property(nameInDb = SSL_TYPE_FIELD_NAME)
    @Convert(converter = SSLTypeConverter.class, columnType = String.class)
    private SysParam.CommSslType sslType = SysParam.CommSslType.SSL;
    @Property(nameInDb = APN_FIELD_NAME)
    private String apn;

    @Property(nameInDb = NII_FIELD_NAME)
    @NotNull
    private String nii;

    @Property(nameInDb = MQTT_IP_FIELD_NAME)
    @NotNull
    private String mqttIp;

    @Property(nameInDb = MQTT_PORT_FIELD_NAME)
    @NotNull
    private int mqttPort;

    @Property(nameInDb = MQTT_QOS_FIELD_NAME)
    @NotNull
    private int qos;

    @Property(nameInDb = MQTT_TIMEOUT_FIELD_NAME)
    @NotNull
    private int mqttTimeout;




    @Generated(hash = 767655913)
    public Provider(Long id, @NotNull String mediaName, @NotNull String commLabel,
            @NotNull String emiValue, @NotNull String providerHost, int providerPort,
            SysParam.CommSslType sslType, String apn, @NotNull String nii, @NotNull String mqttIp,
            int mqttPort, int qos, int mqttTimeout) {
        this.id = id;
        this.mediaName = mediaName;
        this.commLabel = commLabel;
        this.emiValue = emiValue;
        this.providerHost = providerHost;
        this.providerPort = providerPort;
        this.sslType = sslType;
        this.apn = apn;
        this.nii = nii;
        this.mqttIp = mqttIp;
        this.mqttPort = mqttPort;
        this.qos = qos;
        this.mqttTimeout = mqttTimeout;
    }




    @Generated(hash = **********)
    public Provider() {
    }




    public Long getId() {
        return this.id;
    }




    public void setId(Long id) {
        this.id = id;
    }




    public String getMediaName() {
        return this.mediaName;
    }




    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }




    public String getCommLabel() {
        return this.commLabel;
    }




    public void setCommLabel(String commLabel) {
        this.commLabel = commLabel;
    }




    public String getEmiValue() {
        return this.emiValue;
    }




    public void setEmiValue(String emiValue) {
        this.emiValue = emiValue;
    }




    public String getProviderHost() {
        return this.providerHost;
    }




    public void setProviderHost(String providerHost) {
        this.providerHost = providerHost;
    }




    public int getProviderPort() {
        return this.providerPort;
    }




    public void setProviderPort(int providerPort) {
        this.providerPort = providerPort;
    }




    public SysParam.CommSslType getSslType() {
        return this.sslType;
    }




    public void setSslType(SysParam.CommSslType sslType) {
        this.sslType = sslType;
    }




    public String getApn() {
        return this.apn;
    }




    public void setApn(String apn) {
        this.apn = apn;
    }




    public String getNii() {
        return this.nii;
    }




    public void setNii(String nii) {
        this.nii = nii;
    }




    public String getMqttIp() {
        return this.mqttIp;
    }




    public void setMqttIp(String mqttIp) {
        this.mqttIp = mqttIp;
    }




    public int getMqttPort() {
        return this.mqttPort;
    }




    public void setMqttPort(int mqttPort) {
        this.mqttPort = mqttPort;
    }




    public int getQos() {
        return this.qos;
    }




    public void setQos(int qos) {
        this.qos = qos;
    }




    public int getMqttTimeout() {
        return this.mqttTimeout;
    }




    public void setMqttTimeout(int mqttTimeout) {
        this.mqttTimeout = mqttTimeout;
    }




    public static class SSLTypeConverter implements PropertyConverter<SysParam.CommSslType, String> {

        @Override
        public SysParam.CommSslType convertToEntityProperty(String databaseValue) {
            if (databaseValue == null) {
                return null;
            }
            for (SysParam.CommSslType item : SysParam.CommSslType.values()) {
                if (item.toString().equals(databaseValue)) {
                    return item;
                }
            }
            return SysParam.CommSslType.NO_SSL;
        }

        @Override
        public String convertToDatabaseValue(SysParam.CommSslType entityProperty) {
            return entityProperty == null ? null : entityProperty.toString();
        }
    }
}
