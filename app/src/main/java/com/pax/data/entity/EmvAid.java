/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Steven.W                Create
 * ===========================================================================================
 */
package com.pax.data.entity;

import androidx.annotation.IntDef;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;

import com.alibaba.fastjson.annotation.JSONField;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.data.local.GreendaoHelper;
import com.pax.eemv.entity.AidParam;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.NotNull;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Unique;

import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Entity(nameInDb = "aid")
public class EmvAid implements Serializable {
    public static final int PART_MATCH = 0;
    public static final int FULL_MATCH = 1;
    public static final String ID_FIELD_NAME = "id";
    public static final String AID_FIELD_NAME = "aid";
    private static final long serialVersionUID = 1L;
    @Id(autoincrement = true)
    @Property(nameInDb = ID_FIELD_NAME)
    @JSONField
    private Long id;
    /**
     * name
     */
    @NotNull
    private String appName;
    /**
     * aid
     */
    @Property(nameInDb = AID_FIELD_NAME)
    @NotNull
    @Unique
    private String aid;
    /**
     * PART_MATCH/FULL_MATCH
     */
    @NotNull
    @SelType
    private int selFlag;
    /**
     * priority
     */
    @NotNull
    private int priority;
    /**
     * if enable online PIN
     */
    @NotNull
    private boolean onlinePin;
    /**
     * tag DF21
     */
    @NotNull
    private long rdCVMLmt;
    /**
     * tag DF20
     */
    @NotNull
    private long rdClssTxnLmt;
    /**
     * tag DF19
     */
    @NotNull
    private long rdClssFLmt;
    /**
     * clss floor limit flag
     * 0- Deactivated
     * 1- Active and exist
     * 2- Active but not exist
     */
    @NotNull
    @IntRange(from = 0, to = 2)
    private int rdClssFLmtFlg;
    /**
     * clss transaction limit flag
     * 0- Deactivated
     * 1- Active and exist
     * 2- Active but not exist
     */
    @NotNull
    @IntRange(from = 0, to = 2)
    private int rdClssTxnLmtFlg;
    /**
     * clss CVM limit flag
     * 0- Deactivated
     * 1- Active and exist
     * 2- Active but not exist
     */
    @NotNull
    @IntRange(from = 0, to = 2)
    private int rdCVMLmtFlg;
    /**
     * target percent
     */
    @NotNull
    @IntRange(from = 0, to = 100)
    private int targetPer;
    /**
     * max target percent
     */
    @NotNull
    @IntRange(from = 0, to = 100)
    private int maxTargetPer;
    /**
     * floor limit check flag
     * 0- don't check
     * 1- Check
     */
    @NotNull
    @IntRange(from = 0, to = 1)
    private int floorLimitCheckFlg;
    /**
     * do random transaction selection
     */
    @NotNull
    private boolean randTransSel;
    /**
     * velocity check
     */
    @NotNull
    private boolean velocityCheck;
    /**
     * floor limit
     */
    @NotNull
    private long floorLimit;
    /**
     * threshold
     */
    @NotNull
    private long threshold;
    /**
     * TAC denial
     */
    private String tacDenial;
    /**
     * TAC online
     */
    private String tacOnline;
    /**
     * TAC default
     */
    private String tacDefault;
    /**
     * acquirer id
     */
    private String acquirerId;
    /**
     * dDOL
     */
    private String dDOL;
    /**
     * tDOL
     */
    private String tDOL;
    /**
     * application version
     */
    private String version;
    /**
     * risk management data
     */
    private String riskManageData;
    @Generated(hash = 903587869)
    public EmvAid(Long id, @NotNull String appName, @NotNull String aid, int selFlag, int priority, boolean onlinePin,
                  long rdCVMLmt, long rdClssTxnLmt, long rdClssFLmt, int rdClssFLmtFlg, int rdClssTxnLmtFlg, int rdCVMLmtFlg,
                  int targetPer, int maxTargetPer, int floorLimitCheckFlg, boolean randTransSel, boolean velocityCheck,
                  long floorLimit, long threshold, String tacDenial, String tacOnline, String tacDefault, String acquirerId,
                  String dDOL, String tDOL, String version, String riskManageData) {
        this.id = id;
        this.appName = appName;
        this.aid = aid;
        this.selFlag = selFlag;
        this.priority = priority;
        this.onlinePin = onlinePin;
        this.rdCVMLmt = rdCVMLmt;
        this.rdClssTxnLmt = rdClssTxnLmt;
        this.rdClssFLmt = rdClssFLmt;
        this.rdClssFLmtFlg = rdClssFLmtFlg;
        this.rdClssTxnLmtFlg = rdClssTxnLmtFlg;
        this.rdCVMLmtFlg = rdCVMLmtFlg;
        this.targetPer = targetPer;
        this.maxTargetPer = maxTargetPer;
        this.floorLimitCheckFlg = floorLimitCheckFlg;
        this.randTransSel = randTransSel;
        this.velocityCheck = velocityCheck;
        this.floorLimit = floorLimit;
        this.threshold = threshold;
        this.tacDenial = tacDenial;
        this.tacOnline = tacOnline;
        this.tacDefault = tacDefault;
        this.acquirerId = acquirerId;
        this.dDOL = dDOL;
        this.tDOL = tDOL;
        this.version = version;
        this.riskManageData = riskManageData;
    }

    @Generated(hash = 1782441615)
    public EmvAid() {
    }

    /***************************
     * EmvAidParam to AidParam
     ***********************************/
    @NonNull
    public static List<AidParam> toAidParams() {
        List<AidParam> list = new LinkedList<>();

        List<EmvAid> aidList = GreendaoHelper.getEmvAidHelper().loadAll();
        if (aidList == null) {
            return new ArrayList<>(0);
        }
        for (EmvAid emvAidParam : aidList) {
            AidParam aidParam = new AidParam();
            aidParam.setAppName(emvAidParam.getAppName());
            aidParam.setAid(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getAid()));
            aidParam.setSelFlag((byte) emvAidParam.getSelFlag());
            aidParam.setPriority((byte) emvAidParam.getPriority());
            aidParam.setOnlinePin(emvAidParam.getOnlinePin());
            aidParam.setRdCVMLmt(emvAidParam.getRdCVMLmt());
            aidParam.setRdClssTxnLmt(emvAidParam.getRdClssTxnLmt());
            aidParam.setRdClssFLmt(emvAidParam.getRdClssFLmt());
            aidParam.setRdClssFLmtFlag(emvAidParam.getRdClssFLmtFlg());
            aidParam.setRdClssTxnLmtFlag(emvAidParam.getRdClssTxnLmtFlg());
            aidParam.setRdCVMLmtFlag(emvAidParam.getRdCVMLmtFlg());
            aidParam.setFloorLimit(emvAidParam.getFloorLimit());
            aidParam.setFloorLimitCheckFlg(emvAidParam.getFloorLimitCheckFlg());
            aidParam.setThreshold(emvAidParam.getThreshold());
            aidParam.setTargetPer((byte) emvAidParam.getTargetPer());
            aidParam.setMaxTargetPer((byte) emvAidParam.getMaxTargetPer());
            aidParam.setRandTransSel(emvAidParam.getRandTransSel());
            aidParam.setVelocityCheck(emvAidParam.getVelocityCheck());
            aidParam.setTacDenial(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getTacDenial()));
            aidParam.setTacOnline(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getTacOnline()));
            aidParam.setTacDefault(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getTacDefault()));
            if (emvAidParam.getAcquirerId() != null) {
                aidParam.setAcquirerId(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getAcquirerId()));
            }
            if (emvAidParam.getDDOL() != null) {
                aidParam.setdDol(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getDDOL()));
            }
            if (emvAidParam.getTDOL() != null) {
                aidParam.settDol(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getTDOL()));
            }
            aidParam.setVersion(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getVersion()));
            if (emvAidParam.getRiskManageData() != null) {
                aidParam.setRiskManData(ConvertHelper.getConvert().strToBcdPaddingLeft(emvAidParam.getRiskManageData()));
            }
            list.add(aidParam);
        }
        return list;
    }

    public static void load(List<EmvAid> aids) {
        // test apps
        GreendaoHelper.getEmvAidHelper().insert(aids);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getSelFlag() {
        return selFlag;
    }

    public void setSelFlag(@SelType int selFlag) {
        this.selFlag = selFlag;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean getOnlinePin() {
        return onlinePin;
    }

    public void setOnlinePin(boolean onlinePin) {
        this.onlinePin = onlinePin;
    }

    public long getRdCVMLmt() {
        return rdCVMLmt;
    }

    public void setRdCVMLmt(long rdCVMLmt) {
        this.rdCVMLmt = rdCVMLmt;
    }

    public long getRdClssTxnLmt() {
        return rdClssTxnLmt;
    }

    public void setRdClssTxnLmt(long rdClssTxnLmt) {
        this.rdClssTxnLmt = rdClssTxnLmt;
    }

    public long getRdClssFLmt() {
        return rdClssFLmt;
    }

    public void setRdClssFLmt(long rdClssFLmt) {
        this.rdClssFLmt = rdClssFLmt;
    }

    public int getRdClssFLmtFlg() {
        return rdClssFLmtFlg;
    }

    public void setRdClssFLmtFlg(@IntRange(from = 0, to = 2) int rdClssFLmtFlg) {
        this.rdClssFLmtFlg = rdClssFLmtFlg;
    }

    public int getRdClssTxnLmtFlg() {
        return rdClssTxnLmtFlg;
    }

    public void setRdClssTxnLmtFlg(@IntRange(from = 0, to = 2) int rdClssTxnLmtFlg) {
        this.rdClssTxnLmtFlg = rdClssTxnLmtFlg;
    }

    public int getRdCVMLmtFlg() {
        return rdCVMLmtFlg;
    }

    public void setRdCVMLmtFlg(@IntRange(from = 0, to = 2) int rdCVMLmtFlg) {
        this.rdCVMLmtFlg = rdCVMLmtFlg;
    }

    public int getTargetPer() {
        return targetPer;
    }

    public void setTargetPer(@IntRange(from = 0, to = 100) int targetPer) {
        this.targetPer = targetPer;
    }

    public int getMaxTargetPer() {
        return maxTargetPer;
    }

    public void setMaxTargetPer(@IntRange(from = 0, to = 100) int maxTargetPer) {
        this.maxTargetPer = maxTargetPer;
    }

    public int getFloorLimitCheckFlg() {
        return floorLimitCheckFlg;
    }

    public void setFloorLimitCheckFlg(@IntRange(from = 0, to = 1) int floorLimitCheckFlg) {
        this.floorLimitCheckFlg = floorLimitCheckFlg;
    }

    public boolean getRandTransSel() {
        return randTransSel;
    }

    public void setRandTransSel(boolean randTransSel) {
        this.randTransSel = randTransSel;
    }

    public boolean getVelocityCheck() {
        return velocityCheck;
    }

    public void setVelocityCheck(boolean velocityCheck) {
        this.velocityCheck = velocityCheck;
    }

    public long getFloorLimit() {
        return floorLimit;
    }

    public void setFloorLimit(long floorLimit) {
        this.floorLimit = floorLimit;
    }

    public long getThreshold() {
        return threshold;
    }

    public void setThreshold(long threshold) {
        this.threshold = threshold;
    }

    public String getTacDenial() {
        return tacDenial;
    }

    public void setTacDenial(String tacDenial) {
        this.tacDenial = tacDenial;
    }

    public String getTacOnline() {
        return tacOnline;
    }

    public void setTacOnline(String tacOnline) {
        this.tacOnline = tacOnline;
    }

    public String getTacDefault() {
        return tacDefault;
    }

    public void setTacDefault(String tacDefault) {
        this.tacDefault = tacDefault;
    }

    public String getAcquirerId() {
        return acquirerId;
    }

    public void setAcquirerId(String acquirerId) {
        this.acquirerId = acquirerId;
    }

    public String getDDOL() {
        return dDOL;
    }

    public void setDDOL(String dDOL) {
        this.dDOL = dDOL;
    }

    public String getTDOL() {
        return tDOL;
    }

    public void setTDOL(String tDOL) {
        this.tDOL = tDOL;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRiskManageData() {
        return riskManageData;
    }

    public void setRiskManageData(String riskManageData) {
        this.riskManageData = riskManageData;
    }

    @Override
    public String toString() {
        return appName;
    }

    @IntDef({PART_MATCH, FULL_MATCH})
    @Retention(RetentionPolicy.SOURCE)
    public @interface SelType {
    }
}
