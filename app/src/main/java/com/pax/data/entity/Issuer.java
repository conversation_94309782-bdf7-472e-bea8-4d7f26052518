/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.data.entity;

import androidx.annotation.NonNull;

import com.pax.sbipay.R;
import com.pax.settings.SysParam;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Unique;

import java.io.Serializable;
import java.util.Calendar;

/**
 * issuer table
 */
@Entity(nameInDb = "issuer")
public class Issuer implements Serializable {

    public static final String ID_FIELD_NAME = "issuer_id";
    public static final String NAME_FIELD_NAME = "issuer_name";
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Id(autoincrement = true)
    @Property(nameInDb = ID_FIELD_NAME)
    private Long id;

    /**
     * name
     */
    @Property(nameInDb = NAME_FIELD_NAME)
    @Unique
    private String name;

    private long floorLimit;

    /**
     * using regular expression, example {@link com.pax.pay.constant.Constants#DEF_PAN_MASK_PATTERN}
     */
    private String panMaskPattern;

    private boolean isRequirePIN = true;

    private boolean isRequireMaskExpiry = true;
    private boolean isAllowPreAuth = true;


    public Issuer() {
    }

    public Issuer(String name) {
        this.setName(name);
    }

    public Issuer(Long id, String name) {
        this.setId(id);
        this.setName(name);
    }

    @Generated(hash = 1963746907)
    public Issuer(Long id, String name, long floorLimit, String panMaskPattern, boolean isRequirePIN,
                  boolean isRequireMaskExpiry, boolean isAllowPreAuth) {
        this.id = id;
        this.name = name;
        this.floorLimit = floorLimit;
        this.panMaskPattern = panMaskPattern;
        this.isRequirePIN = isRequirePIN;
        this.isRequireMaskExpiry = isRequireMaskExpiry;
        this.isAllowPreAuth = isAllowPreAuth;
    }

    public static boolean validPan(final Issuer issuer, String pan) {
        if (!SysParam.getInstance().getBoolean(R.string.EDC_CHECK_PAN)) {
            return true;
        }

        boolean flag = false;
        int result = 0;
        for (int i = (pan.length() - 1); i >= 0; --i) {
            int tmp = pan.charAt(i) & 15;
            if (flag) {
                tmp *= 2;
            }
            if (tmp > 9) {
                tmp -= 9;
            }
            result = (tmp + result) % 10;
            flag = !flag;
        }

        return result == 0;
    }

    public static boolean validCardExpiry(String date) {
        if (!SysParam.getInstance().getBoolean(R.string.EDC_CHECK_EXPIRY)) {
            return true;
        }
        Calendar now = Calendar.getInstance();

        int year = Integer.parseInt(date.substring(0, 2));
        year += year > 80 ? 1900 : 2000;
        int month = Integer.parseInt(date.substring(2, 4));

        return !(year < now.get(Calendar.YEAR) ||
                (year <= now.get(Calendar.YEAR) && month < (now.get(Calendar.MONTH)+1)));
    }

    //新增接口，不根据参数判断是否检测卡片有效期
    public static boolean validCardExpiryWithoutCondition(String date) {
        Calendar now = Calendar.getInstance();

        int year = Integer.parseInt(date.substring(0, 2));
        year += year > 80 ? 1900 : 2000;
        int month = Integer.parseInt(date.substring(2, 4));

        return !(year < now.get(Calendar.YEAR) ||
                (year <= now.get(Calendar.YEAR) && month < (now.get(Calendar.MONTH)+1)));
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getFloorLimit() {
        return floorLimit;
    }

    public void setFloorLimit(long floorLimit) {
        this.floorLimit = floorLimit;
    }

    public String getPanMaskPattern() {
        return panMaskPattern;
    }

    public void setPanMaskPattern(String panMaskPattern) {
        this.panMaskPattern = panMaskPattern;
    }

    public boolean isRequirePIN() {
        return isRequirePIN;
    }

    public void setRequirePIN(boolean requirePIN) {
        isRequirePIN = requirePIN;
    }

    public boolean isRequireMaskExpiry() {
        return isRequireMaskExpiry;
    }

    public void setRequireMaskExpiry(boolean requireMaskExpiry) {
        isRequireMaskExpiry = requireMaskExpiry;
    }

    @Override
    public boolean equals(Object obj) {
        return obj != null && getClass() == obj.getClass() && ((Issuer) obj).getName().equals(getName());
    }

    @Override
    public int hashCode() {
        return 17 * id.hashCode() + name.hashCode();
    }

    public void update(@NonNull Issuer issuer) {
        floorLimit = issuer.getFloorLimit();
        panMaskPattern = issuer.getPanMaskPattern();
        isRequireMaskExpiry = issuer.isRequireMaskExpiry();
        isRequirePIN = issuer.isRequirePIN();
        isAllowPreAuth = issuer.isAllowPreAuth();
    }

    public boolean getIsRequirePIN() {
        return this.isRequirePIN;
    }

    public void setIsRequirePIN(boolean isRequirePIN) {
        this.isRequirePIN = isRequirePIN;
    }

    public boolean getIsRequireMaskExpiry() {
        return this.isRequireMaskExpiry;
    }

    public void setIsRequireMaskExpiry(boolean isRequireMaskExpiry) {
        this.isRequireMaskExpiry = isRequireMaskExpiry;
    }

    public boolean isAllowPreAuth() {
        return isAllowPreAuth;
    }

    public void setAllowPreAuth(boolean allowPreAuth) {
        isAllowPreAuth = allowPreAuth;
    }

    public boolean getIsAllowPreAuth() {
        return this.isAllowPreAuth;
    }

    public void setIsAllowPreAuth(boolean isAllowPreAuth) {
        this.isAllowPreAuth = isAllowPreAuth;
    }
}
