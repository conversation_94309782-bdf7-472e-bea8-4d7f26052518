package com.pax.data.entity;

public class DeviceDetails {
    private String tid;
    private String mid;
    private String hostIp;
    private int hostPort;
    private String hostNii;
    private String hostApn;
    private String softwareVersion;
    private String buildNum;
    private String buildType;
    private String buildDate;
    private String firmwareVersion;
    private String resourcePackageVersion;
    private String neptuneServiceVersion;
    private String hardwareVersion;
    private String spVersion;
    private String modelName;
    private String serialNumber;
    private String selectedMedia;
    private String simStatus;
    private String simNumber;
    private String networkName;
    private int signalStrength;
    private String sslEnable;
    private String macAddress;
    private String merchantName;
    private String terminalIp;
    private String mqttIp;
    private String mqttPort;
    private String mqttQos;
    private String mqttConnectTimeout;
    private String batteryLeve;
    private String batteryStatus;
    private String paperUsage;

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getHostIp() {
        return hostIp;
    }

    public void setHostIp(String hostIp) {
        this.hostIp = hostIp;
    }

    public int getHostPort() {
        return hostPort;
    }

    public void setHostPort(int hostPort) {
        this.hostPort = hostPort;
    }

    public String getHostNii() {
        return hostNii;
    }

    public void setHostNii(String hostNii) {
        this.hostNii = hostNii;
    }

    public String getHostApn() {
        return hostApn;
    }

    public void setHostApn(String hostApn) {
        this.hostApn = hostApn;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public String getBuildNum() {
        return buildNum;
    }

    public void setBuildNum(String buildNum) {
        this.buildNum = buildNum;
    }

    public String getBuildType() {
        return buildType;
    }

    public void setBuildType(String buildType) {
        this.buildType = buildType;
    }

    public String getBuildDate() {
        return buildDate;
    }

    public void setBuildDate(String buildDate) {
        this.buildDate = buildDate;
    }

    public String getFirmwareVersion() {
        return firmwareVersion;
    }

    public void setFirmwareVersion(String firmwareVersion) {
        this.firmwareVersion = firmwareVersion;
    }

    public String getResourcePackageVersion() {
        return resourcePackageVersion;
    }

    public void setResourcePackageVersion(String resourcePackageVersion) {
        this.resourcePackageVersion = resourcePackageVersion;
    }

    public String getNeptuneServiceVersion() {
        return neptuneServiceVersion;
    }

    public void setNeptuneServiceVersion(String neptuneServiceVersion) {
        this.neptuneServiceVersion = neptuneServiceVersion;
    }

    public String getHardwareVersion() {
        return hardwareVersion;
    }

    public void setHardwareVersion(String hardwareVersion) {
        this.hardwareVersion = hardwareVersion;
    }

    public String getSpVersion() {
        return spVersion;
    }

    public void setSpVersion(String spVersion) {
        this.spVersion = spVersion;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSelectedMedia() {
        return selectedMedia;
    }

    public void setSelectedMedia(String selectedMedia) {
        this.selectedMedia = selectedMedia;
    }


    public String getSimStatus() {
        return simStatus;
    }

    public void setSimStatus(String simStatus) {
        this.simStatus = simStatus;
    }

    public String getSimNumber() {
        return simNumber;
    }

    public void setSimNumber(String simNumber) {
        this.simNumber = simNumber;
    }

    public String getNetworkName() {
        return networkName;
    }

    public void setNetworkName(String networkName) {
        this.networkName = networkName;
    }

    public int getSignalStrength() {
        return signalStrength;
    }

    public void setSignalStrength(int signalStrength) {
        this.signalStrength = signalStrength;
    }

    public String getSslEnable() {
        return sslEnable;
    }

    public void setSslEnable(String sslEnable) {
        this.sslEnable = sslEnable;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getTerminalIp() {
        return terminalIp;
    }

    public void setTerminalIp(String terminalIp) {
        this.terminalIp = terminalIp;
    }

    public String getMqttIp() {
        return mqttIp;
    }

    public void setMqttIp(String mqttIp) {
        this.mqttIp = mqttIp;
    }

    public String getMqttPort() {
        return mqttPort;
    }

    public void setMqttPort(String mqttPort) {
        this.mqttPort = mqttPort;
    }

    public String getMqttQos() {
        return mqttQos;
    }

    public void setMqttQos(String mqttQos) {
        this.mqttQos = mqttQos;
    }

    public String getMqttConnectTimeout() {
        return mqttConnectTimeout;
    }

    public void setMqttConnectTimeout(String mqttConnectTimeout) {
        this.mqttConnectTimeout = mqttConnectTimeout;
    }

    public String getBatteryLeve() {
        return batteryLeve;
    }

    public void setBatteryLeve(String batteryLeve) {
        this.batteryLeve = batteryLeve;
    }

    public String getBatteryStatus() {
        return batteryStatus;
    }

    public void setBatteryStatus(String batteryStatus) {
        this.batteryStatus = batteryStatus;
    }

    public String getPaperUsage() {
        return paperUsage;
    }

    public void setPaperUsage(String paperUsage) {
        this.paperUsage = paperUsage;
    }
}
