/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         huangmuhua              Create
 * ===========================================================================================
 */
package com.pax.eventbus;

/**
 * card searching event
 */
public class SearchCardEvent extends Event {
    public SearchCardEvent(Status status) {
        super(status);
    }

    public SearchCardEvent(Status status, Object data) {
        super(status, data);
    }

    public enum Status {
        ICC_UPDATE_CARD_INFO,
        ICC_CONFIRM_CARD_NUM,
        CLSS_LIGHT_STATUS_NOT_READY,
        CLSS_LIGHT_STATUS_IDLE,
        CLSS_LIGHT_STATUS_READY_FOR_TXN,
        CLSS_LIGHT_STATUS_PROCESSING,
        CLSS_LIGHT_STATUS_REMOVE_CARD,
        CLSS_LIGHT_STATUS_COMPLETE,
        CLSS_LIGHT_STATUS_ERROR,
    }
}
