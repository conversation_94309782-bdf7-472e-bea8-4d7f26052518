/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         huangmuhua              Create
 * ===========================================================================================
 */
package com.pax.eventbus;

/**
 * emv callback event
 */
//接触流程的EMV拦截回调事件
public class EmvCallbackEvent extends Event {
    public EmvCallbackEvent(Status status) {
        super(status);
    }

    public EmvCallbackEvent(Status status, Object data) {
        super(status, data);
    }

    public enum Status {
        OFFLINE_PIN_ENTER_READY,
        CARD_NUM_CONFIRM_SUCCESS,
        CARD_NUM_CONFIRM_ERROR,
        TIMEOUT,
    }
}
