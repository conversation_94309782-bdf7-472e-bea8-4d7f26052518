package com.pax.eventbus;

public class SettlePendingEvent extends Event {

    public enum Status {
        SETTLE_PENDING_FOR_CLSS,
        SETTLE_PENDING_FOR_INSERT
    }

    /**
     * constructor
     *
     * @param status status
     */
    public SettlePendingEvent(Object status) {
        super(status);
    }

    /**
     * constructor
     *
     * @param status status
     * @param data data
     */
    public SettlePendingEvent(Object status, Object data) {
        super(status, data);
    }
}
