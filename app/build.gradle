apply plugin: 'com.android.application'

def TEST_APP_KEY = "\"ZKI6MWTZJROWXMO8955M\""
def TEST_APP_SECRET = "\"O0REAQSREZB0ZYPABLBTGFTZO8Y5HZLFCTVKPI47\""
def RELEASE_APP_KEY = "\"GXHURF0VSFUNHJPXTUP5\""
def RELEASE_APP_SECRET = "\"DY1CBYS1YED2ILSSGBQ5HIOH86EJOPHC7T1BEHJL\""

def static releaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("UTC"))
}


def loadSigningConfigs() {
    Properties props = new Properties()
    def propFile = file('../signing.properties')
    if (propFile.canRead()) {
        props.load(new FileInputStream(propFile))
        if (props != null && props.containsKey('RELEASE_STORE_FILE') && props.containsKey('RELEASE_STORE_PASSWORD') &&
                props.containsKey('RELEASE_KEY_ALIAS') && props.containsKey('RELEASE_KEY_PASSWORD')) {
            android.signingConfigs.release.storeFile = file(props['RELEASE_STORE_FILE'])
            android.signingConfigs.release.storePassword = props['RELEASE_STORE_PASSWORD']
            android.signingConfigs.release.keyAlias = props['RELEASE_KEY_ALIAS']
            android.signingConfigs.release.keyPassword = props['RELEASE_KEY_PASSWORD']
        } else {
            android.buildTypes.release.signingConfig = null
        }
    } else {
        android.buildTypes.release.signingConfig = null
    }
}

android {
    compileSdkVersion 33
    defaultConfig {
        applicationId "com.pax.edc.hase"
        minSdkVersion 23
        targetSdkVersion 33
        versionCode 196
        versionName "1.06.18" + "_" + releaseTime()
        flavorDimensions "default"
        multiDexEnabled true
    }

    lintOptions {
        abortOnError false
    }

    //签名
    signingConfigs {
        debug {
        }
        release {
        }
    }

    buildTypes {
        debug {
            buildConfigField "boolean", "LOG_DEBUG", "true"
            buildConfigField "String", "APP_KEY", "${TEST_APP_KEY}"
            buildConfigField "String", "APP_SECRET", "${TEST_APP_SECRET}"
            versionNameSuffix "-debug"
            minifyEnabled false
            zipAlignEnabled false
            shrinkResources false
        }

        beta{
            buildConfigField "boolean", "LOG_DEBUG", "true"
            buildConfigField "String", "APP_KEY", "${TEST_APP_KEY}"
            buildConfigField "String", "APP_SECRET", "${TEST_APP_SECRET}"
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable true
            matchingFallbacks = ['release']

            loadSigningConfigs()
            //签名
            signingConfig signingConfigs.release
        }

        release {
            buildConfigField "boolean", "LOG_DEBUG", "true"
            buildConfigField "String", "APP_KEY", "${RELEASE_APP_KEY}"
            buildConfigField "String", "APP_SECRET", "${RELEASE_APP_SECRET}"
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable false

            loadSigningConfigs()

            //签名
            signingConfig signingConfigs.release

            applicationVariants.all { variant ->
                variant.outputs.all { output ->
                    def outputFile = output.outputFile
                    if (outputFile != null && outputFile.name.endsWith('.apk')) {
                        def type = ""
                        if (variant.buildType.name == 'debug') {
                            type = "_debug"
                        } else if (variant.buildType.name == 'beta') {
                            type = "_beta"
                        }
                        def fileName = "HK_HASE_EDC(A)_V${defaultConfig.versionName}${type}.apk"
                        //outputFileName  = new File(outputFile.parent, fileName)
                        output.outputFileName = fileName
                    }
                }
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    productFlavors {
        standard {
            manifestPlaceholders = [APP_NAME: "@string/app_name"]
        }
        //        autoTest {
        //            applicationId "com.pax.edc.test"
        //            manifestPlaceholders = [APP_NAME :"@string/app_name_test"]
        //        }
    }
}

repositories {
    flatDir {
        dirs '../sdkwrapper/libs', 'libs', '../appstore/libs'
    }
}

dependencies {
    implementation 'androidx.legacy:legacy-support-v4:1.0.0-alpha1'
    api fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'cn.bingoogolapple:bga-banner:2.1.7'
    implementation 'com.github.bumptech.glide:glide:3.7.0'
    implementation 'org.greenrobot:eventbus:3.0.0'
    implementation 'com.j256.ormlite:ormlite-core:5.0'
    implementation 'com.j256.ormlite:ormlite-android:5.0'
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'
    implementation 'com.alibaba:fastjson:1.2.33'
    implementation project(':sdkwrapper')
    implementation project(':eemv')
    implementation project(':glwrapper')
    implementation project(':expandablerecyclerview')
    implementation project(':opensdk')
    implementation project(':appstore')
    implementation project(':ecrsdk')
    implementation project(':commonlib')
    implementation 'com.github.tbruyelle:rxpermissions:0.10.2'
    implementation 'com.google.code.gson:gson:2.8.7'
    debugImplementation('io.github.skyhacker2:sqliteonweb:1.0.2') {
        exclude module: 'gson'
    }
    releaseImplementation 'io.github.skyhacker2:sqliteonweb-no-op:1.0.2'
    implementation project(':amex')
    testImplementation 'junit:junit:4.12'
}
