import java.io.FileInputStream
import java.util.Properties

plugins {
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.jetbrainsKotlinAndroid)
}

android {
    namespace = "com.pax.sbi.keycheck"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.pax.sbi.keycheck"
        minSdk = 29
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"
        resourceConfigurations.add("en")
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    signingConfigs {
        create("release") {
            val keystorePropertiesFile = file("../signing.properties")
            val keystoreProperties = Properties()
            if (keystorePropertiesFile.exists()) {
                keystoreProperties.load(FileInputStream(keystorePropertiesFile))
                if (keystoreProperties.containsKey("RELEASE_STORE_FILE") && keystoreProperties.containsKey(
                        "RELEASE_STORE_PASSWORD"
                    ) && keystoreProperties.containsKey("RELEASE_KEY_ALIAS") && keystoreProperties.containsKey(
                        "RELEASE_KEY_PASSWORD"
                    )
                ) {
                    keyAlias = keystoreProperties["RELEASE_KEY_ALIAS"] as String
                    keyPassword = keystoreProperties["RELEASE_KEY_PASSWORD"] as String
                    storeFile = file(keystoreProperties["RELEASE_STORE_FILE"] as String)
                    storePassword = keystoreProperties["RELEASE_STORE_PASSWORD"] as String
                }
            }
        }
    }


    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs["release"]
        }

        applicationVariants.all {
            outputs.filterIsInstance<com.android.build.gradle.internal.api.ApkVariantOutputImpl>()
                .forEach {
                    it.outputFileName = "KeyCheck.apk"
                }
        }

    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }

    sourceSets{
        getByName("main"){
            jniLibs.srcDirs("src/main/jniLibs")
        }
    }
}

dependencies {
    implementation(project(":pax-neptunelite"))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.runtime.android)
    implementation(libs.androidx.activity.ktx)
}