/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-7-4
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.sdk;

import android.graphics.Bitmap;
import com.pax.dal.IPrinter;
import com.pax.dal.entity.EFontTypeAscii;
import com.pax.dal.entity.EFontTypeExtCode;
import com.pax.dal.exceptions.PrinterDevException;

/**
 * The type Demo printer.
 */
class DemoPrinter implements IPrinter {

    /**
     * Instantiates a new Demo printer.
     */
    DemoPrinter() {
        //do nothing
    }

    @Override
    public void init() throws PrinterDevException {
        //do nothing
    }

    @Override
    public void fontSet(EFontTypeAscii eFontTypeAscii, EFontTypeExtCode eFontTypeExtCode) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void spaceSet(byte b, byte b1) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void step(int i) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void printStr(String s, String s1) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void printBitmap(Bitmap bitmap) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void print(Bitmap bitmap, IPinterListener iPinterListener) {
        //do nothing
    }

    @Override
    public void print(Bitmap bitmap, int i, IPinterListener iPinterListener) {

    }

    @Override
    public void setFontPath(String s) throws PrinterDevException {

    }

    @Override
    public void printBitmapWithMonoThreshold(Bitmap bitmap, int i) throws PrinterDevException {
        //do nothing
    }

    @Override
    public int start() throws PrinterDevException {
        return 0;
    }

    @Override
    public int getStatus() throws PrinterDevException {
        return 0;
    }

    @Override
    public void leftIndent(int i) throws PrinterDevException {
        //do nothing
    }

    @Override
    public int getDotLine() throws PrinterDevException {
        return 0;
    }

    @Override
    public void setGray(int i) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void doubleWidth(boolean b, boolean b1) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void doubleHeight(boolean b, boolean b1) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void invert(boolean b) throws PrinterDevException {
        //do nothing
    }

    @Override
    public void cutPaper(int i) throws PrinterDevException {
        //do nothing
    }

    @Override
    public int getCutMode() throws PrinterDevException {
        return -1;
    }
}
