/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.sdk;

import android.content.Context;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.IBinder;
import android.os.SystemClock;

import com.pax.commonlib.utils.ThreadPoolManager;
import com.pax.dal.ISys;
import com.pax.dal.entity.ASCaller;
import com.pax.dal.entity.BaseInfo;
import com.pax.dal.entity.EBeepMode;
import com.pax.dal.entity.ENavigationKey;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.dal.entity.ETouchMode;
import com.pax.dal.entity.IAppSwitchListener;
import com.pax.dal.entity.LanParam;
import com.pax.dal.entity.NtpServerParam;
import com.pax.dal.entity.PosMenu;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static android.content.Context.AUDIO_SERVICE;

class DemoSys implements ISys {

    private Context context;
    private Map<ETermInfoKey, String> infoKeyStringMap = new HashMap<>();

    DemoSys(Context context) {
        this.context = context;
        infoKeyStringMap.put(ETermInfoKey.SN, "12345678");
    }

    @Override
    public boolean beep(EBeepMode eBeepMode, final int i) {
        ThreadPoolManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                AudioManager am = (AudioManager) context.getSystemService(AUDIO_SERVICE);
                am.playSoundEffect(AudioManager.FX_KEYPRESS_STANDARD);
                SystemClock.sleep(i);
            }
        });
        return true;
    }

    @Override
    public int beep(String s) {
        return 0;
    }

    @Override
    public Map<ETermInfoKey, String> getTermInfo() {
        return infoKeyStringMap;
    }

    @Override
    public byte[] getRandom(int i) {
        return new byte[0];
    }

    @Override
    public String getDevInterfaceVer() {
        return null;
    }

    @Override
    public boolean checkPermission(String s, String s1) {
        return false;
    }

    @Override
    public void showNavigationBar(boolean b) {
        //do nothing
    }

    @Override
    public void enableNavigationBar(boolean b) {
        //do nothing
    }

    @Override
    public void enableNavigationKey(ENavigationKey eNavigationKey, boolean b) {
        //do nothing
    }

    @Override
    public boolean isNavigationBarVisible() {
        return true;
    }

    @Override
    public boolean isNavigationBarEnabled() {
        return true;
    }

    @Override
    public boolean isNavigationKeyEnabled(ENavigationKey eNavigationKey) {
        return false;
    }

    @Override
    public void showStatusBar(boolean b) {
        //do nothing
    }

    @Override
    public void enableStatusBar(boolean b) {
        //do nothing
    }

    @Override
    public boolean isStatusBarEnabled() {
        return true;
    }

    @Override
    public boolean isStatusBarVisible() {
        return true;
    }

    @Override
    public void resetStatusBar() {
        //do nothing
    }

    @Override
    public void enablePowerKey(boolean b) {
        //do nothing
    }

    @Override
    public boolean isPowerKeyEnabled() {
        return true;
    }

    @Override
    public void setSettingsNeedPassword(boolean b) {
        //do nothing
    }

    @Override
    public void reboot() {
        //do nothing
    }

    @Override
    public void shutdown() {
        //do nothing
    }

    @Override
    public void switchTouchMode(ETouchMode eTouchMode) {
        //do nothing
    }

    @Override
    public String getDate() {
        return null;
    }

    @Override
    public void setDate(String s) {
        //do nothing
    }

    @Override
    public void writeCSN(String s) {
        //do nothing
    }

    @Override
    public void setScreenBrightness(int i) {

    }

    @Override
    public boolean switchPrintService(Context context, String s, String s1, boolean b) {
        return false;
    }

    @Override
    public void disablePosMenu(Map<PosMenu, Boolean> map) {

    }

    @Override
    public void setScreenOffTime(int i) throws Exception {

    }

    @Override
    public int getScreenOffTime() throws Exception {
        return 0;
    }

    @Override
    public void setScreenSaverTime(int i) throws Exception {

    }

    @Override
    public void setUsbMode(int i) throws Exception {

    }

    @Override
    public void enableUsbPermissionDialog(boolean b) {

    }

    @Override
    public boolean setWifiStaticIp(String s, String s1, int i, String s2, String s3, boolean b) {
        return false;
    }

    @Override
    public void addService(String s, IBinder iBinder) {

    }

    @Override
    public void enableAutoTimeZone(boolean b) {

    }

    @Override
    public boolean isAutoTimeZone() {
        return false;
    }

    @Override
    public void enableAutoTime(boolean b) {

    }

    @Override
    public boolean isAutoTime() {
        return false;
    }

    @Override
    public void setScreenSaver(String s, String s1) {

    }

    @Override
    public void setSettingsPassword(byte[] bytes, String s, ASCaller asCaller) {

    }

    @Override
    public String getScreenSaver() throws Exception {
        return null;
    }

    @Override
    public void enableScreenSaver(boolean b) throws Exception {

    }

    @Override
    public void setScreenSaverActivateType(int i) throws Exception {

    }

    @Override
    public void enableShutdownConfirm(boolean b) throws Exception {

    }

    @Override
    public int getUsbMode() throws Exception {
        return 0;
    }

    @Override
    public void enableVolumeKey(boolean b) throws Exception {

    }

    @Override
    public boolean isVolumeKeyEnable() throws Exception {
        return false;
    }

    @Override
    public void enableSystemOTA(boolean b) throws Exception {

    }

    @Override
    public boolean isSystemOTAEnable() throws Exception {
        return false;
    }

    @Override
    public void setScanResultMode(int i) throws Exception {

    }

    @Override
    public void setSettingsNeedPassword(byte[] bytes, boolean b, ASCaller asCaller) throws Exception {

    }

    @Override
    public void removeRecentTasks(List<String> list) throws Exception {

    }

    @Override
    public List<String> getAppsWhitelist(byte[] bytes, ASCaller asCaller) throws Exception {
        return null;
    }

    @Override
    public void setAppsWhitelist(byte[] bytes, byte[] bytes1, ASCaller asCaller) throws Exception {

    }

    @Override
    public boolean verifySign(int i, String s) throws Exception {
        return false;
    }

    @Override
    public boolean enableWiFiDHCP() throws Exception {
        return false;
    }

    @Override
    public void setLauncher(String s, String s1, boolean b) throws Exception {

    }

    @Override
    public void setShortcutAction(String s) throws Exception {

    }

    @Override
    public String getCustomerResVer() throws Exception {
        return null;
    }

    @Override
    public void enableLocation(boolean b) throws Exception {

    }

    @Override
    public void resetNetworkSettings() throws Exception {

    }

    @Override
    public void enableApplication(String s, boolean b) throws Exception {

    }

    @Override
    public void setApplicationNeedPassword(String s, String s1) throws Exception {

    }

    @Override
    public void enableAuthDownload(boolean b) throws Exception {

    }

    @Override
    public String getInterceptorVersion() throws Exception {
        return null;
    }

    @Override
    public void setBootAnimation(String s) throws Exception {

    }

    @Override
    public void setBootLogo(String s) throws Exception {

    }

    @Override
    public void enableShortPressPowerKey(boolean b) throws Exception {

    }

    @Override
    public void enableEthernetTether(boolean b) throws Exception {

    }

    @Override
    public boolean set24Hour(boolean b) {
        return false;
    }

    @Override
    public void enableBaseUsb(boolean b) throws Exception {

    }

    @Override
    public void writeCustomerResConfig(String s) throws Exception {

    }

    @Override
    public boolean turnOnWiFiHotspot(String s, String s1, int i) {
        return false;
    }

    @Override
    public boolean turnOffWiFiHotspot() {
        return false;
    }

    @Override
    public int getWiFiHotspotStatus() {
        return 0;
    }

    @Override
    public void setChargeLimit(boolean b) throws Exception {

    }

    @Override
    public boolean setNTPServerParam(NtpServerParam ntpServerParam) throws Exception {
        return false;
    }

    @Override
    public NtpServerParam getNTPServerParam() throws Exception {
        return null;
    }

    @Override
    public void enableBatterySaverPrompt(boolean b) throws Exception {

    }

    @Override
    public void goToSleep() throws Exception {

    }

    @Override
    public boolean setLocationMode(int i) throws Exception {
        return false;
    }

    @Override
    public Bundle getSecurityInfo() throws Exception {
        return null;
    }

    @Override
    public boolean turnOnWiFiHotspot(String s, String s1, int i, int i1) throws Exception {
        return false;
    }

    @Override
    public int getScreenBrightness() throws Exception {
        return 0;
    }

    @Override
    public void enableMassStorage(boolean b) throws Exception {

    }

    @Override
    public boolean setEcmStaticIP(String s, int i) throws Exception {
        return false;
    }

    @Override
    public String getEcmStaticIP() throws Exception {
        return null;
    }

    @Override
    public void lockTerminal(byte[] bytes, ASCaller asCaller) throws Exception {

    }

    @Override
    public void unlockTerminal(byte[] bytes, ASCaller asCaller) throws Exception {

    }

    @Override
    public boolean getLockTerminalStatus(byte[] bytes, ASCaller asCaller) throws Exception {
        return false;
    }

    @Override
    public boolean installWifiCertificate(byte[] bytes, String s, boolean b, String s1) throws Exception {
        return false;
    }

    @Override
    public void setRebootTime(String s) throws Exception {

    }

    @Override
    public boolean isAutomaticRotation() throws Exception {
        return false;
    }

    @Override
    public boolean setAutomaticRotation(boolean b) throws Exception {
        return false;
    }

    @Override
    public void setRebootTimeEnable(boolean b) throws Exception {

    }

    @Override
    public void enableScreenshot(boolean b) throws Exception {

    }

    @Override
    public void updateSystemTimeZoneData(String s) throws Exception {

    }

    @Override
    public void showCustomizedPowerOffUI(boolean b) throws Exception {

    }

    @Override
    public String getRebootTime() throws Exception {
        return null;
    }

    @Override
    public void setInfoCollect(boolean b) throws Exception {

    }

    @Override
    public boolean isShortPressPowerKeyDisabled() throws Exception {
        return false;
    }

    @Override
    public void setLoadRecentTasks(boolean b) throws Exception {

    }

    @Override
    public void setFontScale(int i) throws Exception {

    }

    @Override
    public boolean isAutomaticBrightness() throws Exception {
        return false;
    }

    @Override
    public void setBrightnessMode(boolean b) throws Exception {

    }

    @Override
    public String getDefaultLauncherPackageName() throws Exception {
        return null;
    }

    @Override
    public String getDefaultLauncherActivityName() throws Exception {
        return null;
    }

    @Override
    public void setDefaultLauncher(boolean b) throws Exception {

    }

    @Override
    public void beep() {

    }

    @Override
    public void disableEmergency(boolean b) throws Exception {

    }

    @Override
    public void disableScreenshot(boolean b) throws Exception {

    }

    @Override
    public void setAppSwitchListener(boolean b, IAppSwitchListener iAppSwitchListener) throws Exception {

    }

    @Override
    public void setDisplaySize(int i) throws Exception {

    }

    @Override
    public boolean setChargerScreenDisabled(boolean b) throws Exception {
        return false;
    }

    @Override
    public void reset() throws Exception {

    }

    @Override
    public void enableBatteryLevelShow(boolean b) throws Exception {

    }

    @Override
    public boolean setCurrentApn(String s) throws Exception {
        return false;
    }

    @Override
    public void setAppPowerSaveConfigWithType(String s, boolean b) throws Exception {

    }

    @Override
    public void disableGlobalAirPlane(boolean b) throws Exception {

    }

    @Override
    public void disableGlobalSilent(boolean b) throws Exception {

    }

    @Override
    public void setBatteryManagerEnable(boolean b) throws Exception {

    }

    @Override
    public void setLongPowerKeyDisable(boolean b) throws Exception {

    }

    @Override
    public void setTetheringIp(int i, String s) throws Exception {

    }

    @Override
    public String getTetheringIp(int i) throws Exception {
        return "";
    }

    @Override
    public void setSecScreenBrightness(int i) throws Exception {

    }

    @Override
    public int getSecScreenBrightness() throws Exception {
        return 0;
    }

    @Override
    public int getTpWakeupMode() throws Exception {
        return 0;
    }

    @Override
    public int setTpWakeupMode(int i) throws Exception {
        return 0;
    }

    @Override
    public boolean getShowBatteryLevel() throws Exception {
        return false;
    }

    @Override
    public int updateAndLaunchApp(String s) {
        return 0;
    }

    @Override
    public int updateAndLaunchAppBackground(String s) {
        return 0;
    }

    @Override
    public void setRebootMode(byte b) throws Exception {

    }

    @Override
    public boolean getRebootTimeEnabled() throws Exception {
        return false;
    }

    @Override
    public void setUsbVirtualEthConfig(String s, LanParam lanParam) throws Exception {

    }

    @Override
    public LanParam getUsbVirtualEthConfig(String s) throws Exception {
        return null;
    }

    @Override
    public void setUsbHostDisable(boolean b) throws Exception {

    }

    @Override
    public boolean isUsbHostDisabled() throws Exception {
        return false;
    }

    @Override
    public boolean setSmartSavingModeWhenCharging(boolean b) throws Exception {
        return false;
    }

    @Override
    public void showCarrierName(boolean b) throws Exception {

    }

    @Override
    public void setPowerSaveMode(int i) throws Exception {

    }

    @Override
    public void setWifiWakeupEnabled(boolean b) throws Exception {

    }

    @Override
    public int getScreenRotation() throws Exception {
        return 0;
    }

    @Override
    public boolean setScreenRotation(int i) throws Exception {
        return false;
    }

    @Override
    public void updateBootAnimation(String s) throws Exception {

    }

    @Override
    public void updateBootLogo(String s) throws Exception {

    }

    @Override
    public void updateSettingsPasswordHashValue(byte[] bytes, String s, ASCaller asCaller) throws Exception {

    }

    @Override
    public void disableAnimations(boolean b) throws Exception {

    }

    @Override
    public int getDeviceTamperInfo() throws Exception {
        return 0;
    }

    @Override
    public void allowAccessContactsBtPairing(boolean b) throws Exception {

    }

    @Override
    public void allowBtOpenAirplane(boolean b) throws Exception {

    }

    @Override
    public void setDefaultDns(String s, String s1) throws Exception {

    }

    @Override
    public void setShowMoreUSBModesDisable(boolean b) throws Exception {

    }

    @Override
    public void setDropUSBMenuPasswordDisable(boolean b) throws Exception {

    }

    @Override
    public void enableFixedSpeed(int i) throws Exception {

    }

    @Override
    public void disableFixedSpeed() throws Exception {

    }

    @Override
    public void setGpsMode(int i) throws Exception {

    }

    @Override
    public void setMobilePolicyWarningBytes(long l) {

    }

    @Override
    public String getPaxDeviceModel(int i) throws Exception {
        return "";
    }

    @Override
    public int updateAppToSDCard(String s) {
        return 0;
    }

    @Override
    public void setAgpsServerDomain(String s) throws Exception {

    }

    @Override
    public String getAgpsServerDomain() throws Exception {
        return "";
    }

    @Override
    public boolean clearDnsCache() throws Exception {
        return false;
    }

    @Override
    public boolean installCaCertificate(byte[] bytes) throws Exception {
        return false;
    }

    @Override
    public void setTpWakeupEnable(boolean b) throws Exception {

    }

    @Override
    public boolean getTpWakeupEnable() throws Exception {
        return false;
    }

    @Override
    public void setAutoAdaptionModeNotificationSilent(boolean b) throws Exception {

    }

    @Override
    public void setHighTextContrastEnabled(boolean b) throws Exception {

    }

    @Override
    public void setAccessibilityDisplayDaltonizerEnabled(boolean b) throws Exception {

    }

    @Override
    public void setAccessibilityDisplayInversionEnabled(boolean b) throws Exception {

    }

    @Override
    public boolean setEthernetSpeedLimit(int i) throws Exception {
        return false;
    }

    @Override
    public int getEthernetSpeedLimit() throws Exception {
        return 0;
    }

    @Override
    public void setPrivateDns(String s) throws Exception {

    }

    @Override
    public void setGuestKeyBoardType(int i) throws Exception {

    }

    @Override
    public void switchPrivateDNSMode(int i) throws Exception {

    }

    @Override
    public int updateTTSVoices(String s) throws Exception {
        return 0;
    }

    @Override
    public boolean enableKeyEvent() {
        return false;
    }

    @Override
    public boolean disableKeyEvent() {
        return false;
    }

    @Override
    public boolean enableADBAndMTP(boolean b) {
        return false;
    }

    @Override
    public byte[] getTermInfoExt() {
        return new byte[0];
    }

    @Override
    public int getAppLogs(String s, String s1, String s2) {
        return 0;
    }

    @Override
    public boolean switchSimCard(int i) {
        return false;
    }

    @Override
    public String readTUSN() {
        return "";
    }

    @Override
    public int getPedMode() {
        return 0;
    }

    @Override
    public BaseInfo getBaseInfo() {
        return null;
    }

    @Override
    public int installApp(String s) {
        return 0;
    }

    @Override
    public boolean isDebug() {
        return false;
    }

    @Override
    public int uninstallApp(String s) {
        return 0;
    }

    @Override
    public int updateFirmware(String s) {
        return 0;
    }

    @Override
    public boolean isOnBase() {
        return false;
    }

    @Override
    public void setTimeZone(String s) {
        //do nothing
    }

    @Override
    public String getSystemLanguage() {
        return null;
    }

    @Override
    public int setSystemLanguage(Locale locale) {
        return 0;
    }

    @Override
    public void ledControl(byte b, byte b1) {

    }

    @Override
    public void lightControl(byte b, byte b1) {

    }

    @Override
    public String getPN() {
        return null;
    }
}
