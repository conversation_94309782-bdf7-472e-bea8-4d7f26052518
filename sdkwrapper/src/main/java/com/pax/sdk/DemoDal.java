/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-7-4
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.sdk;

import android.content.Context;
import com.pax.dal.ICardReaderHelper;
import com.pax.dal.ICashDrawer;
import com.pax.dal.IDAL;
import com.pax.dal.IDalCommManager;
import com.pax.dal.IDeviceInfo;
import com.pax.dal.IFingerprintReader;
import com.pax.dal.IIDReader;
import com.pax.dal.IIDReaderEx;
import com.pax.dal.IIcc;
import com.pax.dal.IKeyBoard;
import com.pax.dal.IMag;
import com.pax.dal.IPed;
import com.pax.dal.IPedBg;
import com.pax.dal.IPedNp;
import com.pax.dal.IPedTrSys;
import com.pax.dal.IPhoneManager;
import com.pax.dal.IPicc;
import com.pax.dal.IPrinter;
import com.pax.dal.IPuk;
import com.pax.dal.IScanCodec;
import com.pax.dal.IScanner;
import com.pax.dal.IScannerHw;
import com.pax.dal.ISignPad;
import com.pax.dal.ISle4442;
import com.pax.dal.ISys;
import com.pax.dal.IWifiProbe;
import com.pax.dal.entity.EPedType;
import com.pax.dal.entity.EPiccType;
import com.pax.dal.entity.EScannerType;
import com.pax.dal.pedkeyisolation.IPedKeyIsolation;

/**
 * Demo Dal
 */
class DemoDal implements IDAL {
    private IMag mag;
    private IIcc icc;
    private IPicc picc;
    private IPed ped;
    private IPedKeyIsolation pedKeyIsolation;
    private ICardReaderHelper cardReaderHelper;
    private IScanner scanner;
    private ISignPad signPad;
    private IKeyBoard keyBoard;
    private IPrinter printer;
    private IDeviceInfo deviceInfo;
    private IPuk puk;
    private ISys sys;
    private IDalCommManager commManager;
    private IIDReader idReader;

    /**
     * 构造器
     *
     * @param context 上下文
     */
    DemoDal(Context context) {
        mag = new DemoMag();
        icc = new DemoIcc();
        picc = new DemoPicc();
        ped = new DemoPed();
        pedKeyIsolation = new DemoPedIsolation();
        cardReaderHelper = new DemoCardReaderHelper();
        scanner = new DemoScanner();
        signPad = new DemoSignPad();
        keyBoard = new DemoKeyBoard();
        printer = new DemoPrinter();
        deviceInfo = new DemoDeviceInfo();
        puk = new DemoPuk();
        sys = new DemoSys(context);
        commManager = new DemoCommManager();
        idReader = new DemoIDReader();
    }

    @Override
    public IMag getMag() {
        return mag;
    }

    @Override
    public IIcc getIcc() {
        return icc;
    }

    @Override
    public IPicc getPicc(EPiccType ePiccType) {
        return picc;
    }

    @Override
    public IPed getPed(EPedType ePedType) {
        return ped;
    }

    @Override
    public IPedTrSys getPedTrSys() {
        return null;
    }

    @Override
    public IPedNp getPedNp() {
        return null;
    }

    @Override
    public IPedBg getPedBg() {
        return null;
    }

    @Override
    public ICardReaderHelper getCardReaderHelper() {
        return cardReaderHelper;
    }

    @Override
    public IScanner getScanner(EScannerType eScannerType) {
        return scanner;
    }

    @Override
    public IScannerHw getScannerHw() {
        return null;
    }

    /**
     * Gets sle 4442.
     *
     * @return the sle 4442
     */
    @Deprecated
    public ISle4442 getSle4442() {
        return null;
    }

    /**
     * Gets i sle 4442.
     *
     * @return the i sle 4442
     */
    @Deprecated
    public ISle4442 getISle4442() {
        return null;
    }

    @Override
    public ISignPad getSignPad() {
        return signPad;
    }

    @Override
    public IKeyBoard getKeyBoard() {
        return keyBoard;
    }

    @Override
    public IPrinter getPrinter() {
        return printer;
    }

    @Override
    public IDeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    @Override
    public IPuk getPuk() {
        return puk;
    }

    @Override
    public ISys getSys() {
        return sys;
    }

    /**
     * Gets i puk.
     *
     * @return the i puk
     */
    @Deprecated
    public IPuk getIPuk() {
        return getPuk();
    }

    @Override
    public IDalCommManager getCommManager() {
        return commManager;
    }

    @Override
    public IIDReader getIDReader() {
        return idReader;
    }

    @Override
    public IPedKeyIsolation getPedKeyIsolation(EPedType var1) {
        return pedKeyIsolation;
    }

    @Override
    public ICashDrawer getCashDrawer() {
        return null;
    }

    @Override
    public IScanCodec getScanCodec() {
        return null;
    }

    @Override
    public IWifiProbe getWifiProbe() {
        return null;
    }

    @Override
    public IPhoneManager getPhoneManager() {
        return null;
    }

    @Override
    public IIDReaderEx getIDReaderEx() {
        return null;
    }

    @Override
    public IFingerprintReader getFingerprintReader() {
        return null;
    }
}
