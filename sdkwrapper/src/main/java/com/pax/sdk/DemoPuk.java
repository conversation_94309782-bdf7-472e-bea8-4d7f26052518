/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */
package com.pax.sdk;

import com.pax.dal.IPuk;
import com.pax.dal.entity.PukInfo;
import com.pax.dal.exceptions.PukDevException;

class DemoPuk implements IPuk {
    DemoPuk() {
        //do nothing
    }

    @Override
    public PukInfo readPuk(byte b) throws PukDevException {
        return null;
    }

    @Override
    public void writePuk(byte b, byte[] bytes) throws PukDevException {
        //do nothing
    }
}
