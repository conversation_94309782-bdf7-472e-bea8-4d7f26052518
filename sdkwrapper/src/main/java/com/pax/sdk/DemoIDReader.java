/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-8-14
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.sdk;

import com.pax.dal.IIDReader;

/**
 * The type Demo id reader.
 */
class DemoIDReader implements IIDReader {

    /**
     * start
     *
     * @param idReadListener idReadListener
     */
    @Override
    public void start(IDReadListener idReadListener) {
        //do nothing
    }
}
