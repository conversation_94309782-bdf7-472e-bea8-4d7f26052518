apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.compileSdkVersion
    buildToolsVersion rootProject.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        autoTest {

        }
        debug {

        }
        releaseEmi {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}
repositories {
    flatDir {
        dirs '../sdkwrapper/libs', 'libs'
    }
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':commonlib')
    api project(':sdkwrapper')
    api "androidx.appcompat:appcompat:$rootProject.androidAppcompat"
    testImplementation "junit:junit:$rootProject.junit"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.androidxExtJunit"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
}
