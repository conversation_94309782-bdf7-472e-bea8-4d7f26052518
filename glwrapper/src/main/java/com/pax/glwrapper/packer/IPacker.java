/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-5-23
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.glwrapper.packer;

import com.pax.gl.pack.IApdu;
import com.pax.gl.pack.IIso8583;
import com.pax.gl.pack.ITlv;

/**
 * packer interface
 */
public interface IPacker {
    /**
     * apdu
     *
     * @return IApdu apdu
     */
    IApdu getApdu();

    /**
     * Iso8583
     *
     * @return IIso8583 iso 8583
     */
    IIso8583 getIso8583();

    /**
     * Tlv
     *
     * @return ITlv tlv
     */
    ITlv getTlv();
}