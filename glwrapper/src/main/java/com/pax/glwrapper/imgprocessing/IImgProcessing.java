/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-5-23
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.glwrapper.imgprocessing;

import android.graphics.Bitmap;
import com.pax.gl.impl.IRgbToMonoAlgorithm;
import com.pax.glwrapper.page.IPage;

/**
 * The interface Img processing.
 */
public interface IImgProcessing {

    /**
     * Bitmap to jbig byte [ ].
     *
     * @param bitmap the bitmap
     * @param algo the algo
     * @return the byte [ ]
     */
    byte[] bitmapToJbig(Bitmap bitmap, IRgbToMonoAlgorithm algo);

    /**
     * Jbig to bitmap bitmap.
     *
     * @param jbig the jbig
     * @return the bitmap
     */
    Bitmap jbigToBitmap(byte[] jbig);

    /**
     * Bitmap to mono dots byte [ ].
     *
     * @param bitmap the bitmap
     * @param algo the algo
     * @return the byte [ ]
     */
    byte[] bitmapToMonoDots(Bitmap bitmap, IRgbToMonoAlgorithm algo);

    /**
     * Bitmap to mono bmp byte [ ].
     *
     * @param bitmap the bitmap
     * @param algo the algo
     * @return the byte [ ]
     */
    byte[] bitmapToMonoBmp(Bitmap bitmap, IRgbToMonoAlgorithm algo);

    /**
     * Scale bitmap.
     *
     * @param bitmap the bitmap
     * @param w the w
     * @param h the h
     * @return the bitmap
     */
    Bitmap scale(Bitmap bitmap, int w, int h);

    /**
     * Generate bar code bitmap.
     *
     * @param contents the contents
     * @param width the width
     * @param height the height
     * @param format the format
     * @return the bitmap
     */
    Bitmap generateBarCode(java.lang.String contents, int width, int height,
            com.google.zxing.BarcodeFormat format);

    /**
     * Create page page.
     *
     * @return the page
     */
    IPage createPage();

    /**
     * Page to bitmap bitmap.
     *
     * @param page the page
     * @param pageWidth the page width
     * @return the bitmap
     */
    Bitmap pageToBitmap(IPage page, int pageWidth);
}

/* Location:           D:\Android逆向助手_v2.2\PaxGL_V1.00.04_20170303.jar
 * Qualified Name:     com.pax.gl.imgprocessing.IImgProcessing
 * JD-Core Version:    0.6.0
 */