/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-5-23
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.glwrapper.comm;

import com.pax.gl.commhelper.IBtLeScanner;
import com.pax.gl.commhelper.IBtScanner;
import com.pax.gl.commhelper.IBtServer;
import com.pax.gl.commhelper.ICommBt;
import com.pax.gl.commhelper.ICommSslClient;
import com.pax.gl.commhelper.ICommTcpClient;
import com.pax.gl.commhelper.IHttpClient;
import com.pax.gl.commhelper.IHttpsClient;
import com.pax.gl.commhelper.ISslKeyStore;
import com.pax.gl.commhelper.ITcpServer;

/**
 * The interface Comm helper.
 */
public interface ICommHelper {
    /**
     * Gets bt scanner.
     *
     * @return the bt scanner
     */
    IBtScanner getBtScanner();

    /**
     * Gets bt le scanner.
     *
     * @return the bt le scanner
     */
    IBtLeScanner getBtLeScanner();

    /**
     * Create bt comm bt.
     *
     * @param identifier the identifier
     * @return the comm bt
     */
    ICommBt createBt(String identifier);

    /**
     * Create bt comm bt.
     *
     * @param identifier the identifier
     * @param useBle the use ble
     * @return the comm bt
     */
    ICommBt createBt(String identifier, boolean useBle);

    /**
     * Create ssl key store ssl key store.
     *
     * @return the ssl key store
     */
    ISslKeyStore createSslKeyStore();

    /**
     * Create ssl client comm ssl client.
     *
     * @param host the host
     * @param port the port
     * @param keystore the keystore
     * @return the comm ssl client
     */
    ICommSslClient createSslClient(String host, int port, ISslKeyStore keystore);

    /**
     * Create tcp client comm tcp client.
     *
     * @param host the host
     * @param port the port
     * @return the comm tcp client
     */
    ICommTcpClient createTcpClient(String host, int port);

    /**
     * Create http client http client.
     *
     * @return the http client
     */
    IHttpClient createHttpClient();

    /**
     * Create https client https client.
     *
     * @param keyStore the key store
     * @return the https client
     */
    IHttpsClient createHttpsClient(ISslKeyStore keyStore);

    /**
     * Create tcp server tcp server.
     *
     * @param port the port
     * @param maxTaskNum the max task num
     * @param listener the listener
     * @return the tcp server
     */
    ITcpServer createTcpServer(int port, int maxTaskNum, ITcpServer.IListener listener);

    /**
     * Create bt server bt server.
     *
     * @param maxTaskNum the max task num
     * @param listener the listener
     * @return the bt server
     */
    IBtServer createBtServer(int maxTaskNum, IBtServer.IListener listener);
}

/* Location:           D:\Android逆向助手_v2.2\PaxGL_V1.00.04_20170303.jar
 * Qualified Name:     com.pax.gl.comm.ICommHelper
 * JD-Core Version:    0.6.0
 */