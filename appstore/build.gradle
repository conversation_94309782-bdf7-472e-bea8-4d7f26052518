apply plugin: 'com.android.library'

android {
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}


dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
//    compile 'com.google.code.gson:gson:2.7'
//    compile 'org.slf4j:slf4j-android:1.6.1-RC1'
    implementation 'androidx.appcompat:appcompat:1.2.0'

    implementation ('com.whatspos.sdk:paxstore-3rd-app-android-sdk:8.7.0'){
        exclude group: 'androidx.core', module: 'core'
        exclude module: 'dom4j'
    }}
