apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.compileSdkVersion
    buildToolsVersion rootProject.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        autoTest {
        }
        debug {
        }
        releaseEmi {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
repositories {
    flatDir {
        dirs '../sdkwrapper/libs', 'libs'
    }
}
dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api project(':commonlib')
    api "org.slf4j:slf4j-android:$rootProject.slf4jAndroid"
    implementation('com.whatspos.sdk:paxstore-3rd-app-android-sdk:8.7.3') {
        exclude group: 'androidx.core'
        exclude module: 'dom4j'
    }
    api 'dom4j:dom4j:1.6.1'

    implementation 'com.whatspos.sdk:paxstore-3rd-app-android-sdk-ext:9.0.0'

    testImplementation "junit:junit:$rootProject.junit"
    testImplementation "org.mockito:mockito-all:$rootProject.mockito"
    testImplementation "org.hamcrest:hamcrest-all:$rootProject.hamcrest"
    testImplementation "androidx.arch.core:core-testing:$rootProject.coreTesting"
    androidTestImplementation "androidx.test:runner:$rootProject.runner"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    androidTestImplementation "junit:junit:$rootProject.junit"
}
