<manifest xmlns:android="http://schemas.android.com/apk/res/android"

          package="com.pax.appstore">

    <!-- 获取网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 读写本地外部存储的权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />


    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:supportsRtl="true">
        <service android:name=".DownloadParamService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.sdk.service.ACTION_TO_DOWNLOAD_PARAMS" />
                <category android:name="${applicationId}" />
            </intent-filter>
        </service>
    </application>

</manifest>
