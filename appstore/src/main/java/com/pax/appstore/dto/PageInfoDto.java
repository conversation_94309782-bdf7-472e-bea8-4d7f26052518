package com.pax.appstore.dto;

import java.util.List;

/**
 * The type Page info dto.
 */
public class PageInfoDto {
    private int totalCount;
    private int businessCode;
    private List<ParamDto> list;

    /**
     * Gets total count.
     *
     * @return the total count
     */
    public int getTotalCount() {
        return totalCount;
    }

    /**
     * Sets total count.
     *
     * @param totalCount the total count
     */
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * Gets business code.
     *
     * @return the business code
     */
    public int getBusinessCode() {
        return businessCode;
    }

    /**
     * Sets business code.
     *
     * @param businessCode the business code
     */
    public void setBusinessCode(int businessCode) {
        this.businessCode = businessCode;
    }

    /**
     * Gets list.
     *
     * @return the list
     */
    public List<ParamDto> getList() {
        return list;
    }

    /**
     * Sets list.
     *
     * @param list the list
     */
    public void setList(List<ParamDto> list) {
        this.list = list;
    }

    /**
     * toString
     *
     * @return String
     */
    @Override
    public String toString() {
        return "PageInfoDto{" +
                "totalCount=" + totalCount +
                ", businessCode=" + businessCode +
                ", list=" + list +
                '}';
    }
}