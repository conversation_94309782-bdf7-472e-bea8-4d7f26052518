/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-2-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.appstore;

import java.io.File;

/**
 * The type Abstract document.
 */
public abstract class AbstractDocument {
    private String filePath;

    /**
     * Instantiates a new Abstract document.
     *
     * @param filePath 文件路径
     */
    public AbstractDocument(String filePath) {
        this.filePath = DownloadManager.getInstance().getFilePath() + File.separator + filePath;
    }

    /**
     * Gets file path.
     *
     * @return the file path
     */
    protected String getFilePath() {
        return filePath;
    }

    /**
     * Is exit boolean.
     *
     * @return the boolean
     */
    boolean isExit() {
        return new File(filePath).exists();
    }

    /**
     * Delete.
     */
    public boolean delete() {
        return new File(filePath).delete();
    }

    /**
     * Gets object.
     *
     * @return the object
     */
    public abstract Object getObject();

    /**
     * parse param
     *
     * @return int int
     */
    public abstract int parse();

    /**
     * save param to db
     */
    public abstract void save();
}
