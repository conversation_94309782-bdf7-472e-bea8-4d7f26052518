/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         laiyi                   Create
 * ===========================================================================================
 */
package com.pax.appstore;

import android.content.Context;
import android.os.RemoteException;

import com.pax.commonlib.utils.LogUtils;
import com.pax.market.android.app.sdk.BaseApiService;
import com.pax.market.android.app.sdk.StoreSdk;
import com.pax.market.android.app.sdk.ext.StoreSdkExtention;
import com.pax.market.api.sdk.java.base.dto.SdkObject;
import com.pax.market.api.sdk.java.base.exception.NotInitException;
import com.pax.market.api.sdk.java.base.util.StringUtils;

import org.slf4j.helpers.Util;

import java.util.ArrayList;


public class DownloadManager {
    private static final String TAG = "PaxStore SDK";
    private ArrayList<DocumentBase> documentList = new ArrayList<>();
    private String appKey;
    private String appSecret;
    private String sn;
    private String filePath;
    private static DownloadManager instance;
    private UpdateAppListener updateAppListener;

    public static synchronized DownloadManager getInstance() {
        if (instance == null) {
            instance = new DownloadManager();
        }
        return instance;
    }

    private DownloadManager() {
    }

    public void addDocument(DocumentBase documentBase) {
        documentList.add(documentBase);
    }

    public boolean backupData() {
        boolean result = true;
        for (DocumentBase document : documentList) {
            if (!document.backup()) {
                LogUtils.e(TAG, "Parameter file backup error");
                result = false;
            }
        }
        return result;
    }

    public void restoreData() {
        for (DocumentBase document : documentList) {
            if (document.restore()) {
                LogUtils.e(TAG, "Parameter file restore error");
            }
        }
    }

    public boolean updateData(UpdateParamListener updateParamListener) {
        boolean result = backupData();
        if (!result) {
            return false;
        }

        try {
            for (DocumentBase document : documentList) {
                if (document.parse(updateParamListener) != 0) {
                    LogUtils.e(TAG, "Parameter file parse error");
                    result = false;
                }
            }

            if (result) {
                for (DocumentBase document : documentList) {
                    if (!document.save(updateParamListener)) {
                        LogUtils.e(TAG, "Parameter file save error");
                        result = false;
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.e(TAG, e);
            updateParamListener.Error("Failed to update parameters");
            result = false;
        } finally {
            for (DocumentBase document : documentList) {
                document.delete();
            }
        }

        if (!result) {
            restoreData();
        }

        return result;
    }

    public void clear() {
        documentList.clear();
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public boolean hasUpdateParam() {
        for (DocumentBase documentBase : documentList) {
            if (documentBase.isExit()) {
                return true;
            }
        }
        return false;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void init(Context context) {
        initPaxStoreSdk(context);
    }

    public boolean initTid(String tid) throws InitTidException {
        final SdkObject sdkObject;
        boolean result = false;
        try {
            sdkObject = StoreSdkExtention.getInstance().activateApi().initByTID(tid);
            if (sdkObject.getBusinessCode() == 0) {
                result = true;
                LogUtils.d(TAG, "PaxStore SDK init tid success");
            } else {
                LogUtils.d(TAG, "PaxStore SDK init tid fail Code:" +
                        sdkObject.getBusinessCode() + " Message:" + sdkObject.getMessage());
                throw new InitTidException(sdkObject.getBusinessCode(), sdkObject.getMessage());
            }
        } catch (NotInitException e) {
            LogUtils.e(TAG, "", e);
            throw new InitTidException(-1, "MAX STORE Not Init");
        }
        return result;
    }

    private void initPaxStoreSdk(Context context) {
        StoreSdk.getInstance().init(context, appKey, appSecret, new BaseApiService.Callback() {
            @Override
            public void initSuccess() {
                LogUtils.d(TAG, "PaxStore SDK init success");
                initInquirer();
            }

            @Override
            public void initFailed(RemoteException e) {
                LogUtils.e(TAG, "", e);
                LogUtils.d(TAG, "PaxStore SDK init fail");
            }
        });
    }

    //初始化应用更新回调
    private void initInquirer() {
        StoreSdk.getInstance().initInquirer(new StoreSdk.Inquirer() {
            @Override
            public boolean isReadyUpdate() {
                if (updateAppListener != null) {
                    return updateAppListener.isReadyUpdate();
                }
                return !isTrading();
            }
        });
    }

    //This is a sample of your business logic method
    public boolean isTrading() {
        return true;
    }

    public void setUpdateAppListener(UpdateAppListener updateAppListener) {
        this.updateAppListener = updateAppListener;
    }

    //应用更新判断的回调接口
    public interface UpdateAppListener {
        boolean isReadyUpdate();
    }

    public interface UpdateParamListener {
        void Error(String msg);
    }

    public static void openDownloadPage(Context context) {
        StoreSdk.getInstance().openDownloadListPage("com.pax.sbipay", context);
    }
}
