/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-2-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.appstore;

import android.util.Log;
import java.io.File;
import java.io.IOException;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

/**
 * The type Document base.
 */
public abstract class DocumentBase {
    private String filePath;

    /**
     * Instantiates a new Document base.
     *
     * @param filePath the file path
     */
    public DocumentBase(String filePath) {
        this.filePath = DownloadManager.getInstance().getFilePath() + File.separator + filePath;
    }

    /**
     * 构造器
     *
     * @return Document document
     */
    protected Document getDocument() {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db;
        try {
            db = dbf.newDocumentBuilder();
            return db.parse(new File(filePath));
        } catch (ParserConfigurationException | SAXException | IOException e) {
            Log.e("DocumentBase", e.getMessage());
        }

        return null;
    }

    /**
     * Is exit boolean.
     *
     * @return the boolean
     */
    public boolean isExit() {
        return new File(filePath).exists();
    }

    /**
     * 删除
     *
     * @return boolean boolean
     */
    public boolean delete() {
        return new File(filePath).delete();
    }

    /**
     * Parse int.
     *
     * @return the int
     */
    public abstract int parse();

    /**
     * Save.
     */
    public abstract void save();

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        DocumentBase that = (DocumentBase) o;

        return filePath != null ? filePath.equals(that.filePath) : that.filePath == null;
    }

    @Override
    public int hashCode() {
        return filePath != null ? filePath.hashCode() : 0;
    }
}
