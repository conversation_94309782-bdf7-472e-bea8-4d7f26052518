/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Linhb                   Create
 * ===========================================================================================
 */

package com.pax.eemv;

import android.util.Log;

import com.pax.commonlib.StatisticManager;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.convert.ConvertHelper;
import com.pax.dal.exceptions.PedDevException;
import com.pax.eemv.entity.AidParam;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.entity.CandList;
import com.pax.eemv.entity.Capk;
import com.pax.eemv.entity.InputParam;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.enums.EFlowType;
import com.pax.eemv.enums.EOnlineResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.eemv.utils.Converter;
import com.pax.eemv.utils.Tools;
import com.pax.jemv.clcommon.ACType;
import com.pax.jemv.clcommon.ByteArray;
import com.pax.jemv.clcommon.EMV_APPLIST;
import com.pax.jemv.clcommon.EMV_CAPK;
import com.pax.jemv.clcommon.RetCode;
import com.pax.jemv.device.DeviceManager;
import com.pax.jemv.emv.api.EMVApi;
import com.pax.jemv.emv.api.EMVCallback;
import com.pax.jemv.emv.model.EmvEXTMParam;
import com.pax.jemv.emv.model.EmvMCKParam;
import com.pax.jemv.emv.model.EmvParam;
import java.nio.ByteBuffer;
import java.util.Arrays;

public class EmvImpl extends EmvBase {
    private static final String TAG = "EmvImpl";
    public static boolean isTimeOut = false;
    private EMVCallback emvCallback;
    private final EmvTrans paxEmvTrans;
    private final EmvParam emvParam;
    private final EmvMCKParam mckParam;


    private class Callback implements EMVCallback.EmvCallbackListener {
        @Override
        public void emvWaitAppSel(int tryCnt, EMV_APPLIST[] list, int appNum) {
            CandList[] candLists = new CandList[list.length];
            int size = Math.min(list.length, appNum);
            for (int i = 0; i < size; ++i) {
                candLists[i] = Converter.toCandList(list[i]);
            }
            try {
                int idx = paxEmvTrans.waitAppSelect(tryCnt, candLists);
                if (idx >= 0) {
                    emvCallback.setCallBackResult(idx);
                } else {
                    emvCallback.setCallBackResult(RetCode.EMV_USER_CANCEL);
                }
            } catch (EmvException e) {
                LogUtils.w(TAG, "", e);
                emvCallback.setCallBackResult(e.getErrCode());
            }
        }

        @Override
        public void emvInputAmount(long[] amt) {
            Amount amount = paxEmvTrans.getAmount();
            if (amount != null) {
                amt[0] = Long.parseLong(amount.getAmount());
                if (amt.length > 1) {
                    if (amount.getCashBackAmt() == null || amount.getCashBackAmt().isEmpty()) {
                        amt[1] = 0;
                    } else {
                        amt[1] = Long.parseLong(amount.getCashBackAmt());
                    }
                }
            }
            emvCallback.setCallBackResult(RetCode.EMV_OK);
        }

        //called after verifing pin or entering pin time out
        @Override
        public void emvGetHolderPwd(int tryFlag, int remainCnt, byte[] pin) {
            if (pin == null) {
                LogUtils.i("log", "emvGetHolderPwd pin is null, tryFlag" + tryFlag + " remainCnt:" + remainCnt);
            } else {
                LogUtils.i("log", "emvGetHolderPwd pin is not null, tryFlag" + tryFlag + " remainCnt:" + remainCnt);
            }

            if (tryFlag == 1) {
                try {
                    paxEmvTrans.onShowOfflinPinError(pin);
                } catch (EmvException e) {
                    LogUtils.i("log", e.getErrMsg());
                }
            }

            int result;
            try {
                result = paxEmvTrans.cardHolderPwd(pin == null, remainCnt, pin);
            } catch (EmvException e) {
                LogUtils.e(TAG, e);
                result = e.getErrCode();
            }

            emvCallback.setCallBackResult(result);
        }

        @Override
        public void emvAdviceProc() {
            //do nothing
        }

        @Override
        public void emvVerifyPINOK() {
            //do nothing
        }

        @Override
        public int emvUnknowTLVData(short tag, ByteArray data) {
            LogUtils.i("EMV", "emvUnknowTLVData tag: " + Integer.toHexString(tag) + " data:" + data.data.length);
            switch (tag) {
                case 0x9A:
                    byte[] date = new byte[7];
                    DeviceManager.getInstance().getTime(date);
                    System.arraycopy(date, 1, data.data, 0, 3);
                    break;
                case (short) 0x9F1E:
                    byte[] sn = new byte[10];
                    DeviceManager.getInstance().readSN(sn);
                    System.arraycopy(sn, 0, data.data, 0, Math.min(data.data.length, sn.length));
                    break;
                case (short) 0x9F21:
                    byte[] time = new byte[7];
                    DeviceManager.getInstance().getTime(time);
                    System.arraycopy(time, 4, data.data, 0, 3);
                    break;
                case (short) 0x9F37:
                    byte[] random = new byte[4];
                    DeviceManager.getInstance().getRand(random, 4);
                    System.arraycopy(random, 0, data.data, 0, data.data.length);
                    break;
                case (short) 0xFF01:
                    Arrays.fill(data.data, (byte) 0x00);
                    break;
                default:
                    return RetCode.EMV_PARAM_ERR;
            }
            data.length = data.data.length;
            return RetCode.EMV_OK;
        }

        @Override
        public void certVerify() {
            emvCallback.setCallBackResult(RetCode.EMV_OK);
        }

        @Override
        public int emvSetParam() {
            return RetCode.EMV_OK;
        }

        @Override
        public int emvVerifyPINfailed(byte[] bytes) {
            return 0;
        }

        @Override
        public int cRFU2() {
            return 0;
        }
    }

    public EmvImpl() {
        super();
        emvParam = new EmvParam();
        mckParam = new EmvMCKParam();
        mckParam.extmParam = new EmvEXTMParam();

        paxEmvTrans = new EmvTrans();
    }

    @Override
    public void init() throws EmvException {
        super.init();
        emvCallback = EMVCallback.getInstance();
        emvCallback.setCallbackListener(new Callback());
        int ret = EMVCallback.EMVCoreInit();
        if (ret == RetCode.EMV_OK) {
            EMVCallback.EMVSetCallback();
            EMVCallback.EMVGetParameter(emvParam);
            EMVCallback.EMVGetMCKParam(mckParam);
            paramToConfig();
            return;
        }

        //support DPAS connect v2.0
        ret = EMVCallback.DPASCTCoreInit();
        if (ret == RetCode.EMV_OK) {
            return;
        }
        throw new EmvException(EEmvExceptions.EMV_ERR_FILE);
    }

    private void paramToConfig() {
        cfg.setCapability(Tools.bcd2Str(emvParam.capability));
        cfg.setCountryCode(Tools.bcd2Str(emvParam.countryCode));
        cfg.setExCapability(Tools.bcd2Str(emvParam.exCapability));
        cfg.setForceOnline(Tools.byte2Boolean(emvParam.forceOnline));
        cfg.setGetDataPIN(Tools.byte2Boolean(emvParam.getDataPIN));
        cfg.setMerchCateCode(Tools.bcd2Str(emvParam.merchCateCode));
        cfg.setReferCurrCode(Tools.bcd2Str(emvParam.referCurrCode));
        cfg.setReferCurrCon(emvParam.referCurrCon);
        cfg.setReferCurrExp(emvParam.referCurrExp);
        cfg.setSurportPSESel(Tools.byte2Boolean(emvParam.surportPSESel));
        cfg.setTermType(emvParam.terminalType);
        cfg.setTransCurrCode(Tools.bcd2Str(emvParam.transCurrCode));
        cfg.setTransCurrExp(emvParam.transCurrExp);
        cfg.setTransType(emvParam.transType);
        cfg.setTermId(Arrays.toString(emvParam.termId));
        cfg.setMerchId(Arrays.toString(emvParam.merchId));
        cfg.setMerchName(Arrays.toString(emvParam.merchName));

        cfg.setBypassPin(Tools.byte2Boolean(mckParam.ucBypassPin));
        cfg.setBatchCapture(mckParam.ucBatchCapture);

        cfg.setTermAIP(Tools.bcd2Str(mckParam.extmParam.aucTermAIP));
        cfg.setBypassAllFlag(Tools.byte2Boolean(mckParam.extmParam.ucBypassAllFlg));
        cfg.setUseTermAIPFlag(Tools.byte2Boolean(mckParam.extmParam.ucUseTermAIPFlg));
    }

    private void configToParam() {
        emvParam.capability = Tools.str2Bcd(cfg.getCapability());
        emvParam.countryCode = Tools.str2Bcd(cfg.getCountryCode());
        emvParam.exCapability = Tools.str2Bcd(cfg.getExCapability());
        emvParam.forceOnline = Tools.boolean2Byte(cfg.getForceOnline());
        emvParam.getDataPIN = Tools.boolean2Byte(cfg.getGetDataPIN());
        emvParam.merchCateCode = Tools.str2Bcd(cfg.getMerchCateCode());
        emvParam.referCurrCode = Tools.str2Bcd(cfg.getReferCurrCode());
        emvParam.referCurrCon = cfg.getReferCurrCon();
        emvParam.referCurrExp = cfg.getReferCurrExp();
        emvParam.surportPSESel = Tools.boolean2Byte(cfg.getSurportPSESel());
        emvParam.terminalType = cfg.getTermType();
        emvParam.transCurrCode = Tools.str2Bcd(cfg.getTransCurrCode());
        emvParam.transCurrExp = cfg.getTransCurrExp();
        emvParam.termId = cfg.getTermId().getBytes();
        emvParam.merchId = cfg.getMerchId().getBytes();
        emvParam.merchName = cfg.getMerchName().getBytes();

        mckParam.ucBypassPin = Tools.boolean2Byte(cfg.getBypassPin());
        mckParam.ucBatchCapture = cfg.getBatchCapture();

        mckParam.extmParam.aucTermAIP = Tools.str2Bcd(cfg.getTermAIP());
        mckParam.extmParam.ucBypassAllFlg = Tools.boolean2Byte(cfg.getBypassAllFlag());
        mckParam.extmParam.ucUseTermAIPFlg = Tools.boolean2Byte(cfg.getUseTermAIPFlag());
    }

    @Override
    public byte[] getTlvSub(int tag) {
        ByteArray byteArray = new ByteArray();
        if (EMVCallback.EMVGetTLVData((short) tag, byteArray) == RetCode.EMV_OK) {
            return Arrays.copyOfRange(byteArray.data, 0, byteArray.length);
        }
        return null;
    }

    @Override
    public void setTlvSub(int tag, byte[] value) throws EmvException {
        int ret = EMVCallback.EMVSetTLVData((short) tag, value, value.length);
        if (ret != EEmvExceptions.EMV_OK.getErrCodeFromBasement()) {
            throw new EmvException(ret);
        }
    }

    // Run callback
    // Parameter settings, loading aid,
    // select the application, application initialization,read application data, offline data authentication,
    // terminal risk management,cardholder authentication, terminal behavior analysis,
    // issuing bank data authentication, execution script
    @Override
    public CTransResult process(InputParam inputParam) throws EmvException {

        configToParam();
        //get the actual transtype form tag9C
        emvParam.transType = inputParam.getTag9CValue();

        EMVCallback.EMVSetParameter(emvParam);
        int ret = EMVCallback.EMVSetMCKParam(mckParam);
        if (ret != RetCode.EMV_OK) {
            throw new EmvException(ret);
        }

        EMVCallback.EMVSetPCIModeParam((byte) 1, "0,4,5,6,7,8,9,10,11,12".getBytes(), inputParam.getPciTimeout());
        EMVCallback.EMVSetExtendFunc(7, new byte[]{0x01}, 1);//区分返回的错误码

        for (AidParam i : aidParamList) {
            ret = EMVCallback.EMVAddApp(Converter.toEMVApp(i));
            if (ret != RetCode.EMV_OK) {
                throw new EmvException(ret);
            }
        }

        ret = EMVCallback.EMVAppSelect(0, Long.parseLong(inputParam.getTransTraceNo()));   //callback emvWaitAppSel
        if (ret != RetCode.EMV_OK) {
            if (ret == RetCode.ICC_CMD_ERR) {
                StatisticManager.getInstance().incFailedSwipes();
            }
            if (ret == RetCode.EMV_DATA_ERR || ret == RetCode.ICC_RESET_ERR || ret == RetCode.EMV_NO_APP
                    || ret == RetCode.ICC_CMD_ERR || ret == RetCode.EMV_RSP_ERR || ret == RetCode.ICC_RSP_6985) {
                throw new EmvException(EEmvExceptions.EMV_ERR_FALL_BACK);
            }
            throw new EmvException(ret);
        }

        //For Tag 9C setting
        setTlv(TagsTable.TRANS_TYPE, new byte[]{inputParam.getTag9CValue()});

        ret = EMVCallback.EMVReadAppData(); //callback emvInputAmount

        if (ret != RetCode.EMV_OK) {
            throw new EmvException(ret);
        }

        ByteArray pan = new ByteArray();
        EMVCallback.EMVGetTLVData((byte) 0x5A, pan);
        String filtPan = Tools.bcd2Str(pan.data, pan.length);
        int indexF = filtPan.indexOf('F');
        String clearPan = null;
        if (pan.length > 0 && pan.data != null) {
            clearPan = filtPan.substring(0, indexF != -1 ? indexF : filtPan.length());
            ret = paxEmvTrans.confirmCardNo(clearPan);
            if (ret != RetCode.EMV_OK) {
                throw new EmvException(ret);
            }
        }

        if (EmvImpl.isTimeOut) {
            return new CTransResult(ETransResult.ABORT_TERMINATED);
        }

        ret = paxEmvTrans.inputInfo();
        if (ret != RetCode.EMV_OK) {
            throw new EmvException(ret);
        }

        addCapkIntoEmvLib(); // ignore return value for some case which the card doesn't has the capk index

        ret = EMVCallback.EMVCardAuth();
        if (ret != RetCode.EMV_OK) {
            if (ret == RetCode.ICC_RSP_6985) {
                throw new EmvException(EEmvExceptions.EMV_ERR_FALL_BACK);
            }
            throw new EmvException(ret);
        }

        int var0 = 0;
        byte[] var1 = new byte[2];
        ret = EMVApi.EMVGetDebugInfo(var0, var1);
        LogUtils.e("EMVGetDebugInfo", Integer.toString(ret));

        //简易流程
        if (inputParam.getFlowType() == EFlowType.SIMPLE) {
            return new CTransResult(ETransResult.SIMPLE_FLOW_END);
        }

        String amount = inputParam.getAmount();
        if (0x09 == emvParam.transType) { //sale&cash 交易另做处理
            long saleAmount = Long.parseLong(inputParam.getAmount()) - Long.parseLong(inputParam.getCashBackAmount());
            amount = Tools.bytes2String(Tools.fillData(12, Tools.string2Bytes(Long.toString(saleAmount)), 12 - Long.toString(saleAmount).length(), (byte) 48));
        }

        ACType acType = new ACType();
        if (amount.length() > 10) {
            LogUtils.d(TAG, "process: " + inputParam.getAmount().length());
            EMVCallback.EMVSetAmount(ConvertHelper.getConvert().strToBcdPaddingRight(amount),
                    ConvertHelper.getConvert().strToBcdPaddingRight(inputParam.getCashBackAmount()));
        }

        ret = EMVCallback.EMVStartTrans(Long.parseLong(amount),
                Long.parseLong(inputParam.getCashBackAmount()), acType);

        if (ret != RetCode.EMV_OK) {
            if (ret == RetCode.ICC_RSP_6985) {
                throw new EmvException(EEmvExceptions.EMV_ERR_FALL_BACK);
            }
            throw new EmvException(ret);
        }

        if (acType.type == ACType.AC_TC && !inputParam.isForceOnline()) {
            return new CTransResult(ETransResult.OFFLINE_APPROVED);
        } else if (acType.type == ACType.AC_AAC) {
            return new CTransResult(ETransResult.OFFLINE_DENIED);
        }


        //EMI Bin Validation Process
        String expDateStr = null;
        byte[] expDate = getTlv(0x5F24);
        if (expDate != null && expDate.length > 0) {
            String temp = ConvertHelper.getConvert().bcdToStr(expDate);
            expDateStr = temp.substring(0, 4);

        }

        if (clearPan != null && expDateStr != null) {
            paxEmvTrans.performCheckCardBin(clearPan, expDateStr);
        }

        if (clearPan != null && expDateStr != null) {
            paxEmvTrans.performBinValidation(clearPan, expDateStr);
        }

        ETransResult result = onlineProc();
        if(result == ETransResult.ONLINE_APPROVED){
            result = onDccProcess();
        }
        LogUtils.d(TAG, "onlineProc result:  " + result.name());
        byte[] script = combine7172(getTlv(0x71), getTlv(0x72));

        /**
         * script cannot be null when invoking EMVCallback.EMVCompleteTrans, otherwise ret could be
         * RetCode.EMV_PARAM_ERR
         */

        if (script == null) {
            script = new byte[0];
        }
        /**
         * should ensure script.length will not throw Null Pointer Exception
         */
        ret = EMVCallback.EMVCompleteTrans(Converter.toOnlineResult(result), script, script.length, acType);
        LogUtils.d(TAG, "EMVCompleteTrans ret:  " + ret);
        if (ret != RetCode.EMV_OK) {
            if (result == ETransResult.ERR_CONNECT) {
                return new CTransResult(ETransResult.ERR_CONNECT);
            } else if (result == ETransResult.ERR_PACKET) {
                return new CTransResult(ETransResult.ERR_PACKET);
            } else if(result == ETransResult.TIMEOUT){
                throw new EmvException(EEmvExceptions.EMV_ERR_TIMEOUT);
            } else if (result != ETransResult.ONLINE_APPROVED && result != ETransResult.ONLINE_DENIED) {
                throw new EmvException(EEmvExceptions.EMV_ERR_ONLINE_TRANS_ABORT);
            }
            ByteArray scriptResult = new ByteArray();
            EMVCallback.EMVGetScriptResult(scriptResult);
            paxEmvTrans.onSaveIssuerScriptResults(scriptResult, ret);
            throw new EmvException(ret);
        }
        // for Rupay
        ByteArray scriptResult = new ByteArray();
        EMVCallback.EMVGetScriptResult(scriptResult);
        paxEmvTrans.onSaveIssuerScriptResults(scriptResult, ret);
        paxEmvTrans.onSaveIssuerScriptResults(scriptResult, ret);

        //For Rupay to update the final select App Name & App Label (tag is DF8108)
        byte[] tags = new byte[260];
        int[] length = new int[1];
        EMVCallback.EMVGetExtendFunc(3, 260, tags, length);
        paxEmvTrans.onUpdateRupayAppLabelAndAppName(tags);

        //add by richard 20181025, set the signature flag for EMV transaction.
        int isSig = EMVCallback.EMVGetParamFlag((byte) 0x01);
        paxEmvTrans.onSetSignatureFlag(isSig);
        paxEmvTrans.onSetOfflinePinResult();

        paxEmvTrans.onCheckCardStatusUpdate(getTlv(0x91));

        if (acType.type == ACType.AC_TC) {
            return new CTransResult(ETransResult.ONLINE_APPROVED);
        } else if (acType.type == ACType.AC_AAC) {
            return new CTransResult(ETransResult.ONLINE_CARD_DENIED);
        }

        ETransResult transResult = Tools.getEnum(ETransResult.class, ret - 1);
        if (transResult == null) {
            throw new EmvException(EEmvExceptions.EMV_ERR_UNKNOWN.getErrCodeFromBasement());
        }
        return new CTransResult(transResult);
    }

    private ETransResult onDccProcess() throws EmvException {
        EOnlineResult ret;
        try {
            ret = paxEmvTrans.onDccProcess();
        }catch (PedDevException e){
            throw new EmvException(e.getErrModule(), e.getErrCode(), e.getErrMsg());
        }
        if (ret == EOnlineResult.APPROVE) {
            return ETransResult.ONLINE_APPROVED;
        } else if (ret == EOnlineResult.ABORT) {
            return ETransResult.ABORT_TERMINATED;
        } else if (ret == EOnlineResult.FAILED) {
            return ETransResult.TIMEOUT;
        } else if (ret == EOnlineResult.ERR_SEND) {
            return ETransResult.ERR_SEND;
        }  else if (ret ==  EOnlineResult.ERR_CONNECT) {
            return ETransResult.ERR_CONNECT;
        } else if (ret == EOnlineResult.ERR_PACKET) {
            return ETransResult.ERR_PACKET;
        } else if (ret == EOnlineResult.REVERSAL_ERR) {
            throw new EmvException(EEmvExceptions.EMV_ERR_REVELSAL);
        } else {
            return ETransResult.ONLINE_DENIED;
        }
    }

    public static byte[] combine7172(byte[] f71, byte[] f72) {

        boolean f71Empty = (null == f71 || f71.length <= 0);
        boolean f72Empty = (null == f72 || f72.length <= 0);

        if (f71Empty && f72Empty) {
            return new byte[0];
        }

        if (f71Empty && !f72Empty) {
            return createTLVByTV((byte) 0x72, f72);
        }

        if (!f71Empty && f72Empty) {
            return createTLVByTV((byte) 0x71, f71);
        }

        return mergeByteArrays(createTLVByTV((byte) 0x71, f71), createTLVByTV((byte) 0x72, f72));
    }

    private static byte[] mergeByteArrays(byte[] byteArr1, byte[] byteArr2) {
        if (null == byteArr1 || byteArr1.length <= 0) {
            return byteArr2;
        }
        if (null == byteArr2 || byteArr2.length <= 0) {
            return byteArr1;
        }
        byte[] result = Arrays.copyOf(byteArr1, byteArr1.length + byteArr2.length);
        System.arraycopy(byteArr2, 0, result, byteArr1.length, byteArr2.length);
        return result;
    }

    private static byte[] createTLVByTV(byte tag, byte[] value) {
        if (null == value || value.length <= 0) {
            return new byte[0];
        }
        ByteBuffer bb = ByteBuffer.allocate(value.length + 3);
        bb.put(tag);
        if (value.length > 127) {//need two bytes to indicate length
            bb.put((byte) 0x81);
        }
        bb.put((byte) value.length);
        bb.put(value, 0, value.length);

        int len = bb.position();
        bb.position(0);

        byte[] tlv = new byte[len];
        bb.get(tlv, 0, len);

        return tlv;
    }


    @Override
    public void setListener(IEmvListener listener) {
        paxEmvTrans.setEmvListener(listener);
    }

    @Override
    public String getVersion() {
        ByteArray byteArray = new ByteArray();
        EMVCallback.EMVReadVerInfo(byteArray);
        return Arrays.toString(byteArray.data);
    }

    public void clear() throws EmvException {
        emvCallback.setCallbackListener(null);
    }

    private int addCapkIntoEmvLib() {
        int ret;
        ByteArray dataList = new ByteArray();
        ret = EMVCallback.EMVGetTLVData((short) 0x4F, dataList);
        if (ret != RetCode.EMV_OK) {
            ret = EMVCallback.EMVGetTLVData((short) 0x84, dataList);
        }
        if (ret != RetCode.EMV_OK) {
            return ret;
        }

        byte[] rid = new byte[5];
        System.arraycopy(dataList.data, 0, rid, 0, 5);
        ret = EMVCallback.EMVGetTLVData((short) 0x8F, dataList);
        if (ret != RetCode.EMV_OK) {
            return ret;
        }
        byte keyId = dataList.data[0];
        for (Capk capk : capkList) {
            if (Tools.bytes2String(capk.getRid()).equals(new String(rid)) && capk.getKeyID() == keyId) {
                EMV_CAPK emvCapk = Converter.toEMVCapk(capk);
                ret = EMVCallback.EMVAddCAPK(emvCapk);
            }
        }
        return ret;
    }

    private ETransResult onlineProc() throws EmvException {
        EOnlineResult ret;
        ret = paxEmvTrans.onlineProc();
        if (ret == EOnlineResult.APPROVE) {
            return ETransResult.ONLINE_APPROVED;
        } else if (ret == EOnlineResult.ABORT) {
            return ETransResult.ABORT_TERMINATED;
        } else if (ret == EOnlineResult.ERR_SEND) {
            return ETransResult.ERR_SEND;
        } else if (ret == EOnlineResult.REVERSAL_ERR) {
            throw new EmvException(EEmvExceptions.EMV_ERR_REVELSAL);
        } else {
            return ETransResult.ONLINE_DENIED;
        }
    }
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.EmvImpl
 * JD-Core Version:    0.6.0
 */