/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         Kim.L                   Create
 * ===========================================================================================
 */

package com.pax.eemv.enums;

public enum ETransResult {
    ONLINE_APPROVED,
    ONLINE_DENIED,
    OFFLINE_APPROVED,
    OFFLINE_DENIED,
    ONLINE_CARD_DENIED,
    ONLINE_CARD_ICC_CMD_ERR,
    ABORT_TERMINATED,
    ARQC,
    SIMPLE_FLOW_END,
    CLSS_OC_A,
    CLSS_OC_APPROVED,
    CLSS_OC_B,
    CLSS_OC_C,
    CLSS_OC_CONFIRM_CODE_VER,
    CLSS_OC_D,
    CLSS_OC_DECLINED,
    CLSS_OC_END_APPLICATION,
    CLSS_OC_NA,
    CLSS_OC_NO_CVM,
    CLSS_OC_OBTAIN_SIGNATURE,
    CLSS_OC_ONLINE_PIN,
    CLSS_OC_ONLINE_REQUEST,
    CLSS_OC_SELECT_NEXT,
    CLSS_OC_TRY_AGAIN,
    CLSS_OC_SEE_PHONE,
    CLSS_OC_TRY_ANOTHER_INTERFACE,
    USER_CANCEL,
    ERR_SEND,
    REVERSAl_ERR,
    TIMEOUT,
    ERR_CONNECT,
    NO_ORIGINAL_NUM,
    ERR_PACKET
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.enums.ETransResult
 * JD-Core Version:    0.6.0
 */