package com.pax.eemv.enums;

/**
 * The enum E trans result.
 */
public enum ETransResult {
    /**
     * Online approved e trans result.
     */
    ONLINE_APPROVED,
    /**
     * Online denied e trans result.
     */
    ONLINE_DENIED,
    /**
     * Offline approved e trans result.
     */
    OFFLINE_APPROVED,
    /**
     * Offline denied e trans result.
     */
    OFFLINE_DENIED,
    /**
     * Online card denied e trans result.
     */
    ONLINE_CARD_DENIED,

    /**
     * Abort terminated e trans result.
     */
    ABORT_TERMINATED,

    /**
     * Arqc e trans result.
     */
    ARQC,

    /**
     * Simple flow end e trans result.
     */
    SIMPLE_FLOW_END,

    /**
     * Clss oc a e trans result.
     */
    CLSS_OC_A,
    /**
     * Clss oc approved e trans result.
     */
    CLSS_OC_APPROVED,
    /**
     * Clss oc b e trans result.
     */
    CLSS_OC_B,
    /**
     * Clss oc c e trans result.
     */
    CLSS_OC_C,
    /**
     * Clss oc confirm code ver e trans result.
     */
    CLSS_OC_CONFIRM_CODE_VER,
    /**
     * Clss oc d e trans result.
     */
    CLSS_OC_D,
    /**
     * Clss oc declined e trans result.
     */
    CLSS_OC_DECLINED,
    /**
     * Clss oc end application e trans result.
     */
    CLSS_OC_END_APPLICATION,
    /**
     * Clss oc na e trans result.
     */
    CLSS_OC_NA,
    /**
     * Clss oc no cvm e trans result.
     */
    CLSS_OC_NO_CVM,
    /**
     * Clss oc obtain signature e trans result.
     */
    CLSS_OC_OBTAIN_SIGNATURE,
    /**
     * Clss oc online pin e trans result.
     */
    CLSS_OC_ONLINE_PIN,
    /**
     * Clss oc online request e trans result.
     */
    CLSS_OC_ONLINE_REQUEST,
    /**
     * Clss oc select next e trans result.
     */
    CLSS_OC_SELECT_NEXT,
    /**
     * Clss oc try again e trans result.
     */
    CLSS_OC_TRY_AGAIN,
    /**
     * Clss oc try another interface e trans result.
     */
    CLSS_OC_TRY_ANOTHER_INTERFACE,
    /**
     * Clss oc see phone e trans result.
     */
    CLSS_OC_SEE_PHONE,
    /**
     * Clss exp card e trans result.
     */
    CLSS_EXP_CARD,
    /**
     * User cancel e trans result.
     */
    USER_CANCEL,
    /**
     * Not hase card e trans result.
     */
    NOT_HASE_CARD,
    /**
     * Err card unsupported e trans result.
     */
    ERR_CARD_UNSUPPORTED,

    /**
     * Online recv fail e trans result.
     */
    ONLINE_RECV_FAIL,

    /**
     * Err time out e trans result.
     */
    ERR_TIME_OUT,
    /**
     * Err connect e trans result.
     */
    ERR_CONNECT,
    /**
     * Err send e trans result.
     */
    ERR_SEND,
    /**
     * Err pack e trans result.
     */
    ERR_PACK,
    /**
     * Err unpack e trans result.
     */
    ERR_UNPACK,
    /**
     * Err packet e trans result.
     */
    ERR_PACKET,
    /**
     * Err mac e trans result.
     */
    ERR_MAC,
    /**
     * Err proc code e trans result.
     */
    ERR_PROC_CODE,
    /**
     * Database update fail e trans result.
     */
    DATABASE_UPDATE_FAIL,
    /**
     * Application rejection e trans result.
     */
    APPLICATION_REJECTION,
    /**
     * Unable to go online e trans result.
     */
    UNABLE_TO_GO_ONLINE,
    /**
     * Trans not accept.
     */
    TRANS_NOT_ACCEPT
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.enums.ETransResult
 * JD-Core Version:    0.6.0
 */