package com.pax.eemv.enums;

/**
 * The enum E online result.
 */
public enum EOnlineResult {
    /**
     * Approve e online result.
     */
    APPROVE,
    /**
     * Failed e online result.
     */
    FAILED,
    /**
     * Refer e online result.
     */
    REFER,
    /**
     * Denial e online result.
     */
    DENIAL,
    /**
     * Abort e online result.
     */
    ABORT,
    /**
     * Recv fail e online result.
     */
    RECV_FAIL,
    /**
     * No card bin e online result.
     */
    NO_CARD_BIN,

    /**
     * Err time out e online result.
     */
    ERR_TIME_OUT,
    /**
     * Err connect e online result.
     */
    ERR_CONNECT,
    /**
     * Err send e online result.
     */
    ERR_SEND,
    /**
     * Err pack e online result.
     */
    ERR_PACK,
    /**
     * Err unpack e online result.
     */
    ERR_UNPACK,
    /**
     * Err packet e online result.
     */
    ERR_PACKET,
    /**
     * Err mac e online result.
     */
    ERR_MAC,
    /**
     * Err proc code e online result.
     */
    ERR_PROC_CODE,
    /**
     * Err contactless e online result.
     */
    ERR_CONTACTLESS,
    /**
     * Database update fail e online result.
     */
    DATABASE_UPDATE_FAIL,
    /**
     * Unable to go online e online result.
     */
    UNABLE_TO_GO_ONLINE
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.enums.EOnlineResult
 * JD-Core Version:    0.6.0
 */