package com.pax.eemv;

import com.pax.eemv.entity.Amounts;
import com.pax.eemv.entity.CandList;
import com.pax.eemv.enums.EOnlineResult;
import java.util.List;

/**
 * The interface Emv listener.
 */
public interface IEmvListener {
    /**
     * On get amounts amounts.
     *
     * @return the amounts
     */
    Amounts onGetAmounts();

    /**
     * On wait app select int.
     *
     * @param isFirstSelect the is first select
     * @param candList the cand list
     * @return the int
     */
    int onWaitAppSelect(boolean isFirstSelect, List<CandList> candList);

    /**
     * Is cup debit card boolean.
     *
     * @return the boolean
     */
    boolean checkCupAcq();

    /**
     * On confirm card no int.
     *
     * @param cardNo the card no
     * @return the int
     */
    int onConfirmCardNo(String cardNo);

    /**
     * On card holder pwd int.
     *
     * @param bOnlinePin the b online pin
     * @param leftTimes the left times
     * @param pinData the pin data
     * @return the int
     */
    int onCardHolderPwd(boolean bOnlinePin, int leftTimes, byte[] pinData);

    /**
     * On enquiry e online result.
     *
     * @return the e online result
     */
    EOnlineResult onEnquiry();

    /**
     * On online proc e online result.
     *
     * @return the e online result
     */
    EOnlineResult onOnlineProc();

    /**
     * On chk exception file boolean.
     *
     * @return the boolean
     */
    boolean onChkExceptionFile();

    /**
     * Sets dcc emv param.
     */
    void setDccEmvParam();
    boolean onSimpleProcessEnd();

    /**
     * Gets trans amount.
     *
     * @return the trans amount
     */
    String getTransAmount();

    /**
     * Find acq by pan string.
     *
     * @param cardNo the card no
     * @return the string
     */
    String findIssuerByPan(String cardNo);

    /**
     * On input ref no int.
     *
     * @return the int
     */
    int onInputRefNo();
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.IEmvListener
 * JD-Core Version:    0.6.0
 */