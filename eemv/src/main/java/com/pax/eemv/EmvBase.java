package com.pax.eemv;

import android.util.SparseArray;
import com.pax.eemv.entity.AidParam;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.entity.Capk;
import com.pax.eemv.entity.Config;
import com.pax.eemv.entity.InputParam;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import java.util.ArrayList;
import java.util.List;

/**
 * The type Emv base.
 */
public abstract class EmvBase implements IEmv {
    /**
     * The Capk list.
     */
    protected List<Capk> capkList;
    /**
     * The Aid param list.
     */
    protected List<AidParam> aidParamList;
    /**
     * The Cfg.
     */
    protected Config cfg = new Config();
    private SparseArray<byte[]> tags = new SparseArray<>();

    static {
        System.loadLibrary("F_DEVICE_LIB_PayDroid");
        System.loadLibrary("F_PUBLIC_LIB_PayDroid");
    }

    /**
     * constructor
     */
    protected EmvBase() {
        capkList = new ArrayList<>();
        aidParamList = new ArrayList<>();
    }

    /**
     * getEmv
     *
     * @return IEmv
     */
    @Override
    public IEmv getEmv() {
        return this;
    }

    /**
     * 初始化
     *
     * @throws EmvException EmvException
     */
    @Override
    public void init() throws EmvException {
        tags.clear();
    }

    /**
     * getTlv
     *
     * @param tag tag
     * @return byte[]
     */
    @Override
    public final byte[] getTlv(int tag) {
        if (tag == 0x71 || tag == 0x72) {
            return tags.get(tag);
        }
        return getTlvSub(tag);
    }

    /**
     * setTlv
     *
     * @param tag tag
     * @param value value
     * @throws EmvException EmvException
     */
    @Override
    public final void setTlv(int tag, byte[] value) throws EmvException {
        if (tag == 0x71 || tag == 0x72) {
            tags.put(tag, value);
            return;
        }
        setTlvSub(tag, value);
    }

    /**
     * setConfig
     *
     * @param emvCfg Config
     */
    @Override
    public void setConfig(Config emvCfg) {
        cfg = emvCfg;
    }

    @Override
    public Config getConfig() {
        return cfg;
    }

    /**
     * Run callback
     * Parameter settings, loading aid,
     * select the application, application initialization,read application data, offline data
     * authentication,
     * terminal risk management,cardholder authentication, terminal behavior analysis,
     * issuing bank data authentication, execution script
     *
     * @param inputParam InputParam
     * @return CTransResult
     * @throws EmvException EmvException
     */
    @Override
    public CTransResult process(InputParam inputParam) throws EmvException {
        throw new EmvException(EEmvExceptions.EMV_ERR_NO_KERNEL);
    }

    @Override
    public void setListener(IEmvListener listener) {
        //do nothing
    }

    /**
     * setAidParamList
     *
     * @param aidParamList aidParamList
     */
    @Override
    public void setAidParamList(List<AidParam> aidParamList) {
        this.aidParamList = aidParamList == null ? new ArrayList<AidParam>() : aidParamList;
    }

    /**
     * setCapkList
     *
     * @param capkList capkList
     */
    @Override
    public void setCapkList(List<Capk> capkList) {
        this.capkList = capkList == null ? new ArrayList<Capk>() : capkList;
    }

    @Override
    public String getVersion() {
        return "";
    }

    /**
     * Get tlv sub byte [ ].
     *
     * @param tag the tag
     * @return the byte [ ]
     */
    protected abstract byte[] getTlvSub(int tag);

    /**
     * Sets tlv sub.
     *
     * @param tag the tag
     * @param value the value
     * @throws EmvException the emv exception
     */
    protected abstract void setTlvSub(int tag, byte[] value) throws EmvException;
}