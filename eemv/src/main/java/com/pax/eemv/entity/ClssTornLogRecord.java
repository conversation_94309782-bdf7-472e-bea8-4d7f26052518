/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-6-15
 * Module Author: Kim.L
 * Description:
 *
 * ============================================================================
 */
package com.pax.eemv.entity;

import com.pax.eemv.utils.Tools;
import com.pax.jemv.clcommon.CLSS_TORN_LOG_RECORD;

/**
 * 简单Java类
 */
public class ClssTornLogRecord {

    private CLSS_TORN_LOG_RECORD clssTornLogRecord;

    /**
     * Instantiates a new Clss torn log record.
     */
    public ClssTornLogRecord() {
        clssTornLogRecord = new CLSS_TORN_LOG_RECORD();
    }

    /**
     * Instantiates a new Clss torn log record.
     *
     * @param record the record
     */
    public ClssTornLogRecord(CLSS_TORN_LOG_RECORD record) {
        clssTornLogRecord = record;
    }

    /**
     * Instantiates a new Clss torn log record.
     *
     * @param pan the pan
     * @param panSeqFlg the pan seq flg
     * @param panSeq the pan seq
     * @param tornData the torn data
     * @param tornDataLen the torn data len
     */
    public ClssTornLogRecord(String pan, boolean panSeqFlg, byte panSeq, byte[] tornData,
            int tornDataLen) {
        byte[] bcdPan = Tools.str2Bcd(pan);
        clssTornLogRecord = new CLSS_TORN_LOG_RECORD(bcdPan, (byte) bcdPan.length,
                Tools.boolean2Byte(panSeqFlg), panSeq, tornData, tornDataLen);
    }

    /**
     * Gets pan.
     *
     * @return the pan
     */
    public String getPan() {
        return Tools.bcd2Str(clssTornLogRecord.aucPAN, clssTornLogRecord.ucPANLen);
    }

    /**
     * Sets pan.
     *
     * @param pan the pan
     */
    public void setPan(String pan) {
        clssTornLogRecord.aucPAN = Tools.str2Bcd(pan);
        clssTornLogRecord.ucPANLen = (byte) clssTornLogRecord.aucPAN.length;
    }

    /**
     * Gets pan seq flg.
     *
     * @return the pan seq flg
     */
    public boolean getPanSeqFlg() {
        return Tools.byte2Boolean(clssTornLogRecord.ucPANSeqFlg);
    }

    /**
     * Sets pan seq flg.
     *
     * @param panSeqFlg the pan seq flg
     */
    public void setPanSeqFlg(boolean panSeqFlg) {
        clssTornLogRecord.ucPANSeqFlg = Tools.boolean2Byte(panSeqFlg);
    }

    /**
     * Gets pan seq.
     *
     * @return the pan seq
     */
    public byte getPanSeq() {
        return clssTornLogRecord.ucPANSeq;
    }

    /**
     * Sets pan seq.
     *
     * @param panSeq the pan seq
     */
    public void setPanSeq(byte panSeq) {
        clssTornLogRecord.ucPANSeq = panSeq;
    }

    /**
     * Get torn data byte [ ].
     *
     * @return the byte [ ]
     */
    public byte[] getTornData() {
        return clssTornLogRecord.aucTornData;
    }

    /**
     * Sets torn data.
     *
     * @param tornData the torn data
     */
    public void setTornData(byte[] tornData) {
        clssTornLogRecord.aucTornData = tornData;
    }

    /**
     * Gets torn data len.
     *
     * @return the torn data len
     */
    public int getTornDataLen() {
        return clssTornLogRecord.unTornDataLen;
    }

    /**
     * Sets torn data len.
     *
     * @param tornDataLen the torn data len
     */
    public void setTornDataLen(int tornDataLen) {
        clssTornLogRecord.unTornDataLen = tornDataLen;
    }
}
