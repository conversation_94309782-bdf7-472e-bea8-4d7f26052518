package com.pax.eemv.entity;

/**
 * The type Config.
 */
public class Config {
    private long referCurrCon;
    private String merchName;
    private String merchCateCode;
    private String merchId;
    private String termId;
    private byte termType;
    private String capability;
    private String exCapability;
    private byte transCurrExp;
    private byte referCurrExp;
    private String referCurrCode;
    private String countryCode;
    private String transCurrCode;
    private byte transType;
    private boolean forceOnline;
    private boolean getDataPIN;
    private boolean supportPSESel;
    private boolean useTermAIPFlag;
    private String termAIP;
    private boolean bypassAllFlag;
    private boolean bypassPin;
    private byte batchCapture;
    private boolean adviceFlag;
    private byte scriptMethod;
    private boolean forceAccept;
    private boolean noPinConfirmAmtFlg;
    private boolean isInputAmount;
    private byte[] acquirerId;

    //For AE
    private String unpredictableNumberRange;
    private boolean supportOptTrans;
    private String transCap;
    private boolean delayAuthFlag;

    /**
     * Instantiates a new Config.
     */
    public Config() {
        this.referCurrCon = 0L;
        this.termType = 0;
        this.transCurrExp = 0;
        this.referCurrExp = 0;
        this.transType = 0;
        this.forceOnline = false;
        this.getDataPIN = false;
        this.supportPSESel = false;
        this.useTermAIPFlag = false;
        this.bypassAllFlag = false;
        this.bypassPin = false;
        this.batchCapture = 0;
        this.adviceFlag = false;
        this.scriptMethod = 0;
        this.forceAccept = false;
        this.noPinConfirmAmtFlg = false;
        this.isInputAmount = false;
        this.acquirerId = new byte[6];
    }

    /**
     * Gets refer curr con.
     *
     * @return the refer curr con
     */
    public long getReferCurrCon() {
        return this.referCurrCon;
    }

    /**
     * Sets refer curr con.
     *
     * @param referCurrCon the refer curr con
     */
    public void setReferCurrCon(long referCurrCon) {
        this.referCurrCon = referCurrCon;
    }

    /**
     * Gets merch name.
     *
     * @return the merch name
     */
    public String getMerchName() {
        return this.merchName;
    }

    /**
     * Sets merch name.
     *
     * @param merchName the merch name
     */
    public void setMerchName(String merchName) {
        this.merchName = merchName;
    }

    /**
     * Gets merch cate code.
     *
     * @return the merch cate code
     */
    public String getMerchCateCode() {
        return this.merchCateCode;
    }

    /**
     * Sets merch cate code.
     *
     * @param merchCateCode the merch cate code
     */
    public void setMerchCateCode(String merchCateCode) {
        this.merchCateCode = merchCateCode;
    }

    /**
     * Gets merch id.
     *
     * @return the merch id
     */
    public String getMerchId() {
        return this.merchId;
    }

    /**
     * Sets merch id.
     *
     * @param merchId the merch id
     */
    public void setMerchId(String merchId) {
        this.merchId = merchId;
    }

    /**
     * Gets term id.
     *
     * @return the term id
     */
    public String getTermId() {
        return this.termId;
    }

    /**
     * Sets term id.
     *
     * @param termId the term id
     */
    public void setTermId(String termId) {
        this.termId = termId;
    }

    /**
     * Gets term type.
     *
     * @return the term type
     */
    public byte getTermType() {
        return this.termType;
    }

    /**
     * Sets term type.
     *
     * @param termType the term type
     */
    public void setTermType(byte termType) {
        this.termType = termType;
    }

    /**
     * Gets capability.
     *
     * @return the capability
     */
    public String getCapability() {
        return this.capability;
    }

    /**
     * Sets capability.
     *
     * @param capability the capability
     */
    public void setCapability(String capability) {
        this.capability = capability;
    }

    /**
     * Gets ex capability.
     *
     * @return the ex capability
     */
    public String getExCapability() {
        return this.exCapability;
    }

    /**
     * Sets ex capability.
     *
     * @param exCapability the ex capability
     */
    public void setExCapability(String exCapability) {
        this.exCapability = exCapability;
    }

    /**
     * Gets trans curr exp.
     *
     * @return the trans curr exp
     */
    public byte getTransCurrExp() {
        return this.transCurrExp;
    }

    /**
     * Sets trans curr exp.
     *
     * @param transCurrExp the trans curr exp
     */
    public void setTransCurrExp(byte transCurrExp) {
        this.transCurrExp = transCurrExp;
    }

    /**
     * Gets refer curr exp.
     *
     * @return the refer curr exp
     */
    public byte getReferCurrExp() {
        return this.referCurrExp;
    }

    /**
     * Sets refer curr exp.
     *
     * @param referCurrExp the refer curr exp
     */
    public void setReferCurrExp(byte referCurrExp) {
        this.referCurrExp = referCurrExp;
    }

    /**
     * Gets refer curr code.
     *
     * @return the refer curr code
     */
    public String getReferCurrCode() {
        return this.referCurrCode;
    }

    /**
     * Sets refer curr code.
     *
     * @param referCurrCode the refer curr code
     */
    public void setReferCurrCode(String referCurrCode) {
        this.referCurrCode = referCurrCode;
    }

    /**
     * Gets country code.
     *
     * @return the country code
     */
    public String getCountryCode() {
        return this.countryCode;
    }

    /**
     * Sets country code.
     *
     * @param countryCode the country code
     */
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    /**
     * Gets trans curr code.
     *
     * @return the trans curr code
     */
    public String getTransCurrCode() {
        return this.transCurrCode;
    }

    /**
     * Sets trans curr code.
     *
     * @param transCurrCode the trans curr code
     */
    public void setTransCurrCode(String transCurrCode) {
        this.transCurrCode = transCurrCode;
    }

    /**
     * Gets trans type.
     *
     * @return the trans type
     */
    public byte getTransType() {
        return this.transType;
    }

    /**
     * Sets trans type.
     *
     * @param transType the trans type
     */
    public void setTransType(byte transType) {
        this.transType = transType;
    }

    /**
     * Gets force online.
     *
     * @return the force online
     */
    public boolean getForceOnline() {
        return this.forceOnline;
    }

    /**
     * Sets force online.
     *
     * @param forceOnline the force online
     */
    public void setForceOnline(boolean forceOnline) {
        this.forceOnline = forceOnline;
    }

    /**
     * Gets get data pin.
     *
     * @return the get data pin
     */
    public boolean getGetDataPIN() {
        return this.getDataPIN;
    }

    /**
     * Sets get data pin.
     *
     * @param getDataPIN the get data pin
     */
    public void setGetDataPIN(boolean getDataPIN) {
        this.getDataPIN = getDataPIN;
    }

    /**
     * Gets surport pse sel.
     *
     * @return the surport pse sel
     */
    public boolean getSurportPSESel() {
        return this.supportPSESel;
    }

    /**
     * Sets surport pse sel.
     *
     * @param supportPSESel the support pse sel
     */
    public void setSurportPSESel(boolean supportPSESel) {
        this.supportPSESel = supportPSESel;
    }

    /**
     * Gets use term aip flag.
     *
     * @return the use term aip flag
     */
    public boolean getUseTermAIPFlag() {
        return this.useTermAIPFlag;
    }

    /**
     * Sets use term aip flag.
     *
     * @param useTermAIPFlag the use term aip flag
     */
    public void setUseTermAIPFlag(boolean useTermAIPFlag) {
        this.useTermAIPFlag = useTermAIPFlag;
    }

    /**
     * Gets term aip.
     *
     * @return the term aip
     */
    public String getTermAIP() {
        return this.termAIP;
    }

    /**
     * Sets term aip.
     *
     * @param termAIP the term aip
     */
    public void setTermAIP(String termAIP) {
        this.termAIP = termAIP;
    }

    /**
     * Gets bypass all flag.
     *
     * @return the bypass all flag
     */
    public boolean getBypassAllFlag() {
        return this.bypassAllFlag;
    }

    /**
     * Sets bypass all flag.
     *
     * @param bypassAllFlag the bypass all flag
     */
    public void setBypassAllFlag(boolean bypassAllFlag) {
        this.bypassAllFlag = bypassAllFlag;
    }

    /**
     * Gets bypass pin.
     *
     * @return the bypass pin
     */
    public boolean getBypassPin() {
        return this.bypassPin;
    }

    /**
     * Sets bypass pin.
     *
     * @param bypassPin the bypass pin
     */
    public void setBypassPin(boolean bypassPin) {
        this.bypassPin = bypassPin;
    }

    /**
     * Gets batch capture.
     *
     * @return the batch capture
     */
    public byte getBatchCapture() {
        return this.batchCapture;
    }

    /**
     * Sets batch capture.
     *
     * @param batchCapture the batch capture
     */
    public void setBatchCapture(byte batchCapture) {
        this.batchCapture = batchCapture;
    }

    /**
     * Gets advice flag.
     *
     * @return the advice flag
     */
    public boolean getAdviceFlag() {
        return this.adviceFlag;
    }

    /**
     * Sets advice flag.
     *
     * @param adviceFlag the advice flag
     */
    public void setAdviceFlag(boolean adviceFlag) {
        this.adviceFlag = adviceFlag;
    }

    /**
     * Gets script method.
     *
     * @return the script method
     */
    public byte getScriptMethod() {
        return this.scriptMethod;
    }

    /**
     * Sets script method.
     *
     * @param scriptMethod the script method
     */
    public void setScriptMethod(byte scriptMethod) {
        this.scriptMethod = scriptMethod;
    }

    /**
     * Gets force accept.
     *
     * @return the force accept
     */
    public boolean getForceAccept() {
        return this.forceAccept;
    }

    /**
     * Sets force accept.
     *
     * @param forceAccept the force accept
     */
    public void setForceAccept(boolean forceAccept) {
        this.forceAccept = forceAccept;
    }

    /**
     * Gets no pin confirm amt flg.
     *
     * @return the no pin confirm amt flg
     */
    public boolean getNoPinConfirmAmtFlg() {
        return this.noPinConfirmAmtFlg;
    }

    /**
     * Sets no pin confirm amt flg.
     *
     * @param noPinConfirmAmtFlg the no pin confirm amt flg
     */
    public void setNoPinConfirmAmtFlg(boolean noPinConfirmAmtFlg) {
        this.noPinConfirmAmtFlg = noPinConfirmAmtFlg;
    }

    /**
     * Gets is input amount.
     *
     * @return the is input amount
     */
    public boolean getIsInputAmount() {
        return this.isInputAmount;
    }

    /**
     * Sets is input amount.
     *
     * @param isInputAmount the is input amount
     */
    public void setIsInputAmount(boolean isInputAmount) {
        this.isInputAmount = isInputAmount;
    }

    /**
     * Get acquirer id byte [ ].
     *
     * @return the byte [ ]
     */
    public byte[] getAcquirerId() {
        return this.acquirerId;
    }

    /**
     * Sets acquirer id.
     *
     * @param acquirerId the acquirer id
     */
    public void setAcquirerId(byte[] acquirerId) {
        this.acquirerId = acquirerId;
    }

    /**
     * Gets unpredictable number range.
     *
     * @return the unpredictable number range
     */
    public String getUnpredictableNumberRange() {
        return unpredictableNumberRange;
    }

    /**
     * Sets unpredictable number range.
     *
     * @param unpredictableNumberRange the unpredictable number range
     */
    public void setUnpredictableNumberRange(String unpredictableNumberRange) {
        this.unpredictableNumberRange = unpredictableNumberRange;
    }

    /**
     * Is support opt trans boolean.
     *
     * @return the boolean
     */
    public boolean isSupportOptTrans() {
        return supportOptTrans;
    }

    /**
     * Sets support opt trans.
     *
     * @param supportOptTrans the support opt trans
     */
    public void setSupportOptTrans(boolean supportOptTrans) {
        this.supportOptTrans = supportOptTrans;
    }

    /**
     * Gets trans cap.
     *
     * @return the trans cap
     */
    public String getTransCap() {
        return transCap;
    }

    /**
     * Sets trans cap.
     *
     * @param transCap the trans cap
     */
    public void setTransCap(String transCap) {
        this.transCap = transCap;
    }

    /**
     * Is delay auth flag boolean.
     *
     * @return the boolean
     */
    public boolean isDelayAuthFlag() {
        return delayAuthFlag;
    }

    /**
     * Sets delay auth flag.
     *
     * @param delayAuthFlag the delay auth flag
     */
    public void setDelayAuthFlag(boolean delayAuthFlag) {
        this.delayAuthFlag = delayAuthFlag;
    }
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.entity.Config
 * JD-Core Version:    0.6.0
 */