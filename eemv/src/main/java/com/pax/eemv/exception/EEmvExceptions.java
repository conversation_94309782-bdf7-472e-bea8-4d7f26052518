package com.pax.eemv.exception;

import com.pax.jemv.clcommon.RetCode;

/**
 * The enum E emv exceptions.
 */
public enum EEmvExceptions {
    /**
     * Emv ok e emv exceptions.
     */
    EMV_OK(RetCode.EMV_OK, "success"),
    /**
     * The Emv err icc reset.
     */
    EMV_ERR_ICC_RESET(RetCode.ICC_RESET_ERR, "icc reset error"),
    /**
     * The Emv err icc cmd.
     */
    EMV_ERR_ICC_CMD(RetCode.ICC_CMD_ERR, "icc cmd error\nplease insert/swipe card"),
    /**
     * The Emv err icc block.
     */
    EMV_ERR_ICC_BLOCK(RetCode.ICC_BLOCK, "icc block"),
    /**
     * The Emv err rsp.
     */
    EMV_ERR_RSP(RetCode.EMV_RSP_ERR, "emv response error"),
    /**
     * The Emv err app block.
     */
    EMV_ERR_APP_BLOCK(RetCode.EMV_APP_BLOCK, "emv application block"),
    /**
     * The Emv err no app.
     */
    EMV_ERR_NO_APP(RetCode.EMV_NO_APP, "emv no application"),
    /**
     * The Emv err user cancel.
     */
    EMV_ERR_USER_CANCEL(RetCode.EMV_USER_CANCEL, "emv user cancel"),
    /**
     * The Emv err timeout.
     */
    EMV_ERR_TIMEOUT(RetCode.EMV_TIME_OUT, "emv timeout"),
    /**
     * The Emv err data.
     */
    EMV_ERR_DATA(RetCode.EMV_DATA_ERR, "emv data error"),
    /**
     * The Emv err not accept.
     */
    EMV_ERR_NOT_ACCEPT(RetCode.EMV_NOT_ACCEPT, "emv not accept"),
    /**
     * The Emv err denial.
     */
    EMV_ERR_DENIAL(RetCode.EMV_DENIAL, "emv denial"),
    /**
     * The Emv err key exp.
     */
    EMV_ERR_KEY_EXP(RetCode.EMV_KEY_EXP, "emv key expiry"),
    /**
     * The Emv err no pinpad.
     */
    EMV_ERR_NO_PINPAD(RetCode.EMV_NO_PINPAD, "emv no pinpad"),
    /**
     * The Emv err no password.
     */
    EMV_ERR_NO_PASSWORD(RetCode.EMV_NO_PASSWORD, "emv no password"),
    /**
     * The Emv err sum.
     */
    EMV_ERR_SUM(RetCode.EMV_SUM_ERR, "emv checksum error"),
    /**
     * The Emv err not found.
     */
    EMV_ERR_NOT_FOUND(RetCode.EMV_NOT_FOUND, "emv not found"),
    /**
     * The Emv err no data.
     */
    EMV_ERR_NO_DATA(RetCode.EMV_NO_DATA, "emv no data"),
    /**
     * The Emv err overflow.
     */
    EMV_ERR_OVERFLOW(RetCode.EMV_OVERFLOW, "emv overflow"),
    /**
     * The Emv err no trans log.
     */
    EMV_ERR_NO_TRANS_LOG(RetCode.NO_TRANS_LOG, "emv no trans log"),
    /**
     * The Emv err record not exist.
     */
    EMV_ERR_RECORD_NOT_EXIST(RetCode.RECORD_NOTEXIST, "emv recode is not existed"),
    /**
     * The Emv err logitem not exist.
     */
    EMV_ERR_LOGITEM_NOT_EXIST(RetCode.LOGITEM_NOTEXIST, "emv log item is not existed"),
    /**
     * The Emv err icc rsp 6985.
     */
    EMV_ERR_ICC_RSP_6985(RetCode.ICC_RSP_6985, "icc response 6985"),
    /**
     * The Emv err clss use contact.
     */
    EMV_ERR_CLSS_USE_CONTACT(RetCode.CLSS_USE_CONTACT, "clss use contact"),
    /**
     * The Emv err file.
     */
    EMV_ERR_FILE(RetCode.EMV_FILE_ERR, "emv file error"),
    /**
     * The Emv err clss terminate.
     */
    EMV_ERR_CLSS_TERMINATE(RetCode.CLSS_TERMINATE, "clss terminate"),
    /**
     * The Emv err clss failed.
     */
    EMV_ERR_CLSS_FAILED(RetCode.CLSS_FAILED, "clss failed"),
    /**
     * The Emv err clss decline.
     */
    EMV_ERR_CLSS_DECLINE(RetCode.CLSS_DECLINE, "clss decline"),
    /**
     * The Emv err param.
     */
    EMV_ERR_PARAM(RetCode.EMV_PARAM_ERR, "emv parameter error"),
    /**
     * The Emv err clss wave 2 oversea.
     */
    EMV_ERR_CLSS_WAVE2_OVERSEA(RetCode.CLSS_WAVE2_OVERSEA, "clss wave2 oversea"),
    /**
     * The Emv err clss wave 2 us card.
     */
    EMV_ERR_CLSS_WAVE2_US_CARD(RetCode.CLSS_WAVE2_US_CARD, "clss wave2 us card"),
    /**
     * The Emv err clss wave 3 ins card.
     */
    EMV_ERR_CLSS_WAVE3_INS_CARD(RetCode.CLSS_WAVE3_INS_CARD, "clss wave3 ins card"),

    /**
     * The Emv err clss reselect app.
     */
    EMV_ERR_CLSS_RESELECT_APP(RetCode.CLSS_RESELECT_APP, "clss reselect app"),

    /**
     * The Emv err clss card expired.
     */
    //EMV_ERR_DATA_OVERFLOW(RetCode.EMV_OVERFLOW, "emv data overflow"),
    EMV_ERR_CLSS_CARD_EXPIRED(RetCode.CLSS_CARD_EXPIRED, "clss card expired"),
    /**
     * The Emv err clss no app ppse.
     */
    EMV_ERR_CLSS_NO_APP_PPSE(RetCode.EMV_NO_APP_PPSE_ERR, "clss no app ppse error"),
    /**
     * The Emv err clss use vsdc.
     */
    EMV_ERR_CLSS_USE_VSDC(RetCode.CLSS_USE_VSDC, "clss use vsdc"),
    /**
     * The Emv err clss cvm decline.
     */
    EMV_ERR_CLSS_CVM_DECLINE(RetCode.CLSS_CVMDECLINE, "clss cvm decline"),
    /**
     * The Emv err clss refer consumer device.
     */
    EMV_ERR_CLSS_REFER_CONSUMER_DEVICE(RetCode.CLSS_REFER_CONSUMER_DEVICE,
            "clss refer consumer device"),
    /**
     * The Emv err clss try another card.
     */
    EMV_ERR_CLSS_TRY_ANOTHER_CARD(RetCode.CLSS_TRY_ANOTHER_CARD, "clss try another card"),
    /**
     * The Emv err no card bin.
     */
    EMV_ERR_NO_CARD_BIN(-64, "Card Bin Not Downloaded"),
    /**
     * The Emv err next cvm.
     */
    EMV_ERR_NEXT_CVM(-8053, "emv next CVM"),
    /**
     * The Emv err quit cvm.
     */
    EMV_ERR_QUIT_CVM(-8057, "emv quit CVM"),
    /**
     * The Emv err select next.
     */
    EMV_ERR_SELECT_NEXT(-8059, "emv select next"),

    /**
     * The Emv err fall back.
     */
    EMV_ERR_FALL_BACK(-8200, "fall back"),
    /**
     * The Emv err channel.
     */
    EMV_ERR_CHANNEL(-8201, "channel error"),
    /**
     * The Emv err no kernel.
     */
    EMV_ERR_NO_KERNEL(-8203, "unknown kernel"),
    /**
     * The Emv err clss no balance.
     */
    EMV_ERR_CLSS_NO_BALANCE(-8205, "clss no balance"),
    /**
     * The Emv err clss over flmt.
     */
    EMV_ERR_CLSS_OVER_FLMT(-8206, "clss over floor limit"),
    /**
     * The Emv err no clss pboc.
     */
    EMV_ERR_NO_CLSS_PBOC(-8207, "no clss pboc card error"),
    /**
     * The Emv err write file fail.
     */
    EMV_ERR_WRITE_FILE_FAIL(-8208, "write file fail error"),
    /**
     * The Emv err read file fail.
     */
    EMV_ERR_READ_FILE_FAIL(-8209, "read file fail error"),
    /**
     * The Emv err invalid para.
     */
    EMV_ERR_INVALID_PARA(-8210, "invalid parameter error"),

    /**
     * The Emv err amount format.
     */
    EMV_ERR_AMOUNT_FORMAT(-8211, "amount format error"),
    /**
     * The Emv err param length.
     */
    EMV_ERR_PARAM_LENGTH(-8212, "parameter length error"),
    /**
     * The Emv err listener is null.
     */
    EMV_ERR_LISTENER_IS_NULL(-8213, "listener is null"),
    /**
     * The Emv err tag length.
     */
    EMV_ERR_TAG_LENGTH(-8214, "tag length error"),

    /**
     * The Emv err online trans abort.
     */
    EMV_ERR_ONLINE_TRANS_ABORT(-8301, "online transaction abort"),
    /**
     * The Emv err function not implemented.
     */
    EMV_ERR_FUNCTION_NOT_IMPLEMENTED(-8302, "function not implemented"),
    /**
     * The Emv err pure ec card not online.
     */
    EMV_ERR_PURE_EC_CARD_NOT_ONLINE(-8303, "pure EC card can't online transaction"),
    /**
     * The Emv err online trans rsp failed.
     */
    EMV_ERR_ONLINE_TRANS_RSP_FAILED(-8304, "online transaction response failed"),
    /**
     * The Emv err card expired.
     */
    EMV_ERR_CARD_EXPIRED(-8305, "card expired"),
    /**
     * The Emv unsupported card.
     */
    EMV_UNSUPPORTED_CARD(-8306, "Unsupported Card"),
    /**
     * The Emv err not hase card.
     */
    EMV_ERR_NOT_HASE_CARD(-8307, "not hase card"),

    /**
     * The Emv err not hase card.
     */
    EMV_ERR_TRANS_NOT_ACCEPTED(-8308, "Trans not accepted"),

    /**
     * The Emv err unknown.
     */
    EMV_ERR_UNKNOWN(-8999, "unknown error");

    private int errCodeFromBasement;
    private String errMsgEn;

    EEmvExceptions(int errCodeFromBasement, String errMsgEn) {
        this.errCodeFromBasement = errCodeFromBasement;
        this.errMsgEn = errMsgEn;
    }

    /**
     * Gets err code from basement.
     *
     * @return the err code from basement
     */
    public int getErrCodeFromBasement() {
        return this.errCodeFromBasement;
    }

    /**
     * Gets err msg.
     *
     * @return the err msg
     */
    public String getErrMsg() {
        return this.errMsgEn;
    }

    /**
     * Gets err msg from err code.
     *
     * @param errCode the err code
     * @return the err msg from err code
     */
    public static String getErrMsgFromErrCode(int errCode) {
        for (EEmvExceptions eEmvExceptions : EEmvExceptions.values()) {
            if (errCode == eEmvExceptions.getErrCodeFromBasement()) {
                return eEmvExceptions.getErrMsg();
            }
        }
        return EMV_ERR_UNKNOWN.getErrMsg();
    }
}

/* Location:           E:\Linhb\projects\Android\PaxEEmv_V1.00.00_20170401\lib\PaxEEmv_V1.00.00_20170401.jar
 * Qualified Name:     com.pax.eemv.exception.EEmvExceptions
 * JD-Core Version:    0.6.0
 */