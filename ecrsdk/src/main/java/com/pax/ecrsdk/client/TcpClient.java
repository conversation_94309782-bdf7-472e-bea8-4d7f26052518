package com.pax.ecrsdk.client;

import com.pax.commonlib.LogUtils;
import com.pax.ecrsdk.utils.EcrUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.Arrays;

public class TcpClient implements Client {
    public static final String TAG = "TcpClient";

    private InputStream in;
    private OutputStream out;
    private Socket socket;

    public TcpClient(Socket clientSocket) {
        try {
            socket = clientSocket;
            socket.setSoTimeout(100);
            in = clientSocket.getInputStream();
            out = clientSocket.getOutputStream();
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }
    }

    @Override
    public byte[] rcvData() throws IOException {
        if (in == null) {
            return null;
        }

        byte[] data = new byte[2048];
        int bytesNum = 0;
        try {
            bytesNum = in.read(data);
        }catch (SocketTimeoutException e){
            return null;
        }

        if (bytesNum == -1) {
            throw new IOException();
        }
        byte[] readData = Arrays.copyOf(data, bytesNum);
        LogUtils.i(TAG, "recv data :" + EcrUtils.bcdToStr(readData));
        return readData;
    }

    @Override
    public int sendData(byte[] data) {
        if (out == null || data == null || data.length == 0) {
            return -1;
        }

        LogUtils.e(TAG, "====TcpClient sendData:" + EcrUtils.bcdToStr(data));

        try {
            out.flush();
            out.write(data);
            out.flush();
            return 0;
        } catch (SocketException e) {
            close();
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
            close();
        }

        return -1;
    }

    @Override
    public void close() {
        try {
            LogUtils.e(TAG, "====TcpClient close========");
            if (in != null) {
                LogUtils.i(TAG, "close in");
                in.close();
                in = null;
            }

            if (out != null) {
                LogUtils.i(TAG, "close out");
                out.close();
                out = null;
            }
            //关闭socket连接
            socket.close();
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        }
    }
}
