package com.pax.ecrsdk.server.param;

//ecr底座的配置参数
public class BaseParam extends Param {
    private int rate;

    public BaseParam(int timeout) {
        super(BaseParam.BASE_RS232, timeout);
    }

    @Override
    public boolean checkArg() {
        return super.checkArg();
    }

    /**
     * getRate
     *
     * @return int
     */
    public int getRate() {
        return rate;
    }

    /**
     * setRate
     *
     * @param rate int
     */
    public void setRate(int rate) {
        this.rate = rate;
    }
}
