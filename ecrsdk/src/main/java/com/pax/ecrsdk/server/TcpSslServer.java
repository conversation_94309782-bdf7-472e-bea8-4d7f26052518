/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-6
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.server;

import android.content.Context;
import android.util.Log;

import com.pax.ecrsdk.client.TcpClient;
import com.pax.ecrsdk.server.param.TcpParam;

import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLServerSocket;
import javax.net.ssl.SSLServerSocketFactory;
import javax.net.ssl.SSLSocket;

import androidx.annotation.NonNull;

class TcpSslServer extends Server {
    public static final String TAG = "ECR WIFI";
    private SSLServerSocket serverSocket;
    private TcpParam param;
    private Context context;

    TcpSslServer(@NonNull Context context, @NonNull TcpParam param, @NonNull IServerListener listener) {
        super(param, listener);
        this.context = context;
        this.param = param;
    }

    @Override
    public void open() {
        try (InputStream is = context.getAssets().open(param.getKeyFileName())) {
            KeyStore serverKeyStore = KeyStore.getInstance("BKS");
            //利用提供的密钥库文件输入流和密码初始化密钥库实例
            serverKeyStore.load(is, param.getPassword().toCharArray());
            //取得SunX509私钥管理器
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("X509");
            //用之前初始化后的密钥库实例初始化私钥管理器
            keyManagerFactory.init(serverKeyStore, param.getPassword().toCharArray());
            //获得TLS协议的SSLContext实例
            SSLContext sslContext = SSLContext.getInstance("TLS");
            //初始化SSLContext实例
            sslContext.init(keyManagerFactory.getKeyManagers(), null, null);
            //以下两步获得SSLServerSocket实例
            SSLServerSocketFactory sslServerSocketFactory = sslContext.getServerSocketFactory();
            serverSocket = (SSLServerSocket) sslServerSocketFactory.createServerSocket(param.getPort());
            Log.i(TAG, "socket success");
        } catch (Exception e) {
            Log.e(TAG, "", e);
        }
    }

    @Override
    public int waitClient() {
        if (serverSocket == null) {
            Log.i(TAG, "server: serverSocket is null");
            return -1;
        }
        Log.i(TAG, "server: waiting.............");
        try {
            SSLSocket clientSocket = (SSLSocket) serverSocket.accept();
            Log.i(TAG, "server: accept client");
            clientSocket.setSoTimeout(getTimeout() * 1000);
            String remoteIP = clientSocket.getInetAddress().getHostAddress();
            int remotePort = clientSocket.getLocalPort();
            Log.i(TAG, "A client connected. IP:" + remoteIP + ", Port: " + remotePort);
            Log.i(TAG, "server: receiving.............");

            return addClient(new TcpClient(clientSocket));
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }

        return -1;
    }

    @Override
    public void close() {
        super.close();
        try {
            if (serverSocket != null) {
                Log.i(TAG, "close serverSocket");
                serverSocket.close();
            }
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }
    }
}
