package com.pax.ecrsdk.server;

import android.util.Log;

import com.pax.ecrsdk.server.param.BaseParam;
import com.pax.ecrsdk.server.param.Param;
import com.pax.ecrsdk.server.param.TcpParam;
import com.pax.ecrsdk.utils.EcrUtils;

public class ServerManager {
    private TcpServerProcess tcpServerProcess;
    private BaseServerProcess baseServerProcess;
    private IServerListener listener;
    private IDeviceListener deviceListener;
    private Param param;
    private boolean isRunning = false;
    private static final String TAG = "ServerManager";

    public ServerManager(IServerListener listener, IDeviceListener deviceListener) {
        this.listener = listener;
        this.deviceListener = deviceListener;
    }

    public void startServer() {
        if (param == null || !param.checkArg() || isRunning) {
            return;
        }
        Log.i(TAG,"start server");
        Server server;
        switch (param.getType()) {
            case Param.WIFI:
                server = new TcpServer((TcpParam) param, listener);
                break;
            case Param.BASE_RS232:
                server = new BaseServer((BaseParam) param, listener);
                break;
            default:
                return;
        }

        isRunning = true;
        EcrUtils.runInBackground(new Runnable() {
            @Override
            public void run() {
                if (param.getType().equals(Param.BASE_RS232)) {
                    baseServerProcess = new BaseServerProcess(server, deviceListener);
                    baseServerProcess.process();
                } else {
                    tcpServerProcess = new TcpServerProcess(server, deviceListener);
                    tcpServerProcess.process();
                }
            }
        });
    }

    public void closeServer() {
        isRunning = false;
        Log.i(TAG,"close server");
        if (tcpServerProcess != null) {
            tcpServerProcess.close();
        }
        if (baseServerProcess != null) {
            baseServerProcess.close();
        }
    }

    public void setParam(Param param) {
        this.param = param;
    }

    public boolean isRunning() {
        return isRunning;
    }

    public String getECRType() {
        return param == null ? null : param.getType();
    }
}
