/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-8
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.server;

import android.os.ConditionVariable;
import android.os.SystemClock;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.EcrUtils;
import com.pax.ecrsdk.message.Message;
import com.pax.ecrsdk.message.MessageUtils;
import com.pax.ecrsdk.message.request.AdjustMsg;
import com.pax.ecrsdk.message.request.DccOptOutMsg;
import com.pax.ecrsdk.message.request.DownloadKeyMsg;
import com.pax.ecrsdk.message.request.GetTotalMsg;
import com.pax.ecrsdk.message.request.GetTxnMsg;
import com.pax.ecrsdk.message.request.InstallmentMsg;
import com.pax.ecrsdk.message.request.OfflineMsg;
import com.pax.ecrsdk.message.request.PreAuthComMsg;
import com.pax.ecrsdk.message.request.PreAuthComVoidMsg;
import com.pax.ecrsdk.message.request.PreAuthMsg;
import com.pax.ecrsdk.message.request.PreAuthVoidMsg;
import com.pax.ecrsdk.message.request.RefundMsg;
import com.pax.ecrsdk.message.request.ReprintMsg;
import com.pax.ecrsdk.message.request.SaleMsg;
import com.pax.ecrsdk.message.request.SelectDccMsg;
import com.pax.ecrsdk.message.request.SettleMsg;
import com.pax.ecrsdk.message.request.VoidMsg;
import com.pax.ecrsdk.message.response.NakMessage;
import com.pax.ecrsdk.message.response.RespDownloadKeyMsg;

class ServerProcess implements IServerProcess {
    private Server server;
    private String sendResult;
    private int id;

    ServerProcess(Server server) {
        this.server = server;
    }

    @Override
    public void process() {
        if (server == null) {
            return;
        }

        server.open();
        resetServer();
    }

    @Override
    public void close() {
        server.close();
    }

    @Override
    public void setChecksum(boolean checksum) {
        server.getParam().setChecksum(checksum);
    }

    @Override
    public void setCalc(boolean calc) {
        server.getParam().setCalc(calc);
    }

    private int handleRcvMsg(String msg) {
        if (msg == null || msg.length() == 0) {
            return -1;
        }

        if (checkData(msg, server.isCalc()) != 0) {
            return -1;
        }

        Message message = MessageUtils.generate(msg);
        if (message != null && message.getDataType().equals(Constants.DOWNLOAD_KEY)) {
            Message ack = new Message();
            ack.setDataType(Constants.ACK);
            server.sendData(MessageUtils.parseObject(ack), id);
            downloadKey();
            return 0;
        }

        if (server.isCalc()) {
            server.setEncrypt(true);
            msg = server.calcData(msg, false);
        }

        Message ack = new Message();
        ack.setDataType(Constants.ACK);
        server.sendData(MessageUtils.parseObject(ack), id);

        String result = server.doServer(msg);
        if (result == null || server.sendData(result, id) != 0) {
            return -1;
        }

        if (handleSendMsg(result) != 0) {
            return -1;
        }

        message = MessageUtils.generate(result);
        if (message != null && Constants.SELECT_DCC.equals(message.getDataType())) {
            return 1;
        }

        return 0;
    }

    private int handleSendMsg(final String result) {
        sendResult = "";
        final ConditionVariable cv = new ConditionVariable();
        EcrUtils.runInBackground(new Runnable() {
            @Override
            public void run() {
                int i = 0;
                while (true) {
                    SystemClock.sleep(1000);

                    if (sendResult == null) {
                        break;
                    }

                    Message message = MessageUtils.generate(sendResult);
                    if (message != null) {
                        break;
                    }

                    i++;
                    if (i % (server.getTimeout() / 3) == 0 && server.sendData(result, id) != 0) {
                        sendResult = null;
                        break;
                    }

                    if (i >= server.getTimeout()) {
                        sendResult = null;
                        break;
                    }
                }
                cv.open();
            }
        });
        sendResult = server.rcvData(id);
        cv.block();

        return sendResult == null ? -1 : 0;
    }

    private void downloadKey() {
        RespDownloadKeyMsg msg = new RespDownloadKeyMsg();
        msg.getData().setRsaModules(EcrUtils.bcdToStr(Constants.rsaModules));
        msg.getData().setRsaPukExp(EcrUtils.bcdToStr(Constants.rsaPukExp));
        if (server.isChecksum()) {
            msg.setChecksum(EcrUtils.calcSHA256(MessageUtils.parseObject(msg.getData())));
        }

        String rsaKey = MessageUtils.parseObject(msg);
        if (server.sendData(rsaKey, id) != 0) {
            return;
        }
        handleSendMsg(rsaKey);
        String data = server.rcvData(id);
        if (data == null) {
            return;
        }

        if (checkData(data, false) != 0) {
            return;
        }

        Message ack = new Message();
        ack.setDataType(Constants.ACK);
        server.sendData(MessageUtils.parseObject(ack), id);

        int ret = server.saveKey(data, msg.getData().getRsaModules(), EcrUtils.bcdToStr(Constants.rsaPvkExp));

        RespDownloadKeyMsg respBaseMsg = new RespDownloadKeyMsg();
        if (ret == 0) {
            respBaseMsg.getData().setStatus("A");
        } else {
            respBaseMsg.getData().setStatus("R");
        }

        if (server.isChecksum()) {
            respBaseMsg.setChecksum(EcrUtils.calcSHA256(MessageUtils.parseObject(respBaseMsg.getData())));
        }

        String response = MessageUtils.parseObject(respBaseMsg);
        if (server.sendData(response, id) != 0) {
            return;
        }

        handleSendMsg(response);
    }

    private int checkData(String data, boolean encrypt) {
        int checkResult = check(data, encrypt);
        if (checkResult < 0) {
            return -1;
        } else if (checkResult != 0) {
            NakMessage message = new NakMessage();
            message.setDataType(Constants.NAK);
            message.setCode(String.valueOf(checkResult));
            server.sendData(MessageUtils.parseObject(message), id);
            return -1;
        }

        return 0;
    }

    private int check(String data, boolean encrypt) {
        Message message = MessageUtils.generate(data);
        if (encrypt && message != null) {
            if (!message.checkArg() || !message.getDataType().equals(Constants.DOWNLOAD_KEY)) {
                return 2;
            }

            if (((DownloadKeyMsg) message).getData().getDownloadKey() == null || ((DownloadKeyMsg) message).getData().getDownloadKey().isEmpty()) {
                return 0;
            }

            if (!server.isChecksum()) {
                return 0;
            }

            if (message.getChecksum() == null || message.getChecksum().isEmpty()) {
                return 1;
            }

            String checksum = MessageUtils.parseObject(((DownloadKeyMsg) message).getData());
            if (checksum == null || checksum.length() <= 0 || !message.getChecksum().equals(EcrUtils.calcSHA256(checksum))) {
                return 1;
            }

            return 0;
        }

        if (encrypt) {
            data = server.calcData(data, false);
            if (data == null || data.length() <= 0) {
                return -1;
            }

            message = MessageUtils.generate(data);
        }

        if (message == null || !message.checkArg()) {
            return 2;
        }

        if (!server.isChecksum()) {
            return 0;
        }

        //HKGPA-265
        if (message.getChecksum() == null || message.getChecksum().isEmpty()) {
            return 1;
        }

        String checksum;
        switch (message.getDataType()) {
            case Constants.SALE:
                checksum = MessageUtils.parseObject(((SaleMsg) message).getData());
                break;
            case Constants.PREAUTH:
                checksum = MessageUtils.parseObject(((PreAuthMsg) message).getData());
                break;
            case Constants.VOID:
                checksum = MessageUtils.parseObject(((VoidMsg) message).getData());
                break;
            case Constants.PREAUTH_COMPLETE_VOID:
                checksum = MessageUtils.parseObject(((PreAuthComVoidMsg) message).getData());
                break;
            case Constants.REFUND:
                checksum = MessageUtils.parseObject(((RefundMsg) message).getData());
                break;
            case Constants.PREAUTH_VOID:
                checksum = MessageUtils.parseObject(((PreAuthVoidMsg) message).getData());
                break;
            case Constants.PREAUTH_COMPLETE:
                checksum = MessageUtils.parseObject(((PreAuthComMsg) message).getData());
                break;
            case Constants.ADJUST:
                checksum = MessageUtils.parseObject(((AdjustMsg) message).getData());
                break;
            case Constants.OFFLINE:
                checksum = MessageUtils.parseObject(((OfflineMsg) message).getData());
                break;
            case Constants.SETTLE:
                checksum = MessageUtils.parseObject(((SettleMsg) message).getData());
                break;
            case Constants.REPRINT:
                checksum = MessageUtils.parseObject(((ReprintMsg) message).getData());
                break;
            case Constants.GET_TXN:
                checksum = MessageUtils.parseObject(((GetTxnMsg) message).getData());
                break;
            case Constants.GET_TOTAL:
                checksum = MessageUtils.parseObject(((GetTotalMsg) message).getData());
                break;
            case Constants.DCC_OPT_OUT:
                checksum = MessageUtils.parseObject(((DccOptOutMsg) message).getData());
                break;
            case Constants.DOWNLOAD_KEY:
                checksum = MessageUtils.parseObject(((DownloadKeyMsg) message).getData());
                break;
            case Constants.INSTALMENT:
                checksum = MessageUtils.parseObject(((InstallmentMsg) message).getData());
                break;
            default:
                checksum = null;
        }

        if (checksum == null || checksum.length() <= 0 || !message.getChecksum().equals(EcrUtils.calcSHA256(checksum))) {
            return 1;
        }

        return 0;
    }

    private void handleRcvDccMsg() {
        String data = server.rcvData(id);
        SelectDccMsg message = new SelectDccMsg();
        message.getData().setDccResult("-1");
        if (data == null || data.length() == 0) {
            data = MessageUtils.parseObject(message);
        }

        if (checkData(data, false) != 0) {
            data = MessageUtils.parseObject(message);
        }

        Message ack = new Message();
        ack.setDataType(Constants.ACK);
        server.sendData(MessageUtils.parseObject(ack), id);

        String result = server.doServer(data);
        if (result == null || server.sendData(result, id) != 0) {
            return;
        }

        handleSendMsg(result);
    }

    private void resetServer() {
        id = server.waitClient();
        if (id >= 0) {
            if (id < server.getClientSum()) {
                String data = server.rcvData(id);
                if (handleRcvMsg(data) > 0) {
                    handleRcvDccMsg();
                }
            }

            server.deleteClient(id);
            resetServer();
        }
    }
}
