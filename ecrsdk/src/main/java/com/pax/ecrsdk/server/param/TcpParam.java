package com.pax.ecrsdk.server.param;

public class Tcp<PERSON>aram extends Param {
    private int port;

    public TcpParam(int timeout, int port) {
        super(BaseParam.WIFI, timeout);
        this.port = port;
    }

    /**
     * getPort
     * 端口信息
     *
     * @return int
     */
    public int getPort() {
        return port;
    }

    /**
     * setPort
     *
     * @param port int
     */
    public void setPort(int port) {
        this.port = port;
    }

    @Override
    public boolean checkArg() {
        return !(port < 1024 || port > 65535) && super.checkArg();
    }
}
