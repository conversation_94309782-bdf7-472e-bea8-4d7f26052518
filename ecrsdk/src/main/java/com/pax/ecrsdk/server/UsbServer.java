/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-4-24
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.server;

import android.util.Log;

import com.pax.dal.IComm;
import com.pax.dal.entity.EUartPort;
import com.pax.dal.entity.UartParam;
import com.pax.dal.exceptions.CommException;
import com.pax.ecrsdk.EcrUtils;
import com.pax.ecrsdk.client.UsbClient;
import com.pax.ecrsdk.server.param.UsbParam;
import com.pax.sdk.Sdk;

class UsbServer extends Server {
    public static final String TAG = "ECR USB";
    private IComm comm;
    private UsbParam param;
    private boolean open = true;


    UsbServer(UsbParam param, IServerListener listener) {
        super(param, listener);
        this.param = param;
    }

    @Override
    public void open() {
        UartParam uartParam = new UartParam();
        uartParam.setAttr("9600,8,n,1");
        uartParam.setPort(EUartPort.USBDEV);
        if (param == null) {
            return;
        }

        try {
            comm = Sdk.getInstance().getDal(param.getContext()).getCommManager().getUartComm(uartParam);
            comm.setRecvTimeout(getTimeout() * 1000);
            comm.connect();
            open = true;
            Log.i(TAG, "usb connect success");
        } catch (CommException e) {
            Log.e(TAG, "", e);
            comm = null;
        }
    }

    @Override
    public int waitClient() {
        if (comm == null || !open) {
            Log.i(TAG, "start usb error");
            return -1;
        }
        Log.i(TAG, "start usb communication");
        return addClient(new UsbClient(comm));
    }

    @Override
    public void close() {
        open = false;
        //HKGPA-267
        EcrUtils.runInBackground(new Runnable() {
            @Override
            public void run() {
                if (comm != null) {
                    try {
                        comm.cancelRecv();
                        comm.disconnect();
                        comm = null;
                    } catch (CommException e) {
                        Log.e(TAG, "", e);
                    }
                }
            }
        });
    }
}
