/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-6
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.server;

import com.pax.ecrsdk.client.Client;
import com.pax.ecrsdk.server.param.BaseParam;

import java.util.ArrayList;
import java.util.List;

public abstract class Server {
    private IServerListener listener;
    private List<Client> clientList = new ArrayList<>();
    private boolean isEncrypt = false;
    private BaseParam param;

    Server(BaseParam param, IServerListener listener) {
        this.param = param;
        this.listener = listener;
    }

    String getType() {
        return param.getType();
    }

    int getTimeout() {
        return param.getTimeout();
    }

    boolean isChecksum() {
        return param.isChecksum();
    }

    boolean isCalc() {
        return param.isCalc();
    }

    int getClientSum() {
        return clientList.size();
    }

    BaseParam getParam() {
        return param;
    }

    int addClient(Client client) {
        clientList.add(client);
        return clientList.indexOf(client);
    }

    String rcvData(int id) {
        if (clientList.size() > id) {
            return isEncrypt ? calcData(clientList.get(id).rcvData(), false) : clientList.get(id).rcvData();
        }
        return null;
    }

    int sendData(String data, int id) {
        if (clientList.size() > id) {
            return isEncrypt ? clientList.get(id).sendData(calcData(data, true)) : clientList.get(id).sendData(data);
        }
        return -1;
    }

    String calcData(String data, boolean isEncrypt) {
        return listener.calcEcrData(data, isEncrypt);
    }

    String doServer(String message) {
        return listener.doServer(message);
    }

    int saveKey(String data, String rsaModule, String rsaPvkExp) {
        return listener.saveKey(data, rsaModule, rsaPvkExp);
    }

    void deleteClient(int id) {
        if (clientList.size() > id) {
            clientList.get(id).close();
            clientList.remove(id);
        }
        setEncrypt(false);
    }

    void close() {
        for (Client client : clientList) {
            client.close();
        }
    }

    public abstract void open();

    public abstract int waitClient();

    boolean isEncrypt() {
        return isEncrypt;
    }

    void setEncrypt(boolean encrypt) {
        isEncrypt = encrypt;
    }
}
