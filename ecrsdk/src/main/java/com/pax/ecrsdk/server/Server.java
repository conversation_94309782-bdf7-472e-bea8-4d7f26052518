package com.pax.ecrsdk.server;

import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.client.Client;
import com.pax.ecrsdk.message.request.Request;
import com.pax.ecrsdk.server.param.Param;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public abstract class Server {
    private IServerListener listener;
    private List<Client> clientList = new ArrayList<>();
    private Param param;
    public static final String TAG = "Server";

    private Timer heartBeatTimer;
    private TimerTask timerTask;

    Server(Param param, IServerListener listener) {
        this.param = param;
        this.listener = listener;
    }

    String getType() {
        return param.getType();
    }

    int getTimeout() {
        return param.getTimeout();
    }

    public Param getParam() {
        return param;
    }

    int getClientSum() {
        return clientList.size();
    }

    int addClient(Client client) {
        clientList.add(client);
        return clientList.indexOf(client);
    }

    byte[] rcvData(int id) throws IOException {
        if (clientList.size() > id) {
            return clientList.get(id).rcvData();
        }
        return null;
    }

    int sendData(byte[] data, int id) {
        if (clientList.size() > id) {
            return clientList.get(id).sendData(data);
        }
        return -1;
    }

    void doServer(Request reqData, TransResponseListener transResponseListener) {
        if (listener == null) {
            return;
        }

        listener.doServer(reqData, transResponseListener);
    }

    void deleteAllClient() {
        for (Client client : clientList) {
            client.close();
        }
        clientList.clear();
    }

    void deleteClient(int id) {
        if (clientList.size() > id) {
            clientList.get(id).close();
            clientList.remove(id);
        }
    }

    void close() {
        stopHeartBeatTimer();
        for (Client client : clientList) {
            client.close();
        }
    }

    /**
     * start timer to send heartbeat
     */
    public void startHeartBeatTimer(final int id) {
        //如果已经启动过定时器, 则不再启动定时器
        if (timerTask != null || heartBeatTimer != null) {
            return;
        }
        timerTask = new TimerTask() {
            @Override
            public void run() {
                sendHeartBeat(id);
            }
        };
        heartBeatTimer = new Timer();
        heartBeatTimer.schedule(timerTask, 2, 2000);
    }

    void sendHeartBeat(int id) {
        sendData(new byte[] { EcrConstants.ACK }, id);
    }

    public void stopHeartBeatTimer() {
        if (heartBeatTimer != null) {
            heartBeatTimer.cancel();
            heartBeatTimer = null;
        }

        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
    }

    public abstract void open();

    public abstract int waitClient();
}
