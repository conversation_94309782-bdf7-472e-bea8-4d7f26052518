package com.pax.ecrsdk.server;

import com.pax.commonlib.LogUtils;
import com.pax.ecrsdk.client.TcpClient;
import com.pax.ecrsdk.server.param.TcpParam;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicReference;

public class TcpServer extends Server {
    public static final String TAG = "TcpServer";
    private ServerSocket serverSocket;
    private TcpParam param;
    //存储ecr 客户端的连接请求
    private AtomicReference<Socket> socketAtomicReference = new AtomicReference<>();

    /**
     *waitClientThread等待接受ecr客户端连接请求，
     * 并判断当前如果正在处理一个ecr客户端的请求，就拒绝新的连接。
     */
    Thread waitClientThread = new Thread(() -> {
        while (!Thread.interrupted()) {
            if (serverSocket == null) return;
            try {
                Socket clientSocket = serverSocket.accept();
                Socket workingSocket = socketAtomicReference.get();
                if (workingSocket != null && !workingSocket.isClosed()) {
                    //拒绝新的连接，pos机一次只和一个ecr客户端连接
                    clientSocket.close();
                    continue;
                }
                socketAtomicReference.set(clientSocket);

            } catch (IOException e) {
                serverSocket = null;
            }
        }
    });
    public TcpServer(TcpParam param, IServerListener listener) {
        super(param, listener);
        this.param = param;
    }

    @Override
    public void open() {
        try {
            serverSocket = new ServerSocket(param.getPort(), 100);
            waitClientThread.start();
            LogUtils.i(TAG, "serverSocket success");
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }
    }
    @Override
    public int waitClient() {
        LogUtils.e(TAG, "====waitClient========");
        while (serverSocket != null) {
            //接受新的ecr客户端连接请求
            if (socketAtomicReference.get() != null && !socketAtomicReference.get().isClosed()) {
                return addClient(new TcpClient(socketAtomicReference.get()));
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return -1;
    }
    @Override
    void close() {
        super.close();
        try {
            LogUtils.e(TAG, "====start close tcp serverSocket");
            if (serverSocket != null) {
                LogUtils.e(TAG, "close serverSocket");
                waitClientThread.interrupt();
                serverSocket.close();
                serverSocket = null;
            }
        } catch (IOException e) {
            LogUtils.e(TAG, "", e);
        }
    }
}
