/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-7
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.server;

public interface IServerListener {

    /**
     * Server在收到消息后调用此方法
     * @param message json message object extend {@link com.pax.ecrsdk.message.Message}
     * @return json message, object extend {@link com.pax.ecrsdk.message.response.RespBaseMsg}
     */
    String doServer(String message);

    /**
     * 使用RSA私钥对ECR传过来的数据进行解密，得到TDK数据，并且保存在本地以便后面通讯的加解密
     * @param data json message, object extend {@link com.pax.ecrsdk.message.request.DownloadKeyMsg}
     * @param rsaModule RSA私钥的模值
     * @param rsaPvkExp RSA私钥的指数
     * @return ret : 0 成功， -1 失败
     */
    int saveKey(String data, String rsaModule, String rsaPvkExp);

    /**
     * @param data 需要加密或解密的数据
     * @param isEncrypt 是否加密
     * @return 加解密后的结果
     */
    String calcEcrData(String data, boolean isEncrypt);
}
