/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-12-8
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk;

/**
 * Factory for create a ClientApi instance
 */

public class ClientApiFactory {

    public static ClientAPI createClientAPI() {
        return new ClientAPI();
    }
}
