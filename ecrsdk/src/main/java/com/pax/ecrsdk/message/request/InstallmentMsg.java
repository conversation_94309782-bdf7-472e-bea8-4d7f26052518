/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.message.request;

import android.text.TextUtils;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class InstallmentMsg extends Message {
    private InstallmentData data = new InstallmentData();

    public InstallmentMsg() {
        super(Constants.INSTALMENT);
    }

    public InstallmentData getData() {
        return data;
    }

    public void setData(InstallmentData data) {
        this.data = data;
    }

    @Override
    public boolean checkArg() {
        return !TextUtils.isEmpty(getData().getAmt()) && !getData().getAmt().equals("0");
    }

    public static class InstallmentData {
        private String amt;
        private String ecrRef;
        private String plan;

        public String getAmt() {
            return amt;
        }

        public void setAmt(String amt) {
            this.amt = amt;
        }

        public String getEcrRef() {
            return ecrRef;
        }

        public void setEcrRef(String ecrRef) {
            this.ecrRef = ecrRef;
        }

        public String getPlan() {
            return plan;
        }

        public void setPlan(String plan) {
            this.plan = plan;
        }
    }
}
