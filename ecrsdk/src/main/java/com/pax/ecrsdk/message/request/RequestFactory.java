/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220713  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.ecrsdk.message.request;

import static com.pax.ecrsdk.EcrConstants.CARD_PROMOTION_SALE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_ADJUST_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_INSTALMENT_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_OFFLINE_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_REFUND_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_RETRIEVAL_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_SALE_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CREDIT_VOID_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CUP_ADJUST_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CUP_QR_RETRIEVAL_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CUP_REFUND_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CUP_RETRIEVAL_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CUP_SALE_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.CUP_VOID_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.EPS_CUP_SALE_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.EPS_CUP_VOID_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.EPS_RETRIEVAL_TYPE;
import static com.pax.ecrsdk.EcrConstants.EPS_SALE_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.LOYALTY_CREATION_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.LOYALTY_ENQUIRY_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.LOYALTY_RETRIEVAL_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.MICRO_PAY_REFUND_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.MICRO_PAY_REFUND_QUERY_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.MICRO_PAY_SALE_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.MICRO_PAY_SALE_QUERY_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.MICRO_PAY_TRANSACTION_RETRIEVAL_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.MICRO_PAY_TRANSACTION_SUMMARY_MSG_TYPE;
import static com.pax.ecrsdk.EcrConstants.PARTYKING_CASH_COUPON_REDEEM;
import static com.pax.ecrsdk.EcrConstants.PARTYKING_COUPON_QUERY;
import static com.pax.ecrsdk.EcrConstants.PARTYKING_GIFT_COUPON_REDEEM;
import static com.pax.ecrsdk.EcrConstants.PARTYKING_TRANSACTION_RETRIEVAL;
import static com.pax.ecrsdk.EcrConstants.PARTYKING_TRANSACTION_SUMMARY;
import static com.pax.ecrsdk.EcrConstants.PARTYKING_VOID_COUPON;

import com.pax.commonlib.LogUtils;

import java.io.IOException;

public class RequestFactory {

    public static Request createRequest(byte[] reqMsg) {
        if (reqMsg.length < 4) {
            return null;
        }

        // here regard reqMsg[3] as msgType temporarily,
        // will check message validation in following concrete Request later
        byte messageType = reqMsg[3];

        Request request = null;
        try {
            switch (messageType) {
                case CREDIT_SALE_MSG_TYPE:
                case CREDIT_OFFLINE_MSG_TYPE:
                case CREDIT_REFUND_MSG_TYPE:
                case CREDIT_VOID_MSG_TYPE:
                case CREDIT_ADJUST_MSG_TYPE:
                case CREDIT_INSTALMENT_MSG_TYPE:
                    request = new CreditRequest();
                    break;
                case CREDIT_RETRIEVAL_MSG_TYPE:
                case EPS_RETRIEVAL_TYPE:
                case CUP_RETRIEVAL_MSG_TYPE:
                case LOYALTY_RETRIEVAL_MSG_TYPE:
                    request = new RetrievalRequest();
                    break;
                case LOYALTY_CREATION_MSG_TYPE:
                case LOYALTY_ENQUIRY_MSG_TYPE:
                    request = new YuuRequest();
                    break;
                case EPS_SALE_MSG_TYPE:
                case EPS_CUP_SALE_MSG_TYPE:
                case EPS_CUP_VOID_MSG_TYPE:
                    request = new EpsRequest();
                    break;
                case CUP_SALE_MSG_TYPE:
                case CUP_VOID_MSG_TYPE:
                case CUP_ADJUST_MSG_TYPE:
                case CUP_REFUND_MSG_TYPE:
                case CUP_QR_RETRIEVAL_MSG_TYPE:
                    request = new CupRequest();
                    break;
                case CARD_PROMOTION_SALE:
                    request = new CardPromotionSaleRequest();
                    break;
                case MICRO_PAY_SALE_MSG_TYPE:
                case MICRO_PAY_SALE_QUERY_MSG_TYPE:
                case MICRO_PAY_REFUND_MSG_TYPE:
                case MICRO_PAY_REFUND_QUERY_MSG_TYPE:
                case MICRO_PAY_TRANSACTION_SUMMARY_MSG_TYPE:
                    request = new MicroPayRequest();
                    break;
                case MICRO_PAY_TRANSACTION_RETRIEVAL_MSG_TYPE:
                    request = new MicroPayTransRetrievalRequest();
                    break;
                //如果是partyking相关的交易，则用partyking相关的request来解析
                case PARTYKING_GIFT_COUPON_REDEEM:
                case PARTYKING_CASH_COUPON_REDEEM:
                case PARTYKING_VOID_COUPON:
                case PARTYKING_COUPON_QUERY:
                case PARTYKING_TRANSACTION_SUMMARY:
                    request = new PartyKingCommRequest();
                    break;
                //如果是partyking的retrieval的交易，则用partyking的 retrieval request来解析
                case PARTYKING_TRANSACTION_RETRIEVAL:
                    request = new PartyKingRetrievalRequest();
                    break;
                default:
                    break;
            }

            if (request != null) {
                request.parse(reqMsg);
            }
        } catch (IOException e) {
            LogUtils.e("RequestFactory", e.getMessage());
        }
        return request;
    }
}
