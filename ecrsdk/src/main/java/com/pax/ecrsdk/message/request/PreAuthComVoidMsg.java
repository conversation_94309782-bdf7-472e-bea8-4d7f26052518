/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.request;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class PreAuthComVoidMsg extends Message {
    private PreAuthComVoidData data = new PreAuthComVoidData();

    public PreAuthComVoidMsg() {
        super(Constants.PREAUTH_COMPLETE_VOID);
    }

    public PreAuthComVoidData getData() {
        return data;
    }

    public void setData(PreAuthComVoidData data) {
        this.data = data;
    }

    @Override
    public boolean checkArg() {
        return getData().getTxnId() != null && !getData().getTxnId().isEmpty() && Integer.parseInt(data.getTxnId()) > 0;
    }

    public static class PreAuthComVoidData {
        private String txnId;
        private String pwd;

        public String getTxnId() {
            return txnId;
        }

        public void setTxnId(String txnId) {
            this.txnId = txnId;
        }

        public String getPwd() {
            return pwd;
        }

        public void setPwd(String pwd) {
            this.pwd = pwd;
        }
    }
}
