/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220711  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.ecrsdk.message.request;

import java.io.IOException;

public class CupRequest extends Request {

    @Override
    protected void dataParse() throws IOException {
        super.dataParse();
        //index从1开始, 0是msgType
        int index = 1;
        String dataStr = new String(data);
        if (dataStr.length() >= 1 && "d".equals(dataStr.substring(0, 1))) {
            traceNumber = dataStr.substring(index);
            cardType = "CQ";
            return;
        }
        //ECR Reference No
        ecrReferenceNo = dataStr.substring(index, index + 16);
        index += 16;
        amountInCents = dataStr.substring(index, index + 12);
        index += 12;
        cardType = dataStr.substring(index, index + 2);
        index += 2;
        traceNumber = dataStr.substring(index, index + 6);
    }

    @Override
    public boolean checkArg() {
        return super.checkArg();
    }
}
