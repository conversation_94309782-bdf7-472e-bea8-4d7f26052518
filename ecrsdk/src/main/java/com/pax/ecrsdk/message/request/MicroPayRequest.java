package com.pax.ecrsdk.message.request;

import java.io.IOException;
/*
    对sale ,sale query ,refund,refund query,transaction summary
    Ecr发送的请求报文格式一致，采用相同的解析方式
 */

public class MicroPayRequest extends Request {
    @Override
    protected void dataParse() throws IOException {
        super.dataParse();
        String dataStr = new String(data);
        //index从1开始, 0是msgType
        int index = 1;
        //ECR Reference No
        ecrReferenceNo = dataStr.substring(index, index + 16);
        index += 16;
        amountInCents = dataStr.substring(index, index + 12);
        index += 12;
        transactionId = dataStr.substring(index, index + 32).trim();
        index += 32;
        outTradeNumber = dataStr.substring(index, index + 30).trim();
        index += 30;
        outRefundNumber = dataStr.substring(index, index + 30).trim();
        index += 30;
        startDate = dataStr.substring(index, index + 8).trim();
        index += 8;
        endDate = dataStr.substring(index, index + 8).trim();
        index += 8;
        reserve = dataStr.substring(index, index + 23);

    }

    @Override
    public boolean checkArg() {
        return super.checkArg();
    }
}
