/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.response;

import com.pax.ecrsdk.message.Message;

public class RespBaseMsg extends Message {
    private RespBaseData data = new RespBaseData();

    public RespBaseMsg(String dataType) {
        super(dataType);
    }

    public RespBaseData getData() {
        return data;
    }

    public void setData(RespBaseData data) {
        this.data = data;
    }

    public static class RespBaseData {
        private String status;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }
}
