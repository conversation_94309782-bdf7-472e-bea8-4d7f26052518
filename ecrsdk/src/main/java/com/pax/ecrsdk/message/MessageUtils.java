/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-6
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.message;


import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.request.AdjustMsg;
import com.pax.ecrsdk.message.request.DccOptOutMsg;
import com.pax.ecrsdk.message.request.DownloadKeyMsg;
import com.pax.ecrsdk.message.request.GetTotalMsg;
import com.pax.ecrsdk.message.request.GetTxnMsg;
import com.pax.ecrsdk.message.request.InstallmentMsg;
import com.pax.ecrsdk.message.request.OfflineMsg;
import com.pax.ecrsdk.message.request.PreAuthComMsg;
import com.pax.ecrsdk.message.request.PreAuthComVoidMsg;
import com.pax.ecrsdk.message.request.PreAuthMsg;
import com.pax.ecrsdk.message.request.PreAuthVoidMsg;
import com.pax.ecrsdk.message.request.RefundMsg;
import com.pax.ecrsdk.message.request.ReprintMsg;
import com.pax.ecrsdk.message.request.SaleMsg;
import com.pax.ecrsdk.message.request.SelectDccMsg;
import com.pax.ecrsdk.message.request.SettleMsg;
import com.pax.ecrsdk.message.request.VoidMsg;
import com.pax.ecrsdk.message.response.RespDownloadKeyMsg;

/**
 * transform Object to String and String to {@link Message}
 */

public class MessageUtils {

    public static Message generate(String json) {
        try {
            Message message = JSONObject.parseObject(json, Message.class);
            switch (message.getDataType()) {
                case Constants.SALE:
                    return JSONObject.parseObject(json, SaleMsg.class);
                case Constants.PREAUTH:
                    return JSONObject.parseObject(json, PreAuthMsg.class);
                case Constants.VOID:
                    return JSONObject.parseObject(json, VoidMsg.class);
                case Constants.PREAUTH_COMPLETE_VOID:
                    return JSONObject.parseObject(json, PreAuthComVoidMsg.class);
                case Constants.REFUND:
                    return JSONObject.parseObject(json, RefundMsg.class);
                case Constants.PREAUTH_VOID:
                    return JSONObject.parseObject(json, PreAuthVoidMsg.class);
                case Constants.PREAUTH_COMPLETE:
                    return JSONObject.parseObject(json, PreAuthComMsg.class);
                case Constants.ADJUST:
                    return JSONObject.parseObject(json, AdjustMsg.class);
                case Constants.OFFLINE:
                    return JSONObject.parseObject(json, OfflineMsg.class);
                case Constants.SETTLE:
                    return JSONObject.parseObject(json, SettleMsg.class);
                case Constants.REPRINT:
                    return JSONObject.parseObject(json, ReprintMsg.class);
                case Constants.GET_TXN:
                    return JSONObject.parseObject(json, GetTxnMsg.class);
                case Constants.GET_TOTAL:
                    return JSONObject.parseObject(json, GetTotalMsg.class);
                case Constants.DCC_OPT_OUT:
                    return JSONObject.parseObject(json, DccOptOutMsg.class);
                case Constants.DOWNLOAD_KEY:
                    return JSONObject.parseObject(json, DownloadKeyMsg.class);
                case Constants.SELECT_DCC:
                    return JSONObject.parseObject(json, SelectDccMsg.class);
                case Constants.INSTALMENT:
                    return JSONObject.parseObject(json, InstallmentMsg.class);
                case Constants.ACTIVE:
                case Constants.ACK:
                case Constants.NAK:
                    return message;
                default:
                    return null;
            }
        } catch (Exception e) {
            Log.e("log", e.toString());
            return null;
        }
    }

    public static Message generateResp(String json) {
        try {
            Message message = JSONObject.parseObject(json, Message.class);
            switch (message.getDataType()) {
                case Constants.DOWNLOAD_KEY:
                    return JSONObject.parseObject(json, RespDownloadKeyMsg.class);
                default:
                    return null;
            }
        } catch (Exception e) {
            Log.e("log", e.toString());
            return null;
        }
    }

    public static String parseObject(Object object) {
        return object == null ? "" : JSONObject.toJSONString(object);
    }
}
