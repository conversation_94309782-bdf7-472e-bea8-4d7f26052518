/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.request;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class SettleMsg extends Message {
    private SettleData data = new SettleData();

    public SettleMsg() {
        super(Constants.SETTLE);
    }

    public SettleData getData() {
        return data;
    }

    public void setData(SettleData data) {
        this.data = data;
    }

    @Override
    public boolean checkArg() {
        return getData().getAcqName() != null && !getData().getAcqName().isEmpty();
    }

    public static class SettleData {
        private String acqName;

        public String getAcqName() {
            return acqName;
        }

        public void setAcqName(String acqName) {
            this.acqName = acqName;
        }
    }
}
