package com.pax.ecrsdk.message.request;

import java.io.IOException;
/*
    解析Ecr发送的Transaction Retrieval请求报文
 */

public class MicroPayTransRetrievalRequest extends Request {
    @Override
    protected void dataParse() throws IOException {
        super.dataParse();
        String dataStr = new String(data);
        //index从1开始, 0是msgType
        int index = 1;
        //out trade number
        outTradeNumber = dataStr.substring(index, index + 30).trim();
        index += 30;
        outRefundNumber = dataStr.substring(index, index + 30).trim();

    }

    @Override
    public boolean checkArg() {
        return super.checkArg();
    }
}
