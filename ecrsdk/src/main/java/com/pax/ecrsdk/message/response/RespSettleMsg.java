/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-30
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.response;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

import java.util.ArrayList;
import java.util.List;

public class RespSettleMsg extends Message {
    private String status;
    private List<RespSettleData> data = new ArrayList<>();

    public RespSettleMsg() {
        super(Constants.SETTLE);
    }

    public List<RespSettleData> getData() {
        return data;
    }

    public void setData(List<RespSettleData> data) {
        this.data = data;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void add() {
        data.add(new RespSettleData());
    }

    public static class RespSettleData {
        private String txnDate;
        private String txnTime;
        private String rspCode;
        private String rspText;
        private String acqName;
        private String batchNo;
        private String batchUpload;

        public String getTxnDate() {
            return txnDate;
        }

        public void setTxnDate(String txnDate) {
            this.txnDate = txnDate;
        }

        public String getTxnTime() {
            return txnTime;
        }

        public void setTxnTime(String txnTime) {
            this.txnTime = txnTime;
        }

        public String getRspCode() {
            return rspCode;
        }

        public void setRspCode(String rspCode) {
            this.rspCode = rspCode;
        }

        public String getRspText() {
            return rspText;
        }

        public void setRspText(String rspText) {
            this.rspText = rspText;
        }

        public String getAcqName() {
            return acqName;
        }

        public void setAcqName(String acqName) {
            this.acqName = acqName;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public void setBatchNo(String batchNo) {
            this.batchNo = batchNo;
        }

        public String getBatchUpload() {
            return batchUpload;
        }

        public void setBatchUpload(String batchUpload) {
            this.batchUpload = batchUpload;
        }
    }
}
