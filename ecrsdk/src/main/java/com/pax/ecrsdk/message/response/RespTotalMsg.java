/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-20
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.response;

public class RespTotalMsg extends RespBaseMsg {
    private RespTotalData data = new RespTotalData();

    public RespTotalMsg(String dataType) {
        super(dataType);
    }

    @Override
    public RespTotalData getData() {
        return data;
    }

    public void setData(RespTotalData data) {
        this.data = data;
    }

    public static class RespTotalData extends RespBaseData {
        private String acqName;
        private String saleNum;
        private String saleAmt;
        private String tipNum;
        private String tipAmt;
        private String voidNum;
        private String voidAmt;
        private String refundNum;
        private String refundAmt;
        private String voidRefundNum;
        private String voidRefundAmt;
        private String offlineNum;
        private String offlineAmt;
        private String preAuthCompNum;
        private String preAuthCompAmt;
        private String preAuthCompVoidNum;
        private String preAuthCompVoidAmt;
        public String getAcqName() {
            return acqName;
        }

        public void setAcqName(String acqName) {
            this.acqName = acqName;
        }

        public String getSaleNum() {
            return saleNum;
        }

        public void setSaleNum(String saleNum) {
            this.saleNum = saleNum;
        }

        public String getSaleAmt() {
            return saleAmt;
        }

        public void setSaleAmt(String saleAmt) {
            this.saleAmt = saleAmt;
        }

        public String getVoidNum() {
            return voidNum;
        }

        public void setVoidNum(String voidNum) {
            this.voidNum = voidNum;
        }

        public String getVoidAmt() {
            return voidAmt;
        }

        public void setVoidAmt(String voidAmt) {
            this.voidAmt = voidAmt;
        }

        public String getRefundNum() {
            return refundNum;
        }

        public void setRefundNum(String refundNum) {
            this.refundNum = refundNum;
        }

        public String getRefundAmt() {
            return refundAmt;
        }

        public void setRefundAmt(String refundAmt) {
            this.refundAmt = refundAmt;
        }

        public String getVoidRefundNum() {
            return voidRefundNum;
        }

        public void setVoidRefundNum(String voidRefundNum) {
            this.voidRefundNum = voidRefundNum;
        }

        public String getVoidRefundAmt() {
            return voidRefundAmt;
        }

        public void setVoidRefundAmt(String voidRefundAmt) {
            this.voidRefundAmt = voidRefundAmt;
        }

        public String getTipNum() {
            return tipNum;
        }

        public void setTipNum(String tipNum) {
            this.tipNum = tipNum;
        }

        public String getTipAmt() {
            return tipAmt;
        }

        public void setTipAmt(String tipAmt) {
            this.tipAmt = tipAmt;
        }

        public String getOfflineNum() {
            return offlineNum;
        }

        public void setOfflineNum(String offlineNum) {
            this.offlineNum = offlineNum;
        }

        public String getOfflineAmt() {
            return offlineAmt;
        }

        public void setOfflineAmt(String offlineAmt) {
            this.offlineAmt = offlineAmt;
        }

        public String getPreAuthCompNum() {
            return preAuthCompNum;
        }

        public void setPreAuthCompNum(String preAuthCompNum) {
            this.preAuthCompNum = preAuthCompNum;
        }

        public String getPreAuthCompAmt() {
            return preAuthCompAmt;
        }

        public void setPreAuthCompAmt(String preAuthCompAmt) {
            this.preAuthCompAmt = preAuthCompAmt;
        }

        public String getPreAuthCompVoidNum() {
            return preAuthCompVoidNum;
        }

        public void setPreAuthCompVoidNum(String preAuthCompVoidNum) {
            this.preAuthCompVoidNum = preAuthCompVoidNum;
        }

        public String getPreAuthCompVoidAmt() {
            return preAuthCompVoidAmt;
        }

        public void setPreAuthCompVoidAmt(String preAuthCompVoidAmt) {
            this.preAuthCompVoidAmt = preAuthCompVoidAmt;
        }
    }
}
