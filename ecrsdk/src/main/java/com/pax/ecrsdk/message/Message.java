/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-6
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.message;

public class Message {
    private String dataType;
    private String checksum;

    public Message(String dataType) {
        this.dataType = dataType;
    }

    public Message() {
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public boolean checkArg() {
        return true;
    }
}
