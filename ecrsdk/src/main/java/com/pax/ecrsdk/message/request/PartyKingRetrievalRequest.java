package com.pax.ecrsdk.message.request;

import android.util.Log;

import java.io.IOException;

/**
 * Author Fanjiaming
 * Date 2024/1/30
 */
public class PartyKingRetrievalRequest extends Request{
    @Override
    protected void dataParse() throws IOException {
        super.dataParse();

        String dataStr = new String(data);
        //index从1开始, 0是msgType
        int index = 1;
        //outTradeNumber
        outTradeNumber = dataStr.substring(index, index + 30);
    }

    @Override
    public boolean checkArg() {
        return super.checkArg();
    }
}
