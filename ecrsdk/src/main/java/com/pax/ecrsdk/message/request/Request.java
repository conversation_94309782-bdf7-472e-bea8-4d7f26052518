/*
 * ===========================================================================================
 * = COPYRIGHT
 *           PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *    This software is supplied under the terms of a license agreement or nondisclosure
 *    agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *    disclosed except in accordance with the terms in that agreement.
 *        Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *                    // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                 Author	                Action
 * 20220711  	         yangrr           	    Create/Add/Modify/Delete
 * ===========================================================================================
 */

package com.pax.ecrsdk.message.request;

import androidx.annotation.NonNull;

import com.pax.ecrsdk.message.Message;

import java.io.IOException;

public class Request extends Message {
    /**
     * Message Type
     */
    protected byte msgType;
    /**
     * ECR Reference No
     */
    protected String ecrReferenceNo;
    /**
     * Transaction Amount including tips. Right justified with leading zero.
     * (For Adjsutment it should be the adjusted value instead of the original value)
     */
    protected String amountInCents;
    /**
     * Original Trace Number of Transaction to be Voided
     */
    protected String traceNumber;
    /**
     * instalment acquirer index
     */
    protected String filler;
    /**
     * 00 – Normal Sales transaction without loyalty.
     * 03 – Full Redeem HangSeng Cash/Jardine Dollars.
     */
    protected String loyaltyRequestCode;
    /**
     * from EPS request message: Transaction amount including other transaction amount, purchase amount, cash
     * back amount.
     */
    protected String totalAmountInCents;
    /**
     * from EPS request message: Amount of service charge / Tips. Right justified with leading zero. This
     * field is reserved for future used. All is all zeros
     */
    protected String otherTransAmountInCents;
    /**
     * from EPS request message
     * ‘EE’ – Normal EPS card
     * ‘NE’ – (reserved for future use)
     */
    protected String cardType;
    /**
     * from EPS request message
     * Amount of goods purchase
     */
    protected String purchaseAmountInCents;
    /**
     * from EPS request message
     * Amount of cash drawn
     */
    protected String cashBackAmountInCents;
    /**
     * YUU request ciam
     */
    protected String ciam;

    /**
     * MicroPay request transactionId
     */
    protected String transactionId;

    /**
     * MicroPay request outTradeNumber
     */
    protected String outTradeNumber;

    /**
     * MicroPay request outRefundNumber
     */
    protected String outRefundNumber;

    /**
     * MicroPay request startDate
     */
    protected String startDate;

    /**
     * MicroPay request endDate
     */
    protected String endDate;

    /**
     * MicroPay request reserve
     */
    protected String reserve;
    protected String cardPromotion;
    protected String otherPromotionCardBin;


    public void parse(@NonNull byte[] reqData) throws IOException {
        int index = 0;

        if (!checkMsgValid(reqData)) {
            throw new IOException("invalid ECR request message");
        }

        setReqRawMsg(reqData);

        stx = reqRawMsg[index++];

        System.arraycopy(reqRawMsg, index, dataLen, 0, dataLen.length);
        index += dataLen.length;

        int len = toIntLen(dataLen);
        if (len > 0) {
            data = new byte[len];
            System.arraycopy(reqRawMsg, index, data, 0, len);
            index += len;
            msgType = data[0];
            dataParse();
        }
        etx = reqRawMsg[index++];
        lrc = reqRawMsg[index];
    }

    protected void dataParse() throws IOException {
        if (data == null || data.length == 0) {
            throw new IOException("protocol request data is null");
        }
    }

    public byte getMsgType() {
        return msgType;
    }

    public void setMsgType(byte msgType) {
        this.msgType = msgType;
    }

    public String getEcrReferenceNo() {
        return ecrReferenceNo;
    }

    public void setEcrReferenceNo(String ecrReferenceNo) {
        this.ecrReferenceNo = ecrReferenceNo;
    }

    public String getAmountInCents() {
        return amountInCents;
    }

    public void setAmountInCents(String amountInCents) {
        this.amountInCents = amountInCents;
    }

    public String getTraceNumber() {
        return traceNumber;
    }

    public void setTraceNumber(String traceNumber) {
        this.traceNumber = traceNumber;
    }

    public String getFiller() {
        return filler;
    }

    public void setFiller(String filler) {
        this.filler = filler;
    }

    public String getLoyaltyRequestCode() {
        return loyaltyRequestCode;
    }

    public void setLoyaltyRequestCode(String loyaltyRequestCode) {
        this.loyaltyRequestCode = loyaltyRequestCode;
    }

    public String getTotalAmountInCents() {
        return totalAmountInCents;
    }

    public void setTotalAmountInCents(String totalAmountInCents) {
        this.totalAmountInCents = totalAmountInCents;
    }

    public String getOtherTransAmountInCents() {
        return otherTransAmountInCents;
    }

    public void setOtherTransAmountInCents(String otherTransAmountInCents) {
        this.otherTransAmountInCents = otherTransAmountInCents;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getPurchaseAmountInCents() {
        return purchaseAmountInCents;
    }

    public void setPurchaseAmountInCents(String purchaseAmountInCents) {
        this.purchaseAmountInCents = purchaseAmountInCents;
    }

    public String getCashBackAmountInCents() {
        return cashBackAmountInCents;
    }

    public void setCashBackAmountInCents(String cashBackAmountInCents) {
        this.cashBackAmountInCents = cashBackAmountInCents;
    }


    public String getCiam() {
        return ciam;
    }

    public void setCiam(String ciam) {
        this.ciam = ciam;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNumber() {
        return outTradeNumber;
    }

    public void setOutTradeNumber(String outTradeNumber) {
        this.outTradeNumber = outTradeNumber;
    }

    public String getOutRefundNumber() {
        return outRefundNumber;
    }

    public void setOutRefundNumber(String outRefundNumber) {
        this.outRefundNumber = outRefundNumber;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getReserve() {
        return reserve;
    }

    public void setReserve(String reserve) {
        this.reserve = reserve;
    }

    public String getCardPromotion() {
        return cardPromotion;
    }

    public void setCardPromotion(String cardPromotion) {
        this.cardPromotion = cardPromotion;
    }

    public String getOtherPromotionCardBin() {
        return otherPromotionCardBin;
    }

    public void setOtherPromotionCardBin(String otherPromotionCardBin) {
        this.otherPromotionCardBin = otherPromotionCardBin;
    }
}
