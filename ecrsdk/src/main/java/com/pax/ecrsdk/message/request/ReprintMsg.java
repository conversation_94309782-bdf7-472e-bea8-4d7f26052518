/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.request;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class ReprintMsg extends Message {
    private ReprintData data = new ReprintData();

    public ReprintMsg() {
        super(Constants.REPRINT);
    }

    @Override
    public boolean checkArg() {
        return data.getTxnId() != null && !data.getTxnId().isEmpty() && Integer.parseInt(data.getTxnId()) > 0;
    }

    public ReprintData getData() {
        return data;
    }

    public void setData(ReprintData data) {
        this.data = data;
    }

    public static class ReprintData {
        private String txnId;

        public String getTxnId() {
            return txnId;
        }

        public void setTxnId(String txnId) {
            this.txnId = txnId;
        }
    }
}
