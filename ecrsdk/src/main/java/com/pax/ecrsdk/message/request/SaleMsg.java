/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.request;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class SaleMsg extends Message {
    private SaleData data = new SaleData();

    public SaleMsg() {
        super(Constants.SALE);
    }

    public SaleData getData() {
        return data;
    }

    public void setData(SaleData data) {
        this.data = data;
    }

    @Override
    public boolean checkArg() {
        return getData().getAmt() != null && !getData().getAmt().isEmpty() && !getData().getAmt().equals("0");
    }

    public static class SaleData {
        private String amt;
        private String tipAmt;
        private String detail;
        private String ecrRef;
        private String txnConsequentialNo;

        public String getAmt() {
            return amt;
        }

        public void setAmt(String amt) {
            this.amt = amt;
        }

        public String getTipAmt() {
            return tipAmt;
        }

        public void setTipAmt(String tipAmt) {
            this.tipAmt = tipAmt;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public String getEcrRef() {
            return ecrRef;
        }

        public void setEcrRef(String ecrRef) {
            this.ecrRef = ecrRef;
        }

        public String getTxnConsequentialNo() {
            return txnConsequentialNo;
        }

        public void setTxnConsequentialNo(String txnConsequentialNo) {
            this.txnConsequentialNo = txnConsequentialNo;
        }
    }
}
