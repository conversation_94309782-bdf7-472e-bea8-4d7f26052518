/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2018-1-24
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.response;

import com.pax.ecrsdk.Constants;

public class RespDownloadKeyMsg extends RespBaseMsg {
    private RespDownloadKeyData data = new RespDownloadKeyData();

    public RespDownloadKeyMsg() {
        super(Constants.DOWNLOAD_KEY);
    }

    @Override
    public RespDownloadKeyData getData() {
        return data;
    }

    public void setData(RespDownloadKeyData data) {
        this.data = data;
    }

    public static class RespDownloadKeyData extends RespBaseMsg.RespBaseData {
        private String rsaModules;
        private String rsaPvkExp;
        private String rsaPukExp;

        public String getRsaModules() {
            return rsaModules;
        }

        public void setRsaModules(String rsaModules) {
            this.rsaModules = rsaModules;
        }

        public String getRsaPvkExp() {
            return rsaPvkExp;
        }

        public void setRsaPvkExp(String rsaPvkExp) {
            this.rsaPvkExp = rsaPvkExp;
        }

        public String getRsaPukExp() {
            return rsaPukExp;
        }

        public void setRsaPukExp(String rsaPukExp) {
            this.rsaPukExp = rsaPukExp;
        }
    }
}
