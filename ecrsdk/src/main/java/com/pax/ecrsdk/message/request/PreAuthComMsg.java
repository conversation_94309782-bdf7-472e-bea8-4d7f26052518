/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.request;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class PreAuthComMsg extends Message {
    private PreAuthComData data = new PreAuthComData();

    public PreAuthComMsg() {
        super(Constants.PREAUTH_COMPLETE);
    }

    public PreAuthComData getData() {
        return data;
    }

    public void setData(PreAuthComData data) {
        this.data = data;
    }

    @Override
    public boolean checkArg() {
        return getData().getAmt() != null && !getData().getAmt().isEmpty() && !getData().getAmt().equals("0") &&
                getData().getRrn() != null && !getData().getRrn().isEmpty() &&
                getData().getAppCode() != null && !getData().getAppCode().isEmpty();
    }

    public static class PreAuthComData {
        private String amt;
        private String rrn;
        private String appCode;
        private String pwd;

        public String getAmt() {
            return amt;
        }

        public void setAmt(String amt) {
            this.amt = amt;
        }

        public String getRrn() {
            return rrn;
        }

        public void setRrn(String rrn) {
            this.rrn = rrn;
        }

        public String getAppCode() {
            return appCode;
        }

        public void setAppCode(String appCode) {
            this.appCode = appCode;
        }

        public String getPwd() {
            return pwd;
        }

        public void setPwd(String pwd) {
            this.pwd = pwd;
        }
    }
}
