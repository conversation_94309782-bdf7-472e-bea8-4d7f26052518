/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2017 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-16
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */

package com.pax.ecrsdk.message.request;

import com.pax.ecrsdk.Constants;
import com.pax.ecrsdk.message.Message;

public class OfflineMsg extends Message {
    private OfflineData data = new OfflineData();

    public OfflineMsg() {
        super(Constants.OFFLINE);
    }

    public OfflineData getData() {
        return data;
    }

    public void setData(OfflineData data) {
        this.data = data;
    }

    @Override
    public boolean checkArg() {
        return getData().getAmt() != null && !getData().getAmt().isEmpty() && !getData().getAmt().equals("0") &&
                getData().getAppCode() != null && !getData().getAppCode().isEmpty();
    }

    public static class OfflineData {
        private String amt;
        private String ecrRef;
        private String rrn;
        private String appCode;
        private String detail;
        private String txnID;

        public String getAmt() {
            return amt;
        }

        public void setAmt(String amt) {
            this.amt = amt;
        }

        public String getEcrRef() {
            return ecrRef;
        }

        public void setEcrRef(String ecrRef) {
            this.ecrRef = ecrRef;
        }

        public String getRrn() {
            return rrn;
        }

        public void setRrn(String rrn) {
            this.rrn = rrn;
        }

        public String getAppCode() {
            return appCode;
        }

        public void setAppCode(String appCode) {
            this.appCode = appCode;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public String getTxnID() {
            return txnID;
        }

        public void setTxnID(String txnID) {
            this.txnID = txnID;
        }
    }
}
