/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-12-11
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.device;

public abstract class Device {
    private String commType;

    Device(String commType) {
        this.commType = commType;
    }

    public String getCommType() {
        return commType;
    }
}
