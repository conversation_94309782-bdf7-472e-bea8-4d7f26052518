/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-12-11
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.pax.ecrsdk.device;

import android.util.Log;

import java.io.IOException;
import java.io.InputStream;

public class TcpDevice extends Device {
    private String ip;
    private String password;
    private int port;
    private InputStream kClientStream;
    private InputStream tClientStream;
    private boolean enableSsl;

    public TcpDevice(String ip, int port) {
        super("tcp");
        this.ip = ip;
        this.port = port;
    }

    public String getIp() {
        return ip;
    }

    public int getPort() {
        return port;
    }

    public InputStream getkClientStream() {
        if (kClientStream == null) {
            return null;
        }

        try {
            kClientStream.reset();
        } catch (IOException e) {
            Log.e("log", "", e);
        }
        return kClientStream;
    }

    public void setkClientStream(InputStream kClientStream) {
        if (kClientStream != null) {
            kClientStream.mark(0);
        }
        this.kClientStream = kClientStream;
    }

    public InputStream gettClientStream() {
        if (tClientStream == null) {
            return null;
        }

        try {
            tClientStream.reset();
        } catch (IOException e) {
            Log.e("log", "", e);
        }
        return tClientStream;
    }

    public void settClientStream(InputStream tClientStream) {
        if (tClientStream != null) {
            tClientStream.mark(0);
        }
        this.tClientStream = tClientStream;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isEnableSsl() {
        return enableSsl;
    }

    public void setEnableSsl(boolean enableSsl) {
        this.enableSsl = enableSsl;
    }
}
