# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4068m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#android.buildCacheDir=E\:\\android-build-cache
#android.enableBuildCache=true
DATABASE_PWD="Paxsz123!~"
android.useAndroidX=true
android.enableJetifier=true
org.gradle.daemon=false
android.jetifier.blacklist=bcprov-jdk15on
#android.debug.obsoleteApi=true