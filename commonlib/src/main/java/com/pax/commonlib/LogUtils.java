package com.pax.commonlib;

import android.util.Log;

/**
 * Created by liaosong on 2018/6/28.
 */

public class LogUtils {
    private static final boolean IS_DEBUG = BuildConfig.LOG_DEBUG;
    private static final String TAG = "ECR_LOG";

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     */
    public static void e(String tag, Object msg) {
        if (IS_DEBUG) {
            Log.e(tag, "" + msg);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     */
    public static void w(String tag, Object msg) {
        if (IS_DEBUG) {
            Log.w(tag, "" + msg);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     */
    public static void i(String tag, Object msg) {
        if (IS_DEBUG) {
            Log.i(tag, "" + msg);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     */
    public static void d(String tag, Object msg) {
        if (IS_DEBUG) {
            Log.d(tag, "" + msg);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     */
    public static void v(String tag, Object msg) {
        if (IS_DEBUG) {
            Log.v(tag, "" + msg);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     * @param th Throwable
     */
    public static void e(String tag, String msg, Throwable th) {
        if (IS_DEBUG) {
            Log.e(tag, msg, th);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     * @param th Throwable
     */
    public static void w(String tag, String msg, Throwable th) {
        if (IS_DEBUG) {
            Log.w(tag, msg, th);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     * @param th Throwable
     */
    public static void i(String tag, String msg, Throwable th) {
        if (IS_DEBUG) {
            Log.i(tag, msg, th);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     * @param th Throwable
     */
    public static void d(String tag, String msg, Throwable th) {
        if (IS_DEBUG) {
            Log.d(tag, msg, th);
        }
    }

    /**
     * Log
     *
     * @param tag String
     * @param msg String
     * @param th Throwable
     */
    public static void v(String tag, String msg, Throwable th) {
        if (IS_DEBUG) {
            Log.v(tag, msg, th);
        }
    }

    /**
     * log d
     *
     * @param obj obj
     * @param msg msg
     */
    public static void d(Object obj, String msg) {
        if (IS_DEBUG) {
            Log.d(TAG, "<<" + obj.getClass().getSimpleName() + ">> " + msg);
        }
    }

    public static void d(String tag, String msg) {
        if (IS_DEBUG) {
            Log.d(tag, msg);
        }
    }

    /**
     * loe error
     *
     * @param obj obj
     * @param msg msg
     */
    public static void e(Object obj, String msg) {
        if (IS_DEBUG) {
            Log.e(TAG, "<<" + obj.getClass().getSimpleName() + ">> " + msg);
        }
    }

    public static void e(String tag, String msg) {
        if (IS_DEBUG) {
            Log.e(tag, msg);
        }
    }
}
