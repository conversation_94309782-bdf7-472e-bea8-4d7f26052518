/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20200309  	         xieYb                   Create
 * ===========================================================================================
 */
package com.pax.commonlib.utils;

public class BinaryConvertHelper {
    public static int bytesToInt(byte[] src, int offset) {
        int value;
        value = (int) (((src[offset] & 0xFF) << 24)
                | ((src[offset + 1] & 0xFF) << 16)
                | ((src[offset + 2] & 0xFF) << 8)
                | (src[offset + 3] & 0xFF));
        return value;
    }
    public static int bytesToShort(byte[] src, int offset) {
        int value;
        value = (int) (((src[offset] & 0xFF) << 8)
                | (src[offset + 1] & 0xFF));
        return value;
    }
    //正常是8个byte,这里得到64个
    public static byte[] pubAsc2Bcd(byte[] dataIn){
        if (dataIn == null || dataIn.length == 0){
            return new byte[0];
        }
        int len = dataIn.length;
        byte tmp;
        byte[] bcdDataIn = new byte[64];
        for (int i = 0;i<len;i+=2){
            tmp = dataIn[i];
            if (tmp > '9'){
                tmp = (byte) (tmp - 'A'+ 0x0A);
            }else {
                tmp &=0x0F;
            }
            bcdDataIn[i/2] = (byte) (tmp << 4);
            tmp = bcdDataIn[i+1];
            if (tmp > '9'){
                tmp = (byte) (tmp - 'A' + 0x0A);
            }else {
                tmp &= 0x0F;
            }
            bcdDataIn[i/2] |= tmp;
        }
        return bcdDataIn;
    }
    //此方法有问题
    public static byte[] pubBcd2Asc(byte[] xorData){
        if (xorData == null || xorData.length == 0){
            return new byte[0];
        }
        byte[] ARRAY_OF_CHAR = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        int len = xorData.length;
        byte[] ascPin = new byte[24];
        for (int i = 0;i<len;i++){
            ascPin[2*i] = ARRAY_OF_CHAR[(xorData[i]>>4)];
            ascPin[2*i +1] = ARRAY_OF_CHAR[(xorData[i] & 0x0F)];
        }
        return ascPin;
    }
    //正常是16个byte,这里得到25个byte
    public static String pubBcd2Asc2(byte[] bytes) {
        StringBuffer temp = new StringBuffer(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            temp.append((byte) ((bytes[i] & 0xf0) >>> 4));
            temp.append((byte) (bytes[i] & 0x0f));
        }
        return temp.toString().substring(0, 1).equalsIgnoreCase("0") ? temp
                .toString().substring(1) : temp.toString();
    }

}
