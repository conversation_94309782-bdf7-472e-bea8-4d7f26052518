ECLIPSE ANDROID PROJECT IMPORT SUMMARY
======================================

Risky Project Location:
-----------------------
The tools *should* handle project locations in any directory. However,
due to bugs, placing projects in directories containing spaces in the
path, or characters like ", ' and &, have had issues. We're working to
eliminate these bugs, but to save yourself headaches you may want to
move your project to a location where this is not a problem.
E:\Linhb\Andriod AS\PaxUpStd
                -           

Ignored Files:
--------------
The following files were *not* copied into the new Gradle project; you
should evaluate whether these are still needed in your project and if
so manually move them:

* ic_launcher-web.png
* proguard-project.txt

Replaced Jars with Dependencies:
--------------------------------
The importer recognized the following .jar files as third party
libraries and replaced them with Gradle dependencies instead. This has
the advantage that more explicit version information is known, and the
libraries can be updated automatically. However, it is possible that
the .jar file in your project was of an older version than the
dependency we picked, which could render the project not compileable.
You can disable the jar replacement in the import wizard and try again:

android-support-v4.jar => com.android.support:support-v4:19.1.0

Moved Files:
------------
Android Gradle projects use a different directory structure than ADT
Eclipse projects. Here's how the projects were restructured:

* AndroidManifest.xml => app\src\main\AndroidManifest.xml
* assets\ => app\src\main\assets\
* libs\ant.jar => app\libs\ant.jar
* libs\PaxNeptuneApi_V1.00.00_TEST_20161031_092900.jar => app\libs\PaxNeptuneApi_V1.00.00_TEST_20161031_092900.jar
* libs\sqlcipherandroid_V20160922.jar => app\libs\sqlcipherandroid_V20160922.jar
* libs\zxing.jar => app\libs\zxing.jar
* lint.xml => app\lint.xml
* res\ => app\src\main\res\
* src\ => app\src\main\java\
* src\com\pax\pay\service\aidl\PayHelper.aidl => app\src\main\aidl\com\pax\pay\service\aidl\PayHelper.aidl

Next Steps:
-----------
You can now build the project. The Gradle project needs network
connectivity to download dependencies.

Bugs:
-----
If for some dupReason your project does not build, and you determine that
it is due to a bug or limitation of the Eclipse to Gradle importer,
please file a bug at http://b.android.com with category
Component-Tools.

(This import summary is for your information only, and can be deleted
after import once you are satisfied with the results.)
