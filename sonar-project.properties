# Root project information
sonar.projectKey=HK_HASE_EDC_A
sonar.projectName=HK_HASE_EDC_A
sonar.projectVersion=1.00.00
sonar.modules=app,amex,eemv,sdkwrapper,expandablerecyclerview,opensdk,appstore,commonlib,ecrsdk,glwrapper
#sonar.modules=app,amex,eemv,glwrapper,sdkwrapper,expandablerecyclerview,opensdk,appstore
# Some properties that will be inherited by the modules
#app module
app.sonar.sources=src/main/java
#app.sonar.tests=src/test/java
app.sonar.java.binaries=build/intermediates/javac
app.sonar.language=java

#amex module
amex.sonar.sources=src/main/java
#amex.sonar.tests=src/test/java
amex.sonar.java.binaries=build/intermediates/javac
amex.sonar.language=java

#eemv module
eemv.sonar.sources=src/main/java
#eemv.sonar.tests=src/test/java
eemv.sonar.java.binaries=build/intermediates/javac
eemv.sonar.language=java

##glwrapper module
#glwrapper.sonar.sources=src/main/java
##glwrapper.sonar.tests=src/test/java
#glwrapper.sonar.java.binaries=build/intermediates/javac/standardDebug/compileStandardDebugJavaWithJavac/classes
#glwrapper.sonar.language=java

#sdkwrapper module
sdkwrapper.sonar.sources=src/main/java
#sdkwrapper.sonar.tests=src/test/java
sdkwrapper.sonar.java.binaries=build/intermediates/javac
sdkwrapper.sonar.language=java

#expandablerecyclerview module
expandablerecyclerview.sonar.sources=src/main/java
#expandablerecyclerview.sonar.tests=src/test/java
expandablerecyclerview.sonar.java.binaries=build/intermediates/javac
expandablerecyclerview.sonar.language=java
#opensdk module
opensdk.sonar.sources=src/main/java
#opensdk.sonar.tests=src/test/java
opensdk.sonar.java.binaries=build/intermediates/javac
opensdk.sonar.language=java
#appstore module
appstore.sonar.sources=src/main/java
#appstore.sonar.tests=src/test/java
appstore.sonar.java.binaries=build/intermediates/javac
appstore.sonar.language=java

#commonlib module
commonlib.sonar.sources=src/main/java
#appstore.sonar.tests=src/test/java
commonlib.sonar.java.binaries=build/intermediates/javac
commonlib.sonar.language=java

#ecrsdk module
ecrsdk.sonar.sources=src/main/java
#appstore.sonar.tests=src/test/java
ecrsdk.sonar.java.binaries=build/intermediates/javac
ecrsdk.sonar.language=java

#glwrapper module
glwrapper.sonar.sources=src/main/java
#appstore.sonar.tests=src/test/java
glwrapper.sonar.java.binaries=build/intermediates/javac
glwrapper.sonar.language=java

#Tells SonarQube where the unit tests execution reports are
#sonar.junit.reportsPath=app/build/test-results/testDebugUnitTest

#Tells SonarQube where the unit tests code coverage report is
#sonar.jacoco.reportPath=app/build/jacoco/testDebugUnitTest.exec

#Tells SonarQube where the integration tests code coverage report is
#sonar.jacoco.itReportPath=app/reports/jacoco/jacoco-it.exec

# Encoding of the source files
sonar.sourceEncoding=UTF-8