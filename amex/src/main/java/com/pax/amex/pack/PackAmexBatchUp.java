package com.pax.amex.pack;

import androidx.annotation.NonNull;

import com.pax.amex.constant.AmexConstant;
import com.pax.amex.modle.AmexTransData;
import com.pax.amex.pack.ipacker.AmexListener;
import com.pax.amex.utils.Utils;
import com.pax.gl.pack.exception.Iso8583Exception;

/*
 * ============================================================================
 * = COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2018-? PAX Technology, Inc. All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                 Author	                        Action
 * 29/10/2018  	         XuShuang           	Create/Add/Modify/Delete
 * ============================================================================
 */
public class PackAmexBatchUp extends PackAmex {
    public PackAmexBatchUp(AmexListener listener) {
        super(listener);
    }

    /**
     * pack
     * 0320
     *
     * @param transData input transaction data structure
     * @return output data
     */
    @NonNull
    @Override
    public byte[] pack(@NonNull AmexTransData transData) {
        try {
            setMandatoryData(transData);
            setSelfData(transData);
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }
        int piiType = AmexConstant.TYPE_NO;
        if (transData.getEnterMode() == AmexTransData.EnterMode.MANUAL) {
            piiType = AmexConstant.TYPE_MANUAL;
        } else if (transData.getEnterMode() == AmexTransData.EnterMode.SWIPE) {
            piiType = AmexConstant.TYPE_SWIPE;
        }

        return pack(piiType);
    }

    private void setSelfData(AmexTransData transData) {
        try {
            setBitData2(transData);
            setBitData4(transData);
            setBitData12(transData);
            setBitData14(transData);
            setBitData22(transData);
            setBitData25(transData);
            setBitData37(transData);
            setBitData38(transData);
            setBitData54(transData);
            setBitData60(transData);
            setBitData61(transData);
            setBitData62(transData);
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    protected void setBitData3(@NonNull AmexTransData transData) throws Iso8583Exception {
        //TO DO: If debit card, should modify to "004000", but debit card just use for local,So HongKong needn't support debit card.
        switch (transData.getOrigTransType()) {
            case SALE:
            case OFFLINE_SALE:
                transData.setProcCode("004000");
                break;
            case REFUND:
                transData.setProcCode("204000");
                break;
            default:
                transData.setProcCode("004000");
        }
        setBitData("3", transData.getProcCode());
    }

    @Override
    protected void setBitData4(@NonNull AmexTransData transData) throws Iso8583Exception {
        if (transData.getTransState() == AmexTransData.AmexETransStatus.VOIDED) {
            setBitData("4", Utils.getPaddedNumber(0, 12));
            return;
        }
        super.setBitData4(transData);
    }

    @Override
    protected void setBitData11(@NonNull AmexTransData transData) throws Iso8583Exception {
        //0320报文F11要使用交易当下的,而不是使用原交易的
        setBitData("11", Utils.getPaddedNumber(transData.getCurTraceNo(), 6));
    }

    @Override
    protected void setBitData12(@NonNull AmexTransData transData) throws Iso8583Exception {
        String temp = transData.getDateTime();
        if (temp != null && !temp.isEmpty()) {
            String date = temp.substring(4, 8);
            String time = temp.substring(8);
            setBitData("12", time);
            setBitData("13", date);
        }
    }

    @Override
    protected void setBitData25(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("25", "00");
        if(transData.isBelowFloorLimitTerminalApproved() && transData.getTransState() != AmexTransData.AmexETransStatus.VOIDED){
            setBitData("25", "06");
        }
    }

    @Override
    protected void setBitData38(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("38", transData.getAuthCode());
        if(transData.isBelowFloorLimitTerminalApproved() && !transData.isReferralApproved()){ //Referral requires other valid format
            setBitData("38", "Y1    ");
        }
    }

    //MTI & STAN
    protected void setBitData60(@NonNull AmexTransData transData) throws Iso8583Exception {
        String mti;
        //TO DO
        switch (transData.getOrigTransType()) {
            case PRE_AUTH:
                mti = "0100";
                break;
            case SALE:
            case VOID:
            case REFUND:
                mti = "0200";
                break;
            case OFFLINE_SALE:
                mti = "0220";
                break;
            default:
                mti = "0200";
        }
        if(transData.isBelowFloorLimitTerminalApproved() && transData.getTransState() != AmexTransData.AmexETransStatus.VOIDED){
            mti = "0220";
        }
        setBitData("60", mti + Utils.getPaddedNumber(transData.getOrigTransNo(), 6) + "            ");
    }

    @Override
    protected void setBitData62(@NonNull AmexTransData transData) throws Iso8583Exception {
        if (transData.getTransState() == AmexTransData.AmexETransStatus.VOIDED) {
            setBitData("62", Utils.getPaddedNumber(transData.getOrigInvoiceNo(), 6));
        }
        setBitData("62", Utils.getPaddedNumber(transData.getInvoiceNo(), 6));
    }

}
