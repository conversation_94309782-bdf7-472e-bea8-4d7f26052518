package com.pax.amex.pack;

import androidx.annotation.NonNull;
import com.pax.amex.constant.AmexConstant;
import com.pax.amex.modle.AmexTransData;
import com.pax.amex.pack.ipacker.AmexListener;
import com.pax.gl.pack.exception.Iso8583Exception;

public class PackAmexRefund extends PackAmex {
    /**
     * constructor
     *
     * @param listener AmexListener
     */
    public PackAmexRefund(AmexListener listener) {
        super(listener);
    }

    /**
     * pack AmexIso8583
     *
     * @param transData AmexTransData
     */
    @NonNull
    @Override
    public byte[] pack(@NonNull AmexTransData transData) {
        try {
            setMandatoryData(transData);
            setSelfData(transData);
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }
        int piiType = AmexConstant.TYPE_NO;
        if (transData.getEnterMode() == AmexTransData.EnterMode.MANUAL) {
            try {
                setBitData2(transData);
                setBitData14(transData);
            } catch (Iso8583Exception e) {
                e.printStackTrace();
            }
            piiType = AmexConstant.TYPE_MANUAL;
        } else if (transData.getEnterMode() == AmexTransData.EnterMode.SWIPE) {
            piiType = AmexConstant.TYPE_SWIPE;
        }

        return pack(piiType);
    }

    /**
     * set field
     *
     * @param transData AmexTransData
     */
    private void setSelfData(AmexTransData transData) {
        try {
            setBitData3(transData);
            setBitData4(transData);
            setBitData22(transData);
            setBitData25(transData);
            setBitData35(transData);
            setBitData45(transData);
            setBitData52(transData);
            setBitData54(transData);
            setField55(transData);
            setBitData62(transData);
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void setBitData3(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("3", "204000");
    }
}
