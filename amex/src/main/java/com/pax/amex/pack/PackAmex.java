package com.pax.amex.pack;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.pax.amex.PackAmexManager;
import com.pax.amex.constant.AmexConstant;
import com.pax.amex.modle.AmexTransData;
import com.pax.amex.pack.ipacker.AmexListener;
import com.pax.amex.pack.ipacker.IPacker;
import com.pax.amex.utils.Utils;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.utils.Tools;
import com.pax.gl.pack.IIso8583;
import com.pax.gl.pack.ITlv;
import com.pax.gl.pack.exception.Iso8583Exception;
import com.pax.gl.pack.exception.TlvException;
import com.pax.glwrapper.convert.IConvert;
import com.pax.jemv.amex.model.TransactionMode;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * ============================================================================
 * = COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2018-? PAX Technology, Inc. All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                 Author	                        Action
 * 19/09/2018  	         XuShuang           	Create/Add/Modify/Delete
 * ============================================================================
 */
public abstract class PackAmex implements IPacker<AmexTransData, byte[]> {
    protected static final String TAG = PackAmex.class.getSimpleName();

    private IIso8583 iso8583;
    private IIso8583.IIso8583Entity entity;
    private AmexListener listener;

    private static final Map<AmexTransData.EnterMode, String> enterModeMap = new EnumMap<>(AmexTransData.EnterMode.class);

    static {
        enterModeMap.put(AmexTransData.EnterMode.MANUAL, "51");
        enterModeMap.put(AmexTransData.EnterMode.SWIPE, "52");
        enterModeMap.put(AmexTransData.EnterMode.INSERT, "55");
        enterModeMap.put(AmexTransData.EnterMode.CLSS, "59");
        enterModeMap.put(AmexTransData.EnterMode.FALLBACK, "62");
        enterModeMap.put(AmexTransData.EnterMode.QR, "53");
    }


    public PackAmex(AmexListener listener) {
        this.listener = listener;
        initEntity();
    }

    /**
     * 获取打包entity
     *
     * @return
     */
    private void initEntity() {
        iso8583 = PackAmexManager.getInstance().getIso8583();
        try {
            entity = iso8583.getEntity();
            entity.loadTemplate(PackAmexManager.getInstance().getContext().getResources().getAssets().open("amex.xml"));
        } catch (Iso8583Exception | IOException | XmlPullParserException e) {
            Log.e(TAG, "", e);
        }
    }

    protected void setBitData(String field, String value) throws Iso8583Exception {
        if (value != null && !value.isEmpty()) {
            entity.setFieldValue(field, value);
        }
    }

    protected void setBitData(String field, byte[] value) throws Iso8583Exception {
        if (value != null && value.length > 0) {
            entity.setFieldValue(field, value);
        }
    }

    @NonNull
    public byte[] pack(int piiType) {
        try {
            if (piiType == AmexConstant.TYPE_MANUAL || piiType == AmexConstant.TYPE_SWIPE) {
                //set field  59   and remove 2, 14,55, 35, 45
                //setBitData59(piiType);
            }

            return iso8583.pack();
        } catch (Iso8583Exception e) {
            Log.e(TAG, "", e);
        }
        return "".getBytes();
    }

    /**
     * generate 59 field data
     *
     * @param
     */
    private void setBitData59(int type) {
        //E800
        byte[] bitMap = generate59Head(type);
        byte[] cipherText = generateCipherText(type);
        int bitMapLen = bitMap.length;
        assert cipherText != null;
        int cipherTextLen = cipherText.length;
        byte[] field59 = new byte[bitMapLen + cipherTextLen];
        System.arraycopy(bitMap, 0, field59, 0, bitMapLen);
        System.arraycopy(cipherText, 0, field59, bitMapLen, cipherTextLen);

        try {
            entity.setFieldValue("59", field59);
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }
    }

    private byte[] generate59Head(int type) {
        int a = 0;
        if (type == AmexConstant.TYPE_MANUAL) {
            if (entity.hasField("2")) {
                a |= 32768;
            }
            if (entity.hasField("14")) {
                a |= 16384;
            }
            if (entity.hasField("55")) {
                a |= 2048;
            }
        } else if (type == AmexConstant.TYPE_SWIPE) {
            if (entity.hasField("35")) {
                a |= 8192;
            }
            if (entity.hasField("45")) {
                a |= 4096;
            }
            if (entity.hasField("55")) {
                a |= 2048;
            }
        }

        Log.d(TAG, "generate59Head: " + PackAmexManager.getInstance().getConvert().bcdToStr(Utils.IntToByte(a, 2)));
        return Utils.IntToByte(a, 2);
    }

    private byte[] generateCipherText(int type) {
        byte[] temp = null;
        if (type == AmexConstant.TYPE_MANUAL) {
            temp = new byte[]{2, 14, 55};
        } else if (type == AmexConstant.TYPE_SWIPE) {
            temp = new byte[]{55, 35, 45};
        }
        ITlv tlv = PackAmexManager.getInstance().getPacker().getTlv();
        ITlv.ITlvDataObjList tlvList = tlv.createTlvDataObjectList();
        byte[] buff;
        byte[] retBuff;

        if (temp != null) {
            for (byte item : temp) {
                addDataObj(tlvList, item);
            }
        }

        try {
            buff = tlv.pack(tlvList);
            int len = buff.length;
            if (len % 8 != 0) {
                len = ((len + 7) / 8) * 8;
            }

            retBuff = Arrays.copyOf(buff, len);
            Log.d(TAG, "CipherText: " + PackAmexManager.getInstance().getConvert().bcdToStr(buff));
        } catch (TlvException e) {
            e.printStackTrace();
            return null;
        }

        return listener.onEncPii(retBuff);  // encrypt
    }

    private void addDataObj(ITlv.ITlvDataObjList list, int fieldId) {
        if (list == null) {
            return;
        }

        ITlv.ITlvDataObj tlv = getOneFieldTlv(fieldId);
        if (tlv != null) {
            list.addDataObj(tlv);
        }
    }

    private ITlv.ITlvDataObj getOneFieldTlv(int filedId){
        byte[] packData;
        ITlv.ITlvDataObj tlv = null;
        if (entity.hasField(Integer.toString(filedId))) {
            List<String> list = new ArrayList<>();
            list.add(Integer.toString(filedId));
            try {
                packData = iso8583.pack(list);
            } catch (Iso8583Exception e) {
                e.printStackTrace();
                return null;
            }
            tlv = PackAmexManager.getInstance().getPacker().getTlv().createTlvDataObject();
            String tagStr = Integer.toOctalString(filedId);
            int tagInt = Integer.parseInt(tagStr, 16);
            tlv.setTag(tagInt);
            tlv.setValue(packData);
            try {
                entity.resetFieldValue(Integer.toString(filedId));
            } catch (Iso8583Exception e) {
                e.printStackTrace();
            }
        }
        return tlv;
    }

    @Override
    public int unpack(@NonNull AmexTransData transData, final byte[] rsp) {

        HashMap<String, byte[]> map;
        try {
            map = iso8583.unpack(rsp, true);
            // 调试信息， 日志输入解包后数据
            entity.dump();
        } catch (Iso8583Exception e) {
            Log.e(TAG, "", e);
            return TransResult.ERR_UNPACK;
        }

        // 报文头
        byte[] header = map.get("h");
        // TPDU检查
        String rspTpdu = new String(header).substring(0, 10);
        String reqTpdu = transData.getTpdu();
        if (!rspTpdu.substring(2, 6).equals(reqTpdu.substring(6, 10))
                || !rspTpdu.substring(6, 10).equals(reqTpdu.substring(2, 6))) {
            return TransResult.ERR_UNPACK;
        }
        transData.setHeader(new String(header).substring(10));

        byte[] buff;
        // 检查39域应答码
        buff = map.get("39");
        if (buff == null) {
            return TransResult.ERR_PACKET;
        }
        transData.setResponseCode(buff);

        // 检查返回包的关键域， 包含field4
        boolean isCheckAmt = true;

        //modified by KevinLiu 20190321
        AmexTransData.AmexTransType transType = transData.getTransType();
        if (transType == AmexTransData.AmexTransType.SETTLEMENT
                || transType == AmexTransData.AmexTransType.SETTLEMENT_END
                || transType == AmexTransData.AmexTransType.VOID) {
            isCheckAmt = false;
        }

//        int ret = checkRecvData(map, transData, isCheckAmt);
//        if (ret != TransResult.SUCC) {
//            return ret;
//        }

        // field 2 主账号


        // field 3 交易处理码
        buff = map.get("3");
        if (buff != null && buff.length > 0) {
            String origField3 = transData.getField3();
            if (origField3 != null && !origField3.isEmpty() && !origField3.equals(new String(buff))) {
                return TransResult.ERR_PROC_CODE;
            }
        }

        // field 4 交易金额
        buff = map.get("4");
        if (buff != null && buff.length > 0) {
            transData.setAmount(new String(buff));
        }

        // field 11 流水号
        buff = map.get("11");
        if (buff != null && buff.length > 0) {
            transData.setTraceNo(Utils.parseLongSafe(new String(buff), -1));
        }

        // field 13 受卡方所在地日期
        String dateTime = "";
        buff = map.get("13");
        if (buff != null) {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            dateTime = year + new String(buff);
        }

        // field 12 受卡方所在地时间
        buff = map.get("12");
        if (buff != null && buff.length > 0) {
            transData.setDateTime(dateTime + new String(buff));
        }

        // field 14 卡有效期
        buff = map.get("14");
        if (buff != null && buff.length > 0) {
            String expDate = new String(buff);
            if (!"0000".equals(expDate)) {
                transData.setExpDate(expDate);
            }
        }

        // field 24
        buff = map.get("24");
        if (buff != null && buff.length > 0) {
            String nii = new String(buff);
            transData.setNii(nii);
        }

        // field 37 检索参考号
        buff = map.get("37");
        if (buff != null && buff.length > 0) {
            transData.setRefNo(new String(buff));
        }

        // field 38 授权码
        buff = map.get("38");
        if (buff != null && buff.length > 0) {
            transData.setAuthCode(new String(buff));
        }

        buff = map.get("55");
        if (buff != null && buff.length > 0) {
            transData.setRecvIccData(Tools.bcd2Str(buff));
        }


        return TransResult.SUCC;
    }

    /**
     * 检查请求和返回的关键域field4, field11, field41, field42
     *
     * @param map        解包后的map
     * @param transData  请求
     * @param isCheckAmt 是否检查field4
     * @return 检查接收数据
     */
    private int checkRecvData(@NonNull HashMap<String, byte[]> map, @NonNull AmexTransData transData, boolean isCheckAmt) {
        // 交易金额
        if (isCheckAmt && !checkAmount(map, transData)) {
            return TransResult.ERR_TRANS_AMT;
        }

        // 校验11域
        if (!checkTraceNo(map, transData))
            return TransResult.ERR_TRACE_NO;

        // 校验终端号
        if (!checkTerminalId(map, transData))
            return TransResult.ERR_TERM_ID;

        // 校验商户号
        if (!checkMerchantId(map, transData))
            return TransResult.ERR_MERCH_ID;

        return TransResult.SUCC;
    }

    private boolean checkAmount(@NonNull final HashMap<String, byte[]> map, @NonNull final AmexTransData transData) {
        byte[] data = map.get("4");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return Utils.parseLongSafe(temp, 0) == Utils.parseLongSafe(transData.getAmount(), 0);
        }
        return true;
    }

    private boolean checkTraceNo(@NonNull final HashMap<String, byte[]> map, @NonNull final AmexTransData transData) {
        byte[] data = map.get("11");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(Utils.getPaddedNumber(transData.getTraceNo(), 6));
        }
        return true;
    }

    private boolean checkTerminalId(@NonNull final HashMap<String, byte[]> map, @NonNull final AmexTransData transData) {
        byte[] data = map.get("41");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(transData.getTerminalId());
        }
        return true;
    }

    private boolean checkMerchantId(@NonNull final HashMap<String, byte[]> map, @NonNull final AmexTransData transData) {
        byte[] data = map.get("42");
        if (data != null && data.length > 0) {
            String temp = new String(data);
            return temp.equals(transData.getMerchantId());
        }
        return true;
    }

    /**
     * 设置必须的数据
     *
     * @param transData 传入transData
     * @throws Iso8583Exception 抛出异常
     */
    protected void setMandatoryData(@NonNull AmexTransData transData) throws Iso8583Exception {
        // h
        String pHeader = transData.getTpdu() + transData.getHeader();
        entity.setFieldValue("h", pHeader);
        // m
        if (transData.getReversalStatus() == AmexTransData.ReversalStatus.REVERSAL) {
            entity.setFieldValue("m", transData.getDupMsgType());
        } else {
            entity.setFieldValue("m", transData.getMsgType());
        }


        // field 3/25 交易处理码/服务码
        setBitData3(transData);

        // field 11 流水号
        setBitData11(transData);
        // field 24 NII
        setBitData24(transData);

        // field 41 终端号
        setBitData41(transData);

        // field 42 商户号
        setBitData42(transData);

        // field 61 Product/Item Descriptor Codes
//        setBitData61(transData);   //removed by KevinLiu 20190320

    }

    /**
     * 设置field 2, 4, 11, 14, 22, 25，35, 52, 53
     *
     * @param transData
     * @return
     */
    void setCommonData(@NonNull AmexTransData transData) throws Iso8583Exception {
        AmexTransData.EnterMode enterMode = transData.getEnterMode();

        if (enterMode == AmexTransData.EnterMode.MANUAL) {
            // 手工输入
            // [14]有效期
//            setBitData14(transData);

        } else if (enterMode == AmexTransData.EnterMode.SWIPE || enterMode == AmexTransData.EnterMode.FALLBACK) {
            // 刷卡

            // [35]二磁道
            setBitData35(transData);
            //一磁道 如果有就传
            setBitData45(transData);


        } else if (enterMode == AmexTransData.EnterMode.INSERT || enterMode == AmexTransData.EnterMode.CLSS) {
            // [2]主账号
//            setBitData2(transData);

            // [14]有效期
            //setBitData14(transData);

            // [35]二磁道
            setBitData35(transData);
            //一磁道 如果有就传
            setBitData45(transData);
            setBitData61(transData);
        }

        // [2]主账号
        setBitData2(transData);
        // field amount
        setBitData4(transData);
        // [14]有效期
        setBitData14(transData);
        // field 22 服务点输入方式码
        setBitData22(transData);
        setBitData25(transData);
        // [52]PIN 如果有pin就传
        setBitData52(transData);
        //[54]tip amount
        setBitData54(transData);

    }

    /**
     * 设置金融类数据
     * <p>
     * 设置域
     * <p>
     * field 2, field 4,field 14, field 22,field 23,field 26, field 35,field 49, field 52,field 53, field 55
     *
     * @param transData
     */
    void setFinancialData(@NonNull AmexTransData transData) throws Iso8583Exception {
        setMandatoryData(transData);
        setCommonData(transData);
        // field 55 ICC
        setField55(transData);
    }

    void setField55(AmexTransData transData) throws Iso8583Exception {
        if (transData.getEnterMode() == AmexTransData.EnterMode.FALLBACK || transData.getEnterMode() == AmexTransData.EnterMode.SWIPE || transData.getEnterMode() == AmexTransData.EnterMode.MANUAL) {
            return;
        }
        if (transData.getEnterMode() == AmexTransData.EnterMode.CLSS && transData.getTransactionPath() == TransactionMode.AE_MAGMODE) {
            return;
        }
        setBitData55(transData);
    }

    protected void setBitData2(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("2", transData.getPan());
    }

    protected void setBitData3(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("3", transData.getProcCode());
    }

    protected void setBitData4(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("4", transData.getAmount());
    }

    protected void setBitData11(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("11", String.valueOf(transData.getTraceNo()));
    }

    protected void setBitData12(@NonNull AmexTransData transData) throws Iso8583Exception {
        String temp = transData.getDateTime();
        if (temp != null && !temp.isEmpty()) {
            String date = temp.substring(4, 8);
            String time = temp.substring(8);
            setBitData("12", time);
            setBitData("13", date);
        }
    }

    protected void setBitData14(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("14", transData.getExpDate());
    }

    protected void setBitData22(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("22", getInputMethod(transData.getEnterMode(), transData.isHasPin()));
    }

    private void setBitData24(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("24", transData.getNii());
    }

    protected void setBitData25(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("25", "00");
    }

    protected void setBitData35(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("35", transData.getTrack2());
    }

    protected void setBitData37(@NonNull AmexTransData transData) throws Iso8583Exception {
        if (transData.getRefNo() != null && !TextUtils.isEmpty(transData.getRefNo())) {
            setBitData("37", transData.getRefNo());
        }
    }

    protected void setBitData38(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("38", transData.getOrigAuthCode());
    }

    private void setBitData41(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("41", transData.getTerminalId());
    }

    private void setBitData42(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("42", transData.getMerchantId());
    }

    void setBitData45(@NonNull AmexTransData transData) throws Iso8583Exception {
        //do nothing
        String track1 = transData.getTrack1();
        if (!track1.isEmpty()) {
            setBitData("45", transData.getTrack1());
        }
    }

    void setBitData52(@NonNull AmexTransData transData) throws Iso8583Exception {
        if (transData.isHasPin() && transData.getPin() != null) {
            setBitData("52", PackAmexManager.getInstance().getConvert().strToBcd(transData.getPin(), IConvert.EPaddingPosition.PADDING_LEFT));
        }
    }

    protected void setBitData54(@NonNull AmexTransData transData) throws Iso8583Exception {
        if (!transData.getTipAmount().equals("0") && !transData.getTipAmount().isEmpty()) {
            setBitData("54", ("000000000000" + transData.getTipAmount()).substring(transData.getTipAmount().length())); //amex requires data field 54 12 bytes
        }
    }

    private void setBitData55(@NonNull AmexTransData transData) throws Iso8583Exception {
        String temp = transData.getSendIccData();
        if (temp != null && temp.length() > 0) {
            setBitData("55", PackAmexManager.getInstance().getConvert().strToBcd(temp, IConvert.EPaddingPosition.PADDING_LEFT));
        }
    }

    void setBitData61(@NonNull AmexTransData transData) {
        //setBitData("61", "61      ");
    }

    protected void setBitData62(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("62", Utils.getPaddedNumber(transData.getInvoiceNo(), 6));
    }

    /**
     * @param enterMode
     * @param hasPin
     * @return
     */
    private String getInputMethod(AmexTransData.EnterMode enterMode, boolean hasPin) {
        if (enterMode == null) //AET-40
            return null;
        String inputMethod;
        try {
            inputMethod = enterModeMap.get(enterMode);
        } catch (Exception e) {
            Log.w(TAG, "", e);
            return null;
        }

        inputMethod += "3";

        return inputMethod;
    }
}
