package com.pax.amex.pack;

import androidx.annotation.NonNull;

import com.pax.amex.constant.AmexConstant;
import com.pax.amex.modle.AmexTransData;
import com.pax.amex.pack.ipacker.AmexListener;
import com.pax.amex.utils.Utils;
import com.pax.gl.pack.exception.Iso8583Exception;

/*
 * ============================================================================
 * = COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2018-? PAX Technology, Inc. All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                 Author	                        Action
 * 20/03/2019  	         KevinLiu           	Create/Add/Modify/Delete
 * ============================================================================
 */

public class PackAmexSaleVoid extends PackAmex {

    public PackAmexSaleVoid(AmexListener listener) {
        super(listener);
    }

    /**
     * pack 0200
     * @param transData
     * @return
     */
    @NonNull
    @Override
    public byte[] pack(@NonNull AmexTransData transData) {
        try {
//            setFinancialData(transData);
            setMandatoryData(transData);
            setCommonData(transData);
            setSelfData(transData);
            setBitData38(transData);
            //invoiceNo  6位数唯一
            setBitData62(transData);
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }

        int piiType = AmexConstant.TYPE_NO;
        if (transData.getEnterMode() == AmexTransData.EnterMode.MANUAL) {
            piiType = AmexConstant.TYPE_MANUAL;
        } else if (transData.getEnterMode() == AmexTransData.EnterMode.SWIPE) {
            piiType = AmexConstant.TYPE_SWIPE;
        }

        return pack(piiType);
    }

    private void setSelfData(AmexTransData transData) {
        try {
            //Processing code must be: for sale:024000. for refund 224000
            if (transData.getOrigTransType() == AmexTransData.AmexTransType.SALE) {
                setBitData("3", "024000");
            } else if (transData.getOrigTransType() == AmexTransData.AmexTransType.REFUND) {
                setBitData("3", "224000");
            } else if (transData.getOrigTransType() == AmexTransData.AmexTransType.OFFLINE_SALE && transData.getOfflineSendState() == AmexTransData.AmexOfflineStatus.OFFLINE_SENT) {
                //已上传offline交易
                setBitData("3", "024000");
            }

            //Amount must be zeros
            setBitData("4", "000000000000");

            setBitData("37", transData.getOrigRefNo());
        } catch (Iso8583Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void setBitData38(@NonNull AmexTransData transData) throws Iso8583Exception {
        AmexTransData.AmexTransType origTransType = transData.getOrigTransType();
        if (origTransType == AmexTransData.AmexTransType.SALE ||
                origTransType == AmexTransData.AmexTransType.REFUND) {
            //AMEX VOID SALE 及 AMEX VOID REFUND报文要带原交易F38
            setBitData("38", transData.getOrigAuthCode());
        }
    }

    @Override
    protected void setBitData62(@NonNull AmexTransData transData) throws Iso8583Exception {
        setBitData("62", Utils.getPaddedNumber(transData.getOrigInvoiceNo(), 6));
    }

}
