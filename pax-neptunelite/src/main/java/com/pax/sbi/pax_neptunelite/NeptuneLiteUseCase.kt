package com.pax.sbi.pax_neptunelite

import android.os.Build
import android.util.Log
import com.pax.api.BaseSystemManager
import com.pax.api.PedManager
import com.pax.api.PrintException
import com.pax.api.PrintManager
import java.lang.StringBuilder

/**
 * <AUTHOR>
 * @Date 2024/5/29
 */
const val TAG = "NeptuneLiteUseCase"
object NeptuneLiteUseCase {
    private lateinit var printers: PrintManager
    private lateinit var ped: PedManager
    private lateinit var terminalSn:String
    private const val UNKNOWN_TERMINAL_SN = "unknown"
    private const val UNKNOWN_PRINTER_ERROR = "Printer unknown error"
    private const val A910S_FLAG = "A910S"
    fun initDal(){
        printers = PrintManager.getInstance()
        ped = PedManager.getInstance()
        kotlin.runCatching {
            terminalSn = BaseSystemManager.getInstance().readSN()
        }.onFailure {
            terminalSn = UNKNOWN_TERMINAL_SN
            Log.e(TAG, "initDal error: ${it.message}")
        }
    }

    fun isA910S() = A910S_FLAG == Build.MODEL

    fun printKeyResult(str:String):String {
            try {
            printers.apply {
                prnInit()
                prnSetGray(500)
                prnSpaceSet(0x01,0x05)
                prnStr(str,null)
                prnStep(60)
                prnStart()
                return ""
            }
        } catch (e: PrintException) {
            Log.e(TAG, "printBitmap error: ${e.message}")
            return e.message ?: UNKNOWN_PRINTER_ERROR
        }
    }

    fun checkKey(index:Byte):KeyData?{
        try {
            val keyInfo = ped.pedQueryKeyInfo(0x07,index)
            val kcv  =  keyInfo.aucKCV
            val ksn  =  keyInfo.aucIKSN
            return KeyData(index.toString(),bcdToString(kcv).take(6),bcdToString(ksn))
        }catch(e:Exception){
            Log.w(TAG, "queryKey $index Error: ",e )
            return null
        }
    }

    fun getSerialNo() = terminalSn

    private fun bcdToString(bcd: ByteArray): String {
        val stringBuilder = StringBuilder()
        for (byte in bcd) {
            val hi = (byte.toInt() and 0xf0) shr 4
            val lo = byte.toInt() and 0x0f
            stringBuilder.append(hi)
            stringBuilder.append(lo)
        }
        return stringBuilder.toString()
    }

    fun release(){
        printers.finalize()
        ped.finalize()
    }

}