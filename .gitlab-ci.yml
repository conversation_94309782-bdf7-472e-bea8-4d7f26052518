# 定义 CI 流程的阶段
stages:
    - "codeCheck" # 代码的初步检查
    - "build"     # 构建 APK
    - "prepareScan"   # 执行扫描前的准备工作
    - "scan"      # 执行代码扫描或 APK 扫描
    - "afterScan"   # 执行扫描后的逻辑
    - "deploy"    # 应用上传
    - "notify"    # 上传通知
    - "test"      # 自动化测试

# CI 缓存，目前只缓存 .gradle 文件夹
cache:
    key: ${CI_PROJECT_ID}
    paths:
        - .gradle/

# local.properties 文件检查，此文件用来声明本地的 SDK 路径，每个人电脑本地的 SDK 路径都不一样，所以不应该上传此文件
localPropFileCheck:
    stage: codeCheck
    rules:
        - exists:   # 只有当项目根目录存在 local.properties 文件时才会将此 Job 添加到 CI 流程中
            - "local.properties"
    script:
        - echo "DO NOT upload local.properties!"
        - exit 1    # 返回值不为 0 时，此 Job 报错，终止 CI 流程。此时开发同事应当移除项目中的 local.properties 文件后再进行提交
    tags:   # 指定此 Job 运行在哪个 Runner 上
        - sit

# 签名文件检查，签名文件不允许上传
signFileCheck:
    stage: codeCheck
    rules:
        - exists:
              - "**.jks"
    script:
        - echo "DO NOT upload sign file!"
        - exit 1
    tags:
        - sit

# 构建 Release 应用，各项目需要根据实际情况进行配置
buildReleaseApk:
    stage: build
    rules:  # 当请求合并到 dev 分支或者 release 分支时，触发构建工作
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^dev.*/
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release.*/
    before_script:  # 这里设置了 Android SDK/JDK 路径。如果需要更新项目构建所使用的 Android SDK 版本，请通知 QA 在服务器端进行设置，并更改此处的配置
        - export ANDROID_HOME="/opt/android/sdk"
        - export JAVA_HOME="/opt/java/openjdk/jdk11" # 根据实际需求修改，例如需要使用JDK8，就改成：jdk8"
        - chmod +x ./gradlew    # 给 Gradle 脚本赋予执行的权限，这个不用动
        - cp /opt/keystore/old/* . # 拷贝 KeyStore，区分老签名和新签名，拷贝到项目中的地址也要注意
    script: # 执行构建工作
        - echo "Build release package"
        - ./gradlew :app:assembleStandard   # 这里请根据项目的实际情况来修改
    cache:  # 将构建过程中生成的 class 文件缓存下来，方便 SonarQube 代码扫描使用
        key: ${CI_COMMIT_SHA}   # 以 Commit SHA 作为 Key，这样如果提交了新代码，那么缓存就会被清空
        paths:
            - "**/build/intermediates/javac"
            - "sonar-project.properties"
    artifacts:  # 构建产物，主要是 APK 文件。如果有其他需要使用的文件（例如生成的 SDK 文件等），也请添加到这里，否则后续的流程无法将其上传到 Confluence
        paths:  # 产物路径，这里支持使用通配符，* 表示任意字符，** 表示任意字符与目录
            - "**/build/outputs/apk/**/*.apk"   # 保留 APK 文件，因为需要扫描 APK 文件以及发布 APK
        untracked: false
        when: on_success    # 只有当构建成功时才输出产物
        expire_in: 1 day    # 过期时间为 1 天，因为这些产物都是马上就被复制走了，所以 GitLab 上不需要保留太久
    tags:
        - sit

# SonarQube 代码扫描，不必修改此处配置
sonarQubeScan:
    stage: scan
    variables:
        SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
        GIT_DEPTH: "0"
    needs:
        - buildReleaseApk
    rules:  # 仅在合并到 dev 或者 release 分支时进行扫描
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^dev.*/
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release.*/
    script:
        - sonar-scanner
            -Dsonar.host.url=$SONAR_HOST_URL
            -Dsonar.login=$SONAR_USER_NAME
            -Dsonar.password=$SONAR_USER_PW
            -Dsonar.qualitygate.wait=true
    cache:
        key: ${CI_COMMIT_SHA}   # 使用与 buildReleaseApk 相同的 Key，这样就不需要再次 build 了
        paths:
            - "**/build/intermediates/javac"
            - "sonar-project.properties"
    tags:
        - sit

# APK 上传到 FTP，执行安全扫描，不必修改此处配置
uploadFtp:
    stage: prepareScan
    needs:
        - buildReleaseApk
    rules:  # 仅在合并到 release 分支时才需要执行 APK 的安全扫描
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release.*/
    before_script:
        - export JAVA_HOME="/opt/java/openjdk/jdk11"
        - export ANDROID_HOME="/opt/android/sdk"
        - chmod +x ./gradlew
        - cp /opt/keystore/old/* .
        # 创建临时目录
        - mkdir -p /opt/CI-Temp/confluence/$CI_PROJECT_NAME || echo "Directory already exists"
        - mkdir -p /opt/CI-Temp/other/$CI_PROJECT_NAME || echo "Directory already exists"

        # 清空临时文件，释放内存
        - rm -r /opt/CI-Temp/confluence/$CI_PROJECT_NAME/* || echo "The file is empty"
        - rm -r /opt/CI-Temp/other/$CI_PROJECT_NAME/* || echo "The file is empty"
        - rm -r /opt/ftp/$CI_PROJECT_NAME/confluence/newest/* || echo "The file is empty"

    script:
        - ./gradlew downloadLicenses    # 先生成依赖 License 分析的报告
        - find . -iname "*.apk" -type f -exec cp {} /opt/CI-Temp/confluence/$CI_PROJECT_NAME \;     # 查找构建得到的 APK，并将所有 APK 放到待上传的目录下
        - cp ./build/reports/license/dependency-license.json /opt/CI-Temp/other/$CI_PROJECT_NAME   # 将依赖 License 分析的报告放到待上传的目录下
        - cp ./security_test_whitelist.py /opt/CI-Temp/other/$CI_PROJECT_NAME  # 将安全扫描白名单放到待上传的目录下
        - /opt/CI-sh/upload-to-ftp.sh $CI_PROJECT_NAME  # 执行 FTP 上传
    tags:
        - sit

# 执行安全扫描，不必修改此处配置
securityScan:
    stage: scan
    needs:
        - uploadFtp
    rules:  # 仅在请求合并到 release 分支时才需要执行 APK 的安全扫描
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release.*/
    script: # 这里的脚本不要改动
        - cmd /C "set LANG=UTF-8"
        - D:/autoTest/script/checkout-from-git.bat $CI_PROJECT_NAME
        - D:/autoTest/script/download-from-ftp.bat $CI_PROJECT_NAME
        - python D:/autoTest/script/runSecurity.py $CI_PROJECT_NAME $CI_PROJECT_ID $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    tags:
        - slave2

# 上传安全扫描报告，仅在安全扫描失败时才执行。不必修改此处配置。
uploadSecurityScanReport:
    stage: afterScan
    needs:
        - securityScan
    rules:  # 仅在请求合并到 release 分支，并且前面的 Job 执行失败时才需要上传安全扫描报告
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release.*/
          allow_failure: true
          when: on_failure
    script:
        - copy "D:/autoTest/workspace/$CI_PROJECT_NAME/SecurityScan/security_test_log.txt" .    # 将扫描报告拷贝到本项目下
    artifacts:
        expose_as: "Security Test Log"  # 在 Merge Request 中显示的名称
        when: on_success    # 只有当本 Job 的命令执行成功时（也就是拷贝扫描报告文件成功时）才发布为产物
        expire_in: 30 days  # 过期时间
        paths:  # 要发布的产物文件列表
            - "security_test_log.txt"
    tags:
        - slave2

# 打包上传到 Confluence，不必修改此处配置
uploadConfluence:
    stage: deploy
    variables:
        PROJECT_GIT_URL: "${CI_PROJECT_URL}.git"
    rules:  # 仅在批准合并到 release 分支或 master 分支时才需要上传 Confluence
        - if: $CI_COMMIT_BRANCH =~ /^release.*/
        - if: $CI_COMMIT_BRANCH =~ /^master.*/
    script:
        - echo "Upload APK to Confluence"
        - D:/autoTest/script/runUploadApkToConfluence.bat $CONFLUENCE_PRODUCTION_PAGE_ID $PROJECT_GIT_URL $CI_COMMIT_BRANCH $CI_PROJECT_NAME
    tags:
        - slave2

# 发送提测邮件，仅在发布到 Confluence 后才需要发送该邮件。不必修改此处配置
sendReleaseEmail:
    stage: notify
    needs:
        - uploadConfluence
    rules:
        - if: $CI_COMMIT_BRANCH =~ /^release.*/
        - if: $CI_COMMIT_BRANCH =~ /^master.*/
    script:
        - python D:/autoTest/script/email_sender.py $CI_PROJECT_NAME $CI_COMMIT_BRANCH $GITLAB_USER_NAME $CI_PROJECT_ID
    tags:
        - slave2

# 执行冒烟自动化测试，此处配置需要联系 QA 后方可修改
autoSmokeTest:
    stage: test
    needs:
        - uploadConfluence
    rules:  # 仅在批准合并到 release 分支时才需要自动化测试
        - if: $CI_COMMIT_BRANCH =~ /^release.*/
    script:
        # 检出自动化测试脚本工程，第三个参数为脚本存放的branch名称，需传入正确的值
        - D:/autoTest/script/checkout-from-git.bat $CI_PROJECT_URL $CI_PROJECT_NAME autoTest
        # 执行自动化测试，最多可传入三个参数，分别为项目名、bat脚本名称和子目录（若bat脚本存放在子目录，则需要传入第三个参数，否则只需要传入前两个参数）
        - D:/autoTest/script/runAutoTest.bat $CI_PROJECT_NAME PAX_EDC_UI4.bat PAX_EDC_A_UI4
    allow_failure: true
    timeout: 1 day
    tags:
        - slave1
